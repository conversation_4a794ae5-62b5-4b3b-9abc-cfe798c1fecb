{"version": 3, "file": "index.js", "sources": ["pages/users/privacy/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlcnMvcHJpdmFjeS9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view>\r\n    <!-- <navbar ref=\"navbar\"></navbar> -->\r\n    <view class=\"content\">\r\n      <mp-html :content=\"content\" :tag-style=\"tagStyle\"></mp-html>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue'\r\nimport { getUserAgreement } from '@/api/user.js'\r\nimport { onLoad } from '@dcloudio/uni-app'\r\nimport mpHtml from '@/uni_modules/mp-html/components/mp-html/mp-html.vue'\r\n\r\n// 获取app实例\r\nconst app = getApp()\r\n\r\n// 响应式数据\r\nconst content = ref(\"\")\r\nconst navH = ref(0)\r\nconst tagStyle = ref({\r\n  img: 'width:100%;display:block;',\r\n  table: 'width:100%',\r\n  video: 'width:100%'\r\n})\r\n\r\n// 初始化导航栏高度\r\nconst initNavHeight = () => {\r\n  // #ifdef MP\r\n  navH.value = app.globalData.navHeight || 88\r\n  // #endif\r\n  // #ifdef H5\r\n  navH.value = 96\r\n  // #endif\r\n  // #ifdef APP-PLUS\r\n  navH.value = 30\r\n  // #endif\r\n}\r\n\r\n// 加载用户协议内容\r\nconst loadUserAgreement = async (type) => {\r\n  try {\r\n    console.log('开始加载用户协议，类型:', type)\r\n    const res = await getUserAgreement(type)\r\n    console.log('用户协议响应:', res)\r\n\r\n    if (res && res.data && res.data.content) {\r\n      content.value = res.data.content\r\n      console.log('内容已设置，长度:', content.value.length)\r\n\r\n      // 设置页面标题\r\n      uni.setNavigationBarTitle({\r\n        title: res.data.title || '用户协议'\r\n      })\r\n    } else {\r\n      console.error('响应数据格式错误:', res)\r\n      uni.showToast({\r\n        title: '内容加载失败',\r\n        icon: 'none',\r\n        duration: 2000\r\n      })\r\n    }\r\n  } catch (err) {\r\n    console.error('加载用户协议失败:', err)\r\n    // 显示错误提示\r\n    uni.showToast({\r\n      title: err.message || err || '加载失败',\r\n      icon: 'none',\r\n      duration: 2000\r\n    })\r\n  }\r\n}\r\n\r\n// 页面加载生命周期\r\nonLoad((options) => {\r\n  console.log('页面参数:', options)\r\n\r\n  // 初始化导航栏高度\r\n  initNavHeight()\r\n\r\n  // 加载用户协议内容\r\n  if (options && options.type) {\r\n    loadUserAgreement(options.type)\r\n  } else {\r\n    console.warn('缺少type参数')\r\n    uni.showToast({\r\n      title: '缺少必要参数',\r\n      icon: 'none',\r\n      duration: 2000\r\n    })\r\n  }\r\n})\r\n</script>\r\n\r\n<style>\r\npage {\r\n  background: #fff;\r\n}\r\n\r\n.content {\r\n  width: calc(100% - 60rpx);\r\n  margin: 0 30rpx;\r\n  font-size: 24rpx;\r\n  line-height: 2;\r\n  padding: 20rpx 0;\r\n  box-sizing: border-box;\r\n}\r\n</style>", "import MiniProgramPage from 'Z:/WWW/shejiao/vue3/pages/users/privacy/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "getUserAgreement", "onLoad", "MiniProgramPage"], "mappings": ";;;;;;AAaA,MAAM,SAAS,MAAW;;;;AAG1B,UAAM,MAAM,OAAQ;AAGpB,UAAM,UAAUA,cAAG,IAAC,EAAE;AACtB,UAAM,OAAOA,cAAG,IAAC,CAAC;AAClB,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACnB,KAAK;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,IACT,CAAC;AAGD,UAAM,gBAAgB,MAAM;AAE1B,WAAK,QAAQ,IAAI,WAAW,aAAa;AAAA,IAQ3C;AAGA,UAAM,oBAAoB,OAAO,SAAS;AACxC,UAAI;AACFC,sBAAAA,MAAY,MAAA,OAAA,uCAAA,gBAAgB,IAAI;AAChC,cAAM,MAAM,MAAMC,SAAgB,iBAAC,IAAI;AACvCD,sBAAAA,0DAAY,WAAW,GAAG;AAE1B,YAAI,OAAO,IAAI,QAAQ,IAAI,KAAK,SAAS;AACvC,kBAAQ,QAAQ,IAAI,KAAK;AACzBA,wBAAA,MAAA,MAAA,OAAA,uCAAY,aAAa,QAAQ,MAAM,MAAM;AAG7CA,wBAAAA,MAAI,sBAAsB;AAAA,YACxB,OAAO,IAAI,KAAK,SAAS;AAAA,UACjC,CAAO;AAAA,QACP,OAAW;AACLA,wBAAAA,MAAc,MAAA,SAAA,uCAAA,aAAa,GAAG;AAC9BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAAA,QACF;AAAA,MACF,SAAQ,KAAK;AACZA,sBAAAA,MAAA,MAAA,SAAA,uCAAc,aAAa,GAAG;AAE9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,IAAI,WAAW,OAAO;AAAA,UAC7B,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACF;AAAA,IACH;AAGAE,kBAAM,OAAC,CAAC,YAAY;AAClBF,oBAAAA,MAAY,MAAA,OAAA,uCAAA,SAAS,OAAO;AAG5B,oBAAe;AAGf,UAAI,WAAW,QAAQ,MAAM;AAC3B,0BAAkB,QAAQ,IAAI;AAAA,MAClC,OAAS;AACLA,sBAAAA,MAAA,MAAA,QAAA,uCAAa,UAAU;AACvBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACF;AAAA,IACH,CAAC;;;;;;;;;;;AC3FD,GAAG,WAAWG,SAAe;"}