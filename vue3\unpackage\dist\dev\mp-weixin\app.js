"use strict";
const common_vendor = require("./common/vendor.js");
const config_app = require("./config/app.js");
const api_public = require("./api/public.js");
const libs_routine = require("./libs/routine.js");
const utils_index = require("./utils/index.js");
const api_api = require("./api/api.js");
const api_user = require("./api/user.js");
const utils_cache = require("./utils/cache.js");
const utils_theme = require("./utils/theme.js");
const store_index = require("./store/index.js");
const stores_index = require("./stores/index.js");
const utils_util = require("./utils/util.js");
const libs_new_chat = require("./libs/new_chat.js");
const utils_lang = require("./utils/lang.js");
const libs_permission = require("./libs/permission.js");
const utils_validate = require("./utils/validate.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/note/circlemember.js";
  "./pages/index/shop.js";
  "./pages/index/dynamic.js";
  "./pages/index/message.js";
  "./pages/index/center.js";
  "./pages/activity/index.js";
  "./pages/activity/details.js";
  "./pages/search/index.js";
  "./pages/user/details.js";
  "./pages/note/add.js";
  "./pages/note/details.js";
  "./pages/note/video.js";
  "./pages/note/circle.js";
  "./pages/note/manghe.js";
  "./pages/note/publish-tree-hole-simple.js";
  "./pages/note/my-tree-hole.js";
  "./pages/note/detail.js";
  "./pages/users/user_sgin_list/index.js";
  "./pages/users/user_sgin/index.js";
  "./pages/goods/classify.js";
  "./pages/goods/cart.js";
  "./pages/goods/details.js";
  "./pages/goods/evaluate.js";
  "./pages/order/settlement.js";
  "./pages/order/index.js";
  "./pages/order/details.js";
  "./pages/center/card.js";
  "./pages/center/means.js";
  "./pages/center/circle.js";
  "./pages/center/follow.js";
  "./pages/center/address.js";
  "./pages/setting/privacy.js";
  "./pages/setting/index.js";
  "./pages/setting/logout.js";
  "./pages/setting/xinxuan.js";
  "./pages/setting/webview.js";
  "./pages/users/wechat_login/index.js";
  "./pages/users/login/index.js";
  "./pages/users/user_pwd_edit/index.js";
  "./pages/users/user_phone/index.js";
  "./pages/users/binding_phone/index.js";
  "./pages/center/circle-create.js";
  "./pages/setting/realname.js";
  "./pages/users/user_integral/index.js";
  "./pages/topic/index.js";
  "./pages/topic/details.js";
  "./pages/center/visitor.js";
  "./pages/users/privacy/index.js";
}
const _sfc_main = {
  __name: "App",
  setup(__props) {
    const { HTTP_REQUEST_URL } = config_app.config;
    const instance = common_vendor.getCurrentInstance();
    const store = common_vendor.useStore();
    const APP_CONFIG = {
      name: "CRMEB社交电商",
      version: "3.0.0",
      debug: true,
      // 性能监控配置
      performance: {
        enableMonitoring: true,
        reportInterval: 3e4
        // 30秒上报一次
      },
      // 错误处理配置
      errorHandling: {
        enableReporting: true,
        maxRetries: 3
      },
      // getApp()支持配置
      getAppSupport: {
        enableGlobalMethods: true,
        enablePerformanceMonitor: true,
        enableDebugInfo: true
      }
    };
    const globalData = common_vendor.reactive({
      // 用户相关
      user: {
        spid: 0,
        isLogin: false,
        userInfo: {},
        locale: ""
      },
      // 应用状态
      app: {
        code: 0,
        isIframe: false,
        tabbarShow: true,
        windowHeight: 0,
        navHeight: 0,
        navH: 0
      },
      // 菜单和配置
      config: {
        MyMenus: [],
        basicConfig: null,
        colorStatus: null,
        viewColor: null
      }
    });
    const isLogin = common_vendor.computed(() => store.getters.isLogin);
    const cartNum = common_vendor.computed(() => store.getters.cartNum);
    const performanceMonitor = {
      startTime: Date.now(),
      metrics: common_vendor.reactive({
        appLaunchTime: 0,
        firstPageLoadTime: 0,
        apiCallCount: 0,
        errorCount: 0
      }),
      // 记录应用启动时间
      recordLaunchTime() {
        this.metrics.appLaunchTime = Date.now() - this.startTime;
      },
      // 记录首页加载时间
      recordFirstPageLoad() {
        this.metrics.firstPageLoadTime = Date.now() - this.startTime;
      },
      // 增加API调用计数
      incrementApiCall() {
        this.metrics.apiCallCount++;
      },
      // 增加错误计数
      incrementError() {
        this.metrics.errorCount++;
      }
    };
    const handleError = (error, context = "") => {
      performanceMonitor.incrementError();
      if (APP_CONFIG.debug) {
        common_vendor.index.__f__("error", "at App.vue:110", `[${context}] Error:`, error);
      }
      if (APP_CONFIG.errorHandling.enableReporting) {
        reportError(error, context);
      }
    };
    const reportError = (error, context) => {
      var _a, _b;
      try {
        const errorInfo = {
          message: error.message || String(error),
          stack: error.stack,
          context,
          timestamp: Date.now(),
          userAgent: (navigator == null ? void 0 : navigator.userAgent) || "unknown",
          url: ((_a = window == null ? void 0 : window.location) == null ? void 0 : _a.href) || "app",
          userId: ((_b = globalData.user.userInfo) == null ? void 0 : _b.uid) || "anonymous"
        };
        if (APP_CONFIG.debug) {
          common_vendor.index.__f__("log", "at App.vue:137", "Error reported:", errorInfo);
        }
      } catch (reportError2) {
        if (APP_CONFIG.debug) {
          common_vendor.index.__f__("error", "at App.vue:141", "Failed to report error:", reportError2);
        }
      }
    };
    common_vendor.watch(isLogin, (newValue, oldValue) => {
      try {
        globalData.user.isLogin = newValue;
        if (newValue) {
        } else {
          store.commit("indexData/setCartNum", "");
          globalData.user.userInfo = {};
        }
      } catch (error) {
        handleError(error, "Login Status Watch");
      }
    }, { immediate: true });
    common_vendor.watch(cartNum, (newCart) => {
      try {
        const cartCount = Number(newCart) || 0;
        store.commit("indexData/setCartNum", cartCount.toString());
        common_vendor.index.$emit("updateCartBadge", {
          count: cartCount,
          show: cartCount > 0
        });
      } catch (error) {
        handleError(error, "Cart Number Watch");
      }
    }, { immediate: true });
    const handleSpreadParams = (queryData) => {
      try {
        if (queryData.query.spread) {
          utils_cache.Cache.set("spread", queryData.query.spread);
          globalData.user.spid = queryData.query.spread;
          globalData.user.pid = queryData.query.spread;
          utils_index.silenceBindingSpread(globalData);
        }
        if (queryData.query.spid) {
          utils_cache.Cache.set("spread", queryData.query.spid);
          globalData.user.spid = queryData.query.spid;
          globalData.user.pid = queryData.query.spid;
          utils_index.silenceBindingSpread(globalData);
        }
      } catch (error) {
        handleError(error, "Handle Spread Params");
      }
    };
    const handleMiniProgramScene = (queryData) => {
      try {
        if (queryData.query.scene) {
          const util = instance.appContext.config.globalProperties.$util;
          const param = util.getUrlParams(decodeURIComponent(queryData.query.scene));
          if (param.pid) {
            utils_cache.Cache.set("spread", param.pid);
            globalData.user.spid = param.pid;
            globalData.user.pid = param.pid;
          } else {
            const sceneHandlers = {
              1047: () => globalData.app.code = queryData.query.scene,
              // 扫描小程序码
              1048: () => globalData.app.code = queryData.query.scene,
              // 长按图片识别小程序码
              1049: () => globalData.app.code = queryData.query.scene,
              // 手机相册选取小程序码
              1001: () => globalData.user.spid = queryData.query.scene
              // 直接进入小程序
            };
            const handler = sceneHandlers[queryData.scene];
            if (handler) {
              handler();
            }
          }
          utils_index.silenceBindingSpread(globalData);
        }
      } catch (error) {
        handleError(error, "Handle MiniProgram Scene");
      }
    };
    const handleShow = () => {
      try {
        const queryData = common_vendor.index.getEnterOptionsSync();
        handleSpreadParams(queryData);
        handleMiniProgramScene(queryData);
      } catch (error) {
        handleError(error, "App Show");
      }
    };
    const initBasicConfig = async () => {
      try {
        const res = await api_public.basicConfig();
        globalData.config.basicConfig = res.data;
        common_vendor.index.setStorageSync("BASIC_CONFIG", res.data);
        return res.data;
      } catch (error) {
        handleError(error, "Init Basic Config");
        return null;
      }
    };
    const initThemeConfig = async () => {
      try {
        const res = await api_api.colorChange("color_change");
        const themeMap = {
          1: "blue",
          2: "green",
          3: "red",
          4: "pink",
          5: "orange"
        };
        const status = res.data.status;
        const themeKey = themeMap[status] || "red";
        const selectedTheme = utils_theme.themeList[themeKey];
        common_vendor.index.setStorageSync("is_diy", res.data.is_diy);
        common_vendor.index.setStorageSync("color_status", status);
        common_vendor.index.setStorageSync("viewColor", selectedTheme);
        globalData.config.colorStatus = status;
        globalData.config.viewColor = selectedTheme;
        common_vendor.index.$emit("is_diy", res.data.is_diy);
        common_vendor.index.$emit("ok", selectedTheme, status);
        return { status, selectedTheme };
      } catch (error) {
        handleError(error, "Init Theme Config");
        return null;
      }
    };
    const handleH5Logic = (option) => {
    };
    const handleLaunch = async (option) => {
      try {
        utils_index.initWorkermanUrl();
        const configPromises = [
          initBasicConfig(),
          initThemeConfig()
        ];
        handleH5Logic(option);
        await Promise.allSettled(configPromises);
        await initLanguageConfig();
        initSystemInfo();
        await handlePlatformSpecificLogic(option);
        initCopyRight();
        performanceMonitor.recordLaunchTime();
        if (APP_CONFIG.performance.enableMonitoring) {
          startPerformanceMonitoring();
        }
        setupGlobalErrorHandling();
      } catch (error) {
        handleError(error, "App Launch");
      }
    };
    const startPerformanceMonitoring = () => {
      if (!APP_CONFIG.performance.enableMonitoring)
        return;
      setInterval(() => {
        const metrics = {
          ...performanceMonitor.metrics,
          memoryUsage: getMemoryUsage(),
          timestamp: Date.now()
        };
        if (APP_CONFIG.debug) {
          common_vendor.index.__f__("log", "at App.vue:377", "Performance metrics:", metrics);
        }
      }, APP_CONFIG.performance.reportInterval);
    };
    const getMemoryUsage = () => {
      try {
        return null;
      } catch (error) {
        return null;
      }
    };
    const setupGlobalErrorHandling = () => {
    };
    const initLanguageConfig = async () => {
      try {
        const res = await api_user.getLangVersion();
        const version = res.data.version;
        const currentVersion = common_vendor.index.getStorageSync("LANG_VERSION");
        if (version !== currentVersion) {
          const langRes = await api_user.getLangJson();
          const langKey = Object.keys(langRes.data)[0];
          utils_cache.Cache.set("locale", langKey);
          globalData.user.locale = langKey;
          const i18n = instance.appContext.config.globalProperties.$i18n;
          if (i18n) {
            i18n.setLocaleMessage(langKey, langRes.data[langKey]);
          }
          common_vendor.index.setStorageSync("localeJson", langRes.data);
        }
        common_vendor.index.setStorageSync("LANG_VERSION", version);
      } catch (error) {
        handleError(error, "Init Language Config");
      }
    };
    const initSystemInfo = () => {
      try {
        const menuButtonInfo = common_vendor.index.getMenuButtonBoundingClientRect();
        globalData.app.navH = menuButtonInfo.top * 2 + menuButtonInfo.height / 2;
        const version = common_vendor.index.getSystemInfoSync().SDKVersion;
        const isNewVersion = libs_routine.Routine.compareVersion(version, "2.21.3") >= 0;
        utils_cache.Cache.set("MP_VERSION_ISNEW", isNewVersion);
      } catch (error) {
        handleError(error, "Init System Info");
      }
    };
    const handlePlatformSpecificLogic = async (option) => {
      try {
        await handleMiniProgramLogic(option);
      } catch (error) {
        handleError(error, "Handle Platform Specific Logic");
      }
    };
    const handleMiniProgramLogic = async (option) => {
      try {
        if (!HTTP_REQUEST_URL) {
          common_vendor.index.__f__(
            "error",
            "at App.vue:508",
            "请配置根目录下的config.js文件中的 'HTTP_REQUEST_URL'\n\n请修改开发者工具中【详情】->【AppID】改为自己的Appid\n\n请前往后台【小程序】->【小程序配置】填写自己的 appId and AppSecret"
          );
          return false;
        }
        const startParamObj = common_vendor.wx$1.getEnterOptionsSync();
        if (common_vendor.wx$1.canIUse("getUpdateManager") && startParamObj.scene !== 1154) {
          handleMiniProgramUpdate();
        }
        return true;
      } catch (error) {
        handleError(error, "Handle MiniProgram Logic");
        return false;
      }
    };
    const handleMiniProgramUpdate = () => {
      try {
        const updateManager = common_vendor.wx$1.getUpdateManager();
        updateManager.onCheckForUpdate((res) => {
          if (res.hasUpdate) {
            updateManager.onUpdateReady(() => {
              common_vendor.wx$1.showModal({
                title: "更新提示",
                content: "新版本已经下载好，是否重启当前应用？",
                success(modalRes) {
                  if (modalRes.confirm) {
                    updateManager.applyUpdate();
                  }
                }
              });
            });
            updateManager.onUpdateFailed(() => {
              common_vendor.wx$1.showModal({
                title: "发现新版本",
                content: "请删除当前小程序，重新搜索打开..."
              });
            });
          }
        });
      } catch (error) {
        handleError(error, "Handle MiniProgram Update");
      }
    };
    const initCopyRight = async () => {
      try {
        const res = await api_api.getCrmebCopyRight();
        common_vendor.index.setStorageSync("copyRight", res.data);
      } catch (error) {
        handleError(error, "Init CopyRight");
      }
    };
    const handleRemoteRegister = async (remote_token) => {
      try {
        const res = await api_public.remoteRegister({ remote_token });
        const data = res.data;
        if (data.get_remote_login_url) {
        } else {
          store.commit("LOGIN", {
            token: data.token,
            time: data.expires_time - utils_cache.Cache.time()
          });
          store.commit("SETUID", data.userInfo.uid);
          globalData.user.isLogin = true;
          globalData.user.userInfo = data.userInfo;
        }
      } catch (error) {
        handleError(error, "Remote Register");
      }
    };
    const handleHide = () => {
    };
    const saveAppState = () => {
      try {
        const appState = {
          user: globalData.user,
          config: globalData.config,
          timestamp: Date.now()
        };
        common_vendor.index.setStorageSync("APP_STATE", appState);
      } catch (error) {
        handleError(error, "Save App State");
      }
    };
    const restoreAppState = () => {
      try {
        const savedState = common_vendor.index.getStorageSync("APP_STATE");
        if (savedState && savedState.timestamp) {
          const isExpired = Date.now() - savedState.timestamp > 24 * 60 * 60 * 1e3;
          if (!isExpired) {
            if (savedState.user) {
              Object.assign(globalData.user, savedState.user);
            }
            if (savedState.config) {
              Object.assign(globalData.config, savedState.config);
            }
            return true;
          }
        }
        return false;
      } catch (error) {
        handleError(error, "Restore App State");
        return false;
      }
    };
    const enhancedHandleShow = () => {
      try {
        restoreAppState();
        handleShow();
        performanceMonitor.recordFirstPageLoad();
      } catch (error) {
        handleError(error, "Enhanced App Show");
      }
    };
    const enhancedHandleHide = () => {
      try {
        saveAppState();
        handleHide();
      } catch (error) {
        handleError(error, "Enhanced App Hide");
      }
    };
    common_vendor.onLaunch((options) => {
      handleLaunch(options);
    });
    common_vendor.onShow((options) => {
      enhancedHandleShow();
    });
    common_vendor.onHide(() => {
      enhancedHandleHide();
    });
    const setupGlobalApp = () => {
      var _a;
      const app2 = common_vendor.getCurrentInstance();
      if ((_a = app2 == null ? void 0 : app2.appContext) == null ? void 0 : _a.app) {
        app2.appContext.app.globalData = globalData;
        const globalProperties = app2.appContext.app.config.globalProperties;
        globalProperties.handleRemoteRegister = handleRemoteRegister;
        globalProperties.APP_CONFIG = APP_CONFIG;
        globalProperties.handleError = handleError;
        globalProperties.$globalData = globalData;
        globalProperties.$performanceMonitor = performanceMonitor;
        app2.appContext.app.version = APP_CONFIG.version;
        app2.appContext.app.name = APP_CONFIG.name;
        app2.appContext.app.utils = {
          // 获取用户信息
          getUserInfo: () => globalData.user.userInfo,
          // 检查登录状态
          isUserLoggedIn: () => globalData.user.isLogin,
          // 获取应用配置
          getAppConfig: () => APP_CONFIG,
          // 获取性能数据
          getPerformanceMetrics: () => performanceMonitor.metrics,
          // 更新用户信息
          updateUserInfo: (userInfo) => {
            globalData.user.userInfo = { ...globalData.user.userInfo, ...userInfo };
          },
          // 清理用户数据
          clearUserData: () => {
            globalData.user.isLogin = false;
            globalData.user.userInfo = {};
            globalData.user.spid = 0;
          },
          // 获取主题配置
          getThemeConfig: () => globalData.config.viewColor,
          // 获取基础配置
          getBasicConfig: () => globalData.config.basicConfig
        };
        return app2.appContext.app;
      }
      return null;
    };
    const appInstance = setupGlobalApp();
    if (appInstance) {
      if (APP_CONFIG.debug) {
        common_vendor.index.__f__("log", "at App.vue:791", "✅ App.vue: 全局数据已设置，可通过getApp().globalData访问");
        common_vendor.index.__f__("log", "at App.vue:792", "📊 性能监控已启用，可通过getApp().$performanceMonitor访问");
      }
    }
    return () => {
    };
  }
};
const app = common_vendor.createApp(_sfc_main);
const eventBus = {
  events: {},
  $emit(event, ...args) {
    if (this.events[event]) {
      this.events[event].forEach((callback) => callback(...args));
    }
  },
  $on(event, callback) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  },
  $off(event, callback) {
    if (this.events[event]) {
      const index = this.events[event].indexOf(callback);
      if (index > -1) {
        this.events[event].splice(index, 1);
      }
    }
  }
};
app.config.globalProperties.$util = utils_util.util;
app.config.globalProperties.$config = config_app.config;
app.config.globalProperties.$Cache = utils_cache.Cache;
app.config.globalProperties.$eventHub = eventBus;
app.config.globalProperties.$socket = new libs_new_chat.Socket();
app.config.productionTip = false;
app.config.globalProperties.$permission = libs_permission.ActivePermission;
app.config.globalProperties.$Debounce = utils_validate.Debounce;
_sfc_main.mpType = "app";
app.use(store_index.store);
app.use(stores_index.pinia);
app.use(utils_lang.i18n);
app.mount("#app");
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
