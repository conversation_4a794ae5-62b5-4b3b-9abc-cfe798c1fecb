
font-family: PingFang SC-Regular, PingFang SC;
/*
 * CRMEB社交电商 - 全局样式
 * 基于uni-app框架的响应式设计
 */

/* 滚动条样式 - 兼容性优化 */
.uni-scroll-view::-webkit-scrollbar,::-webkit-scrollbar {
	display: none;
	width: 0;
	height: 0;
	color: transparent;
}

/* 背景样式 */
.bfw {
	background: #fff;
}
.bf8 {
	background: rgba(248, 248, 248, 0.95);
	-webkit-backdrop-filter: blur(10px);
	backdrop-filter: blur(10px);
}
.bfh {
	background: rgba(0, 0, 0, 0.95);
	-webkit-backdrop-filter: blur(30px);
	backdrop-filter: blur(30px);
}

/* 阴影效果 */
.xwb {
	filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.25));
}

/* 文本溢出处理 */
.ohto {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.ohto2 {
	display: block;
	white-space: pre-line;
	display: -webkit-box;
	overflow: hidden;
	-webkit-line-clamp: 2;
	line-clamp: 2; /* 标准属性 */
	text-overflow: ellipsis;
	-webkit-box-orient: vertical;
}

/* 微标签样式 */
.microlabel {
	position: absolute;
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	background: #fa5150;
	border-width: 4rpx;
	border-style: solid;
	border-color: #fff;
}

/* 空状态样式 */
.empty-box {
	width: 100%;
	padding: 160rpx 0;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}
.empty-box image {
	width: 160rpx;
	height: 160rpx;
}
.empty-box .e1 {
	padding: 20rpx 0 10rpx;
	font-size: 26rpx;
	font-weight: 600;
}
.empty-box .e2 {
	color: #999;
	font-size: 20rpx;
	font-weight: 300;
}

/* 按钮重置 */
button::after {
	border-radius: 0;
	border: none;
}

/* 图片样式 */
img,
[alt],
img[alt] {
	max-width: 100%;
	height: auto;
	display: block;
}

/* 动画容器 */
.heio {
	justify-content: center;
	overflow: hidden;
	transition: height 0.45s ease-in-out;
}

/* 动画定义 */
@keyframes fadeIn {
0% { opacity: 0;
}
100% { opacity: 1;
}
}
@keyframes fadeOut {
0% { opacity: 1;
}
100% { opacity: 0;
}
}
@keyframes wobble {
10% { transform: rotate(15deg);
}
20% { transform: rotate(-10deg);
}
30% { transform: rotate(5deg);
}
40% { transform: rotate(-5deg);
}
50%, 100% { transform: rotate(0);
}
}
@keyframes likes {
0% { transform: scale(1);
}
25% { transform: scale(1.2);
}
50% { transform: scale(0.95);
}
100% { transform: scale(1);
}
}
@keyframes btnEffect {
0% { transform: translate(0);
}
10% { transform: translate(4px);
}
20% { transform: translate(-2px);
}
30% { transform: translate(4px);
}
40% { transform: translate(-2px);
}
50% { transform: translate(0);
}
}
@keyframes bottomUp {
0% { bottom: -240rpx;
}
100% { bottom: 0;
}
}

/* 动画类 */
.fade-in { animation: fadeIn 0.3s ease;
}
.fade-out { animation: fadeOut 0.3s ease;
}
.animate { animation: wobble 1.5s 0.15s linear infinite;
}
.hi { animation: likes 0.45s ease-in-out;
}
.effect { animation: btnEffect 3s both infinite;
}
.bUp { animation: bottomUp 0.6s ease;
}

/* 提示框样式 */
.tips-box {
	margin-top: 12vh;
	width: 100%;
	justify-content: center;
}
.tips-box .tips-item {
	padding: 0 50rpx;
	height: 100rpx;
	line-height: 100rpx;
	font-size: 24rpx;
	font-weight: 700;
	color: #fff;
	background: rgba(0, 0, 0, 0.85);
	border-radius: 50rpx;
}

/* 响应式设计 - 适配不同屏幕尺寸 */
@media screen and (max-width: 750rpx) {
.tips-box {
		margin-top: 8vh;
}
.empty-box {
		padding: 120rpx 0;
}
}
page{--status-bar-height:25px;--top-window-height:0px;--window-top:0px;--window-bottom:0px;--window-left:0px;--window-right:0px;--window-magin:0px}[data-c-h="true"]{display: none !important;}