"use strict";
const common_vendor = require("../../common/vendor.js");
const api_user = require("../../api/user.js");
const api_api = require("../../api/api.js");
const libs_login = require("../../libs/login.js");
const common_assets = require("../../common/assets.js");
const navbar = () => "../../components/navbar/navbar.js";
const uniLoadMore = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const uniPopup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
const emptyPage = () => "../../components/emptyPage/emptyPage.js";
const _sfc_main = {
  components: {
    navbar,
    uniLoadMore,
    uniPopup,
    emptyPage
  },
  data() {
    return {
      statusBarHeight: this.$store.state.statusBarHeight || 20,
      titleBarHeight: this.$store.state.titleBarHeight || 44,
      od: 0,
      id: 0,
      idx: -1,
      addressList: [],
      real_name: "",
      phone: "",
      detail: "",
      is_default: false,
      isEmpty: false,
      loadStatus: "loading",
      tipsTitle: "",
      // 地区选择相关
      region: ["省", "市", "区"],
      valueRegion: [0, 0, 0],
      multiArray: [],
      multiIndex: [0, 0, 0],
      district: [],
      cityId: 0,
      // 分页相关
      page: 1,
      limit: 20,
      loading: false,
      loadend: false,
      loadTitle: "加载更多"
    };
  },
  computed: {
    ...common_vendor.mapGetters(["isLogin"])
  },
  onLoad(options) {
    if (this.isLogin) {
      if (options.od) {
        this.od = options.od;
      }
      this.getAddressList(true);
      this.getCityList();
    } else {
      libs_login.toLogin();
    }
  },
  onShow() {
    if (this.isLogin) {
      this.getAddressList(true);
    }
  },
  methods: {
    // 获取地址列表 - 完全参考 user_address_list 页面
    getAddressList(isPage) {
      let that = this;
      if (isPage) {
        that.loadend = false;
        that.page = 1;
        that.$set(that, "addressList", []);
      }
      if (that.loading)
        return;
      if (that.loadend)
        return;
      that.loading = true;
      that.loadTitle = "";
      api_user.getAddressList({
        page: that.page,
        limit: that.limit
      }).then((res) => {
        let list = res.data;
        let loadend = list.length < that.limit;
        that.addressList = that.SplitArray(list, that.addressList);
        that.$set(that, "addressList", that.addressList);
        that.isEmpty = that.addressList.length === 0;
        that.loadend = loadend;
        that.loadTitle = loadend ? "我也是有底线的" : "加载更多";
        that.loadStatus = loadend ? "noMore" : "more";
        that.page = that.page + 1;
        that.loading = false;
      }).catch((err) => {
        that.loading = false;
        that.loadStatus = "more";
        that.loadTitle = "加载更多";
      });
    },
    // 添加SplitArray工具方法 - 参考页面使用的数组合并方法
    SplitArray(list, arr) {
      if (!Array.isArray(list))
        return arr || [];
      if (!Array.isArray(arr))
        return list;
      return arr.concat(list);
    },
    // 获取城市列表 - 参考 user_address 页面
    getCityList() {
      let that = this;
      api_api.getCity().then((res) => {
        that.district = res.data;
        that.initialize();
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/center/address.vue:216", "获取城市列表失败:", err);
      });
    },
    // 初始化地区选择器 - 参考 user_address 页面
    initialize() {
      let that = this;
      let province = [];
      let city = [];
      let area = [];
      if (!that.district || that.district.length === 0)
        return;
      let cityChildren = that.district[0].c || [];
      cityChildren.length ? cityChildren[0].c || [] : [];
      that.district.forEach((item, i) => {
        province.push(item.n);
        if (item.n === that.region[0]) {
          that.valueRegion[0] = i;
          that.multiIndex[0] = i;
        }
      });
      if (that.district[that.valueRegion[0]] && that.district[that.valueRegion[0]].c) {
        that.district[that.valueRegion[0]].c.forEach((item, i) => {
          if (that.region[1] === item.n) {
            that.valueRegion[1] = i;
            that.multiIndex[1] = i;
          }
          city.push(item.n);
        });
      }
      if (that.district[that.valueRegion[0]] && that.district[that.valueRegion[0]].c && that.district[that.valueRegion[0]].c[that.valueRegion[1]] && that.district[that.valueRegion[0]].c[that.valueRegion[1]].c) {
        that.district[that.valueRegion[0]].c[that.valueRegion[1]].c.forEach((item, i) => {
          if (that.region[2] === item.n) {
            that.valueRegion[2] = i;
            that.multiIndex[2] = i;
          }
          area.push(item.n);
        });
      }
      that.multiArray = [province, city, area];
    },
    // 地区选择改变 - 参考 user_address 页面
    bindPickerChange(e) {
      let multiIndex = this.multiIndex;
      let province = this.district[multiIndex[0]] || { c: [] };
      let city = province.c[multiIndex[1]] || { v: 0 };
      let multiArray = this.multiArray;
      let value = e.detail.value;
      this.region = [multiArray[0][value[0]], multiArray[1][value[1]], multiArray[2][value[2]]];
      this.cityId = city.v;
      this.valueRegion = [0, 0, 0];
      this.initialize();
    },
    // 地区选择列改变 - 参考 user_address 页面
    bindMultiPickerColumnChange(e) {
      let that = this;
      let column = e.detail.column;
      let value = e.detail.value;
      let currentCity = that.district[value] || { c: [] };
      let multiArray = that.multiArray;
      let multiIndex = that.multiIndex;
      multiIndex[column] = value;
      switch (column) {
        case 0:
          let areaList = currentCity.c[0] || { c: [] };
          multiArray[1] = currentCity.c.map((item) => {
            return item.n;
          });
          multiArray[2] = areaList.c.map((item) => {
            return item.n;
          });
          break;
        case 1:
          let cityList = that.district[multiIndex[0]].c[multiIndex[1]].c || [];
          multiArray[2] = cityList.map((item) => {
            return item.n;
          });
          break;
      }
      that.multiArray = multiArray;
      that.multiIndex = multiIndex;
    },
    // 确认添加/编辑地址 - 完全参考 user_address 页面的 formSubmit 方法
    confirmAdds() {
      let that = this;
      if (!that.real_name.trim())
        return that.Tips({
          title: "请填写收货人姓名"
        });
      if (!that.phone)
        return that.Tips({
          title: "请填写联系电话"
        });
      if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.phone))
        return that.Tips({
          title: "请输入正确的手机号码"
        });
      if (that.region[0] === "省")
        return that.Tips({
          title: "请选择所在地区"
        });
      if (!that.detail.trim())
        return that.Tips({
          title: "请填写详细地址"
        });
      let value = {
        id: that.id,
        real_name: that.real_name,
        phone: that.phone,
        address: {
          province: that.region[0],
          city: that.region[1],
          district: that.region[2],
          city_id: that.cityId
        },
        detail: that.detail,
        is_default: that.is_default ? 1 : 0
      };
      if (that.addressList.length === 0) {
        value.is_default = 1;
      }
      common_vendor.index.showLoading({
        title: "保存中",
        mask: true
      });
      api_user.editAddress(value).then((res) => {
        common_vendor.index.hideLoading();
        if (that.id) {
          that.Tips({
            title: "修改成功",
            icon: "success"
          });
        } else {
          that.Tips({
            title: "添加成功",
            icon: "success"
          });
        }
        setTimeout(function() {
          that.getAddressList(true);
          that.$refs.addsPopup.close();
        }, 1e3);
      }).catch((err) => {
        common_vendor.index.hideLoading();
        return that.Tips({
          title: err
        });
      });
    },
    // 编辑地址 - 参考 user_address_list 页面
    updateAddsClick(e) {
      let idx = e.currentTarget.dataset.idx;
      let item = this.addressList[idx];
      this.idx = idx;
      this.id = item.id;
      this.real_name = item.real_name;
      this.phone = item.phone;
      this.region = [item.province, item.city, item.district];
      this.detail = item.detail;
      this.is_default = item.is_default === 1;
      this.cityId = item.city_id || 0;
      this.initialize();
      this.$refs.addsPopup.open();
    },
    // 删除地址 - 参考 user_address_list 页面
    delAddsClick() {
      let that = this;
      common_vendor.index.showModal({
        content: "您确定要删除这个收货地址吗？",
        success: function(res) {
          if (res.confirm) {
            api_user.delAddress(that.id).then((res2) => {
              that.Tips({
                title: "删除成功",
                icon: "success"
              }, function() {
                that.getAddressList(true);
                that.$refs.addsPopup.close();
              });
            }).catch((err) => {
              return that.Tips({
                title: err
              });
            });
          }
        }
      });
    },
    // 设置默认地址 - 完全参考 user_address_list 页面的 radioChange 方法
    defaultAddsClick(e) {
      let that = this;
      let idx = e.currentTarget.dataset.idx;
      let address = that.addressList[idx];
      if (address == void 0)
        return that.Tips({
          title: "您设置的默认地址不存在!"
        });
      api_user.setAddressDefault(address.id).then((res) => {
        for (let i = 0, len = that.addressList.length; i < len; i++) {
          if (i == idx)
            that.addressList[i].is_default = true;
          else
            that.addressList[i].is_default = false;
        }
        that.Tips({
          title: "设置成功",
          icon: "success"
        }, function() {
          that.$set(that, "addressList", that.addressList);
        });
      }).catch((err) => {
        return that.Tips({
          title: err
        });
      });
    },
    // 使用微信地址 - 完全参考 user_address 和 user_address_list 页面
    weixinAddsClick() {
      let that = this;
      common_vendor.index.authorize({
        scope: "scope.address",
        success: function(res) {
          common_vendor.index.chooseAddress({
            success: function(res2) {
              let addressP = {};
              addressP.province = res2.provinceName;
              addressP.city = res2.cityName;
              addressP.district = res2.countyName;
              api_user.editAddress({
                address: addressP,
                is_default: 1,
                real_name: res2.userName,
                post_code: res2.postalCode,
                phone: res2.telNumber,
                detail: res2.detailInfo,
                id: 0,
                type: 1
              }).then((res3) => {
                that.Tips({
                  title: "添加成功",
                  icon: "success"
                }, function() {
                  that.getAddressList(true);
                });
              }).catch((err) => {
                return that.Tips({
                  title: err
                });
              });
            },
            fail: function(err) {
              if (err.errMsg == "chooseAddress:cancel")
                return that.Tips({
                  title: "取消选择"
                });
            }
          });
        },
        fail: function(res) {
          common_vendor.index.showModal({
            title: "您已拒绝导入微信地址权限",
            content: "是否进入权限管理，调整授权？",
            success(res2) {
              if (res2.confirm) {
                common_vendor.index.openSetting({
                  success: function(res3) {
                  }
                });
              } else if (res2.cancel) {
                return that.Tips({
                  title: "已取消！"
                });
              }
            }
          });
        }
      });
    },
    // 弹窗控制
    popupClick(show) {
      if (show) {
        this.id = 0;
        this.real_name = "";
        this.phone = "";
        this.region = ["省", "市", "区"];
        this.detail = "";
        this.is_default = false;
        this.valueRegion = [0, 0, 0];
        this.multiIndex = [0, 0, 0];
        this.initialize();
        this.$refs.addsPopup.open();
      } else {
        this.$refs.addsPopup.close();
      }
    },
    // 选择地址返回
    currentPages(e) {
      let id = e.currentTarget.dataset.id;
      if (this.od && id) {
        var pages = getCurrentPages();
        if (pages.length > 1) {
          pages[pages.length - 2].$vm.addressInfo(id);
        }
        common_vendor.index.navigateBack();
      }
    },
    // 提示弹窗 - 兼容原有方式和参考页面的 $util.Tips 方式
    opTipsPopup(title) {
      let that = this;
      that.tipsTitle = title;
      that.$refs.tipsPopup.open();
      setTimeout(function() {
        that.$refs.tipsPopup.close();
      }, 2e3);
    },
    // Tips 工具方法 - 替代 $util.Tips
    Tips(options, callback) {
      if (typeof options === "string") {
        options = { title: options };
      }
      if (options.icon === "success") {
        common_vendor.index.showToast({
          title: options.title,
          icon: "success",
          duration: 2e3,
          success: function() {
            if (callback && typeof callback === "function") {
              setTimeout(callback, 2e3);
            }
          }
        });
      } else {
        common_vendor.index.showToast({
          title: options.title,
          icon: "none",
          duration: 2e3,
          success: function() {
            if (callback && typeof callback === "function") {
              setTimeout(callback, 2e3);
            }
          }
        });
      }
    },
    // 修改默认地址状态
    ChangeIsDefault() {
      this.is_default = !this.is_default;
    },
    // 获取单个地址详情 - 参考 user_address 页面
    getUserAddress() {
      if (!this.id)
        return false;
      let that = this;
      api_user.getAddressDetail(this.id).then((res) => {
        let region = [res.data.province, res.data.city, res.data.district];
        that.real_name = res.data.real_name;
        that.phone = res.data.phone;
        that.detail = res.data.detail;
        that.is_default = res.data.is_default;
        that.$set(that, "region", region);
        that.cityId = res.data.city_id;
      }).catch((err) => {
        that.Tips({
          title: err || "获取地址详情失败"
        });
      });
    },
    // 新建地址
    addAddress() {
      this.addsPopupClick(true);
    }
  },
  // 触底加载更多
  onReachBottom() {
    this.getAddressList();
  }
};
if (!Array) {
  const _component_navbar = common_vendor.resolveComponent("navbar");
  const _component_emptyPage = common_vendor.resolveComponent("emptyPage");
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_component_navbar + _component_emptyPage + _easycom_uni_load_more2 + _easycom_uni_popup2)();
}
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_load_more + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.isEmpty
  }, $data.isEmpty ? {
    b: common_vendor.o($options.addAddress),
    c: common_vendor.p({
      title: "暂无收货地址",
      description: "可新建地址或使用微信地址",
      buttonText: "新建地址"
    })
  } : {
    d: common_vendor.f($data.addressList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.real_name),
        b: common_vendor.t(item.phone),
        c: common_vendor.t(item.province),
        d: common_vendor.t(item.city),
        e: common_vendor.t(item.district),
        f: common_vendor.t(item.detail),
        g: item.is_default == 1 ? "/static/img/c1.png" : "/static/img/c.png",
        h: index,
        i: common_vendor.o(($event) => $options.defaultAddsClick($event), index),
        j: index,
        k: common_vendor.o(($event) => $options.updateAddsClick($event), index),
        l: index,
        m: item.id,
        n: common_vendor.o(($event) => $options.currentPages($event), index)
      };
    }),
    e: common_assets._imports_0$14
  }, {
    f: common_vendor.p({
      status: $data.loadStatus
    }),
    g: $data.statusBarHeight + $data.titleBarHeight + "px",
    h: common_vendor.o(($event) => $options.popupClick(true)),
    i: common_vendor.o((...args) => $options.weixinAddsClick && $options.weixinAddsClick(...args)),
    j: common_vendor.t($data.id > 0 ? "编辑地址" : "新建地址"),
    k: common_assets._imports_0$4,
    l: common_vendor.o(($event) => $options.popupClick(false)),
    m: $data.real_name,
    n: common_vendor.o(($event) => $data.real_name = $event.detail.value),
    o: $data.phone,
    p: common_vendor.o(($event) => $data.phone = $event.detail.value),
    q: $data.region[0] !== "省"
  }, $data.region[0] !== "省" ? {
    r: common_vendor.t($data.region[0]),
    s: common_vendor.t($data.region[1]),
    t: common_vendor.t($data.region[2])
  } : {}, {
    v: common_vendor.o((...args) => $options.bindPickerChange && $options.bindPickerChange(...args)),
    w: $data.valueRegion,
    x: common_vendor.o((...args) => $options.bindMultiPickerColumnChange && $options.bindMultiPickerColumnChange(...args)),
    y: $data.multiArray,
    z: $data.detail,
    A: common_vendor.o(($event) => $data.detail = $event.detail.value),
    B: $data.is_default ? true : false,
    C: common_vendor.o((...args) => $options.ChangeIsDefault && $options.ChangeIsDefault(...args)),
    D: common_vendor.o((...args) => $options.confirmAdds && $options.confirmAdds(...args)),
    E: $data.id > 0
  }, $data.id > 0 ? {
    F: common_vendor.o((...args) => $options.delAddsClick && $options.delAddsClick(...args))
  } : {}, {
    G: common_vendor.sr("addsPopup", "ff186348-3"),
    H: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    }),
    I: common_vendor.t($data.tipsTitle),
    J: common_vendor.sr("tipsPopup", "ff186348-4"),
    K: common_vendor.p({
      type: "top",
      ["mask-background-color"]: "rgba(0, 0, 0, 0)"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/center/address.js.map
