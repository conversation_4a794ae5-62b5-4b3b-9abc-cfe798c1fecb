{"version": 3, "file": "add.js", "sources": ["pages/note/add.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbm90ZS9hZGQudnVl"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 顶部导航栏 -->\n    <view class=\"nav-box\" :style=\"{'padding-top': statusBarHeight + 'px'}\">\n      <view class=\"nav-item df\" :style=\"{'width': '100%', 'height': titleBarHeight + 'px'}\">\n        <view class=\"nav-back df\" @tap=\"goBack\">\n          <image src=\"/static/img/back.png\" style=\"width:34rpx;height:34rpx\"></image>\n        </view>\n        <view class=\"nav-publish\" :style=\"publishButtonStyle\">\n          <view\n            class=\"publish-btn\"\n            :class=\"{ 'publish-btn-disabled': isContentEmpty }\"\n            @tap=\"handlePublish\">\n            {{ publishButtonText }}\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 内容区域 -->\n    <view class=\"content-box\" :style=\"{'margin-top': statusBarHeight + titleBarHeight + 'px'}\">\n      <!-- 文本编辑区 -->\n      <view class=\"textarea-container\">\n        <!-- 透明的textarea用于输入 -->\n        <textarea\n          class=\"textarea-input\"\n          :show-confirm-bar=\"false\"\n          maxlength=\"2000\"\n          cursor-spacing=\"30\"\n          auto-height\n          placeholder=\"填写笔记内容\"\n          v-model=\"content\"\n          @input=\"contentInput\"\n          @focus=\"onTextareaFocus\"\n          @blur=\"onTextareaBlur\"\n          :focus=\"autoFocus\">\n        </textarea>\n\n        <!-- 覆盖层：显示带颜色的文本 -->\n        <view class=\"text-overlay\" :class=\"{ 'overlay-focused': isEditing }\">\n          <view class=\"formatted-content\">\n            <text\n              v-for=\"(segment, index) in contentSegments\"\n              :key=\"index\"\n              :class=\"segment.isMention ? 'mention-text' : 'normal-text'\"\n            >{{ segment.text }}</text>\n            <!-- 占位符文本 -->\n            <text v-if=\"!content\" class=\"placeholder-text\">填写笔记内容</text>\n          </view>\n        </view>\n\n        <!-- @用户标签显示 -->\n        <view v-if=\"mentionUsers.length > 0\" class=\"mention-tags\">\n          <!-- 已@的用户标签 -->\n          <view\n            v-for=\"(user, index) in mentionUsers\"\n            :key=\"user.uid\"\n            class=\"mention-tag\"\n            @tap=\"removeMentionUser(user.uid)\"\n          >\n            <image :src=\"user.avatar\" class=\"mention-avatar\" mode=\"aspectFill\"></image>\n            <text class=\"mention-nickname\">@{{ user.nickname }}</text>\n            <text class=\"mention-delete\">×</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 多媒体内容区域 -->\n      <scroll-view v-if=\"type != 0\" scroll-x=\"true\" class=\"scroll-box\">\n        <view class=\"scroll-item\">\n          <!-- 图片类型 -->\n          <block v-if=\"type == 2\">\n            <view v-for=\"(item, index) in imgs\" :key=\"index\" class=\"img-box\">\n              <image :src=\"item.url\" mode=\"aspectFill\" @tap=\"previewClick\" :data-i=\"index\"></image>\n              <view class=\"del df\" @tap=\"delClick(0, index)\">\n                <image src=\"/static/img/plus.png\"></image>\n              </view>\n              <view v-if=\"imgs.length > 1\" class=\"sort df\">\n                <view \n                  class=\"sort-item df\" \n                  :style=\"{'color': index > 0 ? '#fff' : '#cecece'}\" \n                  @tap=\"onSort(index, 0)\">\n                  ←\n                </view>\n                <view \n                  class=\"sort-item df\" \n                  :style=\"{'color': index < imgs.length - 1 ? '#fff' : '#cecece'}\" \n                  @tap=\"onSort(index, 1)\">\n                  →\n                </view>\n              </view>\n            </view>\n            \n            <view class=\"img-box file-add df\" @tap=\"addImgClick(1)\">\n              <image src=\"/static/img/add/photo.png\"></image>\n              <text>选择图片</text>\n            </view>\n            <view style=\"flex-shrink:0;width:20rpx;height:20rpx\"></view>\n          </block>\n          \n          <!-- 视频类型 -->\n          <view v-if=\"type == 3\" class=\"video-box df\">\n            <!-- 未上传视频 -->\n            <view v-if=\"!video.url\" class=\"video-item file-add df\" @tap=\"chooseVideo\">\n              <image src=\"/static/img/tabbar/3.png\"></image>\n              <text>选择视频，建议时长</text>\n              <text>60秒以内</text>\n            </view>\n            \n            <!-- 已上传视频 -->\n            <block v-else>\n              <view class=\"video-item\">\n                <video \n                  :src=\"video.url\" \n                  :custom-cache=\"false\"\n                  :controls=\"true\"\n                  :show-center-play-btn=\"true\"\n                  :show-play-btn=\"true\"\n                  :show-fullscreen-btn=\"true\"\n                  :show-progress=\"true\"\n                  :enable-progress-gesture=\"true\"\n                  :object-fit=\"'contain'\"\n                  @error=\"onVideoError\"\n                  @load=\"onVideoLoad\"\n                  style=\"width: 100%; height: 100%; border-radius: 8rpx;\">\n                </video>\n                <view class=\"del df\" @tap=\"delClick(1)\">\n                  <image src=\"/static/img/plus.png\"></image>\n                </view>\n              </view>\n                \n              <!-- 视频封面管理 -->\n              <view v-if=\"video.cover\" class=\"video-item\">\n                <image :src=\"video.cover\" mode=\"aspectFill\"></image>\n                <view class=\"del df\" @tap=\"delClick(2)\">\n                  <image src=\"/static/img/plus.png\"></image>\n                </view>\n              </view>\n              <view v-else class=\"video-item file-add df\" @tap=\"addImgClick(2)\">\n                <image src=\"/static/img/tabbar/3.png\"></image>\n                <text>添加封面</text>\n              </view>\n            </block>\n          </view>\n          \n          <!-- 音频类型 -->\n          <block v-if=\"type == 4\">\n            <!-- 未上传音频 -->\n            <view v-if=\"!audio.url\" class=\"audio-box file-add df\" @tap=\"addFileClick\">\n              <image src=\"/static/img/tabbar/3.png\"></image>\n              <text>上传音频，支持格式mp3和m4a</text>\n            </view>\n            \n            <!-- 已上传音频 -->\n            <view v-else class=\"file-audio df\">\n              <image class=\"audio-bg\" :src=\"audio.cover\"></image>\n              <view class=\"audio-mb\"></view>\n              \n              <!-- 音频封面 -->\n              <view v-if=\"audio.cover\" class=\"audio-left\">\n                <image :src=\"audio.cover\"></image>\n                <image class=\"icon xwb\" src=\"/static/img/audio.png\"></image>\n                <view class=\"del df\" @tap=\"delClick(3)\">\n                  <image src=\"/static/img/plus.png\"></image>\n                </view>\n              </view>\n              <view v-else class=\"audio-left file-add df\" @tap=\"addImgClick(3)\">\n                <image src=\"/static/img/tabbar/3.png\"></image>\n                <text>上传封面</text>\n              </view>\n              \n              <!-- 音频信息 -->\n              <view style=\"width:calc(100% - 268rpx)\">\n                <input \n                  class=\"audio-t1\" \n                  placeholder=\"点击填写音频名称\" \n                  placeholder-class=\"aph\" \n                  v-model=\"audio.name\" />\n                <input \n                  class=\"audio-t2\" \n                  placeholder=\"点击填写音频信息\" \n                  placeholder-class=\"aph\" \n                  v-model=\"audio.intro\" />\n              </view>\n              \n              <view class=\"del df\" @tap=\"delClick(4)\">\n                <image src=\"/static/img/plus.png\"></image>\n              </view>\n            </view>\n          </block>\n        </view>\n      </scroll-view>\n      \n      <!-- 投票组件 - 独立于媒体类型 -->\n      <view v-if=\"showVote\" class=\"vote-box\">\n        <view class=\"vote-container\">\n          <view class=\"vote-header\">\n            <view class=\"vote-title-container\">\n              <image class=\"vote-icon\" src=\"/static/img/toupiao.png\" mode=\"aspectFit\"></image>\n              <text class=\"vote-title\">{{ voteData.title || '哈哈哈' }}</text>\n            </view>\n            <view class=\"vote-delete\" @tap=\"deleteVote\">\n              <image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx;transform:rotate(45deg)\"></image>\n            </view>\n          </view>\n          <view class=\"vote-options\">\n            <view \n              v-for=\"(option, index) in voteData.options\" \n              :key=\"index\" \n              class=\"vote-option\">\n              {{ option }}\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 底部选择区域 -->\n      <view class=\"content-item df\">\n        <!-- 位置选择 -->\n        <view \n          :class=\"['tag-item', 'df', adds.name ? 'selected' : 'unselected']\" \n          style=\"border-radius:64rpx; margin-right: 16rpx;\" \n          @tap=\"locationClick\">\n          <image \n            class=\"icon location-icon\" \n            src=\"/static/img/wz.png\" \n            :style=\"{\n              'border-radius': '50%',\n              'background': '#f8f8f8',\n              'opacity': adds.name ? '1' : '0.6'\n            }\">\n          </image>\n          <text \n            :style=\"{'color': adds.name ? '#000' : '#999'}\"\n            :title=\"adds.address\"\n            class=\"location-text\">\n            {{ adds.name || '添加位置' }}\n          </text>\n        </view>\n        \n        <!-- 圈子选择 -->\n        <view \n          :class=\"['tag-item', 'df', circle.name ? 'selected' : 'unselected']\" \n          style=\"border-radius:64rpx\" \n          @tap=\"upPopupClick(true)\">\n          <image \n            class=\"icon\" \n            :src=\"circle.avatar || '/static/img/qz2.png'\" \n            :style=\"{\n              'border-radius': '50%',\n              'background': '#f8f8f8',\n              'opacity': circle.name ? '1' : '0.6'\n            }\">\n          </image>\n          <text :style=\"{'color': circle.name ? '#000' : '#999'}\">{{ circle.name || '选择圈子' }}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 标签区域 -->\n    <view class=\"tags-box\" v-if=\"(activity && activity.id) || goods.length > 0 || selectedTopics.length > 0\">\n      <!-- 活动标签 - 仅在已选择时显示 -->\n      <view v-if=\"activity && activity.id\" class=\"tag-item df\" @tap=\"activityPopupClick(true)\" style=\"border-radius:64rpx\">\n        <image \n          class=\"icon\" \n          :src=\"activity.img\" \n          style=\"border-radius:50%;background:#f8f8f8\">\n        </image>\n        <text style=\"color:#000\">{{ activity.name }}</text>\n      </view>\n      \n      <!-- 已添加商品标签 -->\n      <view v-for=\"(v, index) in goods\" :key=\"'goods-'+index\" class=\"tag-item df\" style=\"border-radius:64rpx\">\n        <image \n          class=\"icon\" \n          :src=\"v.product_img\" \n          mode=\"aspectFill\" \n          style=\"border-radius:50%;background:#f8f8f8\">\n        </image>\n        <text style=\"color:#000\">{{ v.goods_name }}</text>\n      </view>\n      \n      <!-- 已选话题标签 -->\n      <view v-for=\"(topic, index) in selectedTopics\" :key=\"'topic-'+index\" class=\"tag-item df\" style=\"border-radius:64rpx\">\n        <image \n          class=\"icon\" \n          :src=\"topic.icon\" \n          mode=\"aspectFill\" \n          style=\"border-radius:50%;background:#f8f8f8\">\n        </image>\n        <text style=\"color:#000\">#{{ topic.name }}#</text>\n        <view class=\"tag-delete\" @tap=\"removeSelectedTopic(index)\">\n          <text>×</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 多媒体工具栏 -->\n    <view class=\"media-toolbar-fixed\" :style=\"{ bottom: keyboardHeight + 'px' }\" :class=\"{ 'keyboard-active': keyboardHeight > 0 }\">\n      <view class=\"toolbar-box\">\n        <!-- 图片上传按钮 -->\n        <view class=\"toolbar-item\" @tap=\"addImgClick(1)\">\n          <image src=\"/static/img/add/photo.png\" mode=\"aspectFit\"></image>\n        </view>\n        \n        <!-- 视频上传按钮 -->\n        <view class=\"toolbar-item\" @tap=\"chooseVideo\">\n          <image src=\"/static/img/add/publish-video.png\" mode=\"aspectFit\"></image>\n        </view>\n        \n        <!-- 话题按钮 -->\n        <view class=\"toolbar-item\" @tap=\"topicPopupClick(true)\">\n          <image src=\"/static/img/add/publish_topic.png\" mode=\"aspectFit\"></image>\n        </view>\n        \n        <!-- @用户按钮 -->\n        <view class=\"toolbar-item\" @tap=\"showFollowUsers\">\n          <image src=\"/static/img/add/publish_at.png\" mode=\"aspectFit\"></image>\n        </view>\n        \n        <!-- 表情按钮 -->\n        <view :class=\"['toolbar-item', showEmojiPanel ? 'active' : '']\" @tap=\"toggleEmojiPanel\">\n          <image src=\"/static/img/add/emoji.png\" mode=\"aspectFit\"></image>\n        </view>\n        \n        <!-- 更多按钮 -->\n        <view class=\"toolbar-item\" @tap=\"toggleMoreOptions\">\n          <image src=\"/static/img/add/add.png\" mode=\"aspectFit\"></image>\n        </view>\n      </view>\n      \n      <!-- 更多选项菜单 -->\n      <view class=\"more-options-panel\" :class=\"{ 'expanded': showMoreOptions }\">\n        <view class=\"more-options-row\">\n          <!-- 音频按钮 -->\n          <view class=\"toolbar-item\" @tap=\"handleMediaClick(7)\">\n            <image src=\"/static/img/add/voice.png\" mode=\"aspectFit\"></image>\n            <text class=\"toolbar-label\">音频</text>\n          </view>\n          \n          <!-- 投票按钮 -->\n          <view class=\"toolbar-item\" @tap=\"handleMediaClick(8)\">\n            <image src=\"/static/img/tp.png\" mode=\"aspectFit\"></image>\n            <text class=\"toolbar-label\">投票</text>\n          </view>\n          \n          <!-- 选择活动按钮 -->\n          <view class=\"toolbar-item\" @tap=\"activityPopupClick(true)\">\n            <image src=\"/static/img/huodong.png\" mode=\"aspectFit\"></image>\n            <text class=\"toolbar-label\">活动</text>\n          </view>\n          \n          <!-- 添加商品按钮 -->\n          <view class=\"toolbar-item\" v-if=\"isNoteShop\" @tap=\"shopPopupClick(true)\">\n            <image src=\"/static/img/shangping.png\" mode=\"aspectFit\"></image>\n            <text class=\"toolbar-label\">商品</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 表情面板 -->\n      <emoji-panel\n        v-if=\"showEmojiPanel\"\n        :show=\"showEmojiPanel\"\n        :content=\"content\"\n        @select=\"onSelectEmoji\"\n        @select-gif=\"onSelectGif\"\n        @delete=\"onDeleteEmoji\"\n        @send=\"onSendComment\"\n      ></emoji-panel>\n\n      <!-- @用户搜索面板 - 固定在工具栏上方，横向滑动 -->\n      <view v-if=\"showUserSearch\" class=\"mention-user-panel\" :style=\"{ bottom: keyboardHeight + 'px' }\">\n        <!-- 关闭按钮 -->\n        <view class=\"popup-close df\" @tap=\"closeMentionPanel\">\n          <text>×</text>\n        </view>\n\n        <view v-if=\"searchUserList.length === 0 && !isSearchingUser\" class=\"empty-mention-users\">\n          <text v-if=\"searchUserKeyword.trim()\">暂无此用户，请核对</text>\n          <text v-else>暂无关注用户</text>\n        </view>\n\n        <view v-if=\"isSearchingUser\" class=\"loading-mention-users\">\n          <text>加载中...</text>\n        </view>\n\n        <scroll-view\n          v-if=\"searchUserList.length > 0\"\n          class=\"mention-user-scroll\"\n          scroll-x=\"true\"\n          :show-scrollbar=\"false\"\n        >\n          <view class=\"mention-user-list-horizontal\">\n            <view\n              v-for=\"(user, index) in searchUserList\"\n              :key=\"user.uid\"\n              class=\"mention-user-card\"\n              @tap=\"selectUserForMention(user)\"\n            >\n              <image :src=\"user.avatar\" class=\"mention-user-avatar\" mode=\"aspectFill\"></image>\n              <text class=\"mention-user-nickname\">{{ user.nickname }}</text>\n            </view>\n          </view>\n        </scroll-view>\n      </view>\n    </view>\n    \n    <!-- 商品选择弹窗 -->\n    <uni-popup ref=\"shopPopup\" type=\"bottom\" :safe-area=\"false\">\n      <view class=\"popup-box\">\n        <view class=\"popup-top df\">\n          <view class=\"popup-title\">\n            <view class=\"t1\">选择商品</view>\n            <view class=\"t2\">轻触下方名称以选择相应商品</view>\n          </view>\n          <view class=\"popup-close df\" @tap=\"shopPopupClick(false)\">\n            <image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx\"></image>\n          </view>\n        </view>\n        \n        <view class=\"popup-search df\">\n          <input \n            @confirm=\"searchClick\" \n            focus=\"true\" \n            confirm-type=\"search\" \n            placeholder=\"输入关键词搜索商品\" \n            placeholder-class=\"search-ph\" \n            v-model=\"kw\" />\n          <view class=\"search-btn\" @tap=\"searchClick\">搜索</view>\n        </view>\n        \n        <view class=\"popup-scroll\">\n          <view v-if=\"goodsList.length <= 0\" class=\"empty-box df\">\n            <image src=\"/static/img/empty.png\" />\n            <view class=\"e1\">暂无商品</view>\n            <view class=\"e2\">正在为您制造更多美好的商品</view>\n          </view>\n          \n          <view class=\"circle-box\">\n            <view \n              v-for=\"(item, index) in goodsList\" \n              :key=\"index\" \n              :class=\"['circle-item', 'df', item.selected ? 'active' : '']\" \n              style=\"border-radius:16rpx\" \n              :data-idx=\"index\" \n              @tap=\"goodsClick\">\n              <image :src=\"item.product_img\" style=\"border-radius:8rpx\" mode=\"aspectFill\"></image>\n              <text>{{ item.goods_name }}</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"popup-btn\" @tap=\"shopPopupClick(false)\">确认保存</view>\n      </view>\n    </uni-popup>\n    \n    <!-- 圈子选择弹窗 -->\n    <uni-popup ref=\"upPopup\" type=\"bottom\" :safe-area=\"false\">\n      <view class=\"popup-box\">\n        <view class=\"popup-top df\">\n          <view class=\"popup-title\">\n            <view class=\"t1\">选择一个圈子</view>\n            <view class=\"t2\">轻触下方名称以选择相应圈子</view>\n          </view>\n          <view class=\"popup-close df\" @tap=\"upPopupClick(false)\">\n            <image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx\"></image>\n          </view>\n        </view>\n        \n        <view class=\"popup-scroll\">\n          <view v-if=\"circleList.length <= 0\" class=\"empty-box df\">\n            <image src=\"/static/img/empty.png\" />\n            <view class=\"e1\">暂无圈子</view>\n            <view class=\"e2\">还没有加入任何圈子，加入后即可发布</view>\n          </view>\n          \n          <view class=\"circle-box\">\n            <view \n              v-for=\"(item, index) in circleList\" \n              :key=\"index\" \n              :class=\"['circle-item', 'df', circle.id == item.id ? 'active' : '']\" \n              :data-idx=\"index\" \n              @tap=\"circleClick\">\n              <image :src=\"item.avatar\" mode=\"aspectFill\"></image>\n              <text>{{ item.name }}</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"popup-btn\" @tap=\"upPopupClick(false)\">确认保存</view>\n      </view>\n    </uni-popup>\n    \n    <!-- 活动选择弹窗 -->\n    <uni-popup ref=\"activityPopup\" type=\"bottom\" :safe-area=\"false\">\n      <view class=\"popup-box\">\n        <view class=\"popup-top df\">\n          <view class=\"popup-title\">\n            <view class=\"t1\">选择一个活动</view>\n            <view class=\"t2\">轻触下方名称以选择相应活动</view>\n          </view>\n          <view class=\"popup-close df\" @tap=\"activityPopupClick(false)\">\n            <image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx\"></image>\n          </view>\n        </view>\n        \n        <view class=\"popup-scroll\">\n          <view v-if=\"activityList.length <= 0\" class=\"empty-box df\">\n            <image src=\"/static/img/empty.png\" />\n            <view class=\"e1\">暂无活动</view>\n            <view class=\"e2\">还没有可参与的活动</view>\n          </view>\n          \n          <view class=\"circle-box\">\n            <view \n              v-for=\"(item, index) in activityList\" \n              :key=\"index\" \n              :class=\"['circle-item', 'df', activity.id == item.id ? 'active' : '']\" \n              :data-idx=\"index\" \n              @tap=\"activityClick\">\n              <image :src=\"item.img\" mode=\"aspectFill\"></image>\n              <text>{{ item.name }}</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"popup-btn\" @tap=\"activityPopupClick(false)\">确认保存</view>\n      </view>\n    </uni-popup>\n    \n    <!-- 话题选择弹窗 -->\n    <uni-popup ref=\"topicPopup\" type=\"bottom\" :safe-area=\"false\">\n      <view class=\"popup-box\">\n        <view class=\"popup-top df\">\n          <view class=\"popup-title\">\n            <view class=\"t1\">选择一个话题</view>\n            <view class=\"t2\">轻触下方名称以选择相应话题</view>\n          </view>\n          <view class=\"popup-close df\" @tap=\"topicPopupClick(false)\">\n            <image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx\"></image>\n          </view>\n        </view>\n        \n        <view class=\"popup-search df\">\n          <input \n            @confirm=\"searchTopics\" \n            focus=\"true\" \n            confirm-type=\"search\" \n            placeholder=\"输入关键词搜索话题\" \n            placeholder-class=\"search-ph\" \n            v-model=\"topicKeyword\" />\n          <view class=\"search-btn\" @tap=\"searchTopics\">搜索</view>\n        </view>\n        \n        <view class=\"popup-scroll\">\n          <emptyPage\n            v-if=\"topicList.length <= 0\"\n            title=\"暂无话题\"\n            description=\"暂时没有找到相关话题\"\n          />\n          \n          <view class=\"topic-box\">\n            <view \n              v-for=\"(item, index) in topicList\" \n              :key=\"index\" \n              class=\"topic-item df\" \n              @tap=\"topicClick(item)\">\n              <view class=\"topic-tag\">#</view>\n              <view class=\"topic-content\">\n                <view class=\"topic-name\">{{ item.title }}</view>\n                <view class=\"topic-desc\">{{ item.description || '暂无描述' }}</view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </uni-popup>\n    \n    <!-- 用户选择弹窗 -->\n    <uni-popup ref=\"userPopup\" type=\"bottom\" :safe-area=\"false\">\n      <view class=\"popup-box\">\n        <view class=\"popup-top df\">\n          <view class=\"popup-title\">\n            <view class=\"t1\">选择要@的好友</view>\n            <view class=\"t2\">轻触下方名称以选择相应好友</view>\n          </view>\n          <view class=\"popup-close df\" @tap=\"userPopupClick(false)\">\n            <image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx\"></image>\n          </view>\n        </view>\n        \n        <view class=\"popup-search df\">\n          <input \n            @confirm=\"searchUsers\" \n            focus=\"true\" \n            confirm-type=\"search\" \n            placeholder=\"输入关键词搜索好友\" \n            placeholder-class=\"search-ph\" \n            v-model=\"userKeyword\" />\n          <view class=\"search-btn\" @tap=\"searchUsers\">搜索</view>\n        </view>\n        \n        <view class=\"popup-scroll\">\n          <view v-if=\"userList.length <= 0\" class=\"empty-box df\">\n            <image src=\"/static/img/empty.png\" />\n            <view class=\"e1\">暂无好友</view>\n            <view class=\"e2\">还没有添加任何好友</view>\n          </view>\n          \n          <view class=\"circle-box\">\n            <view \n              v-for=\"(item, index) in userList\" \n              :key=\"index\" \n              class=\"circle-item df\" \n              :data-idx=\"index\" \n              @tap=\"userClick(item)\">\n              <image :src=\"item.avatar\" mode=\"aspectFill\"></image>\n              <text>{{ item.nickname }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </uni-popup>\n    \n    <!-- 录音弹窗 -->\n    <uni-popup ref=\"recordPopup\" type=\"bottom\" :safe-area=\"false\">\n      <jc-record\n        ref=\"jcRecord\"\n        :voicePath=\"audio.url\"\n        :maxTime=\"60\"\n        :minTime=\"1\"\n        @okClick=\"handleRecordOk\"\n        @show=\"onRecordShow\"\n        @close=\"recordPopupClick(false)\"\n      >\n        <view class=\"centerwz\">录制语音</view>\n      </jc-record>\n    </uni-popup>\n    \n    <!-- 提示弹窗 -->\n    <uni-popup ref=\"tipsPopup\" type=\"center\" :mask-click=\"true\">\n      <view class=\"tips-box df\">\n        <view class=\"tips-item\">{{ tipsTitle }}</view>\n      </view>\n    </uni-popup>\n    \n    <!-- 添加canvas元素用于图片压缩 -->\n    <canvas canvas-id=\"canvas\" v-if=\"canvasStatus\"\n      :style=\"{width: canvasWidth + 'px', height: canvasHeight + 'px',position: 'absolute',left:'-100000px',top:'-100000px'}\"></canvas>\n    \n    <!-- 投票弹窗 -->\n    <uni-popup ref=\"votePopup\" type=\"bottom\" :safe-area=\"false\">\n      <view class=\"popup-box vote-popup\">\n        <view class=\"popup-top df\">\n          <view class=\"popup-title\">\n            <view class=\"t1\">投票</view>\n          </view>\n          <view class=\"popup-close df\" @tap=\"votePopupClick(false)\">\n            <image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx\"></image>\n          </view>\n        </view>\n        \n        <!-- 投票标题输入 -->\n        <view class=\"vote-title-input\">\n          <input \n            placeholder=\"添加标题（20个汉字内）\" \n            maxlength=\"20\" \n            v-model=\"voteData.title\"\n            class=\"vote-input\"\n          />\n        </view>\n        \n        <!-- 投票选项列表 -->\n        <view class=\"vote-options\">\n          <view \n            v-for=\"(option, index) in voteData.options\" \n            :key=\"index\" \n            class=\"vote-option-item\"\n          >\n            <input \n              :placeholder=\"'选项' + (index + 1) + '（10个汉字以内）'\" \n              maxlength=\"10\" \n              v-model=\"voteData.options[index]\"\n              class=\"vote-option-input\"\n            />\n            <view class=\"vote-option-delete\" @tap=\"deleteVoteOption(index)\">\n              <view class=\"delete-circle\">\n                <view class=\"delete-line\"></view>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 添加选项按钮 -->\n        <view class=\"vote-add-option\" @tap=\"addVoteOption\">\n          <text class=\"vote-add-icon\">+</text>\n          <text class=\"vote-add-text\">添加一个选项</text>\n        </view>\n        \n        <!-- 底部按钮区 -->\n        <view class=\"vote-bottom-btns\">\n          <view class=\"vote-cancel-btn\" @tap=\"votePopupClick(false)\">取消</view>\n          <view class=\"vote-confirm-btn\" @tap=\"confirmVote\">确定</view>\n        </view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n<script>\nimport jcRecord from '../../components/jc-record/jc-record.vue'\nimport emojiPanel from '../../components/emoji-panel/emoji-panel.vue'\nimport emptyPage from '@/components/emptyPage/emptyPage.vue'\n\n// 导入社交相关API\nimport { publishDynamic, getTopicList, getSocialFollowList, getJoinedCircles, searchUsers } from '@/api/social.js'\n\nexport default {\n  components: {\n    jcRecord,\n    emojiPanel,\n    emptyPage\n  },\n  data() {\n    return {\n      navbarHeight: 0, // 导航栏总高度\n      capsuleInfo: null, // 胶囊按钮信息\n      isUser: false,\n              id: 0,\n        content: \"\",\n        type: 2, // 默认显示为图片动态样式，鼓励用户上传图片 (1-纯文本,2-图片,3-视频,4-音频)\n      order_id: \"\",\n      order_type: 0,\n      goods: [],\n      imgs: [],\n      video: {\n        high: 0,\n        wide: 0,\n        url: \"\",\n        cover: \"\"\n      },\n      audio: {\n        cover: \"\",\n        url: \"\",\n        name: \"\",\n        intro: \"\",\n        size: 0\n      },\n      adds: {\n        name: \"\",\n        address: \"\",\n        latitude: \"\",\n        longitude: \"\"\n      },\n      circle: {\n        id: \"\",\n        name: \"\",\n        avatar: \"\"\n      },\n      activity: {\n        id: \"\",\n        name: \"\",\n        img: \"\"\n      },\n      circleList: [],\n      goodsList: [],\n      activityList: [],\n      kw: \"\",\n      tipsTitle: \"\",\n      keyboardHeight: 0,\n      userKeyword: \"\",\n      topicKeyword: \"\",\n      userList: [],\n      topicList: [],\n      showMoreOptions: false,\n      isRecording: false,\n      canvasStatus: false,\n      canvasWidth: 0,\n      canvasHeight: 0,\n      // 表情面板相关\n      showEmojiPanel: false,\n      emojiMap: {}, // 表情映射\n      commentImage: null, // 评论图片\n      // 草稿相关\n      draftKey: 'note_draft',\n      saveTimer: null, // 用于防抖的定时器\n      contentProcessTimer: null, // 内容处理防抖定时器\n      searchThrottleTimer: null, // 搜索节流定时器\n      isPublished: false, // 标识是否已成功发布\n      autoFocus: true, // 自动聚焦，页面加载时自动调起输入法\n      // 弹窗管理\n      currentPopup: null, // 当前打开的弹窗\n      // H5键盘监听方法\n      handleWindowResize: null,\n      // 已选话题\n      selectedTopics: [],\n      // 投票相关数据\n      showVote: false, // 控制投票组件显示\n      voteData: {\n        title: \"\",\n        options: [\"\", \"\"], // 默认两个选项\n        expireTime: 7 // 默认7天后过期\n      },\n      // @用户相关数据\n      showUserSearch: false, // 显示用户搜索面板\n      searchUserKeyword: '', // 搜索用户关键词\n      searchUserList: [], // 搜索到的用户列表\n      mentionUsers: [], // 已@的用户列表\n      cursorPosition: 0, // 当前光标位置\n      isSearchingUser: false, // 是否正在搜索用户\n      isEditing: false, // 是否处于编辑模式\n      mentionPanelClosed: false, // 用户是否手动关闭了@用户面板\n      excludedMentions: [], // 被排除的@文本（用户关闭面板后的@文本）\n      // 性能优化缓存\n      _segmentCache: null, // contentSegments缓存\n      _segmentCacheKey: '', // 缓存键\n    }\n  },\n  computed: {\n    // 将内容分段，区分普通文本和@用户文本（优化版本）\n    contentSegments() {\n      if (!this.content) return [];\n\n      // 创建缓存键\n      const cacheKey = `${this.content}_${JSON.stringify(this.mentionUsers)}_${JSON.stringify(this.excludedMentions)}`;\n\n      // 如果缓存存在且键匹配，直接返回缓存\n      if (this._segmentCache && this._segmentCacheKey === cacheKey) {\n        return this._segmentCache;\n      }\n\n      const segments = [];\n      const mentionPattern = /@([^\\s@]+)/g;\n      let lastIndex = 0;\n      let match;\n\n      // 预处理用户昵称映射，提高查找性能\n      const userNicknameSet = new Set(this.mentionUsers.map(user => user.nickname));\n\n      // 预处理排除映射，提高查找性能\n      const excludedMap = new Map();\n      this.excludedMentions.forEach(excluded => {\n        excludedMap.set(`${excluded.position}_${excluded.text}`, true);\n      });\n\n      while ((match = mentionPattern.exec(this.content)) !== null) {\n        // 添加@符号前的普通文本\n        const beforeText = this.content.substring(lastIndex, match.index);\n        if (beforeText) {\n          segments.push({\n            text: beforeText,\n            isMention: false\n          });\n        }\n\n        // 添加@用户文本\n        const mentionText = match[0]; // @用户名\n        const nickname = match[1]; // 用户名\n        const isKnownUser = userNicknameSet.has(nickname);\n\n        // 检查是否是被排除的@文本（用户关闭面板后的@文本）\n        const isExcluded = excludedMap.has(`${match.index}_${mentionText}`);\n\n        segments.push({\n          text: mentionText,\n          isMention: !isExcluded && isKnownUser, // 如果被排除，则不显示为@用户\n          isKnownUser: isKnownUser\n        });\n\n        lastIndex = match.index + match[0].length;\n      }\n\n      // 添加剩余的普通文本\n      const remainingText = this.content.substring(lastIndex);\n      if (remainingText) {\n        segments.push({\n          text: remainingText,\n          isMention: false\n        });\n      }\n\n      const result = segments.length > 0 ? segments : [{\n        text: this.content,\n        isMention: false\n      }];\n\n      // 缓存结果\n      this._segmentCache = result;\n      this._segmentCacheKey = cacheKey;\n\n      return result;\n    },\n    // 判断内容是否为空\n    isContentEmpty() {\n      const hasContent = this.content && this.content.trim();\n      const hasImages = this.imgs && this.imgs.length > 0;\n      const hasVideo = this.video && this.video.url;\n      const hasAudio = this.audio && this.audio.url;\n      const hasVote = this.showVote && this.voteData.title;\n\n      return !hasContent && !hasImages && !hasVideo && !hasAudio && !hasVote;\n    },\n\n    // 发布按钮文本\n    publishButtonText() {\n      if (this.isContentEmpty) {\n        return '发布';\n      }\n\n      // 根据内容类型显示不同文本\n      if (this.video.url) {\n        return '发布';\n      } else if (this.audio.url) {\n        return '发布';\n      } else if (this.imgs.length > 0) {\n        return '发布';\n      } else if (this.showVote) {\n        return '发布';\n      } else {\n        return '发布';\n      }\n    },\n\n    // 发布按钮样式（根据胶囊按钮位置动态计算）\n    publishButtonStyle() {\n      const style = {};\n\n      // #ifdef MP-WEIXIN\n      console.log('开始计算发布按钮位置...');\n      console.log('胶囊按钮信息:', this.capsuleInfo);\n\n      if (this.capsuleInfo && this.capsuleInfo.width > 0) {\n        // 获取系统信息\n        const systemInfo = uni.getSystemInfoSync();\n        const capsule = this.capsuleInfo;\n\n        // 计算像素比，用于px转rpx\n        const pixelRatio = systemInfo.pixelRatio || 2;\n        const screenWidth = systemInfo.screenWidth;\n        const windowWidth = systemInfo.windowWidth;\n\n        // 胶囊按钮距离右边的距离（px）\n        const capsuleRightDistance = screenWidth - capsule.right;\n\n        // 转换为rpx（UniApp中1px = 2rpx在大多数设备上）\n        const capsuleRightDistanceRpx = capsuleRightDistance * 2;\n\n        // 安全距离（rpx）\n        const safeDistanceRpx = 20;\n\n        // 最终的右边距（rpx）\n        const finalMarginRpx = capsuleRightDistanceRpx + safeDistanceRpx;\n\n        // 设置样式（使用rpx）\n        style.marginRight = finalMarginRpx + 'rpx';\n        style.paddingRight = '0';\n\n        console.log('胶囊按钮位置计算结果:', {\n          systemInfo: {\n            screenWidth,\n            windowWidth,\n            pixelRatio\n          },\n          capsule: {\n            width: capsule.width,\n            height: capsule.height,\n            left: capsule.left,\n            right: capsule.right,\n            top: capsule.top,\n            bottom: capsule.bottom\n          },\n          calculation: {\n            capsuleRightDistance,\n            capsuleRightDistanceRpx,\n            safeDistanceRpx,\n            finalMarginRpx\n          },\n          finalStyle: style\n        });\n      } else {\n        // 默认边距\n        style.marginRight = '40rpx';\n        style.paddingRight = '0';\n        console.log('未获取到胶囊按钮信息，使用默认边距');\n      }\n      // #endif\n\n      // #ifndef MP-WEIXIN\n      style.marginRight = '0';\n      style.paddingRight = '30rpx';\n      // #endif\n\n      return style;\n    }\n  },\n  created() {\n    // 检查用户登录状态\n    if (this.$store.state.app.token) {\n      this.isUser = true;\n      \n      // 设置音频封面为用户头像\n      const userInfo = uni.getStorageSync('userInfo') || {};\n      this.audio.cover = userInfo.avatar || \"\";\n    }\n    \n    // 初始化导航栏高度\n    this.initNavbar();\n    \n    // 初始化表情映射\n    this.initEmojiMap();\n  },\n  onLoad(option) {\n    let that = this;\n    \n    // 检查是否已登录\n    if (that.$store.state.app.token) {\n      that.isUser = true;\n    }\n    \n    // 解析传入参数\n    if (option.id) {\n      that.id = option.id;\n    }\n    \n    if (option.type) {\n      that.type = option.type;\n    }\n    \n    if (option.order_id) {\n      that.order_id = option.order_id;\n    }\n    \n    if (option.order_type) {\n      that.order_type = option.order_type;\n    }\n    \n    // 加载话题列表\n    that.loadTopics();\n    \n    // 监听键盘高度变化 - 添加平台兼容性检查\n    // #ifdef APP-PLUS || MP-WEIXIN || MP-ALIPAY\n    if (typeof uni.onKeyboardHeightChange === 'function') {\n      uni.onKeyboardHeightChange(res => {\n        that.keyboardHeight = res.height;\n        console.log('键盘高度变化:', res.height);\n      });\n    }\n    // #endif\n    \n    // 为了兼容性，同时监听页面滚动时的键盘状态\n    // #ifdef H5\n    this.handleWindowResize = () => {\n      // H5环境下通过窗口大小变化判断键盘状态\n      const windowHeight = window.innerHeight;\n      const documentHeight = document.documentElement.clientHeight;\n      const heightDiff = documentHeight - windowHeight;\n      \n      if (heightDiff > 150) { // 键盘弹起\n        that.keyboardHeight = heightDiff;\n      } else { // 键盘收起\n        that.keyboardHeight = 0;\n      }\n    };\n    window.addEventListener('resize', this.handleWindowResize);\n    // #endif\n    \n    // 加载草稿\n    that.loadDraft();\n    \n    // 延迟设置自动聚焦，确保页面渲染完成\n    setTimeout(() => {\n      that.autoFocus = true;\n    }, 300);\n  },\n  onShow() {\n    // 每次页面显示时检查用户登录状态\n    if (this.$store.state.app.token) {\n      this.isUser = true;\n    } else {\n      this.isUser = false;\n    }\n  },\n  \n  // 监听页面隐藏\n  onHide() {\n    // 页面隐藏时自动保存草稿（如果未成功发布）\n    if (!this.isPublished) {\n      this.saveDraft();\n    }\n  },\n  \n  // 监听页面卸载\n  onUnload() {\n    // 页面卸载时自动保存草稿（如果未成功发布）\n    if (!this.isPublished) {\n      this.saveDraft();\n    }\n\n    // 清除所有定时器\n    if (this.saveTimer) {\n      clearTimeout(this.saveTimer);\n      this.saveTimer = null;\n    }\n\n    if (this.contentProcessTimer) {\n      clearTimeout(this.contentProcessTimer);\n      this.contentProcessTimer = null;\n    }\n\n    if (this.searchThrottleTimer) {\n      clearTimeout(this.searchThrottleTimer);\n      this.searchThrottleTimer = null;\n    }\n\n    // 清理缓存\n    this.clearSegmentCache();\n\n    // 清除键盘监听\n    // #ifdef H5\n    if (this.handleWindowResize) {\n      window.removeEventListener('resize', this.handleWindowResize);\n      this.handleWindowResize = null;\n    }\n    // #endif\n  },\n  methods: {\n    // 初始化导航栏\n    initNavbar() {\n      // 获取系统信息\n      const systemInfo = uni.getSystemInfoSync();\n      this.statusBarHeight = systemInfo.statusBarHeight || 20;\n      \n      // 获取胶囊按钮信息\n      // #ifdef MP-WEIXIN\n      try {\n        this.capsuleInfo = uni.getMenuButtonBoundingClientRect();\n        console.log('获取胶囊按钮信息:', this.capsuleInfo);\n\n        if (this.capsuleInfo && this.capsuleInfo.width > 0) {\n          // 计算标题栏高度：胶囊按钮高度 + 上下边距\n          const capsuleTop = this.capsuleInfo.top - this.statusBarHeight;\n          this.titleBarHeight = this.capsuleInfo.height + capsuleTop * 2;\n\n          console.log('胶囊按钮信息详细:', {\n            width: this.capsuleInfo.width,\n            height: this.capsuleInfo.height,\n            left: this.capsuleInfo.left,\n            right: this.capsuleInfo.right,\n            top: this.capsuleInfo.top,\n            bottom: this.capsuleInfo.bottom\n          });\n          console.log('计算的标题栏高度:', this.titleBarHeight);\n\n          // 强制更新视图，确保计算属性重新计算\n          this.$nextTick(() => {\n            console.log('胶囊按钮信息设置完成，触发视图更新');\n            this.$forceUpdate();\n          });\n        } else {\n          this.titleBarHeight = 44;\n          console.log('未获取到有效的胶囊按钮信息，使用默认标题栏高度');\n        }\n      } catch (e) {\n        console.error('获取胶囊按钮信息失败:', e);\n        this.titleBarHeight = 44;\n      }\n      // #endif\n      \n      // #ifdef MP-ALIPAY\n      this.titleBarHeight = 48;\n      // #endif\n      \n      // #ifdef APP-PLUS\n      this.titleBarHeight = 44;\n      // #endif\n      \n      // #ifdef H5\n      this.titleBarHeight = 44;\n      // #endif\n      \n      // #ifndef MP-WEIXIN || MP-ALIPAY || APP-PLUS || H5\n      this.titleBarHeight = 44;\n      // #endif\n      \n      console.log('导航栏配置:', {\n        statusBarHeight: this.statusBarHeight,\n        titleBarHeight: this.titleBarHeight,\n        platform: systemInfo.platform\n      });\n    },\n    \n    // 返回上一页\n    goBack() {\n      // 如果有内容，提示用户是否保存草稿\n      if (this.content.trim() || this.imgs.length > 0 || this.video.url || this.audio.url) {\n        uni.showModal({\n          title: '提示',\n          content: '是否保存草稿？',\n          success: (res) => {\n            if (res.confirm) {\n              this.saveDraft();\n            }\n            uni.navigateBack();\n          }\n        });\n      } else {\n        uni.navigateBack();\n      }\n    },\n    \n    // 跳转到完善资料页面\n    navigateToComplete() {\n      uni.navigateTo({\n        url: '/pages/center/means'\n      });\n    },\n\n    // 处理发布按钮点击\n    handlePublish() {\n      // 防止内容为空时点击\n      if (this.isContentEmpty) {\n        this.opTipsPopup(\"请添加一些内容再发布哦～\");\n        return;\n      }\n\n      // 调用发布方法\n      this.saveDynamic(1);\n    },\n    \n    // 初始化表情映射\n    initEmojiMap() {\n      try {\n        // 尝试从本地存储获取表情映射\n        const emojiMapStr = uni.getStorageSync('emoji_map');\n        if (emojiMapStr) {\n          this.emojiMap = JSON.parse(emojiMapStr);\n          console.log('表情映射加载成功，总数:', Object.keys(this.emojiMap).length);\n        } else {\n          // 如果本地没有，可以从表情组件获取\n          // 这里可以添加默认的表情映射\n          console.log('未找到表情映射，将使用默认映射');\n          this.emojiMap = {};\n        }\n      } catch (e) {\n        console.error('初始化表情映射失败', e);\n        this.emojiMap = {};\n      }\n    },\n    \n    // 内容输入\n    contentInput(e) {\n      const value = e.detail.value;\n      const cursor = e.detail.cursor || 0;\n\n      this.content = value;\n      this.cursorPosition = cursor;\n\n      // 使用防抖优化性能\n      this.debouncedContentProcess(value, cursor);\n    },\n\n    // 防抖处理内容变化\n    debouncedContentProcess(value, cursor) {\n      // 清除之前的定时器\n      if (this.contentProcessTimer) {\n        clearTimeout(this.contentProcessTimer);\n      }\n\n      // 设置新的定时器\n      this.contentProcessTimer = setTimeout(() => {\n        // 清理无效的排除记录\n        this.cleanupExcludedMentions();\n\n        // 检测@用户输入\n        this.checkMentionInput(value, cursor);\n\n        // 同步更新mentionUsers列表\n        this.parseMentionUsersFromContent();\n\n        // 自动保存草稿\n        this.autoSaveDraft();\n      }, 300); // 300ms防抖\n    },\n\n\n    \n    // textarea获取焦点\n    onTextareaFocus() {\n      console.log('textarea获取焦点');\n      this.showEmojiPanel = false;\n      this.isEditing = true;\n      // 强制更新键盘高度检测\n      this.$nextTick(() => {\n        setTimeout(() => {\n          console.log('当前键盘高度:', this.keyboardHeight);\n        }, 300);\n      });\n    },\n    \n    // textarea失去焦点\n    onTextareaBlur() {\n      console.log('textarea失去焦点');\n      // 延迟退出编辑模式，避免点击其他元素时立即退出\n      setTimeout(() => {\n        if (!this.showUserSearch) {\n          this.isEditing = false;\n        }\n      }, 200);\n      // 延迟重置键盘高度，避免闪烁\n      setTimeout(() => {\n        if (this.keyboardHeight > 0) {\n          console.log('重置键盘高度');\n          this.keyboardHeight = 0;\n        }\n      }, 100);\n    },\n    \n    // 获取笔记详情\n    dynamicInfo() {\n      let that = this;\n      \n      // 使用uni.request直接请求API\n      uni.request({\n        url: this.$store.state.app.apiUrl + '/note/details',\n        method: 'GET',\n        data: { id: that.id },\n        header: {\n          'Authorization': 'Bearer ' + this.$store.state.app.token\n        },\n        success: (res) => {\n          if (res.data && res.data.status === 200) {\n            const data = res.data.data;\n            that.id = data.id;\n            that.type = data.type;\n            that.content = data.content;\n            that.circle.id = data.circle_id;\n            that.circle.name = data.circle_name;\n            that.circle.avatar = data.circle_avatar;\n            that.activity.id = data.activity_id;\n            that.activity.name = data.activity_name;\n            that.activity.img = data.activity_img;\n            that.adds.name = data.adds_name;\n            that.adds.address = data.adds;\n            that.adds.latitude = data.lat;\n            that.adds.longitude = data.lng;\n            \n            if (that.type == 1) {\n              that.imgs = data.imgs;\n            } else if (that.type == 2) {\n              that.video.high = data.video.high;\n              that.video.wide = data.video.wide;\n              that.video.url = data.video.url;\n              that.video.cover = data.video.cover;\n            } else if (that.type == 3) {\n              that.audio.cover = data.audio.cover;\n              that.audio.url = data.audio.url;\n              that.audio.name = data.audio.name;\n              that.audio.intro = data.audio.intro;\n              that.audio.size = data.audio.size;\n            }\n            \n            that.order_id = data.order_id;\n            that.order_type = data.order_type;\n            \n            if (that.order_id) {\n              that.goods = data.goods;\n            }\n          }\n        },\n        fail: (err) => {\n          console.error('获取笔记详情失败:', err);\n        }\n      });\n    },\n    \n    // 获取订单商品\n    noteOrderGoods() {\n      let that = this;\n      \n      // 使用uni.request直接请求API\n      uni.request({\n        url: this.$store.state.app.apiUrl + '/order/goods',\n        method: 'GET',\n        data: { id: that.order_id },\n        header: {\n          'Authorization': 'Bearer ' + this.$store.state.app.token\n        },\n        success: (res) => {\n          if (res.data && res.data.status === 200) {\n            that.goods = res.data.data;\n          }\n        },\n        fail: (err) => {\n          console.error('获取订单商品失败:', err);\n        }\n      });\n    },\n    \n    // 获取圈子列表\n    circleDynamic() {\n      let that = this;\n      \n      // 使用uni.request直接请求API\n      uni.request({\n        url: this.$store.state.app.apiUrl + '/circle/list',\n        method: 'GET',\n        header: {\n          'Authorization': 'Bearer ' + this.$store.state.app.token\n        },\n        success: (res) => {\n          if (res.data && res.data.status === 200) {\n            that.circleList = res.data.data;\n          }\n        },\n        fail: (err) => {\n          console.error('获取圈子列表失败:', err);\n        }\n      });\n    },\n    \n    // 获取话题列表\n    loadTopics() {\n      let that = this;\n      \n      // 显示加载中\n      uni.showLoading({\n        title: '加载中',\n        mask: true\n      });\n      \n      // 准备请求参数\n      const params = { \n        keyword: that.topicKeyword,\n        page: 1,\n        limit: 20\n      };\n      \n      // 使用getTopicList API函数获取话题列表\n      getTopicList(params)\n        .then(res => {\n          uni.hideLoading();\n          \n          if (res.status === 200) {\n            // 处理返回的话题数据\n            that.topicList = res.data.list || [];\n          } else {\n            uni.showToast({\n              title: res.msg || '获取话题失败',\n              icon: 'none'\n            });\n          }\n        })\n        .catch(err => {\n          uni.hideLoading();\n          console.error('获取话题列表失败:', err);\n          uni.showToast({\n            title: '获取话题失败',\n            icon: 'none'\n          });\n        });\n    },\n    \n    // 搜索话题\n    searchTopics() {\n      this.loadTopics();\n    },\n    \n    // 保存笔记\n    saveDynamic(status) {\n      let that = this;\n      \n      // 验证表单\n      if (that.content.trim() === \"\") {\n        return that.opTipsPopup(\"请输入内容\");\n      }\n      \n      // 根据实际内容判断最终发布类型\n      let finalType;\n      if (that.type == 3 && that.video.url) {\n        finalType = 3; // 视频类型\n      } else if (that.type == 4 && that.audio.url) {\n        finalType = 4; // 音频类型\n      } else if (that.imgs.length > 0) {\n        finalType = 2; // 图片类型\n      } else {\n        finalType = 1; // 文本类型\n      }\n      \n      // 验证视频类型\n      if (that.type == 3) {\n        if (!that.video.url) {\n          return that.opTipsPopup(\"视频笔记需添加一个视频哦！\");\n        }\n      }\n      \n      // 验证音频类型\n      if (that.type == 4) {\n        if (!that.audio.cover) {\n          return that.opTipsPopup(\"音频封面不能为空哦！\");\n        }\n        \n        if (!that.audio.url) {\n          return that.opTipsPopup(\"音频不能为空哦！\");\n        }\n      }\n      \n      // 校验投票数据（如有）\n      let vote = null;\n      let vote_options = null;\n      if (that.showVote) {\n        if (!that.voteData.title || !that.voteData.title.trim()) {\n          return that.opTipsPopup(\"请输入投票标题\");\n        }\n        // 过滤空选项\n        const options = (that.voteData.options || []).map(opt => (opt || '').trim()).filter(opt => opt);\n        if (options.length < 2) {\n          return that.opTipsPopup(\"至少需要2个投票选项\");\n        }\n        vote = { title: that.voteData.title.trim() };\n        vote_options = options;\n      }\n      \n      // 显示加载中\n      uni.showLoading({\n        title: status ? \"发布中...\" : \"保存中...\",\n        mask: true\n      });\n      \n      // 处理表情内容\n      const parsedContent = that.parseEmojiContent(that.content);\n      \n      // 准备请求参数\n      let params = {\n        id: that.id,\n        content: parsedContent, // 使用解析后的内容\n        type: finalType, // 使用实际判断的最终类型\n        location_name: that.adds.name,\n        latitude: that.adds.latitude,\n        longitude: that.adds.longitude,\n        circle_id: that.circle.id,\n        status: status, // 0-草稿，1-发布\n        order_id: that.order_id,\n        order_type: that.order_type,\n        goods: that.goods.map(item => item.id)\n      };\n      // 如果有投票，添加投票参数\n      if (vote && vote_options) {\n        params.vote = vote;\n        params.vote_options = vote_options;\n      }\n      \n      // 如果有GIF表情，添加到图片中\n      if (that.commentImage) {\n        if (finalType === 1 || finalType === 2) {\n          finalType = 2; // 设置为图片类型 (1-纯文本,2-图片,3-视频,4-音频)\n          if (!that.imgs) that.imgs = [];\n          that.imgs.push({\n            url: that.commentImage,\n            wide: 0,\n            high: 0\n          });\n          params.type = finalType;\n        }\n      }\n      \n      // 根据最终类型添加不同的媒体数据\n      if (finalType == 2) {\n        // 图片类型\n        params.images = that.imgs.map(img => img.url);\n      } else if (finalType == 3) {\n        // 视频类型\n        if (that.video.url) {\n          params.video = that.video.url;\n          params.video_cover = that.video.cover || '';\n          params.video_width = that.video.wide || 1280;\n          params.video_height = that.video.high || 720;\n          console.log('视频数据:', {\n            url: that.video.url,\n            cover: that.video.cover,\n            width: that.video.wide,\n            height: that.video.high\n          });\n        }\n      } else if (finalType == 4) {\n        // 音频类型\n        params.audio = that.audio.url;\n        params.audio_cover = that.audio.cover;\n        params.audio_title = that.audio.name;\n      }\n      \n      // 调试：打印发布参数\n      console.log('发布动态参数:', params);\n      console.log('当前类型:', that.type);\n      console.log('最终类型:', finalType);\n      console.log('视频数据:', that.video);\n      \n      // 使用publishDynamic API函数发布动态\n      publishDynamic(params)\n        .then(res => {\n          uni.hideLoading();\n          \n          // 显示结果提示\n          that.opTipsPopup(res.msg || (res.status === 200 ? (status ? '发布成功' : '保存成功') : (status ? '发布失败' : '保存失败')));\n          \n          // 发布成功后跳转\n          if (res.status === 200) {\n            console.log('发布成功，准备跳转到个人中心');\n\n            // 标记已成功发布，避免再次保存草稿\n            if (status === 1) {\n              that.isPublished = true;\n            }\n\n            // 清除草稿\n            that.clearDraft();\n\n            setTimeout(function() {\n              console.log('开始跳转到个人中心');\n              uni.reLaunch({\n                url: \"/pages/tabbar/center\",\n                success: function() {\n                  console.log('跳转到个人中心成功');\n                },\n                fail: function(err) {\n                  console.error('跳转到个人中心失败:', err);\n                  // 如果reLaunch失败，尝试使用navigateTo\n                  uni.navigateTo({\n                    url: \"/pages/tabbar/center\"\n                  });\n                }\n              });\n            }, 1000);\n          }\n        })\n        .catch(err => {\n          uni.hideLoading();\n          console.error('发布笔记失败:', err);\n          that.opTipsPopup('发布失败，请重试');\n        });\n    },\n    \n    // 选择视频（使用新的分片上传方法）\n    chooseVideo() {\n      let that = this;\n      \n      // 如果之前有图片，清除图片\n      if (that.imgs.length > 0) {\n        that.imgs = [];\n      }\n      \n      // 如果之前有音频，清除音频\n      if (that.audio.url) {\n        that.audio = {\n          cover: \"\",\n          url: \"\",\n          name: \"\",\n          intro: \"\",\n          size: 0\n        };\n      }\n      \n      // 设置为视频类型\n      that.type = 3;\n      \n      // 使用简单的视频上传方法\n      that.$util.uploadVideoSimple({\n        count: 1,\n        sourceType: ['album', 'camera'],\n        maxDuration: 60,\n        camera: 'back',\n        url: 'upload/video',\n        name: 'file'\n      }, function(res) {\n        // 上传成功回调\n        console.log('视频上传成功:', res);\n        if (res.status === 200 && res.data && (res.data.url || res.data.src)) {\n          that.video.url = res.data.url || res.data.src;\n          that.video.high = res.data.high || res.data.h || 720; // 使用返回的高度或默认值\n          that.video.wide = res.data.wide || res.data.w || 1280; // 使用返回的宽度或默认值\n          \n          // 强制更新视图\n          that.$forceUpdate();\n          \n          // 处理视频封面\n          that.handleVideoCover();\n        } else {\n          console.error('视频上传响应格式错误:', res);\n          that.opTipsPopup(\"视频上传失败：响应格式错误\");\n        }\n      }, function(err) {\n        // 上传失败回调\n        console.error('视频上传失败:', err);\n        that.opTipsPopup(\"视频上传失败，请重试\");\n      });\n    },\n    \n    // 处理视频封面\n    handleVideoCover() {\n      let that = this;\n      \n      // 调试信息\n      console.log('视频数据:', that.video);\n      console.log('视频URL:', that.video.url);\n      \n      // 如果已经有封面，不需要重新处理\n      if (that.video.cover) {\n        that.opTipsPopup(\"视频上传成功\");\n        return;\n      }\n      \n      // 提示用户添加封面\n      uni.showModal({\n        title: '视频上传成功',\n        content: '是否需要添加视频封面？',\n        confirmText: '添加封面',\n        cancelText: '稍后添加',\n        success: (res) => {\n          if (res.confirm) {\n            // 用户选择添加封面\n            that.addImgClick(2); // 调用添加封面方法\n          } else {\n            // 用户选择稍后添加\n            that.opTipsPopup(\"视频上传成功，请记得添加封面\");\n          }\n          // 不显示任何提示消息\n        }\n      });\n    },\n    \n    // 视频加载成功\n    onVideoLoad(e) {\n      console.log('视频加载成功:', e);\n      this.opTipsPopup(\"视频加载成功\");\n    },\n    \n    // 视频加载错误\n    onVideoError(e) {\n      console.error('视频加载失败:', e);\n      this.opTipsPopup(\"视频加载失败，请检查视频格式\");\n    },\n    \n    // 添加图片（支持多选）\n    addImgClick(type) {\n      let that = this;\n      \n      if (type == 1) {\n        // 上传笔记图片\n        that.canvasStatus = true;\n        \n        // 使用新的简化版上传方法\n        that.$util.uploadImageChange({\n          url: 'upload/image',\n          count: 9 // 支持最多9张图片\n        }, function(res) {\n          // 如果之前有视频，清除视频\n          if (that.video.url) {\n            that.video = {\n              high: 0,\n              wide: 0,\n              url: \"\",\n              cover: \"\"\n            };\n          }\n          \n          // 如果之前有音频，清除音频\n          if (that.audio.url) {\n            that.audio = {\n              cover: \"\",\n              url: \"\",\n              name: \"\",\n              intro: \"\",\n              size: 0\n            };\n          }\n          \n          // 设置为图片类型\n          that.type = 2;\n          \n          // 处理返回的图片数据\n          if (res.data && Array.isArray(res.data)) {\n            // 多张图片\n            res.data.forEach(img => {\n              that.imgs.push({\n                url: img.url || img.src,\n                wide: img.wide || img.w || 0,\n                high: img.high || img.h || 0\n              });\n            });\n            // 移除成功上传提示消息\n          } else if (res.data && (res.data.url || res.data.src)) {\n            // 单张图片\n            that.imgs.push({\n              url: res.data.url || res.data.src,\n              wide: res.data.wide || res.data.w || 0,\n              high: res.data.high || res.data.h || 0\n            });\n            // 移除成功上传提示消息\n          } else if (res.url || res.src) {\n            // 兼容旧格式：直接返回图片对象\n            that.imgs.push({\n              url: res.url || res.src,\n              wide: res.wide || res.w || 0,\n              high: res.high || res.h || 0\n            });\n            // 移除成功上传提示消息\n          } else {\n            console.error('图片数据格式错误:', res);\n            that.opTipsPopup('图片上传失败：数据格式错误');\n          }\n          \n          // 强制更新视图\n          that.$forceUpdate();\n        }, function(err) {\n          console.error('图片上传失败:', err);\n          that.opTipsPopup('图片上传失败，请重试');\n        });\n      } else {\n        // 上传封面图片 (视频或音频封面)\n        this.canvasStatus = true;\n        \n        that.$util.uploadImageChange({\n          url: 'upload/image',\n          count: 1 // 封面只选择一张\n        }, function(res) {\n          console.log('封面上传响应:', res);\n          \n          let coverUrl = '';\n          \n          // 尝试从不同位置获取URL\n          if (res.data && res.data.url) {\n            coverUrl = res.data.url;\n          } else if (res.data && res.data.src) {\n            coverUrl = res.data.src;\n          } else if (res.url) {\n            coverUrl = res.url;\n          } else if (res.src) {\n            coverUrl = res.src;\n          }\n          \n          if (coverUrl) {\n            if (type == 2) {\n              that.video.cover = coverUrl;\n              that.opTipsPopup('视频封面上传成功');\n            } else if (type == 3) {\n              that.audio.cover = coverUrl;\n              that.opTipsPopup('音频封面上传成功');\n            }\n          } else {\n            console.error('无法获取封面URL:', res);\n            that.opTipsPopup('封面上传失败：无法获取图片URL');\n          }\n        }, function(err) {\n          console.error('封面上传失败:', err);\n          that.opTipsPopup('封面上传失败，请重试');\n        });\n      }\n    },\n    \n    // 添加音频文件\n    addFileClick() {\n      let that = this;\n      \n      // 显示选择菜单\n      uni.showActionSheet({\n        itemList: ['录制音频', '选择音频文件'],\n        success: function(res) {\n          if (res.tapIndex === 0) {\n            // 录制音频\n            that.recordPopupClick(true);\n          } else if (res.tapIndex === 1) {\n            // 选择音频文件\n            that.chooseAudioFile();\n          }\n        }\n      });\n    },\n    \n    // 选择音频文件\n    chooseAudioFile() {\n      let that = this;\n      \n      // 使用新的音频上传方法\n      that.$util.uploadAudio({\n        url: 'upload/audio',\n        name: 'file'\n      }, function(res) {\n        // 如果之前有图片，清除图片\n        if (that.imgs.length > 0) {\n          that.imgs = [];\n        }\n        \n        // 如果之前有视频，清除视频\n        if (that.video.url) {\n          that.video = {\n            high: 0,\n            wide: 0,\n            url: \"\",\n            cover: \"\"\n          };\n        }\n        \n        // 设置为音频类型\n        that.type = 4;\n        \n        // 设置音频信息\n        that.audio.url = res.data.url;\n        that.audio.name = res.data.name || \"音频文件\";\n        that.audio.size = res.data.size || 0;\n        \n        // 如果没有设置音频简介，添加默认值\n        if (!that.audio.intro) {\n          that.audio.intro = \"上传于\" + new Date().toLocaleString();\n        }\n        \n        // 如果没有封面，使用用户头像或默认图片\n        if (!that.audio.cover) {\n          const userInfo = uni.getStorageSync('USER_INFO') || {};\n          that.audio.cover = userInfo.avatar || '/static/img/audio_default_cover.png';\n        }\n        \n        that.opTipsPopup('音频上传成功');\n      }, function(err) {\n        console.error('音频上传失败:', err);\n        that.opTipsPopup('音频上传失败，请重试');\n      });\n    },\n    \n    // 处理媒体按钮点击\n    handleMediaClick(type) {\n      switch(type) {\n        case 7: // 音频\n          this.addFileClick();\n          break;\n        case 8: // 投票\n          this.votePopupClick(true);\n          break;\n        default:\n          console.log('未知的媒体类型:', type);\n      }\n    },\n    \n    // 录音弹窗控制\n    recordPopupClick(isOpen) {\n      if (isOpen) {\n        this.openPopup('record', this.$refs.recordPopup);\n      } else {\n        this.$refs.recordPopup.close();\n        this.currentPopup = null;\n      }\n    },\n    \n    // 录音弹窗变化事件\n    onRecordPopupChange(e) {\n      if (!e.show) {\n        this.isRecording = false;\n      }\n    },\n    \n    // 录音完成事件\n    onRecordFinish(audioPath) {\n      console.log('录音完成:', audioPath);\n      \n      if (!audioPath) {\n        this.opTipsPopup('录音失败，请重试');\n        return;\n      }\n      \n      // 处理录音上传\n      this.handleAudioUpload(audioPath);\n    },\n    \n    // 录音关闭事件\n    onRecordClose() {\n      console.log('录音组件关闭');\n    },\n    \n    // 录音确认按钮点击事件\n    handleRecordOk(audioPath) {\n      console.log('录音确认:', audioPath);\n      \n      if (!audioPath) {\n        this.opTipsPopup('录音无效，请重新录制');\n        return;\n      }\n      \n      // 处理录音上传\n      this.handleAudioUpload(audioPath, true);\n    },\n    \n    // 处理音频上传\n    handleAudioUpload(audioPath, closePopup = false) {\n      // audioPath参数暂未使用\n      // 显示加载中\n      uni.showLoading({\n        title: '处理中...',\n        mask: true\n      });\n      \n      // 使用新的音频上传方法\n      let that = this;\n      that.$util.uploadAudio({\n        url: 'upload/audio',\n        name: 'file'\n      }, function(res) {\n        // 设置音频信息\n        that.audio.url = res.data.url;\n        \n        // 如果没有设置音频名称和简介，添加默认值\n        if (!that.audio.name) {\n          that.audio.name = res.data.name || \"我的录音\";\n        }\n        if (!that.audio.intro) {\n          that.audio.intro = \"录制于\" + new Date().toLocaleString();\n        }\n        \n        // 如果没有封面，使用用户头像或默认图片\n        if (!that.audio.cover) {\n          const userInfo = uni.getStorageSync('USER_INFO') || {};\n          that.audio.cover = userInfo.avatar || '/static/img/audio_default_cover.png';\n        }\n        \n        // 如果需要关闭弹窗\n        if (closePopup) {\n          that.recordPopupClick(false);\n        }\n        \n        uni.hideLoading();\n        that.opTipsPopup('录音上传成功');\n      }, function() {\n        uni.hideLoading();\n        that.opTipsPopup('录音上传失败，请重试');\n      });\n    },\n    \n    // 位置选择\n    locationClick() {\n      let that = this;\n      \n      // 显示加载提示\n      uni.showLoading({\n        title: '正在打开位置选择...',\n        mask: true\n      });\n      \n      // 延迟一下再调用，避免界面卡顿\n      setTimeout(() => {\n        uni.hideLoading();\n        \n        uni.chooseLocation({\n          success: function(res) {\n            console.log('位置选择成功:', res);\n            that.adds = {\n              name: res.name || res.address || '未知位置',\n              address: res.address || '',\n              latitude: res.latitude || '',\n              longitude: res.longitude || ''\n            };\n            that.opTipsPopup('位置添加成功');\n          },\n          fail: function(err) {\n            console.error('位置选择失败:', err);\n            \n            // 根据错误类型给出不同提示\n            if (err.errMsg) {\n              if (err.errMsg.includes('auth') || err.errMsg.includes('permission')) {\n                uni.showModal({\n                  title: '权限提示',\n                  content: '需要获取位置权限才能选择位置，请在浏览器设置中允许位置访问',\n                  confirmText: '我知道了',\n                  showCancel: false\n                });\n              } else if (err.errMsg.includes('cancel')) {\n                // 用户取消，不显示错误提示\n                console.log('用户取消位置选择');\n                return;\n              } else if (err.errMsg.includes('system')) {\n                uni.showModal({\n                  title: '系统提示',\n                  content: '系统定位服务未开启，请在设置中开启定位服务',\n                  confirmText: '我知道了',\n                  showCancel: false\n                });\n              } else {\n                that.opTipsPopup('位置选择失败，请重试');\n              }\n            } else {\n              that.opTipsPopup('位置选择失败，请重试');\n            }\n          }\n        });\n      }, 300);\n    },\n    \n    // 搜索商品\n    searchClick(type = 0) {\n      let that = this;\n      \n      // 已有商品列表并且是手动触发搜索\n      if (type == 1 && that.goodsList.length) {\n        return;\n      }\n      \n      // 显示加载中\n      uni.showLoading({\n        title: '加载中',\n        mask: true\n      });\n      \n      // 准备请求参数\n      const params = { \n        keyword: that.kw,\n        page: 1,\n        limit: 20\n      };\n      \n      // 发起请求获取商品列表\n      uni.request({\n        url: that.$store.state.app.apiUrl + '/product/list',\n        method: 'GET',\n        data: params,\n        header: {\n          'Authorization': 'Bearer ' + that.$store.state.app.token\n        },\n        success: (res) => {\n          uni.hideLoading();\n          \n          if (res.data && res.data.status === 200) {\n            // 处理返回的商品数据\n            that.goodsList = res.data.data.list || [];\n          } else {\n            uni.showToast({\n              title: res.data?.msg || '获取商品列表失败',\n              icon: 'none'\n            });\n          }\n        },\n        fail: (err) => {\n          uni.hideLoading();\n          console.error('获取商品列表失败:', err);\n          uni.showToast({\n            title: '获取商品列表失败',\n            icon: 'none'\n          });\n        }\n      });\n    },\n    \n    // 商品弹窗控制\n    shopPopupClick(isOpen) {\n      if (isOpen) {\n        this.openPopup('shop', this.$refs.shopPopup);\n        this.searchClick(1);\n      } else {\n        this.$refs.shopPopup.close();\n        this.currentPopup = null;\n      }\n    },\n    \n    // 圈子弹窗控制\n    upPopupClick(isOpen) {\n      if (isOpen) {\n        this.openPopup('circle', this.$refs.upPopup);\n        this.loadCircles();\n      } else {\n        this.$refs.upPopup.close();\n        this.currentPopup = null;\n      }\n    },\n    \n    // 商品选择\n    goodsClick(e) {\n      const index = e.currentTarget.dataset.idx;\n      this.goodsList[index].selected = !this.goodsList[index].selected;\n      \n      // 更新已选商品列表\n      let selectedGoods = [];\n      for (let item of this.goodsList) {\n        if (item.selected) {\n          selectedGoods.push(item);\n        }\n      }\n      this.goods = selectedGoods;\n    },\n    \n    // 圈子选择\n    circleClick(e) {\n      const index = e.currentTarget.dataset.idx;\n      \n      if (this.circleList[index].id == this.circle.id) {\n        // 取消选择\n        this.circle.id = \"\";\n        this.circle.name = \"\";\n        this.circle.avatar = \"\";\n      } else {\n        // 选择圈子\n        this.circle = {\n          id: this.circleList[index].id,\n          name: this.circleList[index].name,\n          avatar: this.circleList[index].avatar\n        };\n      }\n    },\n    \n    // 图片预览\n    previewClick(e) {\n      const index = e.currentTarget.dataset.i;\n      let urls = [];\n      \n      for (let img of this.imgs) {\n        urls.push(img.url);\n      }\n      \n      uni.previewImage({\n        current: index,\n        urls: urls\n      });\n    },\n    \n    // 显示操作提示\n    opTipsPopup(msg) {\n      uni.showToast({\n        title: msg,\n        icon: 'none',\n        duration: 2000\n      });\n    },\n    \n    // 处理多媒体点击\n    handleMediaClick(type) {\n      // 关闭更多菜单\n      if (type > 5) {\n        this.showMoreOptions = false;\n      }\n\n      // 实现多媒体点击逻辑\n      switch(type) {\n        case 1: // 图片 - 已整合到统一媒体选择器\n        case 2: // 视频 - 已整合到统一媒体选择器\n          this.chooseMedia();\n          break;\n        case 3: // 话题 - 已在按钮处理，保留兼容\n          this.topicPopupClick(true);\n          break;\n        case 4: // @好友 - 已在按钮处理，保留兼容\n          this.userPopupClick(true);\n          break;\n        case 7: // 音频\n          // 设置为音频类型\n          this.type = 4; // 设置为音频类型 (1-纯文本,2-图片,3-视频,4-音频)\n          // 只在没有音频时打开录音弹窗\n          if (!this.audio.url) {\n            this.recordPopupClick(true);\n          }\n          break;\n        case 8: // 投票\n          this.votePopupClick(true);\n          break;\n      }\n    },\n    \n    // 处理多媒体长按 - 已删除长按设置封面功能\n    handleMediaLongPress(type) {\n      // type参数暂未使用\n      // 功能已删除\n    },\n    \n    // 切换更多选项菜单\n    toggleMoreOptions() {\n      if (this.showMoreOptions) {\n        this.showMoreOptions = false;\n        this.currentPopup = null;\n      } else {\n        this.closeAllPopups();\n        this.showMoreOptions = true;\n        this.currentPopup = 'more';\n      }\n    },\n    \n    // 活动弹窗控制\n    activityPopupClick(isOpen) {\n      if (isOpen) {\n        this.openPopup('activity', this.$refs.activityPopup);\n        this.loadActivities();\n      } else {\n        this.$refs.activityPopup.close();\n        this.currentPopup = null;\n      }\n    },\n    \n    // 加载活动列表\n    loadActivities() {\n      let that = this;\n      \n      // 显示加载中\n      uni.showLoading({\n        title: '加载中',\n        mask: true\n      });\n      \n      // 准备请求参数\n      const params = { \n        page: 1,\n        limit: 20\n      };\n      \n      // 发起请求获取活动列表\n      uni.request({\n        url: that.$store.state.app.apiUrl + '/activity/list',\n        method: 'GET',\n        data: params,\n        header: {\n          'Authorization': 'Bearer ' + that.$store.state.app.token\n        },\n        success: (res) => {\n          uni.hideLoading();\n          \n          if (res.data && res.data.status === 200) {\n            // 处理返回的活动数据\n            that.activityList = res.data.data.list || [];\n          } else {\n            uni.showToast({\n              title: res.data?.msg || '获取活动列表失败',\n              icon: 'none'\n            });\n          }\n        },\n        fail: (err) => {\n          uni.hideLoading();\n          console.error('获取活动列表失败:', err);\n          uni.showToast({\n            title: '获取活动列表失败',\n            icon: 'none'\n          });\n        }\n      });\n    },\n    \n    // 搜索用户\n    searchUsers() {\n      this.loadUsers();\n    },\n    \n    // 选择用户\n    selectUser(e) {\n      const index = e.currentTarget.dataset.idx;\n      const user = this.userList[index];\n      \n      // 插入@用户\n      this.content += ` @${user.nickname} `;\n      \n      // 关闭弹窗\n      this.$refs.userPopup.close();\n      \n      // 提示\n      this.opTipsPopup(`已添加@${user.nickname}`);\n    },\n    \n    // 选择话题\n    selectTopic(e) {\n      const index = e.currentTarget.dataset.idx;\n      const topic = this.topicList[index];\n      \n      // 插入话题标签\n      this.content += ` #${topic.name}# `;\n      \n      // 关闭弹窗\n      this.$refs.topicPopup.close();\n      \n      // 提示\n      this.opTipsPopup(`已添加话题#${topic.name}#`);\n    },\n    \n    // 删除已上传文件\n    delClick(type, index) {\n      let that = this;\n      \n      uni.showModal({\n        title: '提示',\n        content: '确定要删除吗？',\n        success: (res) => {\n          if (res.confirm) {\n            switch(type) {\n              case 0: // 删除图片\n                that.imgs.splice(index, 1);\n                // 如果没有图片了，根据其他内容决定类型\n                if (that.imgs.length === 0) {\n                  if (that.video.url) {\n                    that.type = 3; // 视频类型\n                  } else if (that.audio.url) {\n                    that.type = 4; // 音频类型\n                  } else {\n                    that.type = 1; // 纯文本类型\n                  }\n                }\n                break;\n              case 1: // 删除视频\n                that.video = {\n                  high: 0,\n                  wide: 0,\n                  url: \"\",\n                  cover: \"\"\n                };\n                // 根据其他内容决定类型\n                if (that.imgs.length > 0) {\n                  that.type = 2; // 图片类型\n                } else if (that.audio.url) {\n                  that.type = 4; // 音频类型\n                } else {\n                  that.type = 1; // 纯文本类型\n                }\n                break;\n              case 2: // 删除视频封面\n                that.video.cover = \"\";\n                break;\n              case 3: // 删除音频封面\n                that.audio.cover = \"\";\n                break;\n              case 4: // 删除音频\n                that.audio = {\n                  cover: \"\",\n                  url: \"\",\n                  name: \"\",\n                  intro: \"\",\n                  size: 0\n                };\n                // 根据其他内容决定类型\n                if (that.imgs.length > 0) {\n                  that.type = 2; // 图片类型\n                } else if (that.video.url) {\n                  that.type = 3; // 视频类型\n                } else {\n                  that.type = 1; // 纯文本类型\n                }\n                break;\n\n            }\n          }\n        }\n      });\n    },\n    \n    // 调整图片顺序\n    onSort(index, direction) {\n      let that = this;\n      \n      if (direction === 0 && index > 0) {\n        // 左移\n        const temp = that.imgs[index];\n        that.imgs[index] = that.imgs[index - 1];\n        that.imgs[index - 1] = temp;\n      } else if (direction === 1 && index < that.imgs.length - 1) {\n        // 右移\n        const temp = that.imgs[index];\n        that.imgs[index] = that.imgs[index + 1];\n        that.imgs[index + 1] = temp;\n      }\n      \n      // 强制更新数组\n      that.imgs = [...that.imgs];\n    },\n    \n    // 加载圈子列表\n    loadCircles() {\n      let that = this;\n      \n      // 显示加载中\n      uni.showLoading({\n        title: '加载中',\n        mask: true\n      });\n      \n      // 准备请求参数\n      const params = { \n        page: 1,\n        limit: 50  // 增加限制数量，获取更多已加入的圈子\n      };\n      \n      // 使用getJoinedCircles API获取我加入的圈子\n      getJoinedCircles(params)\n        .then(res => {\n          uni.hideLoading();\n          \n          if (res.status === 200) {\n            // 处理返回的圈子数据，映射字段名称\n            that.circleList = (res.data.list || []).map(item => {\n              return {\n                id: item.circle_id || item.id,\n                name: item.circle_name || item.name,\n                avatar: item.circle_avatar || item.avatar,\n                description: item.circle_description || item.description,\n                member_count: item.member_count || 0\n              };\n            });\n            \n\n          } else {\n            uni.showToast({\n              title: res.msg || '获取圈子列表失败',\n              icon: 'none'\n            });\n          }\n        })\n        .catch(err => {\n          uni.hideLoading();\n          console.error('获取圈子列表失败:', err);\n          uni.showToast({\n            title: '获取圈子列表失败',\n            icon: 'none'\n          });\n      });\n    },\n    \n    // 活动选择\n    activityClick(e) {\n      const index = e.currentTarget.dataset.idx;\n      \n      if (this.activityList[index].id == this.activity.id) {\n        // 取消选择\n        this.activity = {\n          id: \"\",\n          name: \"\",\n          img: \"\"\n        };\n      } else {\n        // 选择活动\n        this.activity = {\n          id: this.activityList[index].id,\n          name: this.activityList[index].name,\n          img: this.activityList[index].img\n        };\n      }\n    },\n    \n    // 隐藏键盘\n    hideKeyboard() {\n      uni.hideKeyboard();\n      // 同时隐藏表情面板\n      this.showEmojiPanel = false;\n    },\n    \n    // 快速插入文本\n    handleQuickInsert(text) {\n      this.content += text;\n    },\n    \n    // 用户弹窗控制\n    userPopupClick(isOpen) {\n      if (isOpen) {\n        this.openPopup('user', this.$refs.userPopup);\n        this.loadUsers();\n      } else {\n        this.$refs.userPopup.close();\n        this.currentPopup = null;\n      }\n    },\n    \n    // 话题弹窗控制\n    topicPopupClick(status) {\n      if (status) {\n        this.openPopup('topic', this.$refs.topicPopup);\n        this.loadTopics();\n      } else {\n        this.$refs.topicPopup.close();\n        this.currentPopup = null;\n      }\n    },\n    \n    // 话题点击\n    topicClick(topic) {\n      // 只能选择一个话题，替换掉已选的话题\n      this.selectedTopics = [{\n        id: topic.id,\n        name: topic.title,\n        icon: topic.icon || '/static/img/topic-icon.png'\n      }];\n      // 关闭弹窗\n      this.$refs.topicPopup.close();\n    },\n    \n    // 移除已选话题\n    removeSelectedTopic(index) {\n      // 从数组中移除指定索引的话题\n      this.selectedTopics.splice(index, 1);\n    },\n    \n    // 用户点击\n    userClick(user) {\n      // 在内容中插入@用户\n      const userTag = `@${user.nickname} `;\n      this.content += userTag;\n      \n      // 关闭弹窗\n      this.$refs.userPopup.close();\n    },\n    \n    // 加载用户列表\n    loadUsers() {\n      let that = this;\n      \n      // 显示加载中\n      uni.showLoading({\n        title: '加载中',\n        mask: true\n      });\n      \n      // 准备请求参数\n      const params = { \n        keyword: that.userKeyword,\n        page: 1,\n        limit: 20\n      };\n      \n      // 使用getSocialFollowList API获取关注列表作为好友\n      getSocialFollowList(params)\n        .then(res => {\n          uni.hideLoading();\n          \n          if (res.status === 200) {\n            // 处理返回的用户数据\n            // 将API返回的字段映射到组件需要的字段\n            that.userList = (res.data.list || []).map(item => {\n              return {\n                id: item.follow_uid,\n                nickname: item.follow_nickname,\n                avatar: item.follow_avatar\n              };\n            });\n            \n\n          } else {\n            uni.showToast({\n              title: res.msg || '获取好友列表失败',\n              icon: 'none'\n            });\n          }\n        })\n        .catch(err => {\n          uni.hideLoading();\n          console.error('获取好友列表失败:', err);\n          uni.showToast({\n            title: '获取好友列表失败',\n            icon: 'none'\n          });\n        });\n    },\n    \n    // 删除图片\n    deleteImage(index) {\n      this.images.splice(index, 1);\n    },\n    \n    // 上传图片\n    uploadImage() {\n      let that = this;\n      that.$util.uploadImageChange('upload/image', function(res) {\n        that.images.push(res.data.url);\n      });\n    },\n    \n    // 发布笔记\n    publishNote() {\n      // 验证表单\n      if (!this.title.trim()) {\n        return this.opTipsPopup('请输入笔记标题');\n      }\n    },\n    \n    // 切换表情面板\n    toggleEmojiPanel() {\n      if (this.showEmojiPanel) {\n        this.showEmojiPanel = false;\n        this.currentPopup = null;\n      } else {\n        this.closeAllPopups();\n        this.showEmojiPanel = true;\n        this.currentPopup = 'emoji';\n      }\n    },\n    \n    // 选择表情\n    onSelectEmoji(emoji) {\n      // 获取表情文本或代码\n      const emojiText = emoji.phrase || emoji.alt || emoji.value || '';\n      if (emojiText) {\n        // 将表情插入到内容中\n        this.content += emojiText;\n      }\n    },\n    \n    // 选择GIF表情\n    onSelectGif(gif) {\n      if (gif && gif.url) {\n        // 设置评论图片\n        this.commentImage = gif.url;\n        uni.showToast({\n          title: 'GIF表情已添加',\n          icon: 'none'\n        });\n      }\n    },\n    \n    // 删除表情\n    onDeleteEmoji() {\n      if (this.content.length > 0) {\n        // 删除最后一个字符\n        this.content = this.content.substring(0, this.content.length - 1);\n      }\n    },\n    \n    // 发送评论\n    onSendComment() {\n      // 调用发布笔记方法\n      this.saveDynamic(1);\n    },\n    \n    // 解析表情内容\n    parseEmojiContent(content) {\n      if (!content) return '';\n      \n      // 简单的表情解析，将[表情]格式的文本替换为<img>标签\n      // 实际应用中可能需要更复杂的解析逻辑\n      let parsedContent = content;\n      \n      // 遍历表情映射\n      Object.keys(this.emojiMap).forEach(key => {\n        const decodedKey = decodeURIComponent(key);\n        const url = this.emojiMap[key];\n        \n        // 全局替换所有匹配的表情文本\n        const regex = new RegExp(decodedKey.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g');\n        parsedContent = parsedContent.replace(regex, `<img class=\"emoji\" src=\"${url}\" alt=\"${decodedKey}\" />`);\n      });\n      \n      return parsedContent;\n    },\n    \n    // 保存草稿\n    saveDraft() {\n      // 如果已成功发布，不保存草稿\n      if (this.isPublished) {\n        return;\n      }\n      \n      // 如果内容为空，不保存草稿\n      if (!this.content.trim() && this.imgs.length === 0 && !this.video.url && !this.audio.url) {\n        return;\n      }\n      \n      // 构建草稿数据\n      const draft = {\n        content: this.content,\n        type: this.type,\n        imgs: this.imgs,\n        video: this.video,\n        audio: this.audio,\n        adds: this.adds,\n        circle: this.circle,\n        activity: this.activity,\n        timestamp: Date.now()\n      };\n      \n      // 保存到本地存储\n      try {\n        uni.setStorageSync(this.draftKey, JSON.stringify(draft));\n\n      } catch (e) {\n        console.error('保存草稿失败:', e);\n      }\n    },\n    \n    // 加载草稿\n    loadDraft() {\n      try {\n        const draftStr = uni.getStorageSync(this.draftKey);\n        if (draftStr) {\n          const draft = JSON.parse(draftStr);\n          \n          // 检查草稿是否超过24小时\n          const now = Date.now();\n          const draftTime = draft.timestamp || 0;\n          const hours24 = 24 * 60 * 60 * 1000;\n          \n          if (now - draftTime > hours24) {\n            // 草稿超过24小时，删除草稿\n            this.clearDraft();\n            return;\n          }\n          \n          // 询问用户是否加载草稿\n          uni.showModal({\n            title: '发现草稿',\n            content: '是否恢复上次未完成的内容？',\n            success: res => {\n              if (res.confirm) {\n                // 恢复草稿内容\n                this.content = draft.content || '';\n                this.type = draft.type || 2; // 默认使用图片类型 (1-纯文本,2-图片,3-视频,4-音频)\n                this.imgs = draft.imgs || [];\n                this.video = draft.video || {\n                  high: 0,\n                  wide: 0,\n                  url: \"\",\n                  cover: \"\"\n                };\n                this.audio = draft.audio || {\n                  cover: \"\",\n                  url: \"\",\n                  name: \"\",\n                  intro: \"\",\n                  size: 0\n                };\n                this.adds = draft.adds || {\n                  name: \"\",\n                  address: \"\",\n                  latitude: \"\",\n                  longitude: \"\"\n                };\n                this.circle = draft.circle || {\n                  id: \"\",\n                  name: \"\",\n                  avatar: \"\"\n                };\n                this.activity = draft.activity || {};\n                \n                uni.showToast({\n                  title: '草稿已恢复',\n                  icon: 'success'\n                });\n              } else {\n                // 用户不需要草稿，清除草稿\n                this.clearDraft();\n              }\n            }\n          });\n        }\n      } catch (e) {\n        console.error('加载草稿失败:', e);\n      }\n    },\n    \n    // 清除草稿\n    clearDraft() {\n      try {\n        uni.removeStorageSync(this.draftKey);\n\n      } catch (e) {\n        console.error('清除草稿失败:', e);\n      }\n    },\n    \n    // 关闭所有弹窗\n    closeAllPopups() {\n      if (this.currentPopup) {\n        this.currentPopup = null;\n      }\n      \n      // 关闭所有弹窗\n      this.$refs.shopPopup?.close();\n      this.$refs.upPopup?.close();\n      this.$refs.activityPopup?.close();\n      this.$refs.topicPopup?.close();\n      this.$refs.userPopup?.close();\n      this.$refs.recordPopup?.close();\n      \n      // 关闭表情面板和更多选项\n      this.showEmojiPanel = false;\n      this.showMoreOptions = false;\n    },\n    \n    // 打开指定弹窗\n    openPopup(popupName, popupRef) {\n      // 如果当前有弹窗打开且不是同一个，先关闭\n      if (this.currentPopup && this.currentPopup !== popupName) {\n        this.closeAllPopups();\n      }\n      \n      this.currentPopup = popupName;\n      \n      // 延迟打开新弹窗，确保关闭动画完成\n      setTimeout(() => {\n        if (popupRef) {\n          popupRef.open();\n        }\n      }, this.currentPopup !== popupName ? 200 : 0);\n    },\n    \n    // 投票弹窗控制\n    votePopupClick(isOpen) {\n      if (isOpen) {\n        this.openPopup('vote', this.$refs.votePopup);\n      } else {\n        this.$refs.votePopup.close();\n        this.currentPopup = null;\n      }\n    },\n    \n    // 添加投票选项\n    addVoteOption() {\n      if (this.voteData.options.length < 10) { // 限制最多10个选项\n        this.voteData.options.push(\"\");\n      } else {\n        this.opTipsPopup(\"最多只能添加10个选项\");\n      }\n    },\n    \n    // 删除投票选项\n    deleteVoteOption(index) {\n      if (this.voteData.options.length > 2) { // 至少保留2个选项\n        this.voteData.options.splice(index, 1);\n      } else {\n        this.opTipsPopup(\"至少需要2个选项\");\n      }\n    },\n    \n    // 确认投票\n    confirmVote() {\n      // 验证投票数据\n      if (!this.voteData.title.trim()) {\n        return this.opTipsPopup(\"请输入投票标题\");\n      }\n      \n      // 验证是否有空选项\n      let hasEmptyOption = false;\n      for (let option of this.voteData.options) {\n        if (!option.trim()) {\n          hasEmptyOption = true;\n          break;\n        }\n      }\n      \n      if (hasEmptyOption) {\n        return this.opTipsPopup(\"选项内容不能为空\");\n      }\n      \n      // 显示投票组件\n      this.showVote = true;\n      \n      // 关闭弹窗\n      this.votePopupClick(false);\n    },\n    \n    // 删除投票\n    deleteVote() {\n      uni.showModal({\n        title: '提示',\n        content: '确定要删除投票吗？',\n        success: (res) => {\n          if (res.confirm) {\n            // 隐藏投票组件\n            this.showVote = false;\n            \n            // 重置投票数据\n            this.voteData = {\n              title: \"\",\n              options: [\"\", \"\"],\n              expireTime: 7\n            };\n          }\n        }\n      });\n    },\n    onRecordShow() {\n      // 录音组件显示时的处理（可选）\n    },\n\n    // ==================== @用户相关方法 ====================\n\n    // 检测@用户输入\n    checkMentionInput(content, cursor) {\n      // 检查光标前是否有@符号\n      const beforeCursor = content.substring(0, cursor);\n      const atIndex = beforeCursor.lastIndexOf('@');\n\n      if (atIndex !== -1) {\n        // 检查@符号后面的内容\n        const afterAt = beforeCursor.substring(atIndex + 1);\n\n        // 如果@后面没有空格\n        if (!afterAt.includes(' ')) {\n          // 检查是否是新输入的@符号（重置关闭状态）\n          if (afterAt.length === 0) {\n            // 刚输入@符号，重置关闭状态，显示关注用户列表\n            this.mentionPanelClosed = false;\n            this.searchUserKeyword = '';\n            this.searchUsersForMention();\n          } else if (!this.mentionPanelClosed) {\n            // @后面有字符且面板未被关闭，进行搜索\n            this.searchUserKeyword = afterAt;\n            this.searchUsersForMention();\n          }\n        } else {\n          // @后面有空格，隐藏搜索面板\n          this.hideUserSearch();\n        }\n      } else {\n        // 没有@符号，隐藏搜索面板\n        this.hideUserSearch();\n      }\n    },\n\n    // 搜索用户用于@功能（节流版本）\n    searchUsersForMention() {\n      // 使用节流优化搜索性能\n      if (this.searchThrottleTimer) {\n        clearTimeout(this.searchThrottleTimer);\n      }\n\n      this.searchThrottleTimer = setTimeout(() => {\n        this.performUserSearch();\n      }, 200); // 200ms节流\n    },\n\n    // 执行用户搜索\n    async performUserSearch() {\n      if (this.isSearchingUser) return;\n\n      console.log('开始搜索用户，关键词:', this.searchUserKeyword);\n      this.isSearchingUser = true;\n      this.showUserSearch = true;\n\n      try {\n        if (this.searchUserKeyword.trim()) {\n          // 有搜索关键词，调用搜索接口\n          const res = await searchUsers({\n            keyword: this.searchUserKeyword.trim(),\n            page: 1,\n            limit: 20\n          });\n\n          console.log('搜索用户接口响应:', res);\n          if (res.status === 200) {\n            this.searchUserList = (res.data.list || []).map(item => {\n              // 处理头像URL\n              let avatarUrl = item.avatar;\n              if (avatarUrl && !avatarUrl.startsWith('http')) {\n                avatarUrl = avatarUrl.startsWith('/') ?\n                  `${this.$baseUrl}${avatarUrl}` :\n                  `${this.$baseUrl}/${avatarUrl}`;\n              }\n\n              return {\n                uid: item.uid,\n                nickname: item.nickname,\n                avatar: avatarUrl,\n                is_follow: false // 搜索结果默认不是关注用户\n              };\n            });\n            console.log('处理后的搜索用户列表:', this.searchUserList);\n          } else {\n            this.searchUserList = [];\n            console.log('搜索用户接口响应异常:', res);\n          }\n        } else {\n          // 没有搜索关键词，显示关注用户列表\n          const response = await getSocialFollowList({\n            page: 1,\n            limit: 50\n          });\n\n          console.log('关注用户接口响应:', response);\n          if (response.status === 200 && response.data && response.data.list) {\n            this.searchUserList = response.data.list.map(item => {\n              // 处理头像URL，如果是相对路径则补全\n              let avatarUrl = item.follow_avatar;\n              if (avatarUrl && !avatarUrl.startsWith('http')) {\n                // 如果是相对路径，需要补全域名\n                avatarUrl = avatarUrl.startsWith('/') ?\n                  `${this.$baseUrl}${avatarUrl}` :\n                  `${this.$baseUrl}/${avatarUrl}`;\n              }\n\n              return {\n                uid: item.follow_uid,\n                nickname: item.follow_nickname,\n                avatar: avatarUrl,\n                is_follow: true // 标记为已关注\n              };\n            });\n            console.log('处理后的关注用户列表:', this.searchUserList);\n          } else {\n            this.searchUserList = [];\n            console.log('关注用户接口响应异常:', response);\n          }\n        }\n      } catch (error) {\n        console.error('搜索用户失败:', error);\n        this.searchUserList = [];\n      } finally {\n        this.isSearchingUser = false;\n        console.log('搜索完成，面板状态:', {\n          showUserSearch: this.showUserSearch,\n          searchUserList: this.searchUserList,\n          isSearchingUser: this.isSearchingUser\n        });\n      }\n    },\n\n\n\n    // 选择用户进行@\n    selectUserForMention(user) {\n      const beforeCursor = this.content.substring(0, this.cursorPosition);\n      const afterCursor = this.content.substring(this.cursorPosition);\n      const atIndex = beforeCursor.lastIndexOf('@');\n\n      let newContent;\n      let newCursorPosition;\n\n      if (atIndex !== -1) {\n        // 检查@符号后面是否有空格，如果有空格说明这个@已经完成，不应该替换\n        const afterAt = beforeCursor.substring(atIndex + 1);\n        if (afterAt.includes(' ')) {\n          // @符号后面有空格，说明是已完成的@用户，直接在光标位置插入新的@用户\n          const mentionText = `@${user.nickname} `;\n          newContent = beforeCursor + mentionText + afterCursor;\n          newCursorPosition = this.cursorPosition + mentionText.length;\n        } else {\n          // @符号后面没有空格，说明是正在输入的@，可以替换\n          const mentionText = `@${user.nickname} `;\n          newContent = beforeCursor.substring(0, atIndex) + mentionText + afterCursor;\n          newCursorPosition = atIndex + mentionText.length;\n        }\n      } else {\n        // 如果没有@符号，直接在光标位置插入@用户\n        const mentionText = `@${user.nickname} `;\n        newContent = beforeCursor + mentionText + afterCursor;\n        newCursorPosition = this.cursorPosition + mentionText.length;\n      }\n\n      this.content = newContent;\n      this.cursorPosition = newCursorPosition;\n\n      // 添加到已@用户列表\n      if (!this.mentionUsers.find(u => u.uid === user.uid)) {\n        this.mentionUsers.push({\n          uid: user.uid,\n          nickname: user.nickname,\n          avatar: user.avatar\n        });\n      }\n\n      // 清理对应位置的排除记录（如果有的话）\n      if (atIndex !== -1) {\n        this.excludedMentions = this.excludedMentions.filter(excluded =>\n          excluded.position !== atIndex\n        );\n      }\n\n      // 重置面板关闭状态（用户选择了用户）\n      this.mentionPanelClosed = false;\n\n      // 隐藏搜索面板\n      this.hideUserSearch();\n\n      // 自动保存草稿\n      this.autoSaveDraft();\n    },\n\n    // 隐藏用户搜索面板\n    hideUserSearch() {\n      this.showUserSearch = false;\n      this.searchUserKeyword = '';\n      this.searchUserList = [];\n    },\n\n    // 清理无效的排除记录\n    cleanupExcludedMentions() {\n      const originalLength = this.excludedMentions.length;\n\n      // 检查排除列表中的@文本是否还存在于当前内容中\n      this.excludedMentions = this.excludedMentions.filter(excluded => {\n        const actualText = this.content.substring(excluded.position, excluded.position + excluded.text.length);\n        return actualText === excluded.text;\n      });\n\n      // 如果排除列表发生变化，清理缓存\n      if (this.excludedMentions.length !== originalLength) {\n        this.clearSegmentCache();\n      }\n    },\n\n    // 清理分段缓存\n    clearSegmentCache() {\n      this._segmentCache = null;\n      this._segmentCacheKey = '';\n    },\n\n    // 关闭@用户面板（处理未选择用户的情况）\n    closeMentionPanel() {\n      // 标记用户手动关闭了面板\n      this.mentionPanelClosed = true;\n\n      // 如果用户输入了@文字但没有选择用户，将其标记为排除的@文本\n      if (this.searchUserKeyword.trim()) {\n        console.log('用户输入了@文字但未选择用户，保留为纯文本:', this.searchUserKeyword);\n\n        // 找到当前@符号的位置\n        const beforeCursor = this.content.substring(0, this.cursorPosition);\n        const atIndex = beforeCursor.lastIndexOf('@');\n\n        if (atIndex !== -1) {\n          const mentionText = '@' + this.searchUserKeyword;\n          // 将这个@文本添加到排除列表中\n          this.excludedMentions.push({\n            text: mentionText,\n            position: atIndex\n          });\n        }\n      }\n\n      // 隐藏面板\n      this.hideUserSearch();\n    },\n\n    // 显示关注的用户列表\n    showFollowUsers() {\n      // 重置面板关闭状态，允许重新@用户\n      this.mentionPanelClosed = false;\n      // 清空搜索关键词，显示关注用户\n      this.searchUserKeyword = '';\n      this.searchUsersForMention();\n    },\n\n    // 自动保存草稿（防抖）\n    autoSaveDraft() {\n      // 清除之前的定时器\n      if (this.saveTimer) {\n        clearTimeout(this.saveTimer);\n      }\n\n      // 设置新的定时器\n      this.saveTimer = setTimeout(() => {\n        this.saveDraft();\n      }, 1000); // 1秒后保存\n    },\n\n    // 删除@用户\n    removeMentionUser(uid) {\n      // 先找到要删除的用户信息\n      const userToRemove = this.mentionUsers.find(user => user.uid === uid);\n\n      if (userToRemove) {\n        // 从内容中移除对应的@文本，只处理 @用户名 格式\n        const pattern = new RegExp(`@${this.escapeRegExp(userToRemove.nickname)}\\\\s*`, 'g');\n        this.content = this.content.replace(pattern, '');\n      }\n\n      // 从已@用户列表中移除\n      this.mentionUsers = this.mentionUsers.filter(user => user.uid !== uid);\n\n      // 自动保存草稿\n      this.autoSaveDraft();\n    },\n\n    // 转义正则表达式特殊字符\n    escapeRegExp(string) {\n      return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n    },\n\n    // 解析内容中的@用户并更新mentionUsers列表\n    parseMentionUsersFromContent() {\n      // 从内容中提取所有@用户名\n      const pattern = /@([^\\s@]+)/g;\n      const mentionedNicknames = [];\n      let match;\n\n      while ((match = pattern.exec(this.content)) !== null) {\n        mentionedNicknames.push(match[1]);\n      }\n\n      // 只保留在内容中仍然存在的@用户\n      this.mentionUsers = this.mentionUsers.filter(user =>\n        mentionedNicknames.includes(user.nickname)\n      );\n    },\n  }\n}\n</script>\n<style>\n.container {\n  padding-bottom: 140rpx; /* 为固定工具栏留出空间，减少空白 */\n}\n\n/* 导航栏样式 - 参考details.vue */\n.nav-box{\n  position: fixed;\n  z-index: 99;\n  top: 0;\n  left: 0;\n  width: 100%;\n  background: #fff;\n  box-sizing: border-box;\n}\n.nav-item{\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.nav-item .nav-back{\n  padding: 0 30rpx;\n  width: 34rpx;\n  height: 100%;\n  border-radius: 50%;\n}\n.nav-title {\n  flex: 1;\n  font-size: 32rpx;\n  font-weight: 700;\n  text-align: center;\n  max-width: 400rpx;\n}\n.nav-publish {\n  padding: 0;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n}\n\n/* 非小程序环境的默认内边距 */\n/* #ifndef MP-WEIXIN */\n.nav-publish {\n  padding-right: 30rpx;\n}\n/* #endif */\n\n.publish-btn {\n  position: relative;\n  padding: 0 32rpx;\n  height: 64rpx;\n  min-width: 96rpx;\n  border-radius: 32rpx;\n  background: #000;\n  color: #fff;\n  font-size: 28rpx;\n  font-weight: 700;\n  letter-spacing: 1rpx;\n  text-align: center;\n  line-height: 64rpx;\n  border: none;\n  outline: none;\n  cursor: pointer;\n  overflow: hidden;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  box-shadow:\n    0 4rpx 16rpx rgba(0, 0, 0, 0.15),\n    0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n/* 发光效果 */\n.publish-btn::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(45deg,\n    rgba(255, 255, 255, 0.1) 0%,\n    rgba(255, 255, 255, 0.05) 50%,\n    rgba(255, 255, 255, 0.1) 100%);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n/* 内部高光 */\n.publish-btn::after {\n  content: '';\n  position: absolute;\n  top: 2rpx;\n  left: 2rpx;\n  right: 2rpx;\n  height: 30rpx;\n  background: linear-gradient(180deg,\n    rgba(255, 255, 255, 0.2) 0%,\n    rgba(255, 255, 255, 0.05) 100%);\n  border-radius: 30rpx 30rpx 60rpx 60rpx;\n  opacity: 0.6;\n}\n\n/* 点击效果 */\n.publish-btn:active {\n  transform: translateY(2rpx) scale(0.98);\n  box-shadow:\n    0 2rpx 8rpx rgba(0, 0, 0, 0.2),\n    0 1rpx 4rpx rgba(0, 0, 0, 0.15);\n}\n\n/* 禁用状态 */\n.publish-btn-disabled {\n  background: #666;\n  color: #999;\n  cursor: not-allowed;\n  box-shadow:\n    0 2rpx 8rpx rgba(0, 0, 0, 0.1),\n    0 1rpx 4rpx rgba(0, 0, 0, 0.05);\n}\n\n.publish-btn-disabled::before {\n  display: none;\n}\n\n.publish-btn-disabled::after {\n  opacity: 0.3;\n}\n\n.publish-btn-disabled:active {\n  transform: none;\n}\n\n/* 小程序环境适配 */\n/* #ifdef MP-WEIXIN */\n.publish-btn {\n  background: #000;\n  border: 2rpx solid rgba(255, 255, 255, 0.1);\n}\n\n.publish-btn::before {\n  background: linear-gradient(45deg,\n    rgba(7, 193, 96, 0.1) 0%,\n    rgba(0, 174, 66, 0.05) 50%,\n    rgba(7, 193, 96, 0.1) 100%);\n}\n/* #endif */\n\n/* H5环境优化 */\n/* #ifdef H5 */\n.publish-btn {\n  user-select: none;\n  -webkit-user-select: none;\n  -webkit-tap-highlight-color: transparent;\n}\n\n.publish-btn:hover {\n  transform: translateY(-2rpx);\n  box-shadow:\n    0 8rpx 24rpx rgba(0, 0, 0, 0.2),\n    0 4rpx 12rpx rgba(0, 0, 0, 0.15);\n}\n\n.publish-btn:hover::before {\n  opacity: 1;\n}\n\n.publish-btn:hover::after {\n  opacity: 0.8;\n}\n/* #endif */\n\n/* APP环境优化 */\n/* #ifdef APP-PLUS */\n.publish-btn {\n  background: #000;\n  border: 1rpx solid rgba(255, 255, 255, 0.08);\n}\n\n.publish-btn::before {\n  background: linear-gradient(45deg,\n    rgba(255, 107, 107, 0.1) 0%,\n    rgba(238, 90, 82, 0.05) 50%,\n    rgba(255, 107, 107, 0.1) 100%);\n}\n/* #endif */\n\n/* 深色模式适配 */\n@media (prefers-color-scheme: dark) {\n  .publish-btn {\n    background: #1a1a1a;\n    border: 1rpx solid rgba(255, 255, 255, 0.1);\n    box-shadow:\n      0 4rpx 16rpx rgba(0, 0, 0, 0.3),\n      0 2rpx 8rpx rgba(0, 0, 0, 0.2);\n  }\n\n  .publish-btn-disabled {\n    background: #333;\n    color: #666;\n  }\n}\n\n.content-box {\n  margin: 30rpx;\n  width: calc(100% - 60rpx);\n  border-radius: 30rpx;\n  position: relative;\n}\n\n.textarea-container {\n  position: relative;\n  width: 100%;\n}\n\n/* 透明的textarea用于输入 */\n.textarea-input {\n  width: calc(100% - 40rpx);\n  padding: 20rpx;\n  font-size: 28rpx;\n  min-height: 280rpx;\n  line-height: 1.6;\n  color: transparent; /* 文字透明，只显示光标 */\n  background: transparent;\n  position: relative;\n  z-index: 2;\n}\n\n/* 文本覆盖层 */\n.text-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: calc(100% - 40rpx);\n  padding: 20rpx;\n  font-size: 28rpx;\n  min-height: 280rpx;\n  line-height: 1.6;\n  pointer-events: none; /* 不阻挡textarea的点击 */\n  z-index: 1;\n}\n\n\n\n.formatted-content {\n  width: 100%;\n  font-size: 28rpx;\n  line-height: 1.6;\n  color: #333;\n  word-wrap: break-word;\n  word-break: break-all;\n}\n\n/* 占位符文本样式 */\n.placeholder-text {\n  color: #999;\n  font-size: 28rpx;\n}\n\n/* 普通文本样式 */\n.normal-text {\n  color: #333;\n  font-size: 28rpx;\n}\n\n/* @用户文本样式 - 蓝色链接样式 */\n.mention-text {\n  color: #1890ff;\n  font-size: 28rpx;\n  font-weight: 500;\n}\n\n/* 兼容原有的textarea-item样式 */\n.content-box .textarea-item {\n  width: calc(100% - 40rpx);\n  padding: 20rpx;\n  font-size: 28rpx;\n  min-height: 280rpx;\n}\n\n/* @用户标签样式 */\n.mention-tags {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12rpx;\n  padding: 0 20rpx 10rpx;\n  margin-top: 10rpx;\n}\n\n\n\n.mention-tag {\n  display: flex;\n  align-items: center;\n  background: #f0f9ff;\n  border: 1px solid #07c160;\n  border-radius: 20rpx;\n  padding: 8rpx 12rpx;\n}\n\n.mention-tag:active {\n  background: #e6f7ff;\n}\n\n.mention-avatar {\n  width: 32rpx;\n  height: 32rpx;\n  border-radius: 50%;\n  margin-right: 8rpx;\n  flex-shrink: 0;\n}\n\n.mention-nickname {\n  font-size: 24rpx;\n  color: #07c160;\n  font-weight: 500;\n  margin-right: 8rpx;\n}\n\n.mention-delete {\n  font-size: 28rpx;\n  color: #999;\n  font-weight: bold;\n  width: 24rpx;\n  height: 24rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  background: rgba(0, 0, 0, 0.1);\n}\n\n.content-box .scroll-box {\n  margin: 20rpx 0;\n  white-space: nowrap;\n  width: 100%;\n}\n\n.scroll-box .scroll-item {\n  display: flex;\n  padding: 0 20rpx;\n}\n\n.scroll-item .del {\n  position: absolute;\n  z-index: 9;\n  top: 8rpx;\n  right: 8rpx;\n  width: 38rpx;\n  height: 38rpx;\n  border-radius: 50%;\n  justify-content: center;\n  transform: rotate(45deg);\n  border: 1px solid #fff;\n  background: rgba(0, 0, 0, 0.3);\n}\n\n.scroll-item .del image {\n  width: 18rpx;\n  height: 18rpx;\n}\n\n.scroll-item .img-box {\n  flex-shrink: 0;\n  margin-right: 8rpx;\n  width: 196rpx;\n  height: 196rpx;\n  border-radius: 8rpx;\n  border: 1px solid #f8f8f8;\n  justify-content: center;\n  position: relative;\n  overflow: hidden;\n}\n\n.scroll-item .icon {\n  color: #999;\n  font-size: 36rpx;\n  font-weight: 700;\n}\n\n.scroll-item image,\n.scroll-item video {\n  width: 100%;\n  height: 100%;\n}\n\n.scroll-item .sort {\n  position: absolute;\n  left: 0;\n  bottom: 0;\n  width: 196rpx;\n  height: 45rpx;\n  background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.376));\n  border-radius: 0 0 8rpx 8rpx;\n}\n\n.scroll-item .sort .sort-item {\n  width: 50%;\n  justify-content: center;\n  font-size: 26rpx;\n  font-weight: 700;\n}\n\n.scroll-item .video-box {\n  width: 100%;\n  justify-content: space-between;\n}\n\n.video-box .video-item {\n  width: calc(50% - 8rpx);\n  height: 240rpx;\n  border-radius: 8rpx;\n  position: relative;\n  overflow: hidden;\n  background: #000;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.video-box .video-item video {\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n  border-radius: 8rpx;\n}\n\n.video-box .video-item .del {\n  position: absolute;\n  top: 8rpx;\n  right: 8rpx;\n  width: 40rpx;\n  height: 40rpx;\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 50%;\n  z-index: 10;\n}\n\n.video-box .video-item .del image {\n  width: 20rpx;\n  height: 20rpx;\n}\n\n.scroll-item .audio-box,\n.scroll-item .file-audio {\n  width: 100%;\n  height: 128rpx;\n  border-radius: 8rpx;\n}\n\n.scroll-item .file-audio {\n  color: #fff;\n  position: relative;\n  overflow: hidden;\n}\n\n.file-audio .audio-left {\n  margin-right: 30rpx;\n  width: 128rpx;\n  height: 128rpx;\n  position: relative;\n}\n\n.file-audio .audio-left .icon {\n  position: absolute;\n  top: 40rpx;\n  right: 40rpx;\n  bottom: 40rpx;\n  left: 40rpx;\n  width: 48rpx;\n  height: 48rpx;\n}\n\n.file-audio .audio-bg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: -2;\n  width: 100%;\n  height: 100%;\n}\n\n.file-audio .audio-mb {\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: -1;\n  width: 100%;\n  height: 100%;\n  -webkit-backdrop-filter: saturate(150%) blur(25px);\n  backdrop-filter: saturate(150%) blur(25px);\n  background: rgba(0, 0, 0, 0.5);\n}\n\n.file-audio .audio-t1 {\n  font-size: 26rpx;\n  font-weight: 700;\n}\n\n.file-audio .audio-t2 {\n  margin-top: 10rpx;\n  opacity: 0.8;\n  font-size: 20rpx;\n}\n\n.file-audio .aph {\n  color: #fff;\n}\n\n.scroll-item .file-add {\n  flex-direction: column;\n  justify-content: center;\n  background: #f8f8f8;\n  border: 1px solid #f8f8f8;\n  font-size: 18rpx;\n  color: #000;\n}\n\n.scroll-item .file-add image {\n  width: 20rpx;\n  height: 20rpx;\n  margin-bottom: 8rpx;\n}\n\n.content-box .content-item {\n  width: calc(100% - 40rpx);\n  margin: 20rpx;\n  padding-top: 20rpx;\n  border-top: 1rpx solid #f0f0f0;\n  justify-content: flex-start;\n  flex-wrap: wrap;\n  gap: 16rpx;\n}\n\n/* 内容区域的标签项样式调整 */\n.content-item .tag-item {\n  margin: 0; /* 重置margin，使用gap来控制间距 */\n  flex-shrink: 0; /* 防止标签被压缩 */\n}\n\n/* 标签区域的标签项保持原有边距 */\n.tags-box .tag-item {\n  margin: 0 10rpx 10rpx;\n}\n\n\n\n.tags-box {\n  width: calc(100% - 40rpx);\n  padding: 0 20rpx;\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.tag-item {\n  margin: 0 10rpx 10rpx;\n  height: 64rpx;\n  border-radius: 16rpx;\n  border: 4rpx solid #f5f5f5;\n  transition: all 0.2s ease;\n}\n\n.tag-item:active {\n  transform: scale(0.95);\n  background-color: #f8f8f8;\n}\n\n/* 已选择状态 */\n.tag-item.selected {\n  border: 4rpx solid #f5f5f5;\n  background-color: #fff;\n}\n\n/* 未选择状态 */\n.tag-item.unselected {\n  border: 4rpx dashed #e0e0e0;\n  background-color: #fafafa;\n}\n\n.tag-item.unselected:active {\n  border-color: #d0d0d0;\n  background-color: #f0f0f0;\n  transform: scale(0.95);\n}\n\n.tag-item .icon {\n  margin: 8rpx;\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 8rpx;\n  transition: opacity 0.2s ease;\n}\n\n.tag-item text {\n  font-size: 24rpx;\n  font-weight: 700;\n  margin: 0 16rpx 0 8rpx;\n  transition: color 0.2s ease;\n}\n\n.tag-delete {\n  width: 28rpx;\n  height: 28rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 8rpx;\n}\n\n.tag-delete text {\n  font-size: 24rpx;\n  color: #999;\n  margin: 0;\n}\n\n.location-icon {\n  padding: 12rpx;\n  transition: opacity 0.2s ease;\n}\n\n.location-text {\n  max-width: 200rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.content-hk .hk-item .del {\n  color: #fa5150;\n  font-size: 28rpx;\n  font-weight: 700;\n  transform: rotate(45deg);\n}\n\n.popup-box {\n  width: calc(100% - 40rpx);\n  padding: 20rpx;\n  background: #fff;\n  border-radius: 30rpx 30rpx 0 0;\n  position: relative;\n  overflow: hidden;\n}\n\n.popup-box .popup-top {\n  width: calc(100% - 20rpx);\n  padding: 10rpx;\n  justify-content: space-between;\n}\n\n.popup-top .popup-title .t1 {\n  font-size: 38rpx;\n  font-weight: 700;\n}\n\n.popup-top .popup-title .t2 {\n  color: #999;\n  font-size: 20rpx;\n  font-weight: 300;\n}\n\n.popup-top .popup-close {\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 50%;\n  background: #f8f8f8;\n  justify-content: center;\n  transform: rotate(45deg);\n}\n\n.popup-box .popup-btn {\n  margin: 40rpx 10rpx;\n  width: calc(100% - 20rpx);\n  height: 100rpx;\n  line-height: 100rpx;\n  text-align: center;\n  font-size: 24rpx;\n  font-weight: 700;\n  color: #fff;\n  background: #000;\n  border-radius: 100rpx;\n}\n\n.popup-box .popup-search {\n  margin: 30rpx 10rpx;\n  width: calc(100% - 40rpx);\n  padding: 0 10rpx;\n  height: 80rpx;\n  background: #f8f8f8;\n  border-radius: 12rpx;\n}\n\n.popup-box .popup-search input {\n  width: calc(100% - 130rpx);\n  margin: 0 20rpx;\n  font-size: 24rpx;\n  font-weight: 700;\n}\n\n.popup-search .search-ph {\n  color: #999;\n}\n\n.popup-search .search-btn {\n  width: 90rpx;\n  height: 60rpx;\n  line-height: 60rpx;\n  text-align: center;\n  font-size: 20rpx;\n  font-weight: 700;\n  color: #000;\n  background: #fff;\n  border-radius: 8rpx;\n}\n\n.popup-box .popup-scroll {\n  width: 100%;\n  max-height: 50vh;\n  overflow-y: scroll;\n}\n\n.popup-box .circle-box {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.circle-box .circle-item {\n  margin: 10rpx;\n  padding: 8rpx;\n  color: #000;\n  border-color: #f8f8f8;\n  border-width: 4rpx;\n  border-style: solid;\n  border-radius: 50rpx;\n}\n\n.circle-box .active {\n  border-color: #000 !important;\n  background: #f8f8f8 !important;\n}\n\n.circle-box .circle-item image {\n  width: 64rpx;\n  height: 64rpx;\n  border-radius: 50%;\n}\n\n.circle-box .circle-item text {\n  margin: 0 16rpx;\n  font-size: 26rpx;\n  font-weight: 700;\n}\n\n.empty-box {\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 0;\n}\n\n.empty-box image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 30rpx;\n}\n\n.empty-box .e1 {\n  font-size: 28rpx;\n  font-weight: bold;\n  margin-bottom: 10rpx;\n}\n\n.empty-box .e2 {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.tips-box {\n  padding: 20rpx 30rpx;\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 12rpx;\n  justify-content: center;\n}\n\n.tips-box .tips-item {\n  color: #fff;\n  font-size: 28rpx;\n  font-weight: 700;\n}\n\n.df {\n  display: flex;\n  align-items: center;\n}\n\n.ohto {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.media-toolbar-fixed {\n  position: fixed;\n  left: 0;\n  width: 100%;\n  background-color: #f8f8f8;\n  border-top: 1px solid #e5e5e5;\n  z-index: 99;\n  box-shadow: 0 -2rpx 6rpx rgba(0, 0, 0, 0.05);\n  transition: bottom 0.3s ease;\n}\n\n/* 键盘弹起时的样式调整 */\n.media-toolbar-fixed.keyboard-active {\n  padding-bottom: 0; /* 键盘弹起时移除底部安全区域 */\n}\n\n/* 非键盘状态时保持底部安全区域 */\n.media-toolbar-fixed:not(.keyboard-active) {\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n.toolbar-box {\n  display: flex;\n  justify-content: space-evenly;\n  align-items: center;\n  width: 100%;\n  min-height: 88rpx;\n  position: relative;\n}\n\n.toolbar-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 12rpx 6rpx;\n  border-radius: 12rpx;\n  transition: all 0.2s;\n  flex: 1;\n  max-width: 100rpx;\n  min-height: 70rpx;\n}\n\n.toolbar-item:active {\n  background-color: #eaeaea;\n  transform: scale(0.95);\n}\n\n.toolbar-item.active {\n  background-color: #f0f0f0;\n  border-radius: 12rpx;\n}\n\n.toolbar-item image {\n  width: 44rpx;\n  height: 44rpx;\n}\n\n.toolbar-item .vote {\n  color: #ff9500;\n  text-shadow: 0 1rpx 2rpx rgba(255, 149, 0, 0.3);\n  font-size: 40rpx;\n  font-weight: bold;\n  background-color: rgba(255, 149, 0, 0.1);\n  border-radius: 50%;\n  width: 44rpx;\n  height: 44rpx;\n  line-height: 44rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.more-options-panel {\n  width: 100%;\n  background: #f8f8f8;\n  border-radius: 0;\n  max-height: 0;\n  overflow: hidden;\n  position: relative;\n  top: 0;\n  left: 0;\n}\n\n.more-options-panel.expanded {\n  max-height: 200rpx;\n  padding: 20rpx 0;\n}\n\n.more-options-row {\n  display: flex;\n  justify-content: space-evenly;\n  align-items: center;\n  margin-bottom: 0;\n}\n\n.more-options-row .toolbar-item {\n  background-color: #ffffff;\n  border-radius: 12rpx;\n  padding: 12rpx 6rpx;\n  margin: 0 6rpx;\n}\n\n.more-options-row .toolbar-item image {\n  width: 44rpx;\n  height: 44rpx;\n}\n\n.toolbar-item.invisible {\n  opacity: 0;\n  pointer-events: none;\n}\n\n/* 话题列表样式 */\n.topic-box {\n  width: 100%;\n  padding: 10rpx;\n}\n\n.topic-item {\n  width: calc(100% - 40rpx);\n  padding: 20rpx;\n  border-bottom: 1px solid #f5f5f5;\n}\n\n.topic-tag {\n  width: 60rpx;\n  height: 60rpx;\n  line-height: 60rpx;\n  text-align: center;\n  background: #f8f8f8;\n  border-radius: 50%;\n  font-size: 36rpx;\n  font-weight: bold;\n  margin-right: 20rpx;\n}\n\n.topic-content {\n  flex: 1;\n}\n\n.topic-name {\n  font-size: 28rpx;\n  font-weight: 700;\n  margin-bottom: 6rpx;\n}\n\n.topic-desc {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 录音组件样式 */\n.record-popup-box {\n  padding-bottom: 40rpx;\n}\n\n.record-container {\n  width: 100%;\n  padding: 30rpx 0;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.record-text {\n  font-size: 28rpx;\n  color: #333;\n  text-align: center;\n  padding: 20rpx;\n}\n\n/* 投票弹窗样式 */\n.vote-popup {\n  padding-bottom: 30rpx;\n}\n\n.vote-title-input {\n  margin: 20rpx 10rpx;\n  width: calc(100% - 20rpx);\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.vote-input {\n  width: 100%;\n  height: 80rpx;\n  font-size: 28rpx;\n  font-weight: 500;\n  padding: 0 10rpx;\n}\n\n.vote-options {\n  margin: 20rpx 10rpx;\n  width: calc(100% - 20rpx);\n}\n\n.vote-option-item {\n  position: relative;\n  margin-bottom: 20rpx;\n  background-color: #f5f6fa;\n  border-radius: 16rpx;\n  padding: 0 20rpx;\n  height: 80rpx;\n  display: flex;\n  align-items: center;\n}\n\n.vote-option-input {\n  flex: 1;\n  height: 80rpx;\n  font-size: 26rpx;\n  color: #333;\n}\n\n.vote-option-delete {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.delete-circle {\n  width: 36rpx;\n  height: 36rpx;\n  border-radius: 50%;\n  background-color: #ff5252;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.delete-line {\n  width: 20rpx;\n  height: 4rpx;\n  background-color: #fff;\n}\n\n.vote-add-option {\n  margin: 30rpx 10rpx;\n  width: calc(100% - 20rpx);\n  height: 80rpx;\n  border: 1px dashed #ddd;\n  border-radius: 16rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #999;\n}\n\n.vote-add-icon {\n  font-size: 32rpx;\n  margin-right: 10rpx;\n}\n\n.vote-add-text {\n  font-size: 26rpx;\n}\n\n.vote-bottom-btns {\n  margin-top: 40rpx;\n  display: flex;\n  justify-content: space-between;\n  padding: 0 30rpx;\n}\n\n.vote-cancel-btn, .vote-confirm-btn {\n  width: 45%;\n  height: 80rpx;\n  line-height: 80rpx;\n  text-align: center;\n  font-size: 28rpx;\n  font-weight: 500;\n  border-radius: 40rpx;\n}\n\n.vote-cancel-btn {\n  color: #666;\n  background-color: #f5f5f5;\n}\n\n.vote-confirm-btn {\n  color: #fff;\n  background-color: #000;\n}\n\n/* 投票显示样式 */\n.vote-display {\n  margin: 20rpx 0;\n  padding: 20rpx;\n  background-color: #f8f8f8;\n  border-radius: 16rpx;\n  width: 100%;\n}\n\n.vote-display-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.vote-icon {\n  margin-right: 10rpx;\n  font-size: 32rpx;\n}\n\n.vote-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n}\n\n.vote-display-options {\n  display: flex;\n  flex-direction: column;\n  gap: 12rpx;\n}\n\n.vote-display-option {\n  padding: 20rpx;\n  background-color: #fff;\n  border-radius: 12rpx;\n  font-size: 26rpx;\n  color: #333;\n  text-align: center;\n}\n\n/* 投票显示样式 */\n.vote-box {\n  width: 100%;\n}\n\n.vote-container {\n  width: calc(100% - 40rpx);\n  margin: 0 20rpx;\n  background-color: #f5f5f5;\n  border-radius: 16rpx;\n  padding: 20rpx;\n  position: relative;\n}\n\n.vote-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 16rpx;\n  position: relative;\n}\n\n.vote-title-container {\n  display: flex;\n  align-items: center;\n}\n\n.vote-icon {\n  width: 32rpx;\n  height: 32rpx;\n  margin-right: 10rpx;\n}\n\n.vote-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  text-align: left;\n}\n\n.vote-delete {\n  width: 40rpx;\n  height: 40rpx;\n  border-radius: 50%;\n  background: #f8f8f8;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.vote-options {\n  display: flex;\n  flex-direction: column;\n  gap: 12rpx;\n}\n\n.vote-option {\n  padding: 20rpx;\n  background-color: #ffffff;\n  border-radius: 12rpx;\n  font-size: 26rpx;\n  color: #333;\n  text-align: center;\n}\n\n.toolbar-label {\n  margin-top: 6rpx;\n  font-size: 22rpx;\n  color: #666;\n  text-align: center;\n  line-height: 1;\n}\n\n\n\n.empty-users, .loading-users {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 60rpx 20rpx;\n  color: #999;\n  font-size: 26rpx;\n}\n\n\n\n/* @用户面板样式 - 横向滑动卡片式 */\n.mention-user-panel {\n  position: fixed;\n  left: 0;\n  right: 0;\n  background: #fff;\n  border-top: 1rpx solid #e5e5e5;\n  z-index: 999;\n  padding: 20rpx 0;\n  /* 在工具栏上方，工具栏最小高度88rpx + 安全区域 */\n  margin-bottom: calc(88rpx + env(safe-area-inset-bottom));\n}\n\n/* @用户面板中的关闭按钮定位 */\n.mention-user-panel .popup-close {\n  position: absolute;\n  top: 10rpx;\n  right: 20rpx;\n  z-index: 1000;\n}\n\n\n\n.empty-mention-users,\n.loading-mention-users {\n  text-align: center;\n  padding: 60rpx 0;\n  color: #999;\n  font-size: 28rpx;\n  height: 120rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 横向滚动容器 */\n.mention-user-scroll {\n  width: 100%;\n  height: 120rpx;\n  white-space: nowrap;\n}\n\n/* 横向用户列表容器 */\n.mention-user-list-horizontal {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  height: 120rpx;\n  padding: 0 20rpx;\n  gap: 20rpx;\n}\n\n/* 用户卡片样式 */\n.mention-user-card {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  width: 100rpx;\n  height: 120rpx;\n  flex-shrink: 0;\n  position: relative;\n  border-radius: 12rpx;\n  padding: 8rpx;\n}\n\n.mention-user-card:active {\n  background: #f5f5f5;\n}\n\n/* 用户头像 */\n.mention-user-avatar {\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 50%;\n  margin-bottom: 8rpx;\n  flex-shrink: 0;\n  border: 2rpx solid #fff;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n/* 用户昵称 */\n.mention-user-nickname {\n  font-size: 22rpx;\n  color: #333;\n  font-weight: 500;\n  text-align: center;\n  max-width: 80rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  line-height: 1.2;\n}\n\n\n</style>", "import MiniProgramPage from 'Z:/WWW/shejiao/vue3/pages/note/add.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "getTopicList", "publishDynamic", "getJoinedCircles", "getSocialFollowList", "searchUsers"], "mappings": ";;;;AAssBA,MAAK,WAAY,MAAW;AAC5B,MAAO,aAAY,MAAW;AAC9B,MAAK,YAAa,MAAW;AAK7B,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,cAAc;AAAA;AAAA,MACd,aAAa;AAAA;AAAA,MACb,QAAQ;AAAA,MACA,IAAI;AAAA,MACV,SAAS;AAAA,MACT,MAAM;AAAA;AAAA,MACR,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,OAAO,CAAE;AAAA,MACT,MAAM,CAAE;AAAA,MACR,OAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,QACL,OAAO;AAAA,MACR;AAAA,MACD,OAAO;AAAA,QACL,OAAO;AAAA,QACP,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACP;AAAA,MACD,MAAM;AAAA,QACJ,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW;AAAA,MACZ;AAAA,MACD,QAAQ;AAAA,QACN,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,MACD,UAAU;AAAA,QACR,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,KAAK;AAAA,MACN;AAAA,MACD,YAAY,CAAE;AAAA,MACd,WAAW,CAAE;AAAA,MACb,cAAc,CAAE;AAAA,MAChB,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,cAAc;AAAA,MACd,UAAU,CAAE;AAAA,MACZ,WAAW,CAAE;AAAA,MACb,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,cAAc;AAAA,MACd,aAAa;AAAA,MACb,cAAc;AAAA;AAAA,MAEd,gBAAgB;AAAA,MAChB,UAAU,CAAE;AAAA;AAAA,MACZ,cAAc;AAAA;AAAA;AAAA,MAEd,UAAU;AAAA,MACV,WAAW;AAAA;AAAA,MACX,qBAAqB;AAAA;AAAA,MACrB,qBAAqB;AAAA;AAAA,MACrB,aAAa;AAAA;AAAA,MACb,WAAW;AAAA;AAAA;AAAA,MAEX,cAAc;AAAA;AAAA;AAAA,MAEd,oBAAoB;AAAA;AAAA,MAEpB,gBAAgB,CAAE;AAAA;AAAA,MAElB,UAAU;AAAA;AAAA,MACV,UAAU;AAAA,QACR,OAAO;AAAA,QACP,SAAS,CAAC,IAAI,EAAE;AAAA;AAAA,QAChB,YAAY;AAAA;AAAA,MACb;AAAA;AAAA,MAED,gBAAgB;AAAA;AAAA,MAChB,mBAAmB;AAAA;AAAA,MACnB,gBAAgB,CAAE;AAAA;AAAA,MAClB,cAAc,CAAE;AAAA;AAAA,MAChB,gBAAgB;AAAA;AAAA,MAChB,iBAAiB;AAAA;AAAA,MACjB,WAAW;AAAA;AAAA,MACX,oBAAoB;AAAA;AAAA,MACpB,kBAAkB,CAAE;AAAA;AAAA;AAAA,MAEpB,eAAe;AAAA;AAAA,MACf,kBAAkB;AAAA;AAAA,IACpB;AAAA,EACD;AAAA,EACD,UAAU;AAAA;AAAA,IAER,kBAAkB;AAChB,UAAI,CAAC,KAAK;AAAS,eAAO;AAG1B,YAAM,WAAW,GAAG,KAAK,OAAO,IAAI,KAAK,UAAU,KAAK,YAAY,CAAC,IAAI,KAAK,UAAU,KAAK,gBAAgB,CAAC;AAG9G,UAAI,KAAK,iBAAiB,KAAK,qBAAqB,UAAU;AAC5D,eAAO,KAAK;AAAA,MACd;AAEA,YAAM,WAAW,CAAA;AACjB,YAAM,iBAAiB;AACvB,UAAI,YAAY;AAChB,UAAI;AAGJ,YAAM,kBAAkB,IAAI,IAAI,KAAK,aAAa,IAAI,UAAQ,KAAK,QAAQ,CAAC;AAG5E,YAAM,cAAc,oBAAI;AACxB,WAAK,iBAAiB,QAAQ,cAAY;AACxC,oBAAY,IAAI,GAAG,SAAS,QAAQ,IAAI,SAAS,IAAI,IAAI,IAAI;AAAA,MAC/D,CAAC;AAED,cAAQ,QAAQ,eAAe,KAAK,KAAK,OAAO,OAAO,MAAM;AAE3D,cAAM,aAAa,KAAK,QAAQ,UAAU,WAAW,MAAM,KAAK;AAChE,YAAI,YAAY;AACd,mBAAS,KAAK;AAAA,YACZ,MAAM;AAAA,YACN,WAAW;AAAA,UACb,CAAC;AAAA,QACH;AAGA,cAAM,cAAc,MAAM,CAAC;AAC3B,cAAM,WAAW,MAAM,CAAC;AACxB,cAAM,cAAc,gBAAgB,IAAI,QAAQ;AAGhD,cAAM,aAAa,YAAY,IAAI,GAAG,MAAM,KAAK,IAAI,WAAW,EAAE;AAElE,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,WAAW,CAAC,cAAc;AAAA;AAAA,UAC1B;AAAA,QACF,CAAC;AAED,oBAAY,MAAM,QAAQ,MAAM,CAAC,EAAE;AAAA,MACrC;AAGA,YAAM,gBAAgB,KAAK,QAAQ,UAAU,SAAS;AACtD,UAAI,eAAe;AACjB,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,WAAW;AAAA,QACb,CAAC;AAAA,MACH;AAEA,YAAM,SAAS,SAAS,SAAS,IAAI,WAAW,CAAC;AAAA,QAC/C,MAAM,KAAK;AAAA,QACX,WAAW;AAAA,MACb,CAAC;AAGD,WAAK,gBAAgB;AACrB,WAAK,mBAAmB;AAExB,aAAO;AAAA,IACR;AAAA;AAAA,IAED,iBAAiB;AACf,YAAM,aAAa,KAAK,WAAW,KAAK,QAAQ;AAChD,YAAM,YAAY,KAAK,QAAQ,KAAK,KAAK,SAAS;AAClD,YAAM,WAAW,KAAK,SAAS,KAAK,MAAM;AAC1C,YAAM,WAAW,KAAK,SAAS,KAAK,MAAM;AAC1C,YAAM,UAAU,KAAK,YAAY,KAAK,SAAS;AAE/C,aAAO,CAAC,cAAc,CAAC,aAAa,CAAC,YAAY,CAAC,YAAY,CAAC;AAAA,IAChE;AAAA;AAAA,IAGD,oBAAoB;AAClB,UAAI,KAAK,gBAAgB;AACvB,eAAO;AAAA,MACT;AAGA,UAAI,KAAK,MAAM,KAAK;AAClB,eAAO;AAAA,iBACE,KAAK,MAAM,KAAK;AACzB,eAAO;AAAA,MACP,WAAS,KAAK,KAAK,SAAS,GAAG;AAC/B,eAAO;AAAA,iBACE,KAAK,UAAU;AACxB,eAAO;AAAA,aACF;AACL,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB;AACnB,YAAM,QAAQ,CAAA;AAGdA,oBAAAA,MAAY,MAAA,OAAA,6BAAA,eAAe;AAC3BA,oBAAY,MAAA,MAAA,OAAA,6BAAA,WAAW,KAAK,WAAW;AAEvC,UAAI,KAAK,eAAe,KAAK,YAAY,QAAQ,GAAG;AAElD,cAAM,aAAaA,oBAAI;AACvB,cAAM,UAAU,KAAK;AAGrB,cAAM,aAAa,WAAW,cAAc;AAC5C,cAAM,cAAc,WAAW;AAC/B,cAAM,cAAc,WAAW;AAG/B,cAAM,uBAAuB,cAAc,QAAQ;AAGnD,cAAM,0BAA0B,uBAAuB;AAGvD,cAAM,kBAAkB;AAGxB,cAAM,iBAAiB,0BAA0B;AAGjD,cAAM,cAAc,iBAAiB;AACrC,cAAM,eAAe;AAErBA,sBAAAA,MAAY,MAAA,OAAA,6BAAA,eAAe;AAAA,UACzB,YAAY;AAAA,YACV;AAAA,YACA;AAAA,YACA;AAAA,UACD;AAAA,UACD,SAAS;AAAA,YACP,OAAO,QAAQ;AAAA,YACf,QAAQ,QAAQ;AAAA,YAChB,MAAM,QAAQ;AAAA,YACd,OAAO,QAAQ;AAAA,YACf,KAAK,QAAQ;AAAA,YACb,QAAQ,QAAQ;AAAA,UACjB;AAAA,UACD,aAAa;AAAA,YACX;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACD;AAAA,UACD,YAAY;AAAA,QACd,CAAC;AAAA,aACI;AAEL,cAAM,cAAc;AACpB,cAAM,eAAe;AACrBA,sBAAAA,MAAA,MAAA,OAAA,6BAAY,mBAAmB;AAAA,MACjC;AAQA,aAAO;AAAA,IACT;AAAA,EACD;AAAA,EACD,UAAU;AAER,QAAI,KAAK,OAAO,MAAM,IAAI,OAAO;AAC/B,WAAK,SAAS;AAGd,YAAM,WAAWA,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AACnD,WAAK,MAAM,QAAQ,SAAS,UAAU;AAAA,IACxC;AAGA,SAAK,WAAU;AAGf,SAAK,aAAY;AAAA,EAClB;AAAA,EACD,OAAO,QAAQ;AACb,QAAI,OAAO;AAGX,QAAI,KAAK,OAAO,MAAM,IAAI,OAAO;AAC/B,WAAK,SAAS;AAAA,IAChB;AAGA,QAAI,OAAO,IAAI;AACb,WAAK,KAAK,OAAO;AAAA,IACnB;AAEA,QAAI,OAAO,MAAM;AACf,WAAK,OAAO,OAAO;AAAA,IACrB;AAEA,QAAI,OAAO,UAAU;AACnB,WAAK,WAAW,OAAO;AAAA,IACzB;AAEA,QAAI,OAAO,YAAY;AACrB,WAAK,aAAa,OAAO;AAAA,IAC3B;AAGA,SAAK,WAAU;AAIf,QAAI,OAAOA,cAAAA,MAAI,2BAA2B,YAAY;AACpDA,oBAAG,MAAC,uBAAuB,SAAO;AAChC,aAAK,iBAAiB,IAAI;AAC1BA,sBAAA,MAAA,MAAA,OAAA,8BAAY,WAAW,IAAI,MAAM;AAAA,MACnC,CAAC;AAAA,IACH;AAqBA,SAAK,UAAS;AAGd,eAAW,MAAM;AACf,WAAK,YAAY;AAAA,IAClB,GAAE,GAAG;AAAA,EACP;AAAA,EACD,SAAS;AAEP,QAAI,KAAK,OAAO,MAAM,IAAI,OAAO;AAC/B,WAAK,SAAS;AAAA,WACT;AACL,WAAK,SAAS;AAAA,IAChB;AAAA,EACD;AAAA;AAAA,EAGD,SAAS;AAEP,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,UAAS;AAAA,IAChB;AAAA,EACD;AAAA;AAAA,EAGD,WAAW;AAET,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,UAAS;AAAA,IAChB;AAGA,QAAI,KAAK,WAAW;AAClB,mBAAa,KAAK,SAAS;AAC3B,WAAK,YAAY;AAAA,IACnB;AAEA,QAAI,KAAK,qBAAqB;AAC5B,mBAAa,KAAK,mBAAmB;AACrC,WAAK,sBAAsB;AAAA,IAC7B;AAEA,QAAI,KAAK,qBAAqB;AAC5B,mBAAa,KAAK,mBAAmB;AACrC,WAAK,sBAAsB;AAAA,IAC7B;AAGA,SAAK,kBAAiB;AAAA,EASvB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,aAAa;AAEX,YAAM,aAAaA,oBAAI;AACvB,WAAK,kBAAkB,WAAW,mBAAmB;AAIrD,UAAI;AACF,aAAK,cAAcA,oBAAI;AACvBA,sBAAA,MAAA,MAAA,OAAA,8BAAY,aAAa,KAAK,WAAW;AAEzC,YAAI,KAAK,eAAe,KAAK,YAAY,QAAQ,GAAG;AAElD,gBAAM,aAAa,KAAK,YAAY,MAAM,KAAK;AAC/C,eAAK,iBAAiB,KAAK,YAAY,SAAS,aAAa;AAE7DA,wBAAAA,MAAY,MAAA,OAAA,8BAAA,aAAa;AAAA,YACvB,OAAO,KAAK,YAAY;AAAA,YACxB,QAAQ,KAAK,YAAY;AAAA,YACzB,MAAM,KAAK,YAAY;AAAA,YACvB,OAAO,KAAK,YAAY;AAAA,YACxB,KAAK,KAAK,YAAY;AAAA,YACtB,QAAQ,KAAK,YAAY;AAAA,UAC3B,CAAC;AACDA,yEAAY,aAAa,KAAK,cAAc;AAG5C,eAAK,UAAU,MAAM;AACnBA,0BAAAA,MAAY,MAAA,OAAA,8BAAA,mBAAmB;AAC/B,iBAAK,aAAY;AAAA,UACnB,CAAC;AAAA,eACI;AACL,eAAK,iBAAiB;AACtBA,wBAAAA,MAAA,MAAA,OAAA,8BAAY,yBAAyB;AAAA,QACvC;AAAA,MACF,SAAS,GAAG;AACVA,sBAAA,MAAA,MAAA,SAAA,8BAAc,eAAe,CAAC;AAC9B,aAAK,iBAAiB;AAAA,MACxB;AAmBAA,oBAAAA,iDAAY,UAAU;AAAA,QACpB,iBAAiB,KAAK;AAAA,QACtB,gBAAgB,KAAK;AAAA,QACrB,UAAU,WAAW;AAAA,MACvB,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,SAAS;AAEP,UAAI,KAAK,QAAQ,KAAI,KAAM,KAAK,KAAK,SAAS,KAAK,KAAK,MAAM,OAAO,KAAK,MAAM,KAAK;AACnFA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACf,mBAAK,UAAS;AAAA,YAChB;AACAA,0BAAG,MAAC,aAAY;AAAA,UAClB;AAAA,QACF,CAAC;AAAA,aACI;AACLA,sBAAG,MAAC,aAAY;AAAA,MAClB;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB;AAEd,UAAI,KAAK,gBAAgB;AACvB,aAAK,YAAY,cAAc;AAC/B;AAAA,MACF;AAGA,WAAK,YAAY,CAAC;AAAA,IACnB;AAAA;AAAA,IAGD,eAAe;AACb,UAAI;AAEF,cAAM,cAAcA,cAAAA,MAAI,eAAe,WAAW;AAClD,YAAI,aAAa;AACf,eAAK,WAAW,KAAK,MAAM,WAAW;AACtCA,wBAAAA,MAAA,MAAA,OAAA,8BAAY,gBAAgB,OAAO,KAAK,KAAK,QAAQ,EAAE,MAAM;AAAA,eACxD;AAGLA,wBAAAA,MAAA,MAAA,OAAA,8BAAY,iBAAiB;AAC7B,eAAK,WAAW;QAClB;AAAA,MACF,SAAS,GAAG;AACVA,sBAAc,MAAA,MAAA,SAAA,8BAAA,aAAa,CAAC;AAC5B,aAAK,WAAW;MAClB;AAAA,IACD;AAAA;AAAA,IAGD,aAAa,GAAG;AACd,YAAM,QAAQ,EAAE,OAAO;AACvB,YAAM,SAAS,EAAE,OAAO,UAAU;AAElC,WAAK,UAAU;AACf,WAAK,iBAAiB;AAGtB,WAAK,wBAAwB,OAAO,MAAM;AAAA,IAC3C;AAAA;AAAA,IAGD,wBAAwB,OAAO,QAAQ;AAErC,UAAI,KAAK,qBAAqB;AAC5B,qBAAa,KAAK,mBAAmB;AAAA,MACvC;AAGA,WAAK,sBAAsB,WAAW,MAAM;AAE1C,aAAK,wBAAuB;AAG5B,aAAK,kBAAkB,OAAO,MAAM;AAGpC,aAAK,6BAA4B;AAGjC,aAAK,cAAa;AAAA,MACnB,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAKD,kBAAkB;AAChBA,oBAAAA,MAAY,MAAA,OAAA,8BAAA,cAAc;AAC1B,WAAK,iBAAiB;AACtB,WAAK,YAAY;AAEjB,WAAK,UAAU,MAAM;AACnB,mBAAW,MAAM;AACfA,wBAAY,MAAA,MAAA,OAAA,8BAAA,WAAW,KAAK,cAAc;AAAA,QAC3C,GAAE,GAAG;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACfA,oBAAAA,MAAY,MAAA,OAAA,8BAAA,cAAc;AAE1B,iBAAW,MAAM;AACf,YAAI,CAAC,KAAK,gBAAgB;AACxB,eAAK,YAAY;AAAA,QACnB;AAAA,MACD,GAAE,GAAG;AAEN,iBAAW,MAAM;AACf,YAAI,KAAK,iBAAiB,GAAG;AAC3BA,wBAAAA,iDAAY,QAAQ;AACpB,eAAK,iBAAiB;AAAA,QACxB;AAAA,MACD,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAGD,cAAc;AACZ,UAAI,OAAO;AAGXA,oBAAAA,MAAI,QAAQ;AAAA,QACV,KAAK,KAAK,OAAO,MAAM,IAAI,SAAS;AAAA,QACpC,QAAQ;AAAA,QACR,MAAM,EAAE,IAAI,KAAK,GAAI;AAAA,QACrB,QAAQ;AAAA,UACN,iBAAiB,YAAY,KAAK,OAAO,MAAM,IAAI;AAAA,QACpD;AAAA,QACD,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,QAAQ,IAAI,KAAK,WAAW,KAAK;AACvC,kBAAM,OAAO,IAAI,KAAK;AACtB,iBAAK,KAAK,KAAK;AACf,iBAAK,OAAO,KAAK;AACjB,iBAAK,UAAU,KAAK;AACpB,iBAAK,OAAO,KAAK,KAAK;AACtB,iBAAK,OAAO,OAAO,KAAK;AACxB,iBAAK,OAAO,SAAS,KAAK;AAC1B,iBAAK,SAAS,KAAK,KAAK;AACxB,iBAAK,SAAS,OAAO,KAAK;AAC1B,iBAAK,SAAS,MAAM,KAAK;AACzB,iBAAK,KAAK,OAAO,KAAK;AACtB,iBAAK,KAAK,UAAU,KAAK;AACzB,iBAAK,KAAK,WAAW,KAAK;AAC1B,iBAAK,KAAK,YAAY,KAAK;AAE3B,gBAAI,KAAK,QAAQ,GAAG;AAClB,mBAAK,OAAO,KAAK;AAAA,YACnB,WAAW,KAAK,QAAQ,GAAG;AACzB,mBAAK,MAAM,OAAO,KAAK,MAAM;AAC7B,mBAAK,MAAM,OAAO,KAAK,MAAM;AAC7B,mBAAK,MAAM,MAAM,KAAK,MAAM;AAC5B,mBAAK,MAAM,QAAQ,KAAK,MAAM;AAAA,YAChC,WAAW,KAAK,QAAQ,GAAG;AACzB,mBAAK,MAAM,QAAQ,KAAK,MAAM;AAC9B,mBAAK,MAAM,MAAM,KAAK,MAAM;AAC5B,mBAAK,MAAM,OAAO,KAAK,MAAM;AAC7B,mBAAK,MAAM,QAAQ,KAAK,MAAM;AAC9B,mBAAK,MAAM,OAAO,KAAK,MAAM;AAAA,YAC/B;AAEA,iBAAK,WAAW,KAAK;AACrB,iBAAK,aAAa,KAAK;AAEvB,gBAAI,KAAK,UAAU;AACjB,mBAAK,QAAQ,KAAK;AAAA,YACpB;AAAA,UACF;AAAA,QACD;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,8BAAA,aAAa,GAAG;AAAA,QAChC;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACf,UAAI,OAAO;AAGXA,oBAAAA,MAAI,QAAQ;AAAA,QACV,KAAK,KAAK,OAAO,MAAM,IAAI,SAAS;AAAA,QACpC,QAAQ;AAAA,QACR,MAAM,EAAE,IAAI,KAAK,SAAU;AAAA,QAC3B,QAAQ;AAAA,UACN,iBAAiB,YAAY,KAAK,OAAO,MAAM,IAAI;AAAA,QACpD;AAAA,QACD,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,QAAQ,IAAI,KAAK,WAAW,KAAK;AACvC,iBAAK,QAAQ,IAAI,KAAK;AAAA,UACxB;AAAA,QACD;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,8BAAA,aAAa,GAAG;AAAA,QAChC;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI,OAAO;AAGXA,oBAAAA,MAAI,QAAQ;AAAA,QACV,KAAK,KAAK,OAAO,MAAM,IAAI,SAAS;AAAA,QACpC,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,iBAAiB,YAAY,KAAK,OAAO,MAAM,IAAI;AAAA,QACpD;AAAA,QACD,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,QAAQ,IAAI,KAAK,WAAW,KAAK;AACvC,iBAAK,aAAa,IAAI,KAAK;AAAA,UAC7B;AAAA,QACD;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,8BAAA,aAAa,GAAG;AAAA,QAChC;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,aAAa;AACX,UAAI,OAAO;AAGXA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAGD,YAAM,SAAS;AAAA,QACb,SAAS,KAAK;AAAA,QACd,MAAM;AAAA,QACN,OAAO;AAAA;AAITC,iBAAAA,aAAa,MAAM,EAChB,KAAK,SAAO;AACXD,sBAAG,MAAC,YAAW;AAEf,YAAI,IAAI,WAAW,KAAK;AAEtB,eAAK,YAAY,IAAI,KAAK,QAAQ,CAAA;AAAA,eAC7B;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,OACD,EACA,MAAM,SAAO;AACZA,sBAAG,MAAC,YAAW;AACfA,sBAAc,MAAA,MAAA,SAAA,8BAAA,aAAa,GAAG;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,eAAe;AACb,WAAK,WAAU;AAAA,IAChB;AAAA;AAAA,IAGD,YAAY,QAAQ;AAClB,UAAI,OAAO;AAGX,UAAI,KAAK,QAAQ,KAAI,MAAO,IAAI;AAC9B,eAAO,KAAK,YAAY,OAAO;AAAA,MACjC;AAGA,UAAI;AACJ,UAAI,KAAK,QAAQ,KAAK,KAAK,MAAM,KAAK;AACpC,oBAAY;AAAA,MACd,WAAW,KAAK,QAAQ,KAAK,KAAK,MAAM,KAAK;AAC3C,oBAAY;AAAA,MACZ,WAAS,KAAK,KAAK,SAAS,GAAG;AAC/B,oBAAY;AAAA,aACP;AACL,oBAAY;AAAA,MACd;AAGA,UAAI,KAAK,QAAQ,GAAG;AAClB,YAAI,CAAC,KAAK,MAAM,KAAK;AACnB,iBAAO,KAAK,YAAY,eAAe;AAAA,QACzC;AAAA,MACF;AAGA,UAAI,KAAK,QAAQ,GAAG;AAClB,YAAI,CAAC,KAAK,MAAM,OAAO;AACrB,iBAAO,KAAK,YAAY,YAAY;AAAA,QACtC;AAEA,YAAI,CAAC,KAAK,MAAM,KAAK;AACnB,iBAAO,KAAK,YAAY,UAAU;AAAA,QACpC;AAAA,MACF;AAGA,UAAI,OAAO;AACX,UAAI,eAAe;AACnB,UAAI,KAAK,UAAU;AACjB,YAAI,CAAC,KAAK,SAAS,SAAS,CAAC,KAAK,SAAS,MAAM,QAAQ;AACvD,iBAAO,KAAK,YAAY,SAAS;AAAA,QACnC;AAEA,cAAM,WAAW,KAAK,SAAS,WAAW,CAAE,GAAE,IAAI,UAAQ,OAAO,IAAI,KAAM,CAAA,EAAE,OAAO,SAAO,GAAG;AAC9F,YAAI,QAAQ,SAAS,GAAG;AACtB,iBAAO,KAAK,YAAY,YAAY;AAAA,QACtC;AACA,eAAO,EAAE,OAAO,KAAK,SAAS,MAAM,KAAI;AACxC,uBAAe;AAAA,MACjB;AAGAA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO,SAAS,WAAW;AAAA,QAC3B,MAAM;AAAA,MACR,CAAC;AAGD,YAAM,gBAAgB,KAAK,kBAAkB,KAAK,OAAO;AAGzD,UAAI,SAAS;AAAA,QACX,IAAI,KAAK;AAAA,QACT,SAAS;AAAA;AAAA,QACT,MAAM;AAAA;AAAA,QACN,eAAe,KAAK,KAAK;AAAA,QACzB,UAAU,KAAK,KAAK;AAAA,QACpB,WAAW,KAAK,KAAK;AAAA,QACrB,WAAW,KAAK,OAAO;AAAA,QACvB;AAAA;AAAA,QACA,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,QACjB,OAAO,KAAK,MAAM,IAAI,UAAQ,KAAK,EAAE;AAAA;AAGvC,UAAI,QAAQ,cAAc;AACxB,eAAO,OAAO;AACd,eAAO,eAAe;AAAA,MACxB;AAGA,UAAI,KAAK,cAAc;AACrB,YAAI,cAAc,KAAK,cAAc,GAAG;AACtC,sBAAY;AACZ,cAAI,CAAC,KAAK;AAAM,iBAAK,OAAO,CAAA;AAC5B,eAAK,KAAK,KAAK;AAAA,YACb,KAAK,KAAK;AAAA,YACV,MAAM;AAAA,YACN,MAAM;AAAA,UACR,CAAC;AACD,iBAAO,OAAO;AAAA,QAChB;AAAA,MACF;AAGA,UAAI,aAAa,GAAG;AAElB,eAAO,SAAS,KAAK,KAAK,IAAI,SAAO,IAAI,GAAG;AAAA,MAC9C,WAAW,aAAa,GAAG;AAEzB,YAAI,KAAK,MAAM,KAAK;AAClB,iBAAO,QAAQ,KAAK,MAAM;AAC1B,iBAAO,cAAc,KAAK,MAAM,SAAS;AACzC,iBAAO,cAAc,KAAK,MAAM,QAAQ;AACxC,iBAAO,eAAe,KAAK,MAAM,QAAQ;AACzCA,wBAAAA,iDAAY,SAAS;AAAA,YACnB,KAAK,KAAK,MAAM;AAAA,YAChB,OAAO,KAAK,MAAM;AAAA,YAClB,OAAO,KAAK,MAAM;AAAA,YAClB,QAAQ,KAAK,MAAM;AAAA,UACrB,CAAC;AAAA,QACH;AAAA,MACF,WAAW,aAAa,GAAG;AAEzB,eAAO,QAAQ,KAAK,MAAM;AAC1B,eAAO,cAAc,KAAK,MAAM;AAChC,eAAO,cAAc,KAAK,MAAM;AAAA,MAClC;AAGAA,qEAAY,WAAW,MAAM;AAC7BA,qEAAY,SAAS,KAAK,IAAI;AAC9BA,qEAAY,SAAS,SAAS;AAC9BA,oBAAY,MAAA,MAAA,OAAA,8BAAA,SAAS,KAAK,KAAK;AAG/BE,iBAAAA,eAAe,MAAM,EAClB,KAAK,SAAO;AACXF,sBAAG,MAAC,YAAW;AAGf,aAAK,YAAY,IAAI,QAAQ,IAAI,WAAW,MAAO,SAAS,SAAS,SAAW,SAAS,SAAS,OAAQ;AAG1G,YAAI,IAAI,WAAW,KAAK;AACtBA,wBAAAA,MAAA,MAAA,OAAA,8BAAY,gBAAgB;AAG5B,cAAI,WAAW,GAAG;AAChB,iBAAK,cAAc;AAAA,UACrB;AAGA,eAAK,WAAU;AAEf,qBAAW,WAAW;AACpBA,0BAAAA,iDAAY,WAAW;AACvBA,0BAAAA,MAAI,SAAS;AAAA,cACX,KAAK;AAAA,cACL,SAAS,WAAW;AAClBA,8BAAAA,MAAY,MAAA,OAAA,8BAAA,WAAW;AAAA,cACxB;AAAA,cACD,MAAM,SAAS,KAAK;AAClBA,8BAAA,MAAA,MAAA,SAAA,8BAAc,cAAc,GAAG;AAE/BA,8BAAAA,MAAI,WAAW;AAAA,kBACb,KAAK;AAAA,gBACP,CAAC;AAAA,cACH;AAAA,YACF,CAAC;AAAA,UACF,GAAE,GAAI;AAAA,QACT;AAAA,OACD,EACA,MAAM,SAAO;AACZA,sBAAG,MAAC,YAAW;AACfA,sBAAA,MAAA,MAAA,SAAA,8BAAc,WAAW,GAAG;AAC5B,aAAK,YAAY,UAAU;AAAA,MAC7B,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,cAAc;AACZ,UAAI,OAAO;AAGX,UAAI,KAAK,KAAK,SAAS,GAAG;AACxB,aAAK,OAAO;MACd;AAGA,UAAI,KAAK,MAAM,KAAK;AAClB,aAAK,QAAQ;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA;MAEV;AAGA,WAAK,OAAO;AAGZ,WAAK,MAAM,kBAAkB;AAAA,QAC3B,OAAO;AAAA,QACP,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,KAAK;AAAA,QACL,MAAM;AAAA,MACP,GAAE,SAAS,KAAK;AAEfA,sBAAY,MAAA,MAAA,OAAA,8BAAA,WAAW,GAAG;AAC1B,YAAI,IAAI,WAAW,OAAO,IAAI,SAAS,IAAI,KAAK,OAAO,IAAI,KAAK,MAAM;AACpE,eAAK,MAAM,MAAM,IAAI,KAAK,OAAO,IAAI,KAAK;AAC1C,eAAK,MAAM,OAAO,IAAI,KAAK,QAAQ,IAAI,KAAK,KAAK;AACjD,eAAK,MAAM,OAAO,IAAI,KAAK,QAAQ,IAAI,KAAK,KAAK;AAGjD,eAAK,aAAY;AAGjB,eAAK,iBAAgB;AAAA,eAChB;AACLA,wBAAA,MAAA,MAAA,SAAA,8BAAc,eAAe,GAAG;AAChC,eAAK,YAAY,eAAe;AAAA,QAClC;AAAA,MACD,GAAE,SAAS,KAAK;AAEfA,sBAAc,MAAA,MAAA,SAAA,8BAAA,WAAW,GAAG;AAC5B,aAAK,YAAY,YAAY;AAAA,MAC/B,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB;AACjB,UAAI,OAAO;AAGXA,oBAAY,MAAA,MAAA,OAAA,8BAAA,SAAS,KAAK,KAAK;AAC/BA,0BAAA,MAAA,OAAA,8BAAY,UAAU,KAAK,MAAM,GAAG;AAGpC,UAAI,KAAK,MAAM,OAAO;AACpB,aAAK,YAAY,QAAQ;AACzB;AAAA,MACF;AAGAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,iBAAK,YAAY,CAAC;AAAA,iBACb;AAEL,iBAAK,YAAY,gBAAgB;AAAA,UACnC;AAAA,QAEF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,YAAY,GAAG;AACbA,oBAAA,MAAA,MAAA,OAAA,8BAAY,WAAW,CAAC;AACxB,WAAK,YAAY,QAAQ;AAAA,IAC1B;AAAA;AAAA,IAGD,aAAa,GAAG;AACdA,oBAAA,MAAA,MAAA,SAAA,8BAAc,WAAW,CAAC;AAC1B,WAAK,YAAY,gBAAgB;AAAA,IAClC;AAAA;AAAA,IAGD,YAAY,MAAM;AAChB,UAAI,OAAO;AAEX,UAAI,QAAQ,GAAG;AAEb,aAAK,eAAe;AAGpB,aAAK,MAAM,kBAAkB;AAAA,UAC3B,KAAK;AAAA,UACL,OAAO;AAAA;AAAA,QACR,GAAE,SAAS,KAAK;AAEf,cAAI,KAAK,MAAM,KAAK;AAClB,iBAAK,QAAQ;AAAA,cACX,MAAM;AAAA,cACN,MAAM;AAAA,cACN,KAAK;AAAA,cACL,OAAO;AAAA;UAEX;AAGA,cAAI,KAAK,MAAM,KAAK;AAClB,iBAAK,QAAQ;AAAA,cACX,OAAO;AAAA,cACP,KAAK;AAAA,cACL,MAAM;AAAA,cACN,OAAO;AAAA,cACP,MAAM;AAAA;UAEV;AAGA,eAAK,OAAO;AAGZ,cAAI,IAAI,QAAQ,MAAM,QAAQ,IAAI,IAAI,GAAG;AAEvC,gBAAI,KAAK,QAAQ,SAAO;AACtB,mBAAK,KAAK,KAAK;AAAA,gBACb,KAAK,IAAI,OAAO,IAAI;AAAA,gBACpB,MAAM,IAAI,QAAQ,IAAI,KAAK;AAAA,gBAC3B,MAAM,IAAI,QAAQ,IAAI,KAAK;AAAA,cAC7B,CAAC;AAAA,YACH,CAAC;AAAA,qBAEQ,IAAI,SAAS,IAAI,KAAK,OAAO,IAAI,KAAK,MAAM;AAErD,iBAAK,KAAK,KAAK;AAAA,cACb,KAAK,IAAI,KAAK,OAAO,IAAI,KAAK;AAAA,cAC9B,MAAM,IAAI,KAAK,QAAQ,IAAI,KAAK,KAAK;AAAA,cACrC,MAAM,IAAI,KAAK,QAAQ,IAAI,KAAK,KAAK;AAAA,YACvC,CAAC;AAAA,UAED,WAAS,IAAI,OAAO,IAAI,KAAK;AAE7B,iBAAK,KAAK,KAAK;AAAA,cACb,KAAK,IAAI,OAAO,IAAI;AAAA,cACpB,MAAM,IAAI,QAAQ,IAAI,KAAK;AAAA,cAC3B,MAAM,IAAI,QAAQ,IAAI,KAAK;AAAA,YAC7B,CAAC;AAAA,iBAEI;AACLA,0BAAA,MAAA,MAAA,SAAA,8BAAc,aAAa,GAAG;AAC9B,iBAAK,YAAY,eAAe;AAAA,UAClC;AAGA,eAAK,aAAY;AAAA,QAClB,GAAE,SAAS,KAAK;AACfA,wBAAA,MAAA,MAAA,SAAA,8BAAc,WAAW,GAAG;AAC5B,eAAK,YAAY,YAAY;AAAA,QAC/B,CAAC;AAAA,aACI;AAEL,aAAK,eAAe;AAEpB,aAAK,MAAM,kBAAkB;AAAA,UAC3B,KAAK;AAAA,UACL,OAAO;AAAA;AAAA,QACR,GAAE,SAAS,KAAK;AACfA,wBAAA,MAAA,MAAA,OAAA,8BAAY,WAAW,GAAG;AAE1B,cAAI,WAAW;AAGf,cAAI,IAAI,QAAQ,IAAI,KAAK,KAAK;AAC5B,uBAAW,IAAI,KAAK;AAAA,UACtB,WAAW,IAAI,QAAQ,IAAI,KAAK,KAAK;AACnC,uBAAW,IAAI,KAAK;AAAA,UACtB,WAAW,IAAI,KAAK;AAClB,uBAAW,IAAI;AAAA,UACjB,WAAW,IAAI,KAAK;AAClB,uBAAW,IAAI;AAAA,UACjB;AAEA,cAAI,UAAU;AACZ,gBAAI,QAAQ,GAAG;AACb,mBAAK,MAAM,QAAQ;AACnB,mBAAK,YAAY,UAAU;AAAA,YAC7B,WAAW,QAAQ,GAAG;AACpB,mBAAK,MAAM,QAAQ;AACnB,mBAAK,YAAY,UAAU;AAAA,YAC7B;AAAA,iBACK;AACLA,0BAAA,MAAA,MAAA,SAAA,8BAAc,cAAc,GAAG;AAC/B,iBAAK,YAAY,kBAAkB;AAAA,UACrC;AAAA,QACD,GAAE,SAAS,KAAK;AACfA,wBAAA,MAAA,MAAA,SAAA,8BAAc,WAAW,GAAG;AAC5B,eAAK,YAAY,YAAY;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACb,UAAI,OAAO;AAGXA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,QAAQ;AAAA,QAC3B,SAAS,SAAS,KAAK;AACrB,cAAI,IAAI,aAAa,GAAG;AAEtB,iBAAK,iBAAiB,IAAI;AAAA,qBACjB,IAAI,aAAa,GAAG;AAE7B,iBAAK,gBAAe;AAAA,UACtB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB;AAChB,UAAI,OAAO;AAGX,WAAK,MAAM,YAAY;AAAA,QACrB,KAAK;AAAA,QACL,MAAM;AAAA,MACP,GAAE,SAAS,KAAK;AAEf,YAAI,KAAK,KAAK,SAAS,GAAG;AACxB,eAAK,OAAO;QACd;AAGA,YAAI,KAAK,MAAM,KAAK;AAClB,eAAK,QAAQ;AAAA,YACX,MAAM;AAAA,YACN,MAAM;AAAA,YACN,KAAK;AAAA,YACL,OAAO;AAAA;QAEX;AAGA,aAAK,OAAO;AAGZ,aAAK,MAAM,MAAM,IAAI,KAAK;AAC1B,aAAK,MAAM,OAAO,IAAI,KAAK,QAAQ;AACnC,aAAK,MAAM,OAAO,IAAI,KAAK,QAAQ;AAGnC,YAAI,CAAC,KAAK,MAAM,OAAO;AACrB,eAAK,MAAM,QAAQ,SAAQ,oBAAI,KAAI,GAAG;QACxC;AAGA,YAAI,CAAC,KAAK,MAAM,OAAO;AACrB,gBAAM,WAAWA,cAAG,MAAC,eAAe,WAAW,KAAK,CAAA;AACpD,eAAK,MAAM,QAAQ,SAAS,UAAU;AAAA,QACxC;AAEA,aAAK,YAAY,QAAQ;AAAA,MAC1B,GAAE,SAAS,KAAK;AACfA,sBAAc,MAAA,MAAA,SAAA,8BAAA,WAAW,GAAG;AAC5B,aAAK,YAAY,YAAY;AAAA,MAC/B,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB,MAAM;AACrB,cAAO,MAAI;AAAA,QACT,KAAK;AACH,eAAK,aAAY;AACjB;AAAA,QACF,KAAK;AACH,eAAK,eAAe,IAAI;AACxB;AAAA,QACF;AACEA,wBAAY,MAAA,MAAA,OAAA,8BAAA,YAAY,IAAI;AAAA,MAChC;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB,QAAQ;AACvB,UAAI,QAAQ;AACV,aAAK,UAAU,UAAU,KAAK,MAAM,WAAW;AAAA,aAC1C;AACL,aAAK,MAAM,YAAY;AACvB,aAAK,eAAe;AAAA,MACtB;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB,GAAG;AACrB,UAAI,CAAC,EAAE,MAAM;AACX,aAAK,cAAc;AAAA,MACrB;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,WAAW;AACxBA,qEAAY,SAAS,SAAS;AAE9B,UAAI,CAAC,WAAW;AACd,aAAK,YAAY,UAAU;AAC3B;AAAA,MACF;AAGA,WAAK,kBAAkB,SAAS;AAAA,IACjC;AAAA;AAAA,IAGD,gBAAgB;AACdA,oBAAAA,MAAY,MAAA,OAAA,8BAAA,QAAQ;AAAA,IACrB;AAAA;AAAA,IAGD,eAAe,WAAW;AACxBA,qEAAY,SAAS,SAAS;AAE9B,UAAI,CAAC,WAAW;AACd,aAAK,YAAY,YAAY;AAC7B;AAAA,MACF;AAGA,WAAK,kBAAkB,WAAW,IAAI;AAAA,IACvC;AAAA;AAAA,IAGD,kBAAkB,WAAW,aAAa,OAAO;AAG/CA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAGD,UAAI,OAAO;AACX,WAAK,MAAM,YAAY;AAAA,QACrB,KAAK;AAAA,QACL,MAAM;AAAA,MACP,GAAE,SAAS,KAAK;AAEf,aAAK,MAAM,MAAM,IAAI,KAAK;AAG1B,YAAI,CAAC,KAAK,MAAM,MAAM;AACpB,eAAK,MAAM,OAAO,IAAI,KAAK,QAAQ;AAAA,QACrC;AACA,YAAI,CAAC,KAAK,MAAM,OAAO;AACrB,eAAK,MAAM,QAAQ,SAAQ,oBAAI,KAAI,GAAG;QACxC;AAGA,YAAI,CAAC,KAAK,MAAM,OAAO;AACrB,gBAAM,WAAWA,cAAG,MAAC,eAAe,WAAW,KAAK,CAAA;AACpD,eAAK,MAAM,QAAQ,SAAS,UAAU;AAAA,QACxC;AAGA,YAAI,YAAY;AACd,eAAK,iBAAiB,KAAK;AAAA,QAC7B;AAEAA,sBAAG,MAAC,YAAW;AACf,aAAK,YAAY,QAAQ;AAAA,SACxB,WAAW;AACZA,sBAAG,MAAC,YAAW;AACf,aAAK,YAAY,YAAY;AAAA,MAC/B,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI,OAAO;AAGXA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAEfA,sBAAAA,MAAI,eAAe;AAAA,UACjB,SAAS,SAAS,KAAK;AACrBA,0BAAY,MAAA,MAAA,OAAA,8BAAA,WAAW,GAAG;AAC1B,iBAAK,OAAO;AAAA,cACV,MAAM,IAAI,QAAQ,IAAI,WAAW;AAAA,cACjC,SAAS,IAAI,WAAW;AAAA,cACxB,UAAU,IAAI,YAAY;AAAA,cAC1B,WAAW,IAAI,aAAa;AAAA;AAE9B,iBAAK,YAAY,QAAQ;AAAA,UAC1B;AAAA,UACD,MAAM,SAAS,KAAK;AAClBA,0BAAc,MAAA,MAAA,SAAA,8BAAA,WAAW,GAAG;AAG5B,gBAAI,IAAI,QAAQ;AACd,kBAAI,IAAI,OAAO,SAAS,MAAM,KAAK,IAAI,OAAO,SAAS,YAAY,GAAG;AACpEA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,SAAS;AAAA,kBACT,aAAa;AAAA,kBACb,YAAY;AAAA,gBACd,CAAC;AAAA,cACH,WAAW,IAAI,OAAO,SAAS,QAAQ,GAAG;AAExCA,8BAAAA,MAAY,MAAA,OAAA,8BAAA,UAAU;AACtB;AAAA,cACF,WAAW,IAAI,OAAO,SAAS,QAAQ,GAAG;AACxCA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,SAAS;AAAA,kBACT,aAAa;AAAA,kBACb,YAAY;AAAA,gBACd,CAAC;AAAA,qBACI;AACL,qBAAK,YAAY,YAAY;AAAA,cAC/B;AAAA,mBACK;AACL,mBAAK,YAAY,YAAY;AAAA,YAC/B;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACF,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAGD,YAAY,OAAO,GAAG;AACpB,UAAI,OAAO;AAGX,UAAI,QAAQ,KAAK,KAAK,UAAU,QAAQ;AACtC;AAAA,MACF;AAGAA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAGD,YAAM,SAAS;AAAA,QACb,SAAS,KAAK;AAAA,QACd,MAAM;AAAA,QACN,OAAO;AAAA;AAITA,oBAAAA,MAAI,QAAQ;AAAA,QACV,KAAK,KAAK,OAAO,MAAM,IAAI,SAAS;AAAA,QACpC,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,iBAAiB,YAAY,KAAK,OAAO,MAAM,IAAI;AAAA,QACpD;AAAA,QACD,SAAS,CAAC,QAAQ;;AAChBA,wBAAG,MAAC,YAAW;AAEf,cAAI,IAAI,QAAQ,IAAI,KAAK,WAAW,KAAK;AAEvC,iBAAK,YAAY,IAAI,KAAK,KAAK,QAAQ;iBAClC;AACLA,0BAAAA,MAAI,UAAU;AAAA,cACZ,SAAO,SAAI,SAAJ,mBAAU,QAAO;AAAA,cACxB,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACD;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAG,MAAC,YAAW;AACfA,wBAAc,MAAA,MAAA,SAAA,8BAAA,aAAa,GAAG;AAC9BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,eAAe,QAAQ;AACrB,UAAI,QAAQ;AACV,aAAK,UAAU,QAAQ,KAAK,MAAM,SAAS;AAC3C,aAAK,YAAY,CAAC;AAAA,aACb;AACL,aAAK,MAAM,UAAU;AACrB,aAAK,eAAe;AAAA,MACtB;AAAA,IACD;AAAA;AAAA,IAGD,aAAa,QAAQ;AACnB,UAAI,QAAQ;AACV,aAAK,UAAU,UAAU,KAAK,MAAM,OAAO;AAC3C,aAAK,YAAW;AAAA,aACX;AACL,aAAK,MAAM,QAAQ;AACnB,aAAK,eAAe;AAAA,MACtB;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,GAAG;AACZ,YAAM,QAAQ,EAAE,cAAc,QAAQ;AACtC,WAAK,UAAU,KAAK,EAAE,WAAW,CAAC,KAAK,UAAU,KAAK,EAAE;AAGxD,UAAI,gBAAgB,CAAA;AACpB,eAAS,QAAQ,KAAK,WAAW;AAC/B,YAAI,KAAK,UAAU;AACjB,wBAAc,KAAK,IAAI;AAAA,QACzB;AAAA,MACF;AACA,WAAK,QAAQ;AAAA,IACd;AAAA;AAAA,IAGD,YAAY,GAAG;AACb,YAAM,QAAQ,EAAE,cAAc,QAAQ;AAEtC,UAAI,KAAK,WAAW,KAAK,EAAE,MAAM,KAAK,OAAO,IAAI;AAE/C,aAAK,OAAO,KAAK;AACjB,aAAK,OAAO,OAAO;AACnB,aAAK,OAAO,SAAS;AAAA,aAChB;AAEL,aAAK,SAAS;AAAA,UACZ,IAAI,KAAK,WAAW,KAAK,EAAE;AAAA,UAC3B,MAAM,KAAK,WAAW,KAAK,EAAE;AAAA,UAC7B,QAAQ,KAAK,WAAW,KAAK,EAAE;AAAA;MAEnC;AAAA,IACD;AAAA;AAAA,IAGD,aAAa,GAAG;AACd,YAAM,QAAQ,EAAE,cAAc,QAAQ;AACtC,UAAI,OAAO,CAAA;AAEX,eAAS,OAAO,KAAK,MAAM;AACzB,aAAK,KAAK,IAAI,GAAG;AAAA,MACnB;AAEAA,oBAAAA,MAAI,aAAa;AAAA,QACf,SAAS;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,YAAY,KAAK;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACZ,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB,MAAM;AAErB,UAAI,OAAO,GAAG;AACZ,aAAK,kBAAkB;AAAA,MACzB;AAGA,cAAO,MAAI;AAAA,QACT,KAAK;AAAA,QACL,KAAK;AACH,eAAK,YAAW;AAChB;AAAA,QACF,KAAK;AACH,eAAK,gBAAgB,IAAI;AACzB;AAAA,QACF,KAAK;AACH,eAAK,eAAe,IAAI;AACxB;AAAA,QACF,KAAK;AAEH,eAAK,OAAO;AAEZ,cAAI,CAAC,KAAK,MAAM,KAAK;AACnB,iBAAK,iBAAiB,IAAI;AAAA,UAC5B;AACA;AAAA,QACF,KAAK;AACH,eAAK,eAAe,IAAI;AACxB;AAAA,MACJ;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB,MAAM;AAAA,IAG1B;AAAA;AAAA,IAGD,oBAAoB;AAClB,UAAI,KAAK,iBAAiB;AACxB,aAAK,kBAAkB;AACvB,aAAK,eAAe;AAAA,aACf;AACL,aAAK,eAAc;AACnB,aAAK,kBAAkB;AACvB,aAAK,eAAe;AAAA,MACtB;AAAA,IACD;AAAA;AAAA,IAGD,mBAAmB,QAAQ;AACzB,UAAI,QAAQ;AACV,aAAK,UAAU,YAAY,KAAK,MAAM,aAAa;AACnD,aAAK,eAAc;AAAA,aACd;AACL,aAAK,MAAM,cAAc;AACzB,aAAK,eAAe;AAAA,MACtB;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AACf,UAAI,OAAO;AAGXA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAGD,YAAM,SAAS;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA;AAITA,oBAAAA,MAAI,QAAQ;AAAA,QACV,KAAK,KAAK,OAAO,MAAM,IAAI,SAAS;AAAA,QACpC,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,iBAAiB,YAAY,KAAK,OAAO,MAAM,IAAI;AAAA,QACpD;AAAA,QACD,SAAS,CAAC,QAAQ;;AAChBA,wBAAG,MAAC,YAAW;AAEf,cAAI,IAAI,QAAQ,IAAI,KAAK,WAAW,KAAK;AAEvC,iBAAK,eAAe,IAAI,KAAK,KAAK,QAAQ;iBACrC;AACLA,0BAAAA,MAAI,UAAU;AAAA,cACZ,SAAO,SAAI,SAAJ,mBAAU,QAAO;AAAA,cACxB,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACD;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAG,MAAC,YAAW;AACfA,wBAAc,MAAA,MAAA,SAAA,8BAAA,aAAa,GAAG;AAC9BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,cAAc;AACZ,WAAK,UAAS;AAAA,IACf;AAAA;AAAA,IAGD,WAAW,GAAG;AACZ,YAAM,QAAQ,EAAE,cAAc,QAAQ;AACtC,YAAM,OAAO,KAAK,SAAS,KAAK;AAGhC,WAAK,WAAW,KAAK,KAAK,QAAQ;AAGlC,WAAK,MAAM,UAAU;AAGrB,WAAK,YAAY,OAAO,KAAK,QAAQ,EAAE;AAAA,IACxC;AAAA;AAAA,IAGD,YAAY,GAAG;AACb,YAAM,QAAQ,EAAE,cAAc,QAAQ;AACtC,YAAM,QAAQ,KAAK,UAAU,KAAK;AAGlC,WAAK,WAAW,KAAK,MAAM,IAAI;AAG/B,WAAK,MAAM,WAAW;AAGtB,WAAK,YAAY,SAAS,MAAM,IAAI,GAAG;AAAA,IACxC;AAAA;AAAA,IAGD,SAAS,MAAM,OAAO;AACpB,UAAI,OAAO;AAEXA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,oBAAO,MAAI;AAAA,cACT,KAAK;AACH,qBAAK,KAAK,OAAO,OAAO,CAAC;AAEzB,oBAAI,KAAK,KAAK,WAAW,GAAG;AAC1B,sBAAI,KAAK,MAAM,KAAK;AAClB,yBAAK,OAAO;AAAA,6BACH,KAAK,MAAM,KAAK;AACzB,yBAAK,OAAO;AAAA,yBACP;AACL,yBAAK,OAAO;AAAA,kBACd;AAAA,gBACF;AACA;AAAA,cACF,KAAK;AACH,qBAAK,QAAQ;AAAA,kBACX,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,KAAK;AAAA,kBACL,OAAO;AAAA;AAGT,oBAAI,KAAK,KAAK,SAAS,GAAG;AACxB,uBAAK,OAAO;AAAA,2BACH,KAAK,MAAM,KAAK;AACzB,uBAAK,OAAO;AAAA,uBACP;AACL,uBAAK,OAAO;AAAA,gBACd;AACA;AAAA,cACF,KAAK;AACH,qBAAK,MAAM,QAAQ;AACnB;AAAA,cACF,KAAK;AACH,qBAAK,MAAM,QAAQ;AACnB;AAAA,cACF,KAAK;AACH,qBAAK,QAAQ;AAAA,kBACX,OAAO;AAAA,kBACP,KAAK;AAAA,kBACL,MAAM;AAAA,kBACN,OAAO;AAAA,kBACP,MAAM;AAAA;AAGR,oBAAI,KAAK,KAAK,SAAS,GAAG;AACxB,uBAAK,OAAO;AAAA,2BACH,KAAK,MAAM,KAAK;AACzB,uBAAK,OAAO;AAAA,uBACP;AACL,uBAAK,OAAO;AAAA,gBACd;AACA;AAAA,YAEJ;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,OAAO,OAAO,WAAW;AACvB,UAAI,OAAO;AAEX,UAAI,cAAc,KAAK,QAAQ,GAAG;AAEhC,cAAM,OAAO,KAAK,KAAK,KAAK;AAC5B,aAAK,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ,CAAC;AACtC,aAAK,KAAK,QAAQ,CAAC,IAAI;AAAA,iBACd,cAAc,KAAK,QAAQ,KAAK,KAAK,SAAS,GAAG;AAE1D,cAAM,OAAO,KAAK,KAAK,KAAK;AAC5B,aAAK,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ,CAAC;AACtC,aAAK,KAAK,QAAQ,CAAC,IAAI;AAAA,MACzB;AAGA,WAAK,OAAO,CAAC,GAAG,KAAK,IAAI;AAAA,IAC1B;AAAA;AAAA,IAGD,cAAc;AACZ,UAAI,OAAO;AAGXA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAGD,YAAM,SAAS;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA;AAAA;AAITG,iBAAAA,iBAAiB,MAAM,EACpB,KAAK,SAAO;AACXH,sBAAG,MAAC,YAAW;AAEf,YAAI,IAAI,WAAW,KAAK;AAEtB,eAAK,cAAc,IAAI,KAAK,QAAQ,CAAE,GAAE,IAAI,UAAQ;AAClD,mBAAO;AAAA,cACL,IAAI,KAAK,aAAa,KAAK;AAAA,cAC3B,MAAM,KAAK,eAAe,KAAK;AAAA,cAC/B,QAAQ,KAAK,iBAAiB,KAAK;AAAA,cACnC,aAAa,KAAK,sBAAsB,KAAK;AAAA,cAC7C,cAAc,KAAK,gBAAgB;AAAA;UAEvC,CAAC;AAAA,eAGI;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,OACD,EACA,MAAM,SAAO;AACZA,sBAAG,MAAC,YAAW;AACfA,sBAAc,MAAA,MAAA,SAAA,8BAAA,aAAa,GAAG;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACL,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,cAAc,GAAG;AACf,YAAM,QAAQ,EAAE,cAAc,QAAQ;AAEtC,UAAI,KAAK,aAAa,KAAK,EAAE,MAAM,KAAK,SAAS,IAAI;AAEnD,aAAK,WAAW;AAAA,UACd,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,KAAK;AAAA;aAEF;AAEL,aAAK,WAAW;AAAA,UACd,IAAI,KAAK,aAAa,KAAK,EAAE;AAAA,UAC7B,MAAM,KAAK,aAAa,KAAK,EAAE;AAAA,UAC/B,KAAK,KAAK,aAAa,KAAK,EAAE;AAAA;MAElC;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACbA,oBAAG,MAAC,aAAY;AAEhB,WAAK,iBAAiB;AAAA,IACvB;AAAA;AAAA,IAGD,kBAAkB,MAAM;AACtB,WAAK,WAAW;AAAA,IACjB;AAAA;AAAA,IAGD,eAAe,QAAQ;AACrB,UAAI,QAAQ;AACV,aAAK,UAAU,QAAQ,KAAK,MAAM,SAAS;AAC3C,aAAK,UAAS;AAAA,aACT;AACL,aAAK,MAAM,UAAU;AACrB,aAAK,eAAe;AAAA,MACtB;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB,QAAQ;AACtB,UAAI,QAAQ;AACV,aAAK,UAAU,SAAS,KAAK,MAAM,UAAU;AAC7C,aAAK,WAAU;AAAA,aACV;AACL,aAAK,MAAM,WAAW;AACtB,aAAK,eAAe;AAAA,MACtB;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,OAAO;AAEhB,WAAK,iBAAiB,CAAC;AAAA,QACrB,IAAI,MAAM;AAAA,QACV,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM,QAAQ;AAAA,MACtB,CAAC;AAED,WAAK,MAAM,WAAW;IACvB;AAAA;AAAA,IAGD,oBAAoB,OAAO;AAEzB,WAAK,eAAe,OAAO,OAAO,CAAC;AAAA,IACpC;AAAA;AAAA,IAGD,UAAU,MAAM;AAEd,YAAM,UAAU,IAAI,KAAK,QAAQ;AACjC,WAAK,WAAW;AAGhB,WAAK,MAAM,UAAU;IACtB;AAAA;AAAA,IAGD,YAAY;AACV,UAAI,OAAO;AAGXA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAGD,YAAM,SAAS;AAAA,QACb,SAAS,KAAK;AAAA,QACd,MAAM;AAAA,QACN,OAAO;AAAA;AAITI,iBAAAA,oBAAoB,MAAM,EACvB,KAAK,SAAO;AACXJ,sBAAG,MAAC,YAAW;AAEf,YAAI,IAAI,WAAW,KAAK;AAGtB,eAAK,YAAY,IAAI,KAAK,QAAQ,CAAE,GAAE,IAAI,UAAQ;AAChD,mBAAO;AAAA,cACL,IAAI,KAAK;AAAA,cACT,UAAU,KAAK;AAAA,cACf,QAAQ,KAAK;AAAA;UAEjB,CAAC;AAAA,eAGI;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,OACD,EACA,MAAM,SAAO;AACZA,sBAAG,MAAC,YAAW;AACfA,sBAAc,MAAA,MAAA,SAAA,8BAAA,aAAa,GAAG;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,YAAY,OAAO;AACjB,WAAK,OAAO,OAAO,OAAO,CAAC;AAAA,IAC5B;AAAA;AAAA,IAGD,cAAc;AACZ,UAAI,OAAO;AACX,WAAK,MAAM,kBAAkB,gBAAgB,SAAS,KAAK;AACzD,aAAK,OAAO,KAAK,IAAI,KAAK,GAAG;AAAA,MAC/B,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,cAAc;AAEZ,UAAI,CAAC,KAAK,MAAM,QAAQ;AACtB,eAAO,KAAK,YAAY,SAAS;AAAA,MACnC;AAAA,IACD;AAAA;AAAA,IAGD,mBAAmB;AACjB,UAAI,KAAK,gBAAgB;AACvB,aAAK,iBAAiB;AACtB,aAAK,eAAe;AAAA,aACf;AACL,aAAK,eAAc;AACnB,aAAK,iBAAiB;AACtB,aAAK,eAAe;AAAA,MACtB;AAAA,IACD;AAAA;AAAA,IAGD,cAAc,OAAO;AAEnB,YAAM,YAAY,MAAM,UAAU,MAAM,OAAO,MAAM,SAAS;AAC9D,UAAI,WAAW;AAEb,aAAK,WAAW;AAAA,MAClB;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,KAAK;AACf,UAAI,OAAO,IAAI,KAAK;AAElB,aAAK,eAAe,IAAI;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI,KAAK,QAAQ,SAAS,GAAG;AAE3B,aAAK,UAAU,KAAK,QAAQ,UAAU,GAAG,KAAK,QAAQ,SAAS,CAAC;AAAA,MAClE;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AAEd,WAAK,YAAY,CAAC;AAAA,IACnB;AAAA;AAAA,IAGD,kBAAkB,SAAS;AACzB,UAAI,CAAC;AAAS,eAAO;AAIrB,UAAI,gBAAgB;AAGpB,aAAO,KAAK,KAAK,QAAQ,EAAE,QAAQ,SAAO;AACxC,cAAM,aAAa,mBAAmB,GAAG;AACzC,cAAM,MAAM,KAAK,SAAS,GAAG;AAG7B,cAAM,QAAQ,IAAI,OAAO,WAAW,QAAQ,uBAAuB,MAAM,GAAG,GAAG;AAC/E,wBAAgB,cAAc,QAAQ,OAAO,2BAA2B,GAAG,UAAU,UAAU,MAAM;AAAA,MACvG,CAAC;AAED,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,YAAY;AAEV,UAAI,KAAK,aAAa;AACpB;AAAA,MACF;AAGA,UAAI,CAAC,KAAK,QAAQ,KAAI,KAAM,KAAK,KAAK,WAAW,KAAK,CAAC,KAAK,MAAM,OAAO,CAAC,KAAK,MAAM,KAAK;AACxF;AAAA,MACF;AAGA,YAAM,QAAQ;AAAA,QACZ,SAAS,KAAK;AAAA,QACd,MAAM,KAAK;AAAA,QACX,MAAM,KAAK;AAAA,QACX,OAAO,KAAK;AAAA,QACZ,OAAO,KAAK;AAAA,QACZ,MAAM,KAAK;AAAA,QACX,QAAQ,KAAK;AAAA,QACb,UAAU,KAAK;AAAA,QACf,WAAW,KAAK,IAAI;AAAA;AAItB,UAAI;AACFA,sBAAG,MAAC,eAAe,KAAK,UAAU,KAAK,UAAU,KAAK,CAAC;AAAA,MAEzD,SAAS,GAAG;AACVA,yEAAc,WAAW,CAAC;AAAA,MAC5B;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACV,UAAI;AACF,cAAM,WAAWA,cAAG,MAAC,eAAe,KAAK,QAAQ;AACjD,YAAI,UAAU;AACZ,gBAAM,QAAQ,KAAK,MAAM,QAAQ;AAGjC,gBAAM,MAAM,KAAK;AACjB,gBAAM,YAAY,MAAM,aAAa;AACrC,gBAAM,UAAU,KAAK,KAAK,KAAK;AAE/B,cAAI,MAAM,YAAY,SAAS;AAE7B,iBAAK,WAAU;AACf;AAAA,UACF;AAGAA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,SAAS,SAAO;AACd,kBAAI,IAAI,SAAS;AAEf,qBAAK,UAAU,MAAM,WAAW;AAChC,qBAAK,OAAO,MAAM,QAAQ;AAC1B,qBAAK,OAAO,MAAM,QAAQ,CAAA;AAC1B,qBAAK,QAAQ,MAAM,SAAS;AAAA,kBAC1B,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,KAAK;AAAA,kBACL,OAAO;AAAA;AAET,qBAAK,QAAQ,MAAM,SAAS;AAAA,kBAC1B,OAAO;AAAA,kBACP,KAAK;AAAA,kBACL,MAAM;AAAA,kBACN,OAAO;AAAA,kBACP,MAAM;AAAA;AAER,qBAAK,OAAO,MAAM,QAAQ;AAAA,kBACxB,MAAM;AAAA,kBACN,SAAS;AAAA,kBACT,UAAU;AAAA,kBACV,WAAW;AAAA;AAEb,qBAAK,SAAS,MAAM,UAAU;AAAA,kBAC5B,IAAI;AAAA,kBACJ,MAAM;AAAA,kBACN,QAAQ;AAAA;AAEV,qBAAK,WAAW,MAAM,YAAY,CAAA;AAElCA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR,CAAC;AAAA,qBACI;AAEL,qBAAK,WAAU;AAAA,cACjB;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,SAAS,GAAG;AACVA,yEAAc,WAAW,CAAC;AAAA,MAC5B;AAAA,IACD;AAAA;AAAA,IAGD,aAAa;AACX,UAAI;AACFA,sBAAAA,MAAI,kBAAkB,KAAK,QAAQ;AAAA,MAErC,SAAS,GAAG;AACVA,yEAAc,WAAW,CAAC;AAAA,MAC5B;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;;AACf,UAAI,KAAK,cAAc;AACrB,aAAK,eAAe;AAAA,MACtB;AAGA,iBAAK,MAAM,cAAX,mBAAsB;AACtB,iBAAK,MAAM,YAAX,mBAAoB;AACpB,iBAAK,MAAM,kBAAX,mBAA0B;AAC1B,iBAAK,MAAM,eAAX,mBAAuB;AACvB,iBAAK,MAAM,cAAX,mBAAsB;AACtB,iBAAK,MAAM,gBAAX,mBAAwB;AAGxB,WAAK,iBAAiB;AACtB,WAAK,kBAAkB;AAAA,IACxB;AAAA;AAAA,IAGD,UAAU,WAAW,UAAU;AAE7B,UAAI,KAAK,gBAAgB,KAAK,iBAAiB,WAAW;AACxD,aAAK,eAAc;AAAA,MACrB;AAEA,WAAK,eAAe;AAGpB,iBAAW,MAAM;AACf,YAAI,UAAU;AACZ,mBAAS,KAAI;AAAA,QACf;AAAA,MACD,GAAE,KAAK,iBAAiB,YAAY,MAAM,CAAC;AAAA,IAC7C;AAAA;AAAA,IAGD,eAAe,QAAQ;AACrB,UAAI,QAAQ;AACV,aAAK,UAAU,QAAQ,KAAK,MAAM,SAAS;AAAA,aACtC;AACL,aAAK,MAAM,UAAU;AACrB,aAAK,eAAe;AAAA,MACtB;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI,KAAK,SAAS,QAAQ,SAAS,IAAI;AACrC,aAAK,SAAS,QAAQ,KAAK,EAAE;AAAA,aACxB;AACL,aAAK,YAAY,aAAa;AAAA,MAChC;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB,OAAO;AACtB,UAAI,KAAK,SAAS,QAAQ,SAAS,GAAG;AACpC,aAAK,SAAS,QAAQ,OAAO,OAAO,CAAC;AAAA,aAChC;AACL,aAAK,YAAY,UAAU;AAAA,MAC7B;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AAEZ,UAAI,CAAC,KAAK,SAAS,MAAM,KAAI,GAAI;AAC/B,eAAO,KAAK,YAAY,SAAS;AAAA,MACnC;AAGA,UAAI,iBAAiB;AACrB,eAAS,UAAU,KAAK,SAAS,SAAS;AACxC,YAAI,CAAC,OAAO,QAAQ;AAClB,2BAAiB;AACjB;AAAA,QACF;AAAA,MACF;AAEA,UAAI,gBAAgB;AAClB,eAAO,KAAK,YAAY,UAAU;AAAA,MACpC;AAGA,WAAK,WAAW;AAGhB,WAAK,eAAe,KAAK;AAAA,IAC1B;AAAA;AAAA,IAGD,aAAa;AACXA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,iBAAK,WAAW;AAGhB,iBAAK,WAAW;AAAA,cACd,OAAO;AAAA,cACP,SAAS,CAAC,IAAI,EAAE;AAAA,cAChB,YAAY;AAAA;UAEhB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,eAAe;AAAA,IAEd;AAAA;AAAA;AAAA,IAKD,kBAAkB,SAAS,QAAQ;AAEjC,YAAM,eAAe,QAAQ,UAAU,GAAG,MAAM;AAChD,YAAM,UAAU,aAAa,YAAY,GAAG;AAE5C,UAAI,YAAY,IAAI;AAElB,cAAM,UAAU,aAAa,UAAU,UAAU,CAAC;AAGlD,YAAI,CAAC,QAAQ,SAAS,GAAG,GAAG;AAE1B,cAAI,QAAQ,WAAW,GAAG;AAExB,iBAAK,qBAAqB;AAC1B,iBAAK,oBAAoB;AACzB,iBAAK,sBAAqB;AAAA,UAC5B,WAAW,CAAC,KAAK,oBAAoB;AAEnC,iBAAK,oBAAoB;AACzB,iBAAK,sBAAqB;AAAA,UAC5B;AAAA,eACK;AAEL,eAAK,eAAc;AAAA,QACrB;AAAA,aACK;AAEL,aAAK,eAAc;AAAA,MACrB;AAAA,IACD;AAAA;AAAA,IAGD,wBAAwB;AAEtB,UAAI,KAAK,qBAAqB;AAC5B,qBAAa,KAAK,mBAAmB;AAAA,MACvC;AAEA,WAAK,sBAAsB,WAAW,MAAM;AAC1C,aAAK,kBAAiB;AAAA,MACvB,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAGD,MAAM,oBAAoB;AACxB,UAAI,KAAK;AAAiB;AAE1BA,oBAAY,MAAA,MAAA,OAAA,8BAAA,eAAe,KAAK,iBAAiB;AACjD,WAAK,kBAAkB;AACvB,WAAK,iBAAiB;AAEtB,UAAI;AACF,YAAI,KAAK,kBAAkB,QAAQ;AAEjC,gBAAM,MAAM,MAAMK,uBAAY;AAAA,YAC5B,SAAS,KAAK,kBAAkB,KAAM;AAAA,YACtC,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAEDL,wBAAY,MAAA,MAAA,OAAA,8BAAA,aAAa,GAAG;AAC5B,cAAI,IAAI,WAAW,KAAK;AACtB,iBAAK,kBAAkB,IAAI,KAAK,QAAQ,CAAE,GAAE,IAAI,UAAQ;AAEtD,kBAAI,YAAY,KAAK;AACrB,kBAAI,aAAa,CAAC,UAAU,WAAW,MAAM,GAAG;AAC9C,4BAAY,UAAU,WAAW,GAAG,IAClC,GAAG,KAAK,QAAQ,GAAG,SAAS,KAC5B,GAAG,KAAK,QAAQ,IAAI,SAAS;AAAA,cACjC;AAEA,qBAAO;AAAA,gBACL,KAAK,KAAK;AAAA,gBACV,UAAU,KAAK;AAAA,gBACf,QAAQ;AAAA,gBACR,WAAW;AAAA;AAAA;YAEf,CAAC;AACDA,0BAAY,MAAA,MAAA,OAAA,8BAAA,eAAe,KAAK,cAAc;AAAA,iBACzC;AACL,iBAAK,iBAAiB;AACtBA,0BAAA,MAAA,MAAA,OAAA,8BAAY,eAAe,GAAG;AAAA,UAChC;AAAA,eACK;AAEL,gBAAM,WAAW,MAAMI,+BAAoB;AAAA,YACzC,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAEDJ,wBAAY,MAAA,MAAA,OAAA,8BAAA,aAAa,QAAQ;AACjC,cAAI,SAAS,WAAW,OAAO,SAAS,QAAQ,SAAS,KAAK,MAAM;AAClE,iBAAK,iBAAiB,SAAS,KAAK,KAAK,IAAI,UAAQ;AAEnD,kBAAI,YAAY,KAAK;AACrB,kBAAI,aAAa,CAAC,UAAU,WAAW,MAAM,GAAG;AAE9C,4BAAY,UAAU,WAAW,GAAG,IAClC,GAAG,KAAK,QAAQ,GAAG,SAAS,KAC5B,GAAG,KAAK,QAAQ,IAAI,SAAS;AAAA,cACjC;AAEA,qBAAO;AAAA,gBACL,KAAK,KAAK;AAAA,gBACV,UAAU,KAAK;AAAA,gBACf,QAAQ;AAAA,gBACR,WAAW;AAAA;AAAA;YAEf,CAAC;AACDA,0BAAY,MAAA,MAAA,OAAA,8BAAA,eAAe,KAAK,cAAc;AAAA,iBACzC;AACL,iBAAK,iBAAiB;AACtBA,0BAAY,MAAA,MAAA,OAAA,8BAAA,eAAe,QAAQ;AAAA,UACrC;AAAA,QACF;AAAA,MACA,SAAO,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,8BAAc,WAAW,KAAK;AAC9B,aAAK,iBAAiB;MACxB,UAAU;AACR,aAAK,kBAAkB;AACvBA,sBAAAA,MAAY,MAAA,OAAA,8BAAA,cAAc;AAAA,UACxB,gBAAgB,KAAK;AAAA,UACrB,gBAAgB,KAAK;AAAA,UACrB,iBAAiB,KAAK;AAAA,QACxB,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAKD,qBAAqB,MAAM;AACzB,YAAM,eAAe,KAAK,QAAQ,UAAU,GAAG,KAAK,cAAc;AAClE,YAAM,cAAc,KAAK,QAAQ,UAAU,KAAK,cAAc;AAC9D,YAAM,UAAU,aAAa,YAAY,GAAG;AAE5C,UAAI;AACJ,UAAI;AAEJ,UAAI,YAAY,IAAI;AAElB,cAAM,UAAU,aAAa,UAAU,UAAU,CAAC;AAClD,YAAI,QAAQ,SAAS,GAAG,GAAG;AAEzB,gBAAM,cAAc,IAAI,KAAK,QAAQ;AACrC,uBAAa,eAAe,cAAc;AAC1C,8BAAoB,KAAK,iBAAiB,YAAY;AAAA,eACjD;AAEL,gBAAM,cAAc,IAAI,KAAK,QAAQ;AACrC,uBAAa,aAAa,UAAU,GAAG,OAAO,IAAI,cAAc;AAChE,8BAAoB,UAAU,YAAY;AAAA,QAC5C;AAAA,aACK;AAEL,cAAM,cAAc,IAAI,KAAK,QAAQ;AACrC,qBAAa,eAAe,cAAc;AAC1C,4BAAoB,KAAK,iBAAiB,YAAY;AAAA,MACxD;AAEA,WAAK,UAAU;AACf,WAAK,iBAAiB;AAGtB,UAAI,CAAC,KAAK,aAAa,KAAK,OAAK,EAAE,QAAQ,KAAK,GAAG,GAAG;AACpD,aAAK,aAAa,KAAK;AAAA,UACrB,KAAK,KAAK;AAAA,UACV,UAAU,KAAK;AAAA,UACf,QAAQ,KAAK;AAAA,QACf,CAAC;AAAA,MACH;AAGA,UAAI,YAAY,IAAI;AAClB,aAAK,mBAAmB,KAAK,iBAAiB;AAAA,UAAO,cACnD,SAAS,aAAa;AAAA;MAE1B;AAGA,WAAK,qBAAqB;AAG1B,WAAK,eAAc;AAGnB,WAAK,cAAa;AAAA,IACnB;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,iBAAiB;AACtB,WAAK,oBAAoB;AACzB,WAAK,iBAAiB;IACvB;AAAA;AAAA,IAGD,0BAA0B;AACxB,YAAM,iBAAiB,KAAK,iBAAiB;AAG7C,WAAK,mBAAmB,KAAK,iBAAiB,OAAO,cAAY;AAC/D,cAAM,aAAa,KAAK,QAAQ,UAAU,SAAS,UAAU,SAAS,WAAW,SAAS,KAAK,MAAM;AACrG,eAAO,eAAe,SAAS;AAAA,MACjC,CAAC;AAGD,UAAI,KAAK,iBAAiB,WAAW,gBAAgB;AACnD,aAAK,kBAAiB;AAAA,MACxB;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AAClB,WAAK,gBAAgB;AACrB,WAAK,mBAAmB;AAAA,IACzB;AAAA;AAAA,IAGD,oBAAoB;AAElB,WAAK,qBAAqB;AAG1B,UAAI,KAAK,kBAAkB,QAAQ;AACjCA,sBAAY,MAAA,MAAA,OAAA,8BAAA,0BAA0B,KAAK,iBAAiB;AAG5D,cAAM,eAAe,KAAK,QAAQ,UAAU,GAAG,KAAK,cAAc;AAClE,cAAM,UAAU,aAAa,YAAY,GAAG;AAE5C,YAAI,YAAY,IAAI;AAClB,gBAAM,cAAc,MAAM,KAAK;AAE/B,eAAK,iBAAiB,KAAK;AAAA,YACzB,MAAM;AAAA,YACN,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AAAA,MACF;AAGA,WAAK,eAAc;AAAA,IACpB;AAAA;AAAA,IAGD,kBAAkB;AAEhB,WAAK,qBAAqB;AAE1B,WAAK,oBAAoB;AACzB,WAAK,sBAAqB;AAAA,IAC3B;AAAA;AAAA,IAGD,gBAAgB;AAEd,UAAI,KAAK,WAAW;AAClB,qBAAa,KAAK,SAAS;AAAA,MAC7B;AAGA,WAAK,YAAY,WAAW,MAAM;AAChC,aAAK,UAAS;AAAA,MACf,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,kBAAkB,KAAK;AAErB,YAAM,eAAe,KAAK,aAAa,KAAK,UAAQ,KAAK,QAAQ,GAAG;AAEpE,UAAI,cAAc;AAEhB,cAAM,UAAU,IAAI,OAAO,IAAI,KAAK,aAAa,aAAa,QAAQ,CAAC,QAAQ,GAAG;AAClF,aAAK,UAAU,KAAK,QAAQ,QAAQ,SAAS,EAAE;AAAA,MACjD;AAGA,WAAK,eAAe,KAAK,aAAa,OAAO,UAAQ,KAAK,QAAQ,GAAG;AAGrE,WAAK,cAAa;AAAA,IACnB;AAAA;AAAA,IAGD,aAAa,QAAQ;AACnB,aAAO,OAAO,QAAQ,uBAAuB,MAAM;AAAA,IACpD;AAAA;AAAA,IAGD,+BAA+B;AAE7B,YAAM,UAAU;AAChB,YAAM,qBAAqB,CAAA;AAC3B,UAAI;AAEJ,cAAQ,QAAQ,QAAQ,KAAK,KAAK,OAAO,OAAO,MAAM;AACpD,2BAAmB,KAAK,MAAM,CAAC,CAAC;AAAA,MAClC;AAGA,WAAK,eAAe,KAAK,aAAa;AAAA,QAAO,UAC3C,mBAAmB,SAAS,KAAK,QAAQ;AAAA;IAE5C;AAAA,EACH;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3sGA,GAAG,WAAW,eAAe;"}