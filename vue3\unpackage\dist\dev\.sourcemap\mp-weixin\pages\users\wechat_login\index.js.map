{"version": 3, "file": "index.js", "sources": ["pages/users/wechat_login/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlcnMvd2VjaGF0X2xvZ2luL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"container\" :style=\"colorStyle\">\n\t\t<navbar></navbar>\n\t\t<view class=\"content-box\" :style=\"{'margin-top': statusBarHeight + 'px'}\">\n\t\t\t<view class=\"logo-box\">\n\t\t\t\t<image :src=\"configData.wap_login_logo\" />\n\t\t\t</view>\n\t\t\t\t\n\t\t\t<view class=\"login-box\">\n\t\t\t\t\t<view class=\"title\">{{$t(`商城登录`)}}</view>\n\t\t\t\t\t<view class=\"sub-title\">{{configData.site_name}}</view>\n\t\t\t\t\n\t\t\t\t<!-- #ifdef H5 -->\n\t\t\t\t<view class=\"btn-submit df\" @tap=\"wechatLogin\">{{$t(`微信登录`)}}</view>\n\t\t\t\t<!-- #endif -->\n\t\t\t\t\n\t\t\t\t<!-- #ifdef MP -->\n\t\t\t\t<template v-if=\"configData.wechat_auth_switch\">\n\t\t\t\t\t<view class=\"btn-submit df\" v-if=\"bindPhone\" open-type=\"getPhoneNumber\" @getphonenumber=\"getphonenumber\">{{$t(`授权登录`)}}</view>\n\t\t\t\t\t<view class=\"btn-submit df\" v-else-if=\"!bindPhone\" @tap=\"getAuthLogin\">{{$t(`授权登录`)}}</view>\n\t\t\t\t</template>\n\t\t\t\t\n\t\t\t\t<view class=\"btn-submit phone-btn df\" v-if=\"configData.phone_auth_switch\" @tap=\"phoneLogin\">{{$t(`手机号登录`)}}</view>\n\t\t\t\t<!-- #endif -->\n\t\t\t\t\n\t\t\t\t<view class=\"agreement\" v-if=\"!canGetPrivacySetting\">\n\t\t\t\t\t<checkbox-group @tap.stop='ChangeIsDefault'>\n\t\t\t\t\t\t<label class=\"df\">\n\t\t\t\t\t\t\t<checkbox :class=\"inAnimation ? 'trembling' : ''\" @animationend='inAnimation=false' :checked=\"protocol ? true : false\" style=\"transform:scale(0.7)\"/>\n\t\t\t\t\t\t\t<view class=\"agreement-text\">\n\t\t\t\t\t\t\t\t{{$t(`已阅读并同意`)}}\n\t\t\t\t\t\t\t\t<text @tap.stop=\"privacy(4)\">{{$t(`《用户协议》`)}}</text>\n\t\t\t\t\t\t\t\t{{$t(`与`)}}\n\t\t\t\t\t\t\t\t<text @tap.stop=\"privacy(3)\">{{$t(`《隐私协议》`)}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</label>\n\t\t\t\t\t</checkbox-group>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<block v-if=\"isUp\">\n\t\t\t<mobileLogin :isUp=\"isUp\" :canClose=\"canClose\" @close=\"maskClose\" :authKey=\"authKey\" @wechatPhone=\"wechatPhone\"></mobileLogin>\n\t\t</block>\n\t\t<block v-if=\"isPhoneBox\">\n\t\t\t<routinePhone :logoUrl=\"logoUrl\" :isPhoneBox=\"isPhoneBox\" @loginSuccess=\"bindPhoneClose\" :authKey=\"authKey\">\n\t\t\t</routinePhone>\n\t\t</block>\n\t\t<block>\n\t\t\t<editUserModal :isShow=\"isShow\" @closeEdit=\"closeEdit\" @editSuccess=\"editSuccess\">\n\t\t\t</editUserModal>\n\t\t</block>\n\t\t<!-- #ifdef MP -->\n\t\t<privacyAgreementPopup v-if=\"canGetPrivacySetting\" @onReject=\"onReject\" @onAgree=\"onAgree\">\n\t\t</privacyAgreementPopup>\n\t\t<!-- #endif -->\n\t</view>\n</template>\n\n<script>\n\tconst app = getApp();\n\tlet statusBarHeight = uni.getSystemInfoSync().statusBarHeight + 'px';\n\timport mobileLogin from '../components/login_mobile/index.vue';\n\timport routinePhone from '../components/login_mobile/routine_phone.vue';\n\timport editUserModal from '@/components/eidtUserModal/index.vue'\n\timport privacyAgreementPopup from '@/components/privacyAgreementPopup/index.vue'\n\timport {\n\t\tgetLogo,\n\t\tsilenceAuth,\n\t\troutineBindingPhone,\n\t\twechatAuthV2,\n\t\tauthType,\n\t\tauthLogin,\n\t\twechatAuthLogin\n\t} from '@/api/public';\n\timport cacheConfig from '@/config/cache.js';\n\tconst {\n\t\tLOGO_URL,\n\t\tEXPIRES_TIME,\n\t\tUSER_INFO,\n\t\tSTATE_R_KEY\n\t} = cacheConfig;\n\timport {\n\tgetUserInfo\n} from '@/api/user.js';\nimport {\n\tgetUserSocialInfo\n} from '@/api/social.js';\n\timport Routine from '@/libs/routine';\n\timport wechat from '@/libs/wechat';\n\timport colors from '@/mixins/color.js';\n\timport Auth from '@/libs/wechat.js';\n\timport appConfig from '@/config/app.js';\n\tconst { HTTP_REQUEST_URL } = appConfig;\n\timport {\n\t\tisWeixin\n\t} from \"@/utils\";\n\timport Cache from '@/utils/cache';\n\texport default {\n\t\tmixins: [colors],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\timgHost: HTTP_REQUEST_URL,\n\t\t\t\tisUp: false,\n\t\t\t\tcanClose: true,\n\t\t\t\tphone: '',\n\t\t\t\tstatusBarHeight: statusBarHeight,\n\t\t\t\tisHome: false,\n\t\t\t\tisPhoneBox: false,\n\t\t\t\tprotocol: false,\n\t\t\t\tisShow: false,\n\t\t\t\tisLogin: false,\n\t\t\t\tlogoUrl: '',\n\t\t\t\tcode: '',\n\t\t\t\tauthKey: '',\n\t\t\t\toptions: '',\n\t\t\t\tuserInfo: {},\n\t\t\t\tcodeNum: 0,\n\t\t\t\tcanUseGetUserProfile: false,\n\t\t\t\tcanGetPrivacySetting: false,\n\t\t\t\tinAnimation: false,\n\t\t\t\tcolorStatus: uni.getStorageSync('color_status'),\n\t\t\t\tmp_is_new: this.$Cache.get('MP_VERSION_ISNEW') || false,\n\t\t\t\tconfigData: Cache.get('BASIC_CONFIG'),\n\t\t\t\tbindPhone: false\n\t\t\t};\n\t\t},\n\t\tcomponents: {\n\t\t\tmobileLogin,\n\t\t\troutinePhone,\n\t\t\teditUserModal,\n\t\t\tprivacyAgreementPopup\n\t\t},\n\t\tonLoad(options) {\n\t\t\tif (uni.getUserProfile) {\n\t\t\t\tthis.canUseGetUserProfile = true\n\t\t\t}\n\t\t\t// #ifdef MP\n\t\t\tif (wx.getPrivacySetting) {\n\t\t\t\tthis.canGetPrivacySetting = true\n\t\t\t}\n\t\t\t// #endif\n\t\t\tlet that = this;\n\t\t\t// #ifdef MP\n\t\t\tthis.userLogin()\n\t\t\t// #endif\n\t\t\t// #ifdef H5\n\t\t\tconst {\n\t\t\t\tcode,\n\t\t\t\tstate\n\t\t\t} = options;\n\t\t\tif (code) {\n\t\t\t\tlet spread = this.$Cache.get(\"spread\") || '';\n\t\t\t\tlet backUrl = state ? decodeURIComponent(state) : ''\n\t\t\t\tthis.wechatAuthLogin({\n\t\t\t\t\tcode,\n\t\t\t\t\tspread\n\t\t\t\t}, backUrl)\n\t\t\t}\n\t\t\t// #endif\n\t\t\tlet pages = getCurrentPages();\n\t\t\tlet prePage = pages[pages.length - 2];\n\t\t\tif (prePage && prePage.route == 'pages/order_addcart/order_addcart') {\n\t\t\t\tthis.isHome = true;\n\t\t\t} else {\n\t\t\t\tthis.isHome = false;\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\twechatAuthLogin(d, back_url) {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: this.$t(`正在登录中`)\n\t\t\t\t});\n\t\t\t\twechatAuthLogin(d).then(res => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tif (res.data.bindPhone) {\n\t\t\t\t\t\tthis.authKey = res.data.key\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: `/pages/users/binding_phone/index?authKey=${this.authKey}&backUrl=${back_url}`\n\t\t\t\t\t\t})\n\t\t\t\t\t} else {\n\t\t\t\t\t\tlet time = res.data.expires_time - this.$Cache.time();\n\t\t\t\t\t\tthis.$store.commit('LOGIN', {\n\t\t\t\t\t\t\ttoken: res.data.token,\n\t\t\t\t\t\t\ttime: time\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.getUserInfo(0, back_url)\n\t\t\t\t\t}\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: err,\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\t\t\tonAgree() {\n\t\t\t\tthis.protocol = true\n\t\t\t},\n\t\t\t// 小程序 22.11.8日删除getUserProfile 接口获取用户昵称头像\n\t\t\tuserLogin() {\n\t\t\t\t// if (!this.protocol) {\n\t\t\t\t// \tuni.showToast({\n\t\t\t\t// \t\ttitle: this.$t('请先阅读并同意协议'),\n\t\t\t\t// \t\ticon: 'none',\n\t\t\t\t// \t\tduration: 2000\n\t\t\t\t// \t});\n\t\t\t\t// \treturn\n\t\t\t\t// }\n\t\t\t\tRoutine.getCode()\n\t\t\t\t\t.then(code => {\n\t\t\t\t\t\t// uni.showLoading({\n\t\t\t\t\t\t// \ttitle: this.$t(`正在登录中`)\n\t\t\t\t\t\t// });\n\t\t\t\t\t\tauthType({\n\t\t\t\t\t\t\t\tcode,\n\t\t\t\t\t\t\t\tspread_spid: app.globalData.spid,\n\t\t\t\t\t\t\t\tspread_code: app.globalData.code\n\t\t\t\t\t\t\t}).then(res => {\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tthis.authKey = res.data.key;\n\t\t\t\t\t\t\t\tthis.bindPhone = res.data.bindPhone\n\t\t\t\t\t\t\t\t// uni.navigateTo({\n\t\t\t\t\t\t\t\t// \turl: `/pages/users/binding_phone/index?authKey=${res.data.key}`\n\t\t\t\t\t\t\t\t// })\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: err,\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t});\n\t\t\t\t\t})\n\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\tconsole.log(err)\n\t\t\t\t\t});\n\t\t\t},\n\t\t\tgetAuthLogin() {\n\t\t\t\tif (!this.authKey) return\n\t\t\t\tif (!this.protocol) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: this.$t('请先阅读并同意协议'),\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: this.$t(`正在登录中`)\n\t\t\t\t});\n\t\t\t\tauthLogin({\n\t\t\t\t\tkey: this.authKey\n\t\t\t\t}).then(res => {\n\t\t\t\t\tlet time = res.data.expires_time - this.$Cache.time();\n\t\t\t\t\tthis.$store.commit('LOGIN', {\n\t\t\t\t\t\ttoken: res.data.token,\n\t\t\t\t\t\ttime: time\n\t\t\t\t\t});\n\t\t\t\t\tthis.getUserInfo(res.data.bindName)\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: err,\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\t\t\tChangeIsDefault(e) {\n\t\t\t\tthis.$set(this, 'protocol', !this.protocol);\n\t\t\t},\n\t\t\teditSuccess() {\n\t\t\t\tthis.isShow = false\n\t\t\t},\n\t\t\tphoneLogin() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/users/binding_phone/index?authKey=${this.authKey}&pageType=0`\n\t\t\t\t})\n\t\t\t},\n\t\t\tcloseEdit() {\n\t\t\t\tthis.isShow = false\n\t\t\t\tthis.$util.Tips({\n\t\t\t\t\ttitle: this.$t(`登录成功`),\n\t\t\t\t\ticon: 'success'\n\t\t\t\t}, {\n\t\t\t\t\ttab: 3\n\t\t\t\t});\n\t\t\t},\n\t\t\tonReject() {\n\t\t\t\tuni.navigateBack();\n\t\t\t},\n\t\t\t// #ifdef MP\n\t\t\tback() {\n\t\t\t\tif (this.isLogin) {\n\t\t\t\t\tthis.$store.commit('LOGIN', {\n\t\t\t\t\t\ttoken: '',\n\t\t\t\t\t\ttime: 0\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tuni.navigateBack();\n\t\t\t},\n\t\t\t// #endif\n\t\t\t// #ifndef MP\n\t\t\tback() {\n\t\t\t\tuni.navigateBack({\n\t\t\t\t\tdelta: 1\n\t\t\t\t})\n\t\t\t},\n\t\t\t// #endif\n\t\t\thome() {\n\t\t\t\tuni.switchTab({\n\t\t\t\t\turl: '/pages/index/index'\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 弹窗关闭\n\t\t\tmaskClose(new_user) {\n\t\t\t\tthis.isUp = false;\n\t\t\t\t// #ifdef MP\n\t\t\t\tif (new_user) {\n\t\t\t\t\tthis.isShow = true\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tbindPhoneClose(data) {\n\t\t\t\tthis.isPhoneBox = false;\n\t\t\t\tif (data.isStatus) {\n\t\t\t\t\t// #ifdef MP\n\t\t\t\t\tthis.getUserInfo(data.new_user)\n\t\t\t\t\t// #endif\n\t\t\t\t\t// #ifndef MP\n\t\t\t\t\tthis.$util.Tips({\n\t\t\t\t\t\ttitle: this.$t(`登录成功`),\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t}, {\n\t\t\t\t\t\ttab: 3\n\t\t\t\t\t});\n\t\t\t\t\t// #endif\n\t\t\t\t}\n\t\t\t},\n\t\t\t// #ifdef MP\n\t\t\t// 小程序获取手机号码\n\t\t\tgetphonenumber(e) {\n\t\t\t\tif (!this.protocol) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: this.$t('请先阅读并同意协议'),\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: this.$t(`正在登录中`)\n\t\t\t\t});\n\t\t\t\tRoutine.getCode()\n\t\t\t\t\t.then(code => {\n\t\t\t\t\t\tthis.getUserPhoneNumber(e.detail.encryptedData, e.detail.iv, code);\n\t\t\t\t\t})\n\t\t\t\t\t.catch(error => {\n\t\t\t\t\t\tuni.$emit('closePage', false);\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t});\n\t\t\t},\n\t\t\t// 小程序获取手机号码回调\n\t\t\tgetUserPhoneNumber(encryptedData, iv, code) {\n\t\t\t\troutineBindingPhone({\n\t\t\t\t\t\tencryptedData: encryptedData,\n\t\t\t\t\t\tiv: iv,\n\t\t\t\t\t\tcode: code,\n\t\t\t\t\t\tspread_spid: app.globalData.spid,\n\t\t\t\t\t\tspread_code: app.globalData.code,\n\t\t\t\t\t\tkey: this.authKey\n\t\t\t\t\t})\n\t\t\t\t\t.then(res => {\n\t\t\t\t\t\tlet time = res.data.expires_time - this.$Cache.time();\n\t\t\t\t\t\tthis.$store.commit('LOGIN', {\n\t\t\t\t\t\t\ttoken: res.data.token,\n\t\t\t\t\t\t\ttime: time\n\t\t\t\t\t\t});\n\t\t\t\t\t\t// this.userInfo = res.data.userInfo;\n\t\t\t\t\t\t// this.$store.commit('SETUID', res.data.userInfo.uid);\n\t\t\t\t\t\t// this.$store.commit('UPDATE_USERINFO', res.data.userInfo);\n\t\t\t\t\t\tthis.$Cache.clear('snsapiKey');\n\t\t\t\t\t\tthis.getUserInfo(res.data.bindName)\n\t\t\t\t\t\t// this.$util.Tips({\n\t\t\t\t\t\t// \ttitle: this.$t(`登录成功`),\n\t\t\t\t\t\t// \ticon: 'success'\n\t\t\t\t\t\t// }, {\n\t\t\t\t\t\t// \ttab: 3\n\t\t\t\t\t\t// });\n\t\t\t\t\t})\n\t\t\t\t\t.catch(res => {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t});\n\t\t\t},\n\t\t\t// #endif\n\t\t\t/**\n\t\t\t * 获取个人用户信息\n\t\t\t */\n\t\t\tgetUserInfo(new_user, back_url) {\n\t\t\t\tlet that = this;\n\t\t\t\t// 先获取基础用户信息\n\t\t\t\tgetUserInfo().then(res => {\n\t\t\t\t\tthat.userInfo = res.data;\n\t\t\t\t\tthat.$store.commit('SETUID', res.data.uid);\n\t\t\t\t\t\n\t\t\t\t\t// 再获取用户社交信息（拓展信息）\n\t\t\t\t\tgetUserSocialInfo()\n\t\t\t\t\t\t.then((socialRes) => {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t// 更新用户社交信息到Vuex\n\t\t\t\t\t\t\tif (socialRes.data) {\n\t\t\t\t\t\t\t\tthat.$store.commit('UPDATE_USERINFO', socialRes.data);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tif (new_user) {\n\t\t\t\t\t\t\t\tthis.isShow = true\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// #ifdef MP\n\t\t\t\t\t\t\t\tthat.$util.Tips({\n\t\t\t\t\t\t\t\t\ttitle: that.$t(`登录成功`),\n\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t}, {\n\t\t\t\t\t\t\t\t\ttab: 3\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t\t// #ifndef MP\n\t\t\t\t\t\t\t\tthat.$util.Tips({\n\t\t\t\t\t\t\t\t\ttitle: that.$t(`登录成功`),\n\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t}, {\n\t\t\t\t\t\t\t\t\ttab: 4,\n\t\t\t\t\t\t\t\t\turl: back_url || '/pages/user/index'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.catch(() => {\n\t\t\t\t\t\t\t// 即使社交信息获取失败，仍然继续\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tif (new_user) {\n\t\t\t\t\t\t\t\tthis.isShow = true\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// #ifdef MP\n\t\t\t\t\t\t\t\tthat.$util.Tips({\n\t\t\t\t\t\t\t\t\ttitle: that.$t(`登录成功`),\n\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t}, {\n\t\t\t\t\t\t\t\t\ttab: 3\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t\t// #ifndef MP\n\t\t\t\t\t\t\t\tthat.$util.Tips({\n\t\t\t\t\t\t\t\t\ttitle: that.$t(`登录成功`),\n\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t}, {\n\t\t\t\t\t\t\t\t\ttab: 4,\n\t\t\t\t\t\t\t\t\turl: back_url || '/pages/user/index'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: err.msg,\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\t\t\tprivacy(type) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: \"/pages/users/privacy/index?type=\" + type\n\t\t\t\t})\n\t\t\t},\n\t\t\t// #ifdef H5\n\t\t\t// 获取url后面的参数\n\t\t\tgetQueryString(name) {\n\t\t\t\tvar reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');\n\t\t\t\tvar reg_rewrite = new RegExp('(^|/)' + name + '/([^/]*)(/|$)', 'i');\n\t\t\t\tvar r = window.location.search.substr(1).match(reg);\n\t\t\t\tvar q = window.location.pathname.substr(1).match(reg_rewrite);\n\t\t\t\tif (r != null) {\n\t\t\t\t\treturn unescape(r[2]);\n\t\t\t\t} else if (q != null) {\n\t\t\t\t\treturn unescape(q[2]);\n\t\t\t\t} else {\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 公众号登录\n\t\t\twechatLogin() {\n\t\t\t\tif (!this.protocol) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: this.$t('请先阅读并同意协议'),\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (!this.code || this.options.scope !== 'snsapi_base') {\n\t\t\t\t\tthis.$wechat.oAuth('snsapi_userinfo', location.href);\n\t\t\t\t} else {\n\t\t\t\t\tif (this.authKey) {\n\t\t\t\t\t\t// this.isUp = true;\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: `/pages/users/binding_phone/index?authKey=${this.authKey}`\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\n\t\t\t// 输入手机号后的回调\n\t\t\twechatPhone() {\n\t\t\t\tthis.$Cache.clear('snsapiKey');\n\t\t\t\tif (this.options.back_url) {\n\t\t\t\t\tlet url = uni.getStorageSync('snRouter');\n\t\t\t\t\turl = url.indexOf('/pages/index/index') != -1 ? '/' : url;\n\t\t\t\t\tif (url.indexOf('/pages/users/wechat_login/index') !== -1) {\n\t\t\t\t\t\turl = '/';\n\t\t\t\t\t}\n\t\t\t\t\tif (!url) {\n\t\t\t\t\t\turl = '/pages/index/index';\n\t\t\t\t\t}\n\t\t\t\t\tthis.isUp = false;\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: this.$t(`登录成功`),\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tsetTimeout(res => {\n\t\t\t\t\t\tlocation.href = url;\n\t\t\t\t\t}, 800);\n\t\t\t\t} else {\n\t\t\t\t\tthis.isUp = false;\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: this.$t(`登录成功`),\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tsetTimeout(res => {\n\t\t\t\t\t\tlocation.href = '/pages/index/index';\n\t\t\t\t\t}, 800);\n\t\t\t\t}\n\t\t\t}\n\t\t\t// #endif\n\t\t}\n\t};\n</script>\n\n<style lang=\"scss\">\n.container {\n\twidth: 100%;\n\tbackground: #fff;\n\theight: 100vh;\n\toverflow: hidden;\n}\n\n.content-box {\n\twidth: 100%;\n}\n\n.logo-box {\n\twidth: 100%;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\t/* #ifdef APP-VUE */\n\tmargin-top: 50rpx;\n\t/* #endif */\n\t/* #ifndef APP-VUE */\n\tmargin-top: 200rpx;\n\t/* #endif */\n\tmargin-bottom: 60rpx;\n}\n\n.logo-box image {\n\twidth: 180rpx;\n\theight: 180rpx;\n\tborder-radius: 20rpx;\n}\n\n.login-box {\n\twidth: calc(100% - 60rpx);\n\tmargin: 30rpx;\n}\n\n.login-box .title {\n\tfont-size: 40rpx;\n\tfont-weight: 700;\n\tcolor: #000;\n\ttext-align: center;\n}\n\n.login-box .sub-title {\n\tmargin-top: 20rpx;\n\tfont-size: 26rpx;\n\tcolor: #999;\n\ttext-align: center;\n\tmargin-bottom: 80rpx;\n}\n\n.login-box .btn-submit {\n\tmargin-top: 40rpx;\n\twidth: 100%;\n\theight: 100rpx;\n\tline-height: 100rpx;\n\ttext-align: center;\n\tfont-size: 28rpx;\n\tfont-weight: 700;\n\tbackground: #000;\n\tcolor: #fff;\n\tborder-radius: 100rpx;\n\tjustify-content: center;\n}\n\n.login-box .phone-btn {\n\tbackground: #fff;\n\tcolor: #333;\n\tborder: 1px solid #e4e4e4;\n}\n\n.login-box .agreement {\n\tmargin-top: 60rpx;\n\tfont-size: 24rpx;\n\tcolor: #999;\n\ttext-align: center;\n}\n\n.agreement .agreement-text {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.agreement-text text {\n\tcolor: #576b95;\n}\n\n.df {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.trembling {\n\tanimation: shake 0.6s;\n}\n\n@keyframes shake {\n\t0%, 100% { transform: translateX(0); }\n\t10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }\n\t20%, 40%, 60%, 80% { transform: translateX(5px); }\n}\n</style>\n", "import MiniProgramPage from 'Z:/WWW/shejiao/vue3/pages/users/wechat_login/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "appConfig", "colors", "<PERSON><PERSON>", "wx", "wechatAuthLogin", "Routine", "authType", "auth<PERSON><PERSON><PERSON>", "routineBindingPhone", "getUserInfo", "getUserSocialInfo"], "mappings": ";;;;;;;;;;AA4DC,MAAM,MAAM,OAAM;AAClB,IAAI,kBAAkBA,cAAG,MAAC,kBAAiB,EAAG,kBAAkB;AAChE,MAAK,cAAe,MAAW;AAC/B,MAAO,eAAc,MAAW;AAChC,MAAK,gBAAiB,MAAW;AACjC,8BAA8B,MAAW;AA4BzC,MAAM,EAAE,qBAAqBC;AAK7B,MAAK,YAAU;AAAA,EACd,QAAQ,CAACC,aAAAA,MAAM;AAAA,EACf,OAAO;AACN,WAAO;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,MACV,OAAO;AAAA,MACP;AAAA,MACA,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU,CAAE;AAAA,MACZ,SAAS;AAAA,MACT,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,aAAa;AAAA,MACb,aAAaF,cAAAA,MAAI,eAAe,cAAc;AAAA,MAC9C,WAAW,KAAK,OAAO,IAAI,kBAAkB,KAAK;AAAA,MAClD,YAAYG,YAAAA,MAAM,IAAI,cAAc;AAAA,MACpC,WAAW;AAAA;EAEZ;AAAA,EACD,YAAY;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AACf,QAAIH,cAAAA,MAAI,gBAAgB;AACvB,WAAK,uBAAuB;AAAA,IAC7B;AAEA,QAAII,cAAAA,KAAG,mBAAmB;AACzB,WAAK,uBAAuB;AAAA,IAC7B;AAIA,SAAK,UAAU;AAgBf,QAAI,QAAQ;AACZ,QAAI,UAAU,MAAM,MAAM,SAAS,CAAC;AACpC,QAAI,WAAW,QAAQ,SAAS,qCAAqC;AACpE,WAAK,SAAS;AAAA,WACR;AACN,WAAK,SAAS;AAAA,IACf;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,gBAAgB,GAAG,UAAU;AAC5BJ,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO,KAAK,GAAG,OAAO;AAAA,MACvB,CAAC;AACDK,iBAAAA,gBAAgB,CAAC,EAAE,KAAK,SAAO;AAC9BL,sBAAG,MAAC,YAAW;AACf,YAAI,IAAI,KAAK,WAAW;AACvB,eAAK,UAAU,IAAI,KAAK;AACxBA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK,4CAA4C,KAAK,OAAO,YAAY,QAAQ;AAAA,WACjF;AAAA,eACK;AACN,cAAI,OAAO,IAAI,KAAK,eAAe,KAAK,OAAO;AAC/C,eAAK,OAAO,OAAO,SAAS;AAAA,YAC3B,OAAO,IAAI,KAAK;AAAA,YAChB;AAAA,UACD,CAAC;AACD,eAAK,YAAY,GAAG,QAAQ;AAAA,QAC7B;AAAA,MACD,CAAC,EAAE,MAAM,SAAO;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACX,CAAC;AAAA,MACF,CAAC;AAAA,IACD;AAAA,IACD,UAAU;AACT,WAAK,WAAW;AAAA,IAChB;AAAA;AAAA,IAED,YAAY;AASXM,mBAAAA,QAAQ,QAAQ,EACd,KAAK,UAAQ;AAIbC,4BAAS;AAAA,UACP;AAAA,UACA,aAAa,IAAI,WAAW;AAAA,UAC5B,aAAa,IAAI,WAAW;AAAA,SAC5B,EAAE,KAAK,SAAO;AACdP,wBAAG,MAAC,YAAW;AACf,eAAK,UAAU,IAAI,KAAK;AACxB,eAAK,YAAY,IAAI,KAAK;AAAA,SAI1B,EACA,MAAM,SAAO;AACbA,wBAAG,MAAC,YAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACX,CAAC;AAAA,QACF,CAAC;AAAA,OACF,EACA,MAAM,SAAO;AACbA,sBAAAA,MAAY,MAAA,OAAA,6CAAA,GAAG;AAAA,MAChB,CAAC;AAAA,IACF;AAAA,IACD,eAAe;AACd,UAAI,CAAC,KAAK;AAAS;AACnB,UAAI,CAAC,KAAK,UAAU;AACnBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,KAAK,GAAG,WAAW;AAAA,UAC1B,MAAM;AAAA,UACN,UAAU;AAAA,QACX,CAAC;AACD;AAAA,MACD;AACAA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO,KAAK,GAAG,OAAO;AAAA,MACvB,CAAC;AACDQ,2BAAU;AAAA,QACT,KAAK,KAAK;AAAA,OACV,EAAE,KAAK,SAAO;AACd,YAAI,OAAO,IAAI,KAAK,eAAe,KAAK,OAAO;AAC/C,aAAK,OAAO,OAAO,SAAS;AAAA,UAC3B,OAAO,IAAI,KAAK;AAAA,UAChB;AAAA,QACD,CAAC;AACD,aAAK,YAAY,IAAI,KAAK,QAAQ;AAAA,MACnC,CAAC,EAAE,MAAM,SAAO;AACfR,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACX,CAAC;AAAA,MACF,CAAC;AAAA,IACD;AAAA,IACD,gBAAgB,GAAG;AAClB,WAAK,KAAK,MAAM,YAAY,CAAC,KAAK,QAAQ;AAAA,IAC1C;AAAA,IACD,cAAc;AACb,WAAK,SAAS;AAAA,IACd;AAAA,IACD,aAAa;AACZA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,4CAA4C,KAAK,OAAO;AAAA,OAC7D;AAAA,IACD;AAAA,IACD,YAAY;AACX,WAAK,SAAS;AACd,WAAK,MAAM,KAAK;AAAA,QACf,OAAO,KAAK,GAAG,MAAM;AAAA,QACrB,MAAM;AAAA,MACP,GAAG;AAAA,QACF,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA,IACD,WAAW;AACVA,oBAAG,MAAC,aAAY;AAAA,IAChB;AAAA,IAED,OAAO;AACN,UAAI,KAAK,SAAS;AACjB,aAAK,OAAO,OAAO,SAAS;AAAA,UAC3B,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AACAA,oBAAG,MAAC,aAAY;AAAA,IAChB;AAAA,IASD,OAAO;AACNA,oBAAAA,MAAI,UAAU;AAAA,QACb,KAAK;AAAA,OACL;AAAA,IACD;AAAA;AAAA,IAED,UAAU,UAAU;AACnB,WAAK,OAAO;AAEZ,UAAI,UAAU;AACb,aAAK,SAAS;AAAA,MACf;AAAA,IAEA;AAAA,IACD,eAAe,MAAM;AACpB,WAAK,aAAa;AAClB,UAAI,KAAK,UAAU;AAElB,aAAK,YAAY,KAAK,QAAQ;AAAA,MAU/B;AAAA,IACA;AAAA;AAAA,IAGD,eAAe,GAAG;AACjB,UAAI,CAAC,KAAK,UAAU;AACnBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,KAAK,GAAG,WAAW;AAAA,UAC1B,MAAM;AAAA,UACN,UAAU;AAAA,QACX,CAAC;AACD;AAAA,MACD;AACAA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO,KAAK,GAAG,OAAO;AAAA,MACvB,CAAC;AACDM,mBAAAA,QAAQ,QAAQ,EACd,KAAK,UAAQ;AACb,aAAK,mBAAmB,EAAE,OAAO,eAAe,EAAE,OAAO,IAAI,IAAI;AAAA,OACjE,EACA,MAAM,WAAS;AACfN,sBAAAA,MAAI,MAAM,aAAa,KAAK;AAC5BA,sBAAG,MAAC,YAAW;AAAA,MAChB,CAAC;AAAA,IACF;AAAA;AAAA,IAED,mBAAmB,eAAe,IAAI,MAAM;AAC3CS,qCAAoB;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,QACA,aAAa,IAAI,WAAW;AAAA,QAC5B,aAAa,IAAI,WAAW;AAAA,QAC5B,KAAK,KAAK;AAAA,OACV,EACA,KAAK,SAAO;AACZ,YAAI,OAAO,IAAI,KAAK,eAAe,KAAK,OAAO;AAC/C,aAAK,OAAO,OAAO,SAAS;AAAA,UAC3B,OAAO,IAAI,KAAK;AAAA,UAChB;AAAA,QACD,CAAC;AAID,aAAK,OAAO,MAAM,WAAW;AAC7B,aAAK,YAAY,IAAI,KAAK,QAAQ;AAAA,OAOlC,EACA,MAAM,SAAO;AACbT,sBAAG,MAAC,YAAW;AAAA,MAChB,CAAC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,YAAY,UAAU,UAAU;AAC/B,UAAI,OAAO;AAEXU,2BAAa,EAAC,KAAK,SAAO;AACzB,aAAK,WAAW,IAAI;AACpB,aAAK,OAAO,OAAO,UAAU,IAAI,KAAK,GAAG;AAGzCC,qCAAkB,EAChB,KAAK,CAAC,cAAc;AACpBX,wBAAG,MAAC,YAAW;AAEf,cAAI,UAAU,MAAM;AACnB,iBAAK,OAAO,OAAO,mBAAmB,UAAU,IAAI;AAAA,UACrD;AAEA,cAAI,UAAU;AACb,iBAAK,SAAS;AAAA,iBACR;AAEN,iBAAK,MAAM,KAAK;AAAA,cACf,OAAO,KAAK,GAAG,MAAM;AAAA,cACrB,MAAM;AAAA,YACP,GAAG;AAAA,cACF,KAAK;AAAA,YACN,CAAC;AAAA,UAWF;AAAA,SACA,EACA,MAAM,MAAM;AAEZA,wBAAG,MAAC,YAAW;AACf,cAAI,UAAU;AACb,iBAAK,SAAS;AAAA,iBACR;AAEN,iBAAK,MAAM,KAAK;AAAA,cACf,OAAO,KAAK,GAAG,MAAM;AAAA,cACrB,MAAM;AAAA,YACP,GAAG;AAAA,cACF,KAAK;AAAA,YACN,CAAC;AAAA,UAWF;AAAA,QACD,CAAC;AAAA,MACH,CAAC,EAAE,MAAM,SAAO;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,IAAI;AAAA,UACX,MAAM;AAAA,UACN,UAAU;AAAA,QACX,CAAC;AAAA,MACF,CAAC;AAAA,IACD;AAAA,IACD,QAAQ,MAAM;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,qCAAqC;AAAA,OAC1C;AAAA,IACD;AAAA,EAuEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACniBF,GAAG,WAAW,eAAe;"}