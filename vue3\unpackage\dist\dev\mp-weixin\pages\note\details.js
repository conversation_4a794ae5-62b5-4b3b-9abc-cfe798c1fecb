"use strict";
const common_vendor = require("../../common/vendor.js");
const components_emojiPanel_sina = require("../../components/emoji-panel/sina.js");
const api_social = require("../../api/social.js");
const common_assets = require("../../common/assets.js");
const lazyImage = () => "../../components/lazyImage/lazyImage.js";
const play = () => "../../components/play/play.js";
const uniLoadMore = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const SharePanel = () => "../../components/share/index.js";
const CommentInput = () => "../../components/comment-input/comment-input.js";
const _sfc_main = {
  name: "NoteDetails",
  components: {
    lazyImage,
    play,
    uniLoadMore,
    SharePanel,
    CommentInput
  },
  watch: {
    // 监听swiperIdx的变化
    swiperIdx(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.$forceUpdate();
      }
    }
  },
  data() {
    return {
      statusBarHeight: this.$store.state.statusBarHeight || 20,
      titleBarHeight: this.$store.state.titleBarHeight || 44,
      keyboardHeight: 0,
      userId: 0,
      isUser: false,
      userName: "",
      userNickname: "",
      userAvatar: "/static/img/avatar_default.png",
      // 设置默认头像
      isFollowing: false,
      followChecked: false,
      isLoadingChildReplies: false,
      commentsCache: {},
      // 初始化评论缓存对象
      commentCache: {},
      // 初始化回复缓存对象
      noteInfo: {
        id: 0,
        uid: 0,
        user_id: 0,
        type: 0,
        user_info: {
          nickname: "昵称加载中",
          avatar: "/static/img/avatar_default.png"
          // 设置默认头像
        },
        content: "内容加载中",
        comments: 0,
        likes: 0,
        views: 0,
        is_like: false,
        create_time: "日期",
        location_name: "",
        province: "IP属地",
        images: [],
        video: "",
        video_cover: "",
        audio: null
      },
      swiperIdx: 0,
      isEmpty: false,
      commentView: false,
      shareView: false,
      commentList: [],
      cType: 0,
      isThrottling: true,
      cIdx: -1,
      cI: -1,
      page: 1,
      sonPage: 1,
      limit: 10,
      loadStatus: "loading",
      cCId: 0,
      cUId: 0,
      isComment: false,
      // 评论框是否显示
      isFocus: false,
      // 是否有焦点
      comtips: "说点什么...",
      // 提示文字
      comtext: "",
      // 评论内容
      commentActioning: false,
      // 评论操作进行中
      commentSource: null,
      // 评论来源
      commentBlurTimer: null,
      // 控制失焦关闭的定时器
      // 音频相关状态 - 只有音频动态时才初始化
      bgAudioStatus: false,
      bgAudioManager: null,
      audioRetryCount: 0,
      audioPlayingId: "",
      tipsTitle: "",
      isSubmittingComment: false,
      // 是否正在提交评论
      isOpeningComment: false,
      // 是否正在打开评论框
      isLoadingReplies: false,
      // 是否正在加载回复
      isDeletingComment: false,
      // 是否正在删除评论
      likeThrottling: false,
      // 点赞防抖
      isShareVisible: false,
      // 分享面板显示状态
      replyIndices: /* @__PURE__ */ new Map(),
      // 存储回复ID到索引的映射
      imagesLoaded: false,
      // 图片是否已加载
      actionInProgress: false,
      debounceTimer: null,
      lastScrollTop: 0,
      commentImage: null,
      atUserList: [],
      // 表情相关优化
      emojiMap: /* @__PURE__ */ new Map(),
      // 表情映射缓存，提高查找性能
      parsedContentCache: /* @__PURE__ */ new Map(),
      // 解析内容缓存
      emojiClickTimer: null,
      // 表情点击防抖定时器
      isEmojiLoading: false,
      // 表情加载状态
      previewEmojiData: null,
      // 预览表情数据
      maxCacheSize: 200,
      // 最大缓存条目数
      // 性能优化相关
      isPageActive: true,
      // 页面是否活跃
      performanceTimer: null,
      // 性能监控定时器
      lazyLoadObserver: null,
      // 懒加载观察器
      debug: true,
      // 是否开启调试日志
      voting: false
    };
  },
  computed: {
    // 空状态判断
    showEmptyState() {
      return this.isEmpty && this.page === 1;
    },
    // 处理头像展示
    formattedUserAvatar() {
      return this.userAvatar || "/static/img/avatar_default.png";
    },
    // 发送按钮图标
    sendButtonSrc() {
      return this.comtext.length ? "/static/img/fs1.png" : "/static/img/fs.png";
    },
    // 是否为音频动态
    isAudioNote() {
      return this.noteInfo.type === 4;
    },
    // 是否为视频动态
    isVideoNote() {
      return this.noteInfo.type === 3;
    },
    // 是否为图片动态
    isImageNote() {
      return this.noteInfo.type === 1 || this.noteInfo.type === 2;
    },
    // 表情缓存统计（用于性能监控）
    emojiCacheInfo() {
      return {
        emojiMapSize: this.emojiMap.size,
        parsedContentCacheSize: this.parsedContentCache.size,
        cacheHitRate: this.parsedContentCache.size > 0 ? (this.parsedContentCache.size / (this.parsedContentCache.size + 10)).toFixed(2) : "0.00"
      };
    },
    // 页面性能统计
    pagePerformanceInfo() {
      return {
        commentCacheSize: Object.keys(this.commentCache).length,
        replyIndicesSize: this.replyIndices.size,
        isPageActive: this.isPageActive
      };
    },
    votePeopleText() {
      if (!this.noteInfo.vote_info)
        return "";
      const total = this.noteInfo.vote_info.total || 0;
      if (total >= 1e4)
        return (total / 1e4).toFixed(1) + "万人参与了投票";
      if (total >= 1e3)
        return (total / 1e3).toFixed(1) + "K人参与了投票";
      return total + "人参与了投票";
    }
  },
  async onLoad(options) {
    common_vendor.index.__f__("log", "at pages/note/details.vue:658", "页面加载参数:", options);
    this.isPageActive = true;
    if (common_vendor.index.showShareMenu && typeof common_vendor.index.showShareMenu === "function") {
      common_vendor.index.showShareMenu();
    }
    await this.$onLaunched;
    common_vendor.index.__f__("log", "at pages/note/details.vue:670", "小程序初始化完成");
    this.initEmojiMap();
    this.loadRecentEmojis();
    if (options.id) {
      common_vendor.index.__f__("log", "at pages/note/details.vue:679", "获取到笔记ID:", options.id);
      this.noteInfo.id = options.id;
      if (options.comment) {
        this.commentView = !!options.comment;
        common_vendor.index.__f__("log", "at pages/note/details.vue:685", "从评论跳转");
      }
      if (options.share) {
        this.shareView = !!options.share;
        common_vendor.index.__f__("log", "at pages/note/details.vue:691", "从分享跳转");
      }
      common_vendor.index.__f__("log", "at pages/note/details.vue:695", "开始获取用户信息");
      this.userInfoHandle();
      common_vendor.index.__f__("log", "at pages/note/details.vue:699", "开始获取笔记详情");
      this.dynamicDetails();
    } else {
      common_vendor.index.__f__("error", "at pages/note/details.vue:702", "未提供笔记ID");
      this.opTipsPopup("笔记异常或已被删除，请稍后重试！", true);
    }
  },
  onShow() {
    this.isPageActive = true;
    this.userInfoHandle();
    if (this.isAudioNote && this.bgAudioManager) {
      this.checkAudioStatus();
    }
  },
  onHide() {
    this.isPageActive = false;
    if (this.isAudioNote && this.bgAudioManager && this.bgAudioStatus) {
      this.pauseAudio();
    }
  },
  mounted() {
    if (common_vendor.index.onPageScroll && typeof common_vendor.index.onPageScroll === "function") {
      common_vendor.index.onPageScroll(this.handlePageScroll);
    }
    this.initLazyLoad();
  },
  beforeUnmount() {
    this.isPageActive = false;
    this.cleanupResources();
  },
  methods: {
    // 添加调试日志函数
    debugLog(...args) {
      if (this.debug) {
        common_vendor.index.__f__("log", "at pages/note/details.vue:751", "[Details]", ...args);
      }
    },
    // 加载最近使用的表情
    loadRecentEmojis() {
      try {
        const recentEmojisStr = common_vendor.index.getStorageSync("recent_emojis");
        if (recentEmojisStr) {
          this.recentEmojis = JSON.parse(recentEmojisStr);
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/details.vue:763", "加载表情记录失败", e);
      }
    },
    // 初始化懒加载
    initLazyLoad() {
      common_vendor.index.__f__("log", "at pages/note/details.vue:770", "初始化懒加载");
    },
    // 清理所有资源
    cleanupResources() {
      common_vendor.index.__f__("log", "at pages/note/details.vue:775", "清理页面资源");
      if (common_vendor.index.offPageScroll && typeof common_vendor.index.offPageScroll === "function") {
        common_vendor.index.offPageScroll(this.handlePageScroll);
      }
      if (this.isAudioNote) {
        this.destroyAudioInstance();
      }
      this.clearAllTimers();
      this.cleanupEmojiResources();
      if (this.lazyLoadObserver) {
        this.lazyLoadObserver.disconnect();
        this.lazyLoadObserver = null;
      }
    },
    // 清除所有定时器
    clearAllTimers() {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = null;
      }
      if (this.commentBlurTimer) {
        clearTimeout(this.commentBlurTimer);
        this.commentBlurTimer = null;
      }
      if (this.performanceTimer) {
        clearTimeout(this.performanceTimer);
        this.performanceTimer = null;
      }
      if (this.imageCheckInterval) {
        clearInterval(this.imageCheckInterval);
        this.imageCheckInterval = null;
      }
    },
    // 清理表情相关资源
    cleanupEmojiResources() {
      if (this.emojiClickTimer) {
        clearTimeout(this.emojiClickTimer);
        this.emojiClickTimer = null;
      }
      this.clearEmojiCache();
    },
    // 检查是否有圈子
    hasCircle() {
      if (this.noteInfo.circle_info && this.noteInfo.circle_info.circle_id > 0) {
        return true;
      }
      if (this.noteInfo.circle_id && this.noteInfo.circle_id > 0) {
        return true;
      }
      return false;
    },
    // 获取圈子ID
    getCircleId() {
      if (this.noteInfo.circle_info && this.noteInfo.circle_info.circle_id) {
        return this.noteInfo.circle_info.circle_id;
      }
      return this.noteInfo.circle_id || 0;
    },
    // 获取圈子名称
    getCircleName() {
      if (this.noteInfo.circle_info && this.noteInfo.circle_info.circle_name) {
        return this.noteInfo.circle_info.circle_name;
      }
      return this.noteInfo.circle_name || "";
    },
    // 获取圈子头像
    getCircleAvatar() {
      if (this.noteInfo.circle_info && this.noteInfo.circle_info.circle_avatar) {
        return this.noteInfo.circle_info.circle_avatar;
      }
      return this.noteInfo.circle_avatar || "/static/img/qz1.png";
    },
    // 计算图片高度
    isHigh() {
      let height = "750rpx";
      const imageArray = this.noteInfo.type === 1 ? this.noteInfo.imgs : this.noteInfo.images;
      if (imageArray && imageArray.length > 0 && imageArray[this.swiperIdx]) {
        const currentImage = imageArray[this.swiperIdx];
        let wide = 750;
        let high = 750;
        if (typeof currentImage === "object") {
          wide = currentImage.wide || currentImage.width || 750;
          high = currentImage.high || currentImage.height || 750;
        }
        if (wide && high) {
          let ratio = high / wide;
          let calcHeight = Math.floor(750 * ratio);
          if (calcHeight > 1200)
            calcHeight = 1200;
          if (calcHeight < 500)
            calcHeight = 500;
          height = calcHeight + "rpx";
        }
      }
      return height;
    },
    // 获取用户信息
    userInfoHandle() {
      var _a;
      try {
        let userInfo = common_vendor.index.getStorageSync("USER_INFO") || {};
        common_vendor.index.__f__("log", "at pages/note/details.vue:912", "本地存储用户数据类型:", typeof userInfo);
        if (typeof userInfo === "string") {
          try {
            userInfo = JSON.parse(userInfo);
            common_vendor.index.__f__("log", "at pages/note/details.vue:918", "已将字符串解析为对象");
          } catch (e) {
            common_vendor.index.__f__("error", "at pages/note/details.vue:920", "解析用户数据失败:", e);
            userInfo = {};
          }
        }
        const storeUid = ((_a = this.$store.state.app) == null ? void 0 : _a.uid) || 0;
        this.userId = userInfo.uid || userInfo.id || storeUid || 0;
        this.userAvatar = userInfo.avatar || "";
        this.userNickname = userInfo.nickname || "";
        const hasUserId = this.userId > 0;
        const hasPhone = !!userInfo.phone;
        this.isUser = hasUserId && hasPhone;
        common_vendor.index.__f__("log", "at pages/note/details.vue:938", "用户信息处理完成:", {
          userId: this.userId,
          userAvatar: this.userAvatar,
          userNickname: this.userNickname,
          hasUserId,
          hasPhone,
          isUser: this.isUser
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/note/details.vue:947", "获取用户信息失败:", error);
        this.userId = 0;
        this.userAvatar = "";
        this.userNickname = "";
        this.isUser = false;
      }
    },
    // 获取笔记详情
    dynamicDetails() {
      common_vendor.index.showLoading({
        title: "加载中",
        mask: true
      });
      this.debugLog("请求笔记详情，笔记ID:", this.noteInfo.id);
      api_social.getDynamicDetail(this.noteInfo.id).then((res) => {
        common_vendor.index.hideLoading();
        this.debugLog("获取到笔记详情数据:", res);
        if (res.status === 200 && res.data && res.data.detail) {
          const detail = res.data.detail;
          if ((detail.status == 2 || detail.status == 3) && this.userId != detail.uid) {
            return this.opTipsPopup("笔记审核中或已被删除，请稍后重试！", true);
          }
          if (detail.topic_id) {
            try {
              detail.topic_id = JSON.parse(detail.topic_id);
            } catch (e) {
              detail.topic_id = [];
            }
          }
          if (detail.topic_info && Array.isArray(detail.topic_info)) {
            detail.topic_info = detail.topic_info.map((topic) => ({
              id: topic.id,
              title: topic.title
            }));
          } else {
            detail.topic_info = [];
          }
          if (detail.goods_info) {
            detail.goods_info = {
              id: detail.goods_info.id,
              image: detail.goods_info.image,
              store_name: detail.goods_info.store_name,
              price: detail.goods_info.price
            };
          }
          if (detail.circle_info) {
            detail.circle_info = {
              circle_id: detail.circle_info.circle_id || 0,
              circle_name: detail.circle_info.circle_name || "",
              circle_avatar: detail.circle_info.circle_avatar || ""
            };
          }
          if (detail.user_info) {
            detail.user_info = {
              uid: detail.user_info.uid,
              nickname: detail.user_info.nickname || "用户",
              avatar: detail.user_info.avatar || "/static/img/avatar_default.png",
              is_follow: !!detail.user_info.is_follow,
              is_mutual_follow: !!detail.user_info.is_mutual_follow
            };
          } else {
            detail.user_info = {
              uid: detail.uid,
              nickname: "用户",
              avatar: "/static/img/avatar_default.png",
              is_follow: false,
              is_mutual_follow: false
            };
          }
          if (detail.images) {
            if (typeof detail.images === "string") {
              try {
                detail.images = JSON.parse(detail.images);
              } catch (e) {
                detail.images = detail.images ? [detail.images] : [];
              }
            }
            if (!Array.isArray(detail.images)) {
              detail.images = [];
            }
          } else {
            detail.images = [];
          }
          this.noteInfo = {
            ...this.noteInfo,
            ...detail,
            // 确保基础字段存在
            id: detail.id,
            uid: detail.uid,
            type: detail.type || 0,
            content: detail.content || "",
            location_name: detail.location_name || "",
            latitude: detail.latitude || "",
            longitude: detail.longitude || "",
            create_time: detail.create_time || "",
            update_time: detail.update_time || "",
            likes: parseInt(detail.likes) || 0,
            comments: parseInt(detail.comments) || 0,
            views: parseInt(detail.views) || 0,
            shares: parseInt(detail.shares) || 0,
            is_like: !!detail.is_like,
            status: detail.status || 0,
            is_show: detail.is_show || 0,
            visibility: detail.visibility || 0,
            is_top: detail.is_top || 0,
            video: detail.video || "",
            video_cover: detail.video_cover || "",
            audio: detail.audio || "",
            audio_cover: detail.audio_cover || "",
            audio_title: detail.audio_title || "",
            product_id: detail.product_id || 0,
            circle_id: detail.circle_id || 0
          };
          this.debugLog("更新后的笔记数据:", this.noteInfo);
          this.processMediaData();
          this.getCommentList();
          if (this.shareView) {
            this.shareClick(true);
          }
          if (this.noteInfo.user_info) {
            this.isFollowing = !!this.noteInfo.user_info.is_follow;
            this.followChecked = true;
            this.debugLog("关注状态:", this.isFollowing);
          }
        } else {
          this.debugLog("获取笔记失败:", res.msg);
          this.opTipsPopup(res.msg || "获取笔记失败", true);
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        this.debugLog("获取笔记失败:", err);
        this.handleError(err, "获取笔记失败，请稍后重试！");
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 2e3);
      });
    },
    // 抽取媒体处理逻辑
    processMediaData() {
      try {
        this.debugLog("开始处理媒体数据，笔记类型:", this.noteInfo.type);
        switch (this.noteInfo.type) {
          case 1:
          case 2:
            this.processImageData();
            break;
          case 3:
            this.processVideoData();
            break;
          case 4:
            this.processAudioData();
            break;
          default:
            this.debugLog("未知的媒体类型:", this.noteInfo.type);
            if (this.noteInfo.video) {
              this.noteInfo.type = 3;
              this.processVideoData();
            } else if (this.noteInfo.audio) {
              this.noteInfo.type = 4;
              this.processAudioData();
            } else if (this.noteInfo.images && this.noteInfo.images.length) {
              this.noteInfo.type = this.noteInfo.images.length > 1 ? 2 : 1;
              this.processImageData();
            }
        }
      } catch (error) {
        this.debugLog("处理媒体数据失败:", error);
      }
    },
    // 处理图片数据
    processImageData() {
      this.debugLog("处理图片数据");
      try {
        let images = this.noteInfo.images;
        if (!Array.isArray(images)) {
          images = [];
        }
        this.noteInfo.images = images.map((img) => {
          if (typeof img === "string") {
            return {
              url: img,
              wide: 750,
              high: 750
            };
          }
          return img;
        });
        this.debugLog("图片数据处理完成，数量:", this.noteInfo.images.length);
      } catch (e) {
        this.debugLog("处理图片数据失败:", e);
        this.noteInfo.images = [];
      }
    },
    // 处理视频数据
    processVideoData() {
      common_vendor.index.__f__("log", "at pages/note/details.vue:1189", "处理视频数据:", this.noteInfo.video);
      if (!this.noteInfo.video_cover && this.noteInfo.video && this.noteInfo.video.cover) {
        this.noteInfo.video_cover = this.noteInfo.video.cover;
      }
      if (!this.noteInfo.video && this.noteInfo.video_url) {
        this.noteInfo.video = this.noteInfo.video_url;
      }
    },
    // 处理音频数据 - 只有音频动态时才执行
    processAudioData() {
      if (!this.isAudioNote) {
        common_vendor.index.__f__("log", "at pages/note/details.vue:1205", "非音频动态，跳过音频数据处理");
        return;
      }
      common_vendor.index.__f__("log", "at pages/note/details.vue:1209", "处理音频数据:", this.noteInfo.audio);
      if (!this.noteInfo.audio_cover) {
        this.noteInfo.audio_cover = "/static/img/audio_default_cover.png";
      }
      if (!this.noteInfo.audio_title) {
        this.noteInfo.audio_title = "音频";
      }
      this.initAudioState();
    },
    // 初始化音频状态 - 只有音频动态时才调用
    initAudioState() {
      if (!this.isAudioNote)
        return;
      common_vendor.index.__f__("log", "at pages/note/details.vue:1229", "初始化音频状态");
      this.bgAudioStatus = false;
      this.bgAudioManager = null;
      this.audioRetryCount = 0;
      this.audioPlayingId = "";
    },
    // 标准化图片数组
    normalizeImageArray(originalImages) {
      if (!originalImages) {
        common_vendor.index.__f__("log", "at pages/note/details.vue:1242", "图片数据为空，初始化为空数组");
        return [];
      }
      if (typeof originalImages === "string") {
        common_vendor.index.__f__("log", "at pages/note/details.vue:1248", "图片数据是字符串，尝试解析JSON");
        if (originalImages.startsWith("[")) {
          try {
            originalImages = JSON.parse(originalImages);
            common_vendor.index.__f__("log", "at pages/note/details.vue:1252", "JSON解析成功:", originalImages);
          } catch (parseErr) {
            common_vendor.index.__f__("error", "at pages/note/details.vue:1254", "JSON解析失败:", parseErr);
            originalImages = [originalImages];
          }
        } else {
          common_vendor.index.__f__("log", "at pages/note/details.vue:1258", "图片数据是单个字符串，转换为数组");
          originalImages = [originalImages];
        }
      }
      if (Array.isArray(originalImages)) {
        common_vendor.index.__f__("log", "at pages/note/details.vue:1265", "处理图片数组，数量:", originalImages.length);
        return originalImages.map((img, index) => {
          common_vendor.index.__f__("log", "at pages/note/details.vue:1267", `处理第${index + 1}张图片:`, img);
          if (typeof img === "string") {
            return { url: img, wide: 750, high: 750 };
          } else if (img && typeof img === "object") {
            const imgObj = {
              url: img.url || img.path || img.src || img.image || "",
              wide: parseInt(img.wide || img.width || 750),
              high: parseInt(img.high || img.height || 750)
            };
            common_vendor.index.__f__("log", "at pages/note/details.vue:1277", `图片${index + 1}处理结果:`, imgObj);
            return imgObj;
          }
          return { url: "", wide: 750, high: 750 };
        }).filter((img) => !!img.url);
      } else {
        common_vendor.index.__f__("log", "at pages/note/details.vue:1283", "图片数据不是数组，初始化为空数组");
        return [];
      }
    },
    // 处理通用数据
    processCommonData() {
      if (!this.noteInfo.user_info) {
        this.noteInfo.user_info = {
          nickname: "用户",
          avatar: "/static/img/avatar_default.png"
        };
      }
      if (!this.noteInfo.user_info.avatar) {
        this.noteInfo.user_info.avatar = "/static/img/avatar_default.png";
      }
      if (this.noteInfo.comments === void 0 || this.noteInfo.comments === null) {
        this.noteInfo.comments = 0;
      } else if (typeof this.noteInfo.comments === "string") {
        this.noteInfo.comments = parseInt(this.noteInfo.comments) || 0;
      }
      if (!this.noteInfo.uid && this.noteInfo.user_id) {
        this.noteInfo.uid = this.noteInfo.user_id;
      }
      common_vendor.index.__f__("log", "at pages/note/details.vue:1315", "通用数据处理完成");
    },
    // 获取评论列表 - 优化版本
    getCommentList() {
      if (this.isLoadingComments) {
        this.debugLog("正在加载评论，跳过重复请求");
        return;
      }
      if (this.loadStatus === "no-more" && this.page > 1) {
        this.debugLog("已无更多评论，跳过请求");
        return;
      }
      this.isLoadingComments = true;
      if (this.page === 1) {
        common_vendor.index.showLoading({
          title: "加载中",
          mask: true
        });
      } else {
        this.loadStatus = "loading";
      }
      const params = {
        type: 0,
        // 动态评论类型
        page: this.page || 1,
        sort_type: this.cType || 0
        // 0-热门，1-最新
      };
      this.debugLog("获取评论列表", {
        动态ID: this.noteInfo.id,
        页码: params.page,
        排序方式: params.sort_type === 0 ? "热门" : "最新"
      });
      api_social.getCommentsList(this.noteInfo.id, params).then((res) => {
        if (this.page === 1) {
          common_vendor.index.hideLoading();
        }
        this.isLoadingComments = false;
        if (res.status === 200) {
          this.debugLog("评论列表获取成功", res.data);
          const list = res.data.list || [];
          if (res.data.total !== void 0 && res.data.total !== null) {
            this.noteInfo.comments = parseInt(res.data.total) || 0;
            this.debugLog("从API获取总评论数", this.noteInfo.comments);
          } else if (res.data.count !== void 0 && res.data.count !== null) {
            this.noteInfo.comments = parseInt(res.data.count) || 0;
            this.debugLog("从API获取总评论数(count字段)", this.noteInfo.comments);
          }
          const processedList = list.map((item) => {
            var _a, _b;
            return {
              ...item,
              user_info: {
                uid: item.uid || 0,
                nickname: ((_a = item.user_info) == null ? void 0 : _a.nickname) || "用户",
                avatar: ((_b = item.user_info) == null ? void 0 : _b.avatar) || "/static/img/avatar_default.png"
              },
              reply_count: parseInt(item.reply_count) || 0,
              like_count: parseInt(item.like_count) || 0,
              is_like: !!item.is_like,
              create_time: item.create_time || "",
              content: item.content || "",
              images: Array.isArray(item.images) ? item.images : [],
              replies: Array.isArray(item.replies) ? item.replies.map((reply) => {
                var _a2, _b2;
                return {
                  ...reply,
                  user_info: {
                    uid: reply.uid || 0,
                    nickname: ((_a2 = reply.user_info) == null ? void 0 : _a2.nickname) || "用户",
                    avatar: ((_b2 = reply.user_info) == null ? void 0 : _b2.avatar) || "/static/img/avatar_default.png"
                  },
                  reply_user_info: reply.reply_user_info ? {
                    uid: reply.reply_user_info.uid || 0,
                    nickname: reply.reply_user_info.nickname || "用户"
                  } : null,
                  like_count: parseInt(reply.like_count) || 0,
                  is_like: !!reply.is_like,
                  create_time: reply.create_time || "",
                  content: reply.content || ""
                };
              }) : []
            };
          });
          if (this.page === 1) {
            this.commentList = processedList;
            this.isEmpty = processedList.length === 0;
          } else {
            this.commentList = [...this.commentList, ...processedList];
          }
          if (list.length < 10) {
            this.loadStatus = "no-more";
          } else {
            this.loadStatus = "more";
          }
          if (this.page === 1) {
            const cacheKey = `comments_${this.noteInfo.id}_${this.cType}_${this.page}`;
            this.commentsCache[cacheKey] = {
              list: processedList,
              isEmpty: this.isEmpty,
              loadStatus: this.loadStatus,
              totalComments: this.noteInfo.comments
            };
          }
          this.debugLog("评论列表更新完成", {
            当前页码: this.page,
            评论总数: this.commentList.length,
            加载状态: this.loadStatus,
            是否为空: this.isEmpty
          });
        } else {
          this.debugLog("评论列表获取失败", res);
          this.loadStatus = "no-more";
          common_vendor.index.showToast({
            title: res.msg || "获取评论失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        if (this.page === 1) {
          common_vendor.index.hideLoading();
        }
        this.isLoadingComments = false;
        this.loadStatus = "no-more";
        this.debugLog("获取评论列表异常", err);
        const errorMsg = this.handleError(err, "获取评论失败");
        common_vendor.index.showToast({
          title: errorMsg,
          icon: "none"
        });
      });
    },
    async onVote(optionId) {
      if (this.voting || this.noteInfo.vote_info.user_selected)
        return;
      this.voting = true;
      try {
        const voteId = this.noteInfo.vote_info.vote.id;
        await api_social.vote({ vote_id: voteId, option_id: optionId });
        this.dynamicDetails();
      } catch (e) {
        this.handleError(e, "投票失败");
      } finally {
        this.voting = false;
      }
    },
    // 加载评论回复（分页方式）
    loadAllReplies(e) {
      let id = parseInt(e.currentTarget.dataset.id) || 0;
      let idx = parseInt(e.currentTarget.dataset.idx) || 0;
      common_vendor.index.__f__("log", "at pages/note/details.vue:1497", "加载评论回复:", {
        commentId: id,
        commentIndex: idx
      });
      if (this.isLoadingReplies)
        return;
      this.isLoadingReplies = true;
      const commentItem = this.commentList[idx];
      if (commentItem) {
        this.$set(commentItem, "loading_replies", true);
      }
      const currentPage = parseInt(commentItem.replyPage) || 1;
      const cacheKey = `replies_${id}_${currentPage}`;
      if (this.commentCache[cacheKey]) {
        common_vendor.index.__f__("log", "at pages/note/details.vue:1520", "使用缓存中的回复数据");
        this.handleAllRepliesData(this.commentCache[cacheKey], idx, currentPage);
        return;
      }
      const params = {
        parent_id: parseInt(id) || 0,
        page: currentPage,
        limit: 3,
        // 每次只加载少量数据
        sort_type: 1
        // 按时间最新排序
      };
      common_vendor.index.__f__("log", "at pages/note/details.vue:1533", "获取回复请求参数:", params);
      api_social.getCommentReplies(params).then((res) => {
        if (res.status === 200) {
          common_vendor.index.__f__("log", "at pages/note/details.vue:1539", "获取到回复数据:", res.data);
          this.commentCache[cacheKey] = res.data;
          this.handleAllRepliesData(res.data, idx, currentPage);
        } else {
          if (commentItem) {
            this.$set(commentItem, "loading_replies", false);
          }
          this.isLoadingReplies = false;
          common_vendor.index.showToast({
            title: res.msg || "获取回复失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/note/details.vue:1560", "获取回复失败:", err);
        if (commentItem) {
          this.$set(commentItem, "loading_replies", false);
        }
        this.isLoadingReplies = false;
        common_vendor.index.showToast({
          title: "获取回复失败",
          icon: "none"
        });
      });
    },
    // 加载子回复列表 - 扁平化处理
    loadChildrenReplies(e) {
      let parentId = parseInt(e.currentTarget.dataset.parentId) || 0;
      let replyId = parseInt(e.currentTarget.dataset.replyId) || 0;
      let idx = parseInt(e.currentTarget.dataset.idx) || 0;
      let replyIndex = parseInt(e.currentTarget.dataset.replyIndex) || 0;
      common_vendor.index.__f__("log", "at pages/note/details.vue:1582", "加载子回复:", {
        parentId,
        replyId,
        commentIndex: idx,
        replyIndex
      });
      if (this.isLoadingChildReplies)
        return;
      this.isLoadingChildReplies = true;
      const commentItem = this.commentList[idx];
      const replyItem = commentItem.replies[replyIndex];
      const currentPage = parseInt(replyItem.childrenPage) || 1;
      this.$set(replyItem, "loading_children", true);
      const cacheKey = `replies_${parentId}_${replyId}_${currentPage}_2`;
      if (this.commentCache[cacheKey]) {
        common_vendor.index.__f__("log", "at pages/note/details.vue:1606", "使用缓存中的子回复数据");
        this.handleChildrenRepliesData(this.commentCache[cacheKey], idx, replyIndex, currentPage);
        return;
      }
      const params = {
        parent_id: parseInt(parentId) || 0,
        // 父评论ID
        reply_id: parseInt(replyId) || 0,
        // 回复ID
        reply_type: parseInt(2),
        // 回复类型：2=子回复
        page: parseInt(currentPage) || 1,
        // 页码
        limit: parseInt(10),
        // 每页数量
        sort_type: parseInt(0)
        // 默认热门排序
      };
      common_vendor.index.__f__("log", "at pages/note/details.vue:1621", "获取子回复请求参数:", params);
      api_social.getCommentReplies(params).then((res) => {
        this.$set(replyItem, "loading_children", false);
        this.isLoadingChildReplies = false;
        common_vendor.index.__f__("log", "at pages/note/details.vue:1630", "获取子回复响应:", res);
        if (res.status === 200) {
          this.commentCache[cacheKey] = res.data;
          this.handleChildrenRepliesData(res.data, idx, replyIndex, currentPage);
        } else {
          common_vendor.index.showToast({
            title: res.msg || "获取子回复失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        this.$set(replyItem, "loading_children", false);
        this.isLoadingChildReplies = false;
        common_vendor.index.__f__("error", "at pages/note/details.vue:1650", "获取子回复失败:", err);
        common_vendor.index.showToast({
          title: "获取子回复失败",
          icon: "none"
        });
      });
    },
    // 处理回复数据（分页方式）
    handleAllRepliesData(data, idx, page) {
      if (this.commentList[idx]) {
        const commentItem = this.commentList[idx];
        const replies = data.list || [];
        common_vendor.index.__f__("log", "at pages/note/details.vue:1664", "获取到回复数据:", replies);
        if (page === 1) {
          this.$set(commentItem, "replies", replies);
        } else {
          const existingIds = (commentItem.replies || []).map((r) => r.id);
          const newReplies = replies.filter((r) => !existingIds.includes(r.id));
          this.$set(commentItem, "replies", [...commentItem.replies || [], ...newReplies]);
        }
        this.$set(commentItem, "replyPage", page + 1);
        const replyCount = parseInt(data.count || 0);
        const currentLoadedCount = commentItem.replies ? commentItem.replies.length : 0;
        const hasNoMoreReplies = replies.length < 3 || currentLoadedCount >= replyCount;
        this.$set(commentItem, "has_more_replies", !hasNoMoreReplies);
        if (data.count !== void 0) {
          this.$set(commentItem, "reply_count", replyCount);
        }
        this.updateReplyIndices(commentItem.replies);
        this.$set(commentItem, "loading_replies", false);
        this.isLoadingReplies = false;
      }
    },
    // 处理子回复数据
    handleChildrenRepliesData(data, idx, replyIndex, page) {
      if (this.commentList[idx] && this.commentList[idx].replies[replyIndex]) {
        const replyItem = this.commentList[idx].replies[replyIndex];
        const childReplies = data.list || [];
        childReplies.forEach((child) => {
          child.reply_nickname = replyItem.nickname || "用户";
          this.commentList[idx].replies.push(child);
        });
        this.updateReplyIndices(this.commentList[idx].replies);
        this.$set(replyItem, "has_more_children", false);
        this.$set(replyItem, "loading_children", false);
        this.isLoadingChildReplies = false;
      }
    },
    // 递归更新回复索引映射
    updateReplyIndices(replies) {
      if (!replies || !replies.length)
        return;
      replies.forEach((reply, index) => {
        this.replyIndices.set(reply.id, index);
        if (reply.children && reply.children.length) {
          this.updateReplyIndices(reply.children);
        }
      });
    },
    // 获取子评论（分页加载，已被loadAllReplies替代，保留作为备用）
    sonComment(e) {
      let id = e.currentTarget.dataset.id;
      let idx = e.currentTarget.dataset.idx;
      common_vendor.index.__f__("log", "at pages/note/details.vue:1752", "获取子评论:", {
        commentId: id,
        commentIndex: idx,
        currentPage: this.sonPage
      });
      if (this.isLoadingReplies)
        return;
      this.isLoadingReplies = true;
      const commentItem = this.commentList[idx];
      if (commentItem) {
        this.$set(commentItem, "loading_replies", true);
      }
      this.sonPage = this.sonPage + 1;
      const cacheKey = `reply_${id}_${this.sonPage}`;
      if (this.commentCache[cacheKey]) {
        common_vendor.index.__f__("log", "at pages/note/details.vue:1776", "使用缓存中的回复数据");
        this.handleReplyData(this.commentCache[cacheKey], idx);
        return;
      }
      const params = {
        parent_id: id,
        // 父评论ID
        page: this.sonPage,
        // 页码
        limit: 10,
        // 每页数量
        sort_type: 1
        // 排序类型：1=最新(创建时间)
      };
      common_vendor.index.__f__("log", "at pages/note/details.vue:1789", "回复请求参数:", params);
      api_social.getCommentReplies(params).then((res) => {
        if (commentItem) {
          this.$set(commentItem, "loading_replies", false);
        }
        this.isLoadingReplies = false;
        common_vendor.index.__f__("log", "at pages/note/details.vue:1800", "获取回复响应:", res);
        if (res.status === 200) {
          this.commentCache[cacheKey] = res.data;
          this.handleReplyData(res.data, idx);
        } else {
          common_vendor.index.showToast({
            title: res.msg || "获取回复失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        if (commentItem) {
          this.$set(commentItem, "loading_replies", false);
        }
        this.isLoadingReplies = false;
        common_vendor.index.__f__("error", "at pages/note/details.vue:1822", "获取回复失败:", err);
        common_vendor.index.showToast({
          title: "获取回复失败",
          icon: "none"
        });
      });
    },
    // 处理回复数据
    handleReplyData(data, idx) {
      const list = data.list || [];
      if (this.commentList[idx]) {
        if (!this.commentList[idx].replies) {
          this.commentList[idx].replies = [];
        }
        const previousReplyCount = this.commentList[idx].replies.length;
        this.commentList[idx].replies = [...this.commentList[idx].replies, ...list];
        list.forEach((reply, i) => {
          this.replyIndices.set(reply.id, previousReplyCount + i);
        });
        if (data.count !== void 0 && data.count !== null) {
          this.commentList[idx].reply_count = parseInt(data.count) || 0;
        } else {
          this.commentList[idx].reply_count = this.commentList[idx].replies.length;
        }
        if (list.length < 10 || this.commentList[idx].replies.length >= this.commentList[idx].reply_count) {
          this.$set(this.commentList[idx], "all_replies_loaded", true);
        }
      }
    },
    // 切换评论排序
    commentClick(type) {
      if (!this.isThrottling || this.actionInProgress)
        return;
      if (this.cType === type)
        return;
      this.isThrottling = false;
      this.cType = type;
      this.page = 1;
      this.loadStatus = "loading";
      this.commentList = [];
      this.isEmpty = false;
      this.getCommentList();
      setTimeout(() => {
        this.isThrottling = true;
      }, 500);
    },
    // 打开评论框
    openComment(e) {
      e = e || { currentTarget: { dataset: { type: 0 } } };
      e.stopPropagation && e.stopPropagation();
      common_vendor.index.__f__("log", "at pages/note/details.vue:1897", "尝试打开评论框，用户状态:", {
        isUser: this.isUser,
        userId: this.userId,
        userInfo: {
          avatar: this.userAvatar,
          nickname: this.userNickname
        }
      });
      if (!this.isUser) {
        common_vendor.index.__f__("log", "at pages/note/details.vue:1907", "用户未完善资料，无法评论");
        this.opTipsPopup("完善账号资料后即可评论！");
        setTimeout(() => {
          common_vendor.index.navigateTo({
            url: "/pages/center/means"
          });
        }, 1e3);
        return;
      }
      let dataset = e.currentTarget.dataset || {};
      let type = dataset.type || 0;
      let uid = dataset.uid || 0;
      let cid = dataset.cid || 0;
      let name = dataset.name || "";
      this.cIdx = dataset.idx !== void 0 ? dataset.idx : -1;
      this.cI = dataset.i !== void 0 ? dataset.i : -1;
      this.isComment = false;
      this.isSubmittingComment = false;
      if (type == 1) {
        this.cCId = cid;
        this.cUId = uid;
        this.comtips = "回复：" + name;
      } else {
        this.cCId = 0;
        this.cUId = 0;
        this.comtips = "说点什么...";
      }
      this.$nextTick(() => {
        this.isComment = true;
        setTimeout(() => {
          this.isFocus = true;
          if (!this.isComment) {
            this.isComment = true;
          }
        }, 150);
      });
    },
    // 保存评论
    saveComment() {
      this.isSubmittingComment = true;
      let self = this;
      if (!self.comtext.trim()) {
        self.isSubmittingComment = false;
        return self.opTipsPopup("表达你的态度再评论吧！");
      }
      self.isComment = false;
      self.isFocus = false;
      common_vendor.index.showLoading({
        title: "发布中",
        mask: true
      });
      const commentParams = {
        type: 0,
        // 评论类型：0-动态
        target_id: self.noteInfo.id,
        // 目标ID
        content: self.comtext,
        // 评论内容 - 保留原始表情格式如[微笑]
        reply_id: self.cCId || 0,
        // 父评论ID（后端会自动处理parent_id）
        image: ""
        // 图片字段
      };
      common_vendor.index.__f__("log", "at pages/note/details.vue:1990", "提交评论参数:", commentParams);
      api_social.addComment(commentParams).then((res) => {
        common_vendor.index.hideLoading();
        self.isSubmittingComment = false;
        common_vendor.index.__f__("log", "at pages/note/details.vue:1997", "评论提交响应:", res);
        if (res.status === 200) {
          self.noteInfo.comments = (self.noteInfo.comments || 0) + 1;
          let commentData = res.data || self.createDefaultCommentData(self.comtext, "");
          common_vendor.index.__f__("log", "at pages/note/details.vue:2006", "使用的评论数据:", commentData);
          if (self.cIdx >= 0 && self.cI == -1) {
            common_vendor.index.__f__("log", "at pages/note/details.vue:2010", "添加回复到主评论:", self.cIdx);
            if (!self.commentList[self.cIdx].replies) {
              self.commentList[self.cIdx].replies = [];
            }
            if (!self.commentList[self.cIdx].reply_count) {
              self.commentList[self.cIdx].reply_count = 0;
            }
            self.commentList[self.cIdx].replies.push(commentData);
            self.commentList[self.cIdx].reply_count += 1;
            self.replyIndices.set(commentData.id, self.commentList[self.cIdx].replies.length - 1);
            self.$forceUpdate();
          } else if (self.cIdx >= 0 && self.cI >= 0) {
            common_vendor.index.__f__("log", "at pages/note/details.vue:2031", "添加回复到子评论:", self.cIdx, self.cI);
            if (!self.commentList[self.cIdx].replies) {
              self.commentList[self.cIdx].replies = [];
            }
            if (!self.commentList[self.cIdx].reply_count) {
              self.commentList[self.cIdx].reply_count = 0;
            }
            self.commentList[self.cIdx].replies.push(commentData);
            self.commentList[self.cIdx].reply_count += 1;
            self.replyIndices.set(commentData.id, self.commentList[self.cIdx].replies.length - 1);
            self.$forceUpdate();
          } else {
            common_vendor.index.__f__("log", "at pages/note/details.vue:2052", "添加新评论");
            if (self.commentList.length <= 0) {
              self.isEmpty = false;
              self.commentList = [];
            }
            commentData.replies = [];
            commentData.reply_count = 0;
            self.commentList.unshift(commentData);
            self.$forceUpdate();
          }
          self.comtext = "";
          self.cCId = 0;
          self.cUId = 0;
          self.comtips = "说点什么...";
          self.opTipsPopup("评论成功");
        } else {
          self.opTipsPopup(res.msg || "评论失败");
        }
        self.isComment = false;
      }).catch((err) => {
        common_vendor.index.hideLoading();
        self.isSubmittingComment = false;
        common_vendor.index.__f__("error", "at pages/note/details.vue:2081", "评论提交失败:", err);
        self.handleError(err, "评论失败");
        self.isComment = false;
      });
    },
    // 删除评论
    delComment(e) {
      let self = this;
      let idx = e.currentTarget.dataset.idx;
      let i = e.currentTarget.dataset.i;
      let commentId = e.currentTarget.dataset.id;
      if (self.isDeletingComment)
        return;
      common_vendor.index.showModal({
        content: "确定要永久删除该评论吗？",
        confirmColor: "#FA5150",
        success: function(res) {
          if (res.confirm) {
            self.isDeletingComment = true;
            common_vendor.index.showLoading({
              title: "删除中",
              mask: true
            });
            api_social.deleteComment(commentId).then((res2) => {
              common_vendor.index.hideLoading();
              self.isDeletingComment = false;
              if (res2.status === 200) {
                if (self.noteInfo.comments > 0) {
                  self.noteInfo.comments--;
                }
                if (i == -1) {
                  self.commentList[idx].delete_time = (/* @__PURE__ */ new Date()).toISOString();
                  self.commentList[idx].status = 0;
                } else {
                  if (self.commentList[idx].replies && self.commentList[idx].replies.length > i) {
                    self.commentList[idx].replies[i].delete_time = (/* @__PURE__ */ new Date()).toISOString();
                    self.commentList[idx].replies[i].status = 0;
                    if (self.commentList[idx].reply_count > 0) {
                      self.commentList[idx].reply_count--;
                    }
                  }
                }
                self.opTipsPopup("删除成功");
              } else {
                self.opTipsPopup(res2.msg || "删除失败");
              }
            }).catch((err) => {
              common_vendor.index.hideLoading();
              self.isDeletingComment = false;
              self.handleError(err, "删除失败");
            });
          }
        }
      });
    },
    // 点赞/取消点赞评论
    toggleCommentLike(commentId, isCurrentlyLiked) {
      if (!this.isUser) {
        this.opTipsPopup("请先完善账号资料");
        return;
      }
      if (this.likeThrottling)
        return;
      this.likeThrottling = true;
      setTimeout(() => {
        this.likeThrottling = false;
      }, 500);
      const currentLikeState = isCurrentlyLiked ? 1 : 0;
      const newLikeState = currentLikeState ? 0 : 1;
      for (let i = 0; i < this.commentList.length; i++) {
        const comment = this.commentList[i];
        if (comment.id === commentId) {
          comment.is_like = newLikeState;
          if (newLikeState) {
            comment.likes = (comment.likes || 0) + 1;
          } else if (comment.likes > 0) {
            comment.likes--;
          }
          break;
        }
        if (comment.replies && comment.replies.length > 0) {
          for (let j = 0; j < comment.replies.length; j++) {
            const reply = comment.replies[j];
            if (reply.id === commentId) {
              reply.is_like = newLikeState;
              if (newLikeState) {
                reply.likes = (reply.likes || 0) + 1;
              } else if (reply.likes > 0) {
                reply.likes--;
              }
              break;
            }
          }
        }
      }
      if (currentLikeState) {
        api_social.unlikeComment(commentId).then((res) => {
        }).catch((err) => {
          this.restoreCommentLikeStatus(commentId, currentLikeState);
          this.handleError(err, "操作失败，请重试");
        });
      } else {
        api_social.likeComment(commentId).then((res) => {
        }).catch((err) => {
          this.restoreCommentLikeStatus(commentId, currentLikeState);
          this.handleError(err, "操作失败，请重试");
        });
      }
    },
    // 恢复评论点赞状态（操作失败时）
    restoreCommentLikeStatus(commentId, originalLikeState) {
      for (let i = 0; i < this.commentList.length; i++) {
        const comment = this.commentList[i];
        if (comment.id === commentId) {
          comment.is_like = originalLikeState;
          if (originalLikeState === 1) {
            if (comment.likes > 0)
              comment.likes--;
          } else {
            comment.likes++;
          }
          break;
        }
        if (comment.replies && comment.replies.length > 0) {
          for (let j = 0; j < comment.replies.length; j++) {
            const reply = comment.replies[j];
            if (reply.id === commentId) {
              reply.is_like = originalLikeState;
              if (originalLikeState === 1) {
                if (reply.likes > 0)
                  reply.likes--;
              } else {
                reply.likes++;
              }
              break;
            }
          }
        }
      }
    },
    // 删除笔记
    delDynamic() {
      let self = this;
      common_vendor.index.showModal({
        content: "确认要永久删除这篇笔记吗？",
        confirmColor: "#FA5150",
        success: function(res) {
          if (res.confirm) {
            common_vendor.index.showLoading({
              mask: true
            });
            api_social.deleteDynamic(self.noteInfo.id).then((res2) => {
              common_vendor.index.hideLoading();
              getApp().globalData.isCenterPage = true;
              if (res2.status === 200) {
                self.opTipsPopup("删除成功", true);
              } else {
                self.opTipsPopup(res2.msg || "删除失败");
              }
            }).catch((err) => {
              common_vendor.index.hideLoading();
              self.handleError(err, "删除失败");
            });
          }
        }
      });
    },
    // 举报笔记
    reasonClick(reason) {
      let self = this;
      common_vendor.index.showLoading({
        mask: true
      });
      let imageUrl = "";
      if (self.noteInfo.type == 2 && self.noteInfo.images && self.noteInfo.images.length > 0) {
        imageUrl = self.noteInfo.images[0].url;
      } else if (self.noteInfo.type == 3 && self.noteInfo.video && self.noteInfo.video_cover) {
        imageUrl = self.noteInfo.video_cover;
      } else if (self.noteInfo.type == 4 && self.noteInfo.audio && self.noteInfo.audio.cover) {
        imageUrl = self.noteInfo.audio.cover;
      }
      api_social.reportDynamic(
        reason,
        self.noteInfo.id,
        self.noteInfo.uid,
        self.noteInfo.content,
        imageUrl
      ).then((res) => {
        common_vendor.index.hideLoading();
        self.opTipsPopup(res.msg || "举报成功");
        self.menuPopupClick(false);
      }).catch((err) => {
        common_vendor.index.hideLoading();
        self.handleError(err, "举报失败");
      });
    },
    // 点赞笔记
    likeDynamic() {
      if (this.actionInProgress)
        return;
      this.actionInProgress = true;
      const currentLikeState = this.noteInfo.is_like ? 1 : 0;
      const newLikeState = currentLikeState ? 0 : 1;
      const oldLikes = this.noteInfo.likes;
      this.noteInfo.is_like = newLikeState;
      this.noteInfo.likes = newLikeState ? oldLikes + 1 : oldLikes - 1;
      api_social.likeDynamic({
        id: this.noteInfo.id,
        is_like: newLikeState
        // 直接使用0或1
      }).then((res) => {
      }).catch((err) => {
        this.noteInfo.is_like = currentLikeState;
        this.noteInfo.likes = oldLikes;
        this.handleError(err, "操作失败，请重试");
      }).finally(() => {
        setTimeout(() => {
          this.actionInProgress = false;
        }, 500);
      });
    },
    // 关注/取消关注用户
    followUser() {
      if (!this.noteInfo.uid || this.actionInProgress)
        return;
      this.actionInProgress = true;
      const newFollowState = !this.isFollowing;
      const oldFollowState = this.isFollowing;
      this.isFollowing = newFollowState;
      if (this.noteInfo.user_info) {
        this.noteInfo.user_info.is_follow = newFollowState;
      }
      api_social.followUser({
        follow_uid: this.noteInfo.uid,
        is_follow: newFollowState ? 1 : 0
      }).then((res) => {
      }).catch((err) => {
        this.isFollowing = oldFollowState;
        if (this.noteInfo.user_info) {
          this.noteInfo.user_info.is_follow = oldFollowState;
        }
        this.handleError(err, "操作失败，请重试");
      }).finally(() => {
        setTimeout(() => {
          this.actionInProgress = false;
        }, 500);
      });
    },
    // 点击图片预览
    swiperClick(e) {
      if (this.noteInfo.type !== 2 && this.noteInfo.type !== 1)
        return;
      let i = e.currentTarget.dataset.i;
      let urls = [];
      const imageArray = this.noteInfo.type === 1 ? this.noteInfo.imgs : this.noteInfo.images;
      if (imageArray && imageArray.length > 0) {
        if (typeof imageArray[0] === "string") {
          urls = imageArray;
        } else {
          for (let item of imageArray) {
            const imageUrl = this.getImageSrc(item);
            if (imageUrl) {
              urls.push(imageUrl);
            }
          }
        }
      }
      if (urls.length === 0) {
        common_vendor.index.__f__("error", "at pages/note/details.vue:2439", "没有可预览的图片URL");
        return;
      }
      common_vendor.index.previewImage({
        current: urls[i] || urls[0],
        urls
      });
    },
    // 关闭评论框
    closeComment(e) {
      e && e.stopPropagation && e.stopPropagation();
      common_vendor.index.__f__("log", "at pages/note/details.vue:2455", "手动关闭评论框");
      if (this.isSubmittingComment) {
        return;
      }
      if (this.commentBlurTimer) {
        clearTimeout(this.commentBlurTimer);
        this.commentBlurTimer = null;
      }
      this.commentActioning = false;
      this.isComment = false;
      this.isFocus = false;
      this.comtext = "";
    },
    // 轮播图切换
    swiperChange(e) {
      setTimeout(() => {
        this.swiperIdx = e.detail.current;
        this.$forceUpdate();
      }, 0);
    },
    // 打开位置
    openLocationClick() {
      common_vendor.index.openLocation({
        latitude: parseFloat(this.noteInfo.latitude),
        longitude: parseFloat(this.noteInfo.longitude),
        name: this.noteInfo.location_name
      });
    },
    // 更多弹窗功能已移至分享组件
    // 分享
    shareClick(show) {
      this.isShareVisible = show;
    },
    // 关闭分享面板
    closeShare() {
      this.isShareVisible = false;
    },
    // 处理编辑笔记
    handleEdit(noteId) {
      common_vendor.index.navigateTo({
        url: `/pages/note/add?id=${noteId}`
      });
    },
    // 处理删除笔记
    handleDelete(noteId) {
      this.delDynamic();
    },
    // 处理举报笔记
    handleReport(reportData) {
      this.reasonClick(reportData.reason);
    },
    // 处理不感兴趣
    handleDislike(noteId) {
      common_vendor.index.__f__("log", "at pages/note/details.vue:2529", "标记不感兴趣:", noteId);
    },
    // 页面跳转
    navigateToFun(e) {
      common_vendor.index.navigateTo({
        url: "/pages/" + e.currentTarget.dataset.url
      });
    },
    // 初始化表情映射缓存
    initEmojiMap() {
      if (this.emojiMap.size > 0)
        return;
      try {
        components_emojiPanel_sina.sinaEmoji.forEach((emoji) => {
          if (emoji && emoji.phrase) {
            this.emojiMap.set(emoji.phrase, emoji);
          }
        });
        common_vendor.index.__f__("log", "at pages/note/details.vue:2551", "表情映射缓存初始化完成，共", this.emojiMap.size, "个表情");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/note/details.vue:2553", "初始化表情映射失败:", error);
      }
    },
    // 优化后的表情内容解析
    parseEmojiContent(text) {
      if (!text || typeof text !== "string")
        return [];
      const cacheKey = text;
      if (this.parsedContentCache.has(cacheKey)) {
        return this.parsedContentCache.get(cacheKey);
      }
      const nodes = [];
      let lastIndex = 0;
      const regex = /\[([^\[\]]{1,10})\]/g;
      let match;
      try {
        while ((match = regex.exec(text)) !== null) {
          if (match.index > lastIndex) {
            const textContent = text.substring(lastIndex, match.index);
            if (textContent) {
              nodes.push({
                type: "text",
                text: textContent
              });
            }
          }
          const emojiPhrase = match[0];
          const emoji = this.emojiMap.get(emojiPhrase);
          if (emoji && emoji.url) {
            nodes.push({
              type: "image",
              attrs: {
                src: emoji.url,
                class: "emoji-img",
                style: "width: 32rpx !important; height: 32rpx !important; max-width: 32rpx !important; max-height: 32rpx !important; min-width: 32rpx !important; min-height: 32rpx !important; vertical-align: middle; margin: 0 4rpx; display: inline-block; border-radius: 4rpx; object-fit: cover;",
                "data-emoji": emojiPhrase,
                "data-url": emoji.url
              }
            });
          } else {
            nodes.push({
              type: "text",
              text: emojiPhrase
            });
          }
          lastIndex = regex.lastIndex;
        }
        if (lastIndex < text.length) {
          const remainingText = text.substring(lastIndex);
          if (remainingText) {
            nodes.push({
              type: "text",
              text: remainingText
            });
          }
        }
        this.cacheEmojiParseResult(cacheKey, nodes);
        return nodes;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/note/details.vue:2630", "解析表情内容出错:", error);
        return [{
          type: "text",
          text
        }];
      }
    },
    // 缓存表情解析结果
    cacheEmojiParseResult(cacheKey, nodes) {
      if (this.parsedContentCache.size >= this.maxCacheSize) {
        const firstKey = this.parsedContentCache.keys().next().value;
        this.parsedContentCache.delete(firstKey);
      }
      this.parsedContentCache.set(cacheKey, nodes);
    },
    // 优化后的表情内容解析（用于rich-text组件）
    parseEmojiContentForRichText(text) {
      if (!text || typeof text !== "string")
        return text;
      const cacheKey = `richtext_${text}`;
      if (this.parsedContentCache.has(cacheKey)) {
        return this.parsedContentCache.get(cacheKey);
      }
      try {
        let processedText = text.replace(/\[([^\[\]]{1,10})\]/g, (match) => {
          const emoji = this.emojiMap.get(match);
          if (emoji && emoji.url) {
            return `<img src="${emoji.url}" class="emoji-img-inline" style="width: 32rpx !important; height: 32rpx !important; max-width: 32rpx !important; max-height: 32rpx !important; min-width: 32rpx !important; min-height: 32rpx !important; vertical-align: middle; margin: 0 4rpx; display: inline-block; border-radius: 4rpx; object-fit: cover;" data-emoji="${match}" />`;
          }
          return match;
        });
        if (this.parsedContentCache.size >= this.maxCacheSize) {
          const firstKey = this.parsedContentCache.keys().next().value;
          this.parsedContentCache.delete(firstKey);
        }
        this.parsedContentCache.set(cacheKey, processedText);
        return processedText;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/note/details.vue:2678", "解析表情内容出错:", error);
        return text;
      }
    },
    // 表情点击预览（可选功能）
    onEmojiClick(event) {
      if (this.emojiClickTimer) {
        clearTimeout(this.emojiClickTimer);
      }
      this.emojiClickTimer = setTimeout(() => {
        const target = event.target || event.currentTarget;
        const emojiPhrase = target.getAttribute("data-emoji");
        const emojiUrl = target.getAttribute("data-url");
        if (emojiPhrase && emojiUrl) {
          this.showEmojiPreview(emojiPhrase, emojiUrl);
        }
      }, 300);
    },
    // 显示表情预览
    showEmojiPreview(phrase, url) {
      this.previewEmojiData = {
        phrase,
        url,
        timestamp: Date.now()
      };
      common_vendor.index.showActionSheet({
        itemList: ["复制表情", "查看大图"],
        success: (res) => {
          if (res.tapIndex === 0) {
            common_vendor.index.setClipboardData({
              data: phrase,
              success: () => {
                common_vendor.index.showToast({
                  title: "表情已复制",
                  icon: "success"
                });
              }
            });
          } else if (res.tapIndex === 1) {
            common_vendor.index.previewImage({
              urls: [url],
              current: url
            });
          }
        }
      });
    },
    // 清理表情缓存
    clearEmojiCache() {
      this.parsedContentCache.clear();
      common_vendor.index.__f__("log", "at pages/note/details.vue:2739", "表情缓存已清理");
    },
    // 获取表情缓存统计
    getEmojiCacheStats() {
      return {
        emojiMapSize: this.emojiMap.size,
        parsedContentCacheSize: this.parsedContentCache.size,
        maxCacheSize: this.maxCacheSize
      };
    },
    // 强制应用表情样式（运行时修复）
    forceApplyEmojiStyles() {
      this.$nextTick(() => {
        try {
          const emojiImages = common_vendor.index.createSelectorQuery().in(this).selectAll("image[data-emoji], img[data-emoji]").exec((res) => {
            if (res && res[0]) {
              res[0].forEach((node, index) => {
                common_vendor.index.createSelectorQuery().in(this).select(`image[data-emoji]:nth-child(${index + 1}), img[data-emoji]:nth-child(${index + 1})`).fields({
                  node: true,
                  size: true
                }).exec((nodeRes) => {
                  if (nodeRes && nodeRes[0] && nodeRes[0].node) {
                    const node2 = nodeRes[0].node;
                    node2.style.width = "32rpx";
                    node2.style.height = "32rpx";
                    node2.style.maxWidth = "32rpx";
                    node2.style.maxHeight = "32rpx";
                    node2.style.minWidth = "32rpx";
                    node2.style.minHeight = "32rpx";
                    node2.style.objectFit = "cover";
                  }
                });
              });
            }
          });
        } catch (error) {
          common_vendor.index.__f__("warn", "at pages/note/details.vue:2785", "强制应用表情样式失败:", error);
        }
      });
    },
    // 聚焦评论框
    focusClick(e) {
      common_vendor.index.__f__("log", "at pages/note/details.vue:2792", "评论框获得焦点", e.detail);
      if (e.detail && e.detail.height !== void 0) {
        this.keyboardHeight = e.detail.height;
      }
    },
    // 返回上一页
    navBack() {
      if (getCurrentPages().length > 1) {
        common_vendor.index.navigateBack();
      } else {
        common_vendor.index.switchTab({
          url: "/pages/index/index"
        });
      }
    },
    // 处理音频URL
    formatAudioUrl(url) {
      if (!url)
        return "";
      if (!url.startsWith("http")) {
        if (url.startsWith("//")) {
          return "https:" + url;
        }
        if (url.startsWith("/")) {
          return "https://yourdomain.com" + url;
        }
        return "https://yourdomain.com/" + url;
      }
      return url;
    },
    // 音频播放 - 只有音频动态时才执行
    audioBgClick() {
      if (!this.isAudioNote) {
        common_vendor.index.__f__("log", "at pages/note/details.vue:2836", "非音频动态，不执行音频播放逻辑");
        return;
      }
      try {
        if (this.bgAudioStatus) {
          this.pauseAudio();
        } else {
          this.playAudio();
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/details.vue:2850", "音频控制异常:", e);
        this.handleAudioError();
      }
    },
    // 暂停音频
    pauseAudio() {
      if (!this.isAudioNote || !this.bgAudioManager)
        return;
      try {
        this.bgAudioManager.pause();
        this.bgAudioStatus = false;
        common_vendor.index.__f__("log", "at pages/note/details.vue:2862", "音频已暂停");
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/details.vue:2864", "暂停音频失败:", e);
        this.handleAudioError();
      }
    },
    // 播放音频 - 只有音频动态时才执行
    playAudio() {
      if (!this.isAudioNote) {
        common_vendor.index.__f__("log", "at pages/note/details.vue:2873", "非音频动态，不执行音频播放逻辑");
        return;
      }
      if (!this.noteInfo.audio) {
        return this.opTipsPopup("音频资源不可用");
      }
      if (this.bgAudioManager) {
        try {
          common_vendor.index.__f__("log", "at pages/note/details.vue:2885", "继续播放现有音频");
          this.bgAudioManager.play();
          this.bgAudioStatus = true;
          return;
        } catch (e) {
          common_vendor.index.__f__("error", "at pages/note/details.vue:2890", "播放现有音频失败，重新创建:", e);
          this.createAudioInstance();
        }
      } else {
        this.createAudioInstance();
      }
    },
    // 创建音频实例 - 只有音频动态时才执行
    createAudioInstance() {
      if (!this.isAudioNote)
        return;
      try {
        common_vendor.index.showToast({
          title: "加载音频中...",
          icon: "loading",
          mask: true
        });
        this.bgAudioManager = common_vendor.index.getBackgroundAudioManager();
        this.bgAudioManager.title = this.noteInfo.audio_title || "音频";
        this.bgAudioManager.singer = this.noteInfo.user_info.nickname || "未知作者";
        this.bgAudioManager.coverImgUrl = this.noteInfo.audio_cover || "/static/img/audio_default_cover.png";
        this.bgAudioManager.epname = "笔记音频";
        this.audioPlayingId = this.noteInfo.id + "_" + Date.now();
        const currentAudioId = this.audioPlayingId;
        this.setupAudioListeners(currentAudioId);
        const audioUrl = this.formatAudioUrl(this.noteInfo.audio);
        this.bgAudioManager.src = audioUrl;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/details.vue:2944", "创建音频实例异常:", e);
        this.handleAudioError();
      }
    },
    // 设置音频事件监听 - 只有音频动态时才执行
    setupAudioListeners(currentAudioId) {
      if (!this.isAudioNote || !this.bgAudioManager)
        return;
      try {
        this.bgAudioManager.onPlay(() => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.hideToast();
          this.bgAudioStatus = true;
          common_vendor.index.__f__("log", "at pages/note/details.vue:2960", "音频开始播放");
        });
        this.bgAudioManager.onError((err) => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.__f__("error", "at pages/note/details.vue:2967", "音频播放错误:", err);
          this.handleAudioError(err);
        });
        this.bgAudioManager.onEnded(() => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.__f__("log", "at pages/note/details.vue:2973", "音频播放结束");
          this.bgAudioStatus = false;
        });
        this.bgAudioManager.onStop(() => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.__f__("log", "at pages/note/details.vue:2979", "音频播放停止");
          this.bgAudioStatus = false;
        });
        this.bgAudioManager.onPause(() => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.__f__("log", "at pages/note/details.vue:2985", "音频播放暂停");
          this.bgAudioStatus = false;
        });
        this.bgAudioManager.onWaiting(() => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.__f__("log", "at pages/note/details.vue:2991", "音频加载中");
        });
        this.bgAudioManager.onCanplay(() => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.__f__("log", "at pages/note/details.vue:2996", "音频可以播放");
          common_vendor.index.hideToast();
        });
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/details.vue:3001", "设置音频监听器失败:", e);
        this.handleAudioError();
      }
    },
    // 处理音频错误
    handleAudioError(err = null) {
      if (!this.isAudioNote)
        return;
      common_vendor.index.hideToast();
      this.bgAudioStatus = false;
      let errorMsg = "音频播放失败，请稍后重试";
      if (err && err.errCode) {
        switch (err.errCode) {
          case 10001:
            errorMsg = "系统错误，请重启应用";
            break;
          case 10002:
            errorMsg = "网络错误，请检查网络连接";
            break;
          case 10003:
            errorMsg = "音频文件错误，请更换音频";
            break;
          case 10004:
            errorMsg = "音频格式不支持";
            break;
          default:
            errorMsg = "音频播放失败，错误码: " + err.errCode;
        }
      }
      this.opTipsPopup(errorMsg);
      this.bgAudioManager = null;
      this.audioPlayingId = "";
    },
    // 检查音频状态 - 只有音频动态时才执行
    checkAudioStatus() {
      if (!this.isAudioNote || !this.bgAudioManager)
        return;
      try {
        common_vendor.index.__f__("log", "at pages/note/details.vue:3038", "检查音频状态");
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/details.vue:3040", "检查音频状态失败:", e);
      }
    },
    // 销毁音频实例 - 只有音频动态时才执行
    destroyAudioInstance() {
      if (!this.isAudioNote) {
        common_vendor.index.__f__("log", "at pages/note/details.vue:3047", "非音频动态，不执行音频销毁逻辑");
        return;
      }
      common_vendor.index.__f__("log", "at pages/note/details.vue:3051", "销毁音频实例");
      if (this.bgAudioManager) {
        try {
          if (this.bgAudioStatus) {
            this.bgAudioManager.stop();
          }
          try {
            if (this.bgAudioManager.offPlay) {
              this.bgAudioManager.offPlay();
              this.bgAudioManager.offPause();
              this.bgAudioManager.offStop();
              this.bgAudioManager.offEnded();
              this.bgAudioManager.offTimeUpdate();
              this.bgAudioManager.offWaiting();
              this.bgAudioManager.offCanplay();
              this.bgAudioManager.offError();
            }
          } catch (e) {
            common_vendor.index.__f__("error", "at pages/note/details.vue:3081", "微信小程序取消音频事件监听失败:", e);
          }
          this.bgAudioManager = null;
          this.bgAudioStatus = false;
          this.audioPlayingId = "";
          common_vendor.index.__f__("log", "at pages/note/details.vue:3090", "音频实例销毁完成");
        } catch (e) {
          common_vendor.index.__f__("error", "at pages/note/details.vue:3092", "处理音频实例销毁过程中出错:", e);
          this.bgAudioManager = null;
          this.bgAudioStatus = false;
          this.audioPlayingId = "";
        }
      }
    },
    // 输入框失焦处理
    textareaBlur(e) {
      this.lastTouchX = e && e.detail && e.detail.x;
      this.lastTouchY = e && e.detail && e.detail.y;
      setTimeout(() => {
        if (this.isComment && !this.isSubmittingComment) {
          this.isComment = false;
          this.isFocus = false;
        }
      }, 500);
    },
    openFooterComment() {
      if (!this.isUser) {
        this.opTipsPopup("完善账号资料后即可评论！");
        setTimeout(() => {
          common_vendor.index.navigateTo({
            url: "/pages/center/means"
          });
        }, 1e3);
        return;
      }
      this.cCId = 0;
      this.cUId = 0;
      this.cIdx = -1;
      this.cI = -1;
      this.comtips = "说点什么...";
      this.isSubmittingComment = false;
      this.isComment = true;
      common_vendor.index.__f__("log", "at pages/note/details.vue:3138", "底部评论框已显示");
    },
    // 统一处理评论点击
    handleCommentClick(event, type = 0, uid = 0, cid = 0, name = "", idx = -1, i = -1) {
      common_vendor.index.__f__("log", "at pages/note/details.vue:3145", "点击评论/回复", { type, uid, cid, name, idx, i });
      if (event && event.stopPropagation) {
        event.stopPropagation();
      }
      if (!this.isUser) {
        common_vendor.index.__f__("log", "at pages/note/details.vue:3154", "用户未完善资料，检查原因:", {
          userId: this.userId,
          hasToken: !!this.$store.state.app.token,
          hasPhone: this.$store.state.app.userInfo ? !!this.$store.state.app.userInfo.phone : false
        });
        this.opTipsPopup("完善账号资料后即可评论！");
        setTimeout(() => {
          common_vendor.index.navigateTo({
            url: "/pages/center/means"
          });
        }, 1e3);
        return;
      }
      if (this.isOpeningComment)
        return;
      this.isOpeningComment = true;
      this.cIdx = idx;
      this.cI = i;
      this.cCId = cid;
      this.cUId = uid;
      if (type === 1 && name) {
        this.comtips = "回复：" + name;
      } else {
        this.comtips = "说点什么...";
      }
      this.comtext = "";
      this.isSubmittingComment = false;
      this.isComment = true;
      common_vendor.index.__f__("log", "at pages/note/details.vue:3194", "评论框已显示");
      setTimeout(() => {
        this.isOpeningComment = false;
      }, 500);
    },
    // 处理评论框失焦事件
    handleBlur() {
      common_vendor.index.__f__("log", "at pages/note/details.vue:3203", "评论框失焦");
    },
    // 预览评论图片
    previewCommentImage(image) {
      common_vendor.index.previewImage({
        urls: [image],
        current: image
      });
    },
    // 处理发送评论
    handleSendComment(commentData) {
      if (this.isSubmittingComment)
        return;
      this.isSubmittingComment = true;
      const content = commentData.content;
      const image = commentData.image;
      if (!content && !image) {
        this.isSubmittingComment = false;
        return this.opTipsPopup("表达你的态度再评论吧！");
      }
      common_vendor.index.showLoading({
        title: "发布中",
        mask: true
      });
      this.isComment = false;
      this.isFocus = false;
      this.showEmoji = false;
      const params = {
        dynamic_id: this.noteInfo.id,
        content,
        pid: this.cCId || 0,
        to_uid: this.cUId || 0
      };
      if (image) {
        params.image = image;
      }
      api_social.addComment(params).then((res) => {
        common_vendor.index.hideLoading();
        if (res.status === 200) {
          const commentData2 = res.data || this.createDefaultCommentData(content, image);
          this.processCommentSuccess(commentData2);
          this.opTipsPopup("评论成功");
          if (this.isEmpty) {
            this.isEmpty = false;
          }
        } else {
          this.opTipsPopup(res.msg || "评论失败");
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        this.opTipsPopup("评论失败，请重试");
        common_vendor.index.__f__("error", "at pages/note/details.vue:3280", "评论请求异常", err);
      }).finally(() => {
        this.isSubmittingComment = false;
        this.cCId = 0;
        this.cUId = 0;
        this.comtips = "说点什么...";
        this.comtext = "";
      });
    },
    // 创建默认评论数据（当API返回为空时）
    createDefaultCommentData(content, imageUrl) {
      var _a, _b, _c;
      const tempId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const userProvince = ((_a = this.noteInfo) == null ? void 0 : _a.province) || ((_c = (_b = this.$store.state.app) == null ? void 0 : _b.userInfo) == null ? void 0 : _c.province) || "";
      const commentData = {
        id: tempId,
        // 使用更安全的临时ID
        uid: this.userId,
        nickname: this.userNickname || "用户",
        avatar: this.userAvatar || "/static/img/avatar_default.png",
        content: content || "",
        image: imageUrl || "",
        // 评论图片URL
        create_time: this.formatDate(/* @__PURE__ */ new Date()),
        likes: 0,
        like_count: 0,
        // 兼容不同字段名
        is_like: false,
        status: 5,
        // 正常状态：5
        province: userProvince,
        delete_time: null,
        // 删除时间，用于标记是否被删除
        replies: [],
        // 初始化回复数组
        reply_count: 0,
        // 回复数量
        has_more_replies: false,
        // 是否有更多回复
        replyPage: 1,
        // 回复页码
        loading_replies: false
        // 是否正在加载回复
      };
      common_vendor.index.__f__("log", "at pages/note/details.vue:3324", "创建默认评论数据:", commentData);
      return commentData;
    },
    // 格式化日期为字符串
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },
    // 处理评论成功
    processCommentSuccess(commentData) {
      common_vendor.index.__f__("log", "at pages/note/details.vue:3341", "处理评论成功", {
        commentData,
        cIdx: this.cIdx,
        cI: this.cI,
        noteInfoComments: this.noteInfo.comments
      });
      if (!commentData) {
        common_vendor.index.__f__("error", "at pages/note/details.vue:3350", "评论数据为空，无法处理");
        return;
      }
      this.noteInfo.comments = (this.noteInfo.comments || 0) + 1;
      if (this.cIdx >= 0) {
        common_vendor.index.__f__("log", "at pages/note/details.vue:3359", "处理回复评论", this.cIdx);
        if (!this.commentList[this.cIdx].replies) {
          this.commentList[this.cIdx].replies = [];
        }
        if (this.commentList[this.cIdx].reply_count === void 0) {
          this.commentList[this.cIdx].reply_count = 0;
        }
        if (this.cUId) {
          const nickname = this.comtips.replace("回复：", "");
          commentData.reply_uid = this.cUId;
          commentData.reply_nickname = nickname;
        }
        this.commentList[this.cIdx].replies.push(commentData);
        this.commentList[this.cIdx].reply_count++;
        this.replyIndices.set(commentData.id, this.commentList[this.cIdx].replies.length - 1);
        this.$forceUpdate();
        common_vendor.index.__f__("log", "at pages/note/details.vue:3386", "回复评论处理完成", {
          回复数量: this.commentList[this.cIdx].reply_count,
          回复列表: this.commentList[this.cIdx].replies.length
        });
      } else {
        common_vendor.index.__f__("log", "at pages/note/details.vue:3393", "处理新评论");
        if (this.isEmpty || this.commentList.length === 0) {
          this.isEmpty = false;
          this.commentList = [];
          common_vendor.index.__f__("log", "at pages/note/details.vue:3398", "重置评论列表");
        }
        commentData.replies = [];
        commentData.reply_count = 0;
        this.commentList.unshift(commentData);
        this.$forceUpdate();
        common_vendor.index.__f__("log", "at pages/note/details.vue:3411", "新评论处理完成", {
          评论列表长度: this.commentList.length,
          评论总数: this.noteInfo.comments
        });
      }
    },
    // 优化页面滚动事件处理
    handlePageScroll(e) {
      if (!e || !this.isPageActive)
        return;
      const scrollTop = e.scrollTop;
      const direction = scrollTop > this.lastScrollTop ? "down" : "up";
      this.lastScrollTop = scrollTop;
      if (direction === "down" && !this.actionInProgress && scrollTop > 300 && this.loadStatus === "more") {
        this.preloadComments();
      }
    },
    // 预加载评论 - 性能优化
    preloadComments() {
      if (this.debounceTimer)
        clearTimeout(this.debounceTimer);
      this.debounceTimer = setTimeout(() => {
        if (this.page > 1 && !this.commentCache[this.page + 1]) {
          this.fetchCommentsForPage(this.page + 1, true);
        }
      }, 300);
    },
    // 统一错误处理方法
    handleError(error, defaultMessage = "操作失败") {
      var _a;
      common_vendor.index.__f__("error", "at pages/note/details.vue:3448", "错误处理:", error);
      let message = defaultMessage;
      if (typeof error === "string") {
        message = error;
      } else if (error && typeof error === "object") {
        message = error.msg || error.message || ((_a = error.data) == null ? void 0 : _a.msg) || defaultMessage;
      }
      this.opTipsPopup(message);
      return message;
    },
    // 显示提示
    opTipsPopup(msg, back = false) {
      let self = this;
      common_vendor.index.__f__("log", "at pages/note/details.vue:3469", "显示提示", msg, back);
      self.tipsTitle = msg;
      self.$refs.tipsPopup.open();
      setTimeout(() => {
        self.$refs.tipsPopup.close();
        if (back) {
          self.navBack();
        }
      }, 2e3);
    },
    // 获取回复索引 - 使用Map提高效率
    getReplyIndex(replies, replyId) {
      if (this.replyIndices.has(replyId)) {
        return this.replyIndices.get(replyId);
      }
      for (let i = 0; i < replies.length; i++) {
        if (replies[i].id === replyId) {
          this.replyIndices.set(replyId, i);
          return i;
        }
      }
      return -1;
    },
    // 排序回复 - 改进排序算法，处理可能的日期格式问题和嵌套回复
    sortRepliesByTime(replies) {
      if (!replies || !replies.length)
        return [];
      const sorted = [...replies].sort((a, b) => {
        if (a.is_nested && !b.is_nested)
          return 1;
        if (!a.is_nested && b.is_nested)
          return -1;
        let timeA, timeB;
        try {
          timeA = new Date(a.create_time).getTime();
        } catch (e) {
          timeA = 0;
        }
        try {
          timeB = new Date(b.create_time).getTime();
        } catch (e) {
          timeB = 0;
        }
        if (!isNaN(timeA) && !isNaN(timeB)) {
          return timeA - timeB;
        } else {
          return (a.create_time || "").localeCompare(b.create_time || "");
        }
      });
      return sorted;
    },
    // 销毁音频实例
    destroyAudioInstance() {
      common_vendor.index.__f__("log", "at pages/note/details.vue:3544", "销毁音频实例");
      if (this.bgAudioManager) {
        try {
          if (this.bgAudioStatus) {
            this.bgAudioManager.stop();
          }
          this.bgAudioManager = null;
          this.bgAudioStatus = false;
        } catch (e) {
          common_vendor.index.__f__("error", "at pages/note/details.vue:3563", "处理音频实例销毁过程中出错:", e);
          this.bgAudioManager = null;
          this.bgAudioStatus = false;
        }
      }
    },
    // 获取图片路径
    getImageSrc(item) {
      let url = "";
      if (typeof item === "string") {
        url = item;
        common_vendor.index.__f__("log", "at pages/note/details.vue:3577", "图片URL是字符串:", url);
      } else if (item && typeof item === "object") {
        url = item.url || item.path || item.src || item.image || "";
        common_vendor.index.__f__("log", "at pages/note/details.vue:3583", "图片URL从对象中提取:", url, "原对象:", item);
      }
      if (url) {
        if (url.startsWith("//")) {
          common_vendor.index.__f__("log", "at pages/note/details.vue:3590", "处理无协议URL:", url);
          url = "https:" + url;
        }
        if (!url.startsWith("http") && !url.startsWith("data:")) {
          if (url.startsWith("/static")) {
            common_vendor.index.__f__("log", "at pages/note/details.vue:3598", "使用静态资源路径:", url);
            return url;
          }
          if (!url.startsWith("/")) {
            common_vendor.index.__f__("log", "at pages/note/details.vue:3604", "添加前缀斜杠:", url);
            url = "/" + url;
          }
        }
      }
      const finalUrl = url || "/static/img/default_img.png";
      common_vendor.index.__f__("log", "at pages/note/details.vue:3612", "最终图片URL:", finalUrl);
      return finalUrl;
    },
    // 获取分享图片URL
    getShareImageUrl() {
      if (this.noteInfo.type == 2) {
        if (this.noteInfo.images && this.noteInfo.images.length > 0) {
          const firstImage = this.noteInfo.images[0];
          if (typeof firstImage === "string") {
            return firstImage;
          } else if (firstImage.url) {
            return firstImage.url;
          }
        }
        return "";
      } else if (this.noteInfo.type == 3) {
        return this.noteInfo.video_cover || "";
      } else if (this.noteInfo.type == 4) {
        return this.noteInfo.audio_cover || "";
      }
      return "";
    },
    // 显示表情面板
    toggleEmoji() {
      this.isFocus = false;
      setTimeout(() => {
        this.showEmoji = !this.showEmoji;
        if (this.showEmoji) {
          try {
            const recentEmojiStr = common_vendor.index.getStorageSync("recent_emojis");
            if (recentEmojiStr) {
              this.recentEmojis = JSON.parse(recentEmojiStr);
            }
          } catch (e) {
            common_vendor.index.__f__("error", "at pages/note/details.vue:3647", "读取最近使用表情失败", e);
          }
        }
      }, 100);
    },
    // 选择表情
    selectEmoji(emoji) {
      this.comtext += emoji.alt;
      if (!this.recentEmojis.find((item) => item.url === emoji.url)) {
        this.recentEmojis.unshift(emoji);
        if (this.recentEmojis.length > 20) {
          this.recentEmojis.pop();
        }
      }
      try {
        common_vendor.index.setStorageSync("recent_emojis", JSON.stringify(this.recentEmojis));
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/details.vue:3669", "保存表情记录失败", e);
      }
    },
    // 选择GIF表情
    selectGif(gif) {
      common_vendor.index.__f__("log", "at pages/note/details.vue:3675", "选择了GIF表情", gif);
      common_vendor.index.showToast({
        title: "GIF表情功能开发中",
        icon: "none"
      });
    },
    // 处理评论提交
    handleCommentSubmit(commentData) {
      if (this.isSubmittingComment)
        return;
      this.isSubmittingComment = true;
      const content = commentData.content.trim();
      const image = commentData.image;
      if (!content && !image) {
        this.isSubmittingComment = false;
        return;
      }
      const commentParams = {
        type: 0,
        // 评论类型：0-动态
        target_id: this.noteInfo.id,
        content,
        // 保留原始表情格式如[微笑]
        reply_id: this.cCId || 0,
        // 父评论ID（后端会自动处理parent_id）
        image: image || ""
        // 图片字段
      };
      common_vendor.index.__f__("log", "at pages/note/details.vue:3706", "提交评论参数:", commentParams);
      api_social.addComment(commentParams).then((res) => {
        common_vendor.index.__f__("log", "at pages/note/details.vue:3711", "评论提交响应:", res);
        try {
          if (res.status === 200) {
            this.noteInfo.comments = (this.noteInfo.comments || 0) + 1;
            let commentData2 = res.data || this.createDefaultCommentData(content, image);
            common_vendor.index.__f__("log", "at pages/note/details.vue:3721", "使用的评论数据:", commentData2);
            if (this.cCId) {
              const parentComment = this.commentList.find((item) => item.id === this.cCId);
              if (parentComment) {
                if (!parentComment.replies) {
                  parentComment.replies = [];
                }
                parentComment.replies.unshift({
                  id: commentData2.id,
                  pid: this.cCId,
                  uid: this.userId,
                  nickname: this.userNickname,
                  avatar: this.userAvatar,
                  content,
                  image,
                  // 修改为image字段，与后端接口一致
                  create_time: "刚刚",
                  province: this.noteInfo.province || "",
                  likes: 0,
                  is_like: false,
                  status: 5,
                  reply_uid: this.cUId,
                  reply_content: this.commentSource && this.commentSource.nickname ? this.commentSource.nickname + ": " + (this.commentSource.content || "") : ""
                });
                parentComment.reply_count = (parentComment.reply_count || 0) + 1;
              }
            } else {
              this.commentList.unshift({
                id: commentData2.id,
                uid: this.userId,
                nickname: this.userNickname,
                avatar: this.userAvatar,
                content,
                image,
                // 修改为image字段，与后端接口一致
                create_time: "刚刚",
                province: this.noteInfo.province || "",
                likes: 0,
                is_like: false,
                status: 5,
                replies: [],
                reply_count: 0
              });
            }
            this.closeComment();
            this.opTipsPopup("评论成功");
            if (this.isEmpty) {
              this.isEmpty = false;
            }
          } else {
            this.opTipsPopup(res.msg || "评论失败，请重试");
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/note/details.vue:3788", "处理评论响应时出错:", error);
          this.opTipsPopup("评论处理失败，请重试");
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/note/details.vue:3794", "评论提交失败:", err);
        this.handleError(err, "评论失败，请重试");
      }).finally(() => {
        this.isSubmittingComment = false;
      });
    },
    // 关闭评论输入框
    closeComment() {
      if (this.isSubmittingComment) {
        return;
      }
      if (this.commentBlurTimer) {
        clearTimeout(this.commentBlurTimer);
        this.commentBlurTimer = null;
      }
      this.isComment = false;
      this.isFocus = false;
      this.comtext = "";
      this.commentImage = null;
    },
    // 关闭@用户选择弹窗
    closeAtUsers() {
      this.$refs.atUserPopup.close();
    },
    // 这些方法已移至评论输入组件中
    // 处理表情解析
    parseEmojiContent(content) {
      if (!content)
        return "";
      try {
        let replacedStr = content.replace(/\[([^(\]|\[)]*)\]/g, (item, index) => {
          const emoji = components_emojiPanel_sina.sinaEmoji.find((e) => e.phrase === item);
          if (emoji) {
            return `<img src="${emoji.url}" style="width: 28rpx; height: 28rpx; vertical-align: middle; display:inline-block;">`;
          }
          return item;
        });
        return '<span style="display:inline;">' + replacedStr + "</span>";
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/note/details.vue:3852", "解析表情出错:", error);
        return '<span style="display:inline;">' + content + "</span>";
      }
    },
    // 解析评论中的表情
    parseCommentEmoji(comment) {
      if (!comment || !comment.content)
        return comment;
      let newComment = JSON.parse(JSON.stringify(comment));
      newComment.parsedContent = this.parseEmojiContent(newComment.content);
      return newComment;
    }
  },
  // 触底加载更多
  onReachBottom() {
    if (!this.isEmpty && this.commentList.length > 0 && this.loadStatus === "more" && this.isThrottling && !this.actionInProgress) {
      this.page = this.page + 1;
      this.loadStatus = "loading";
      this.getCommentList();
    }
  },
  // 分享设置
  onShareAppMessage: function() {
    return {
      title: this.noteInfo.content,
      imageUrl: this.getShareImageUrl(),
      path: "/pages/note/details?id=" + this.noteInfo.id
    };
  },
  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: this.noteInfo.content,
      imageUrl: this.getShareImageUrl(),
      query: "id=" + this.noteInfo.id
    };
  },
  // 微信小程序页面卸载
  onUnload() {
    common_vendor.index.__f__("log", "at pages/note/details.vue:3904", "页面卸载，释放资源");
    this.cleanupResources();
  },
  // 微信小程序页面隐藏
  onHide() {
    common_vendor.index.__f__("log", "at pages/note/details.vue:3910", "页面隐藏");
    this.isPageActive = false;
    if (this.isAudioNote && this.bgAudioManager && this.bgAudioStatus) {
      this.pauseAudio();
    }
  }
};
if (!Array) {
  const _component_play = common_vendor.resolveComponent("play");
  const _component_lazy_image = common_vendor.resolveComponent("lazy-image");
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  const _component_comment_input = common_vendor.resolveComponent("comment-input");
  const _component_SharePanel = common_vendor.resolveComponent("SharePanel");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_component_play + _component_lazy_image + _easycom_uni_load_more2 + _component_comment_input + _component_SharePanel + _easycom_uni_popup2)();
}
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_load_more + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_2$4,
    b: common_vendor.o((...args) => $options.navBack && $options.navBack(...args)),
    c: $data.noteInfo.user_info.avatar,
    d: common_vendor.t($data.noteInfo.user_info.nickname),
    e: $data.noteInfo.location_name
  }, $data.noteInfo.location_name ? {
    f: common_assets._imports_5$2,
    g: common_vendor.t($data.noteInfo.location_name),
    h: common_vendor.o((...args) => $options.openLocationClick && $options.openLocationClick(...args))
  } : {}, {
    i: "user/details?id=" + $data.noteInfo.uid,
    j: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    k: $data.noteInfo.uid && $data.noteInfo.uid != $data.userId
  }, $data.noteInfo.uid && $data.noteInfo.uid != $data.userId ? {
    l: common_vendor.t($data.isFollowing ? "已关注" : "＋关注"),
    m: common_vendor.n($data.isFollowing ? "active" : ""),
    n: common_vendor.o((...args) => $options.followUser && $options.followUser(...args))
  } : {}, {
    o: $data.titleBarHeight + "px",
    p: $data.statusBarHeight + "px",
    q: $options.isImageNote && ($data.noteInfo.images && $data.noteInfo.images.length > 0 || $data.noteInfo.imgs && $data.noteInfo.imgs.length > 0)
  }, $options.isImageNote && ($data.noteInfo.images && $data.noteInfo.images.length > 0 || $data.noteInfo.imgs && $data.noteInfo.imgs.length > 0) ? common_vendor.e({
    r: common_vendor.f($data.noteInfo.type == 1 ? $data.noteInfo.imgs : $data.noteInfo.images, (item, index, i0) => {
      return {
        a: $options.getImageSrc(item),
        b: index,
        c: index,
        d: common_vendor.o((...args) => $options.swiperClick && $options.swiperClick(...args), index)
      };
    }),
    s: $options.isHigh(),
    t: common_vendor.o((...args) => $options.swiperChange && $options.swiperChange(...args)),
    v: $data.noteInfo.images && $data.noteInfo.images.length > 1 || $data.noteInfo.imgs && $data.noteInfo.imgs.length > 1
  }, $data.noteInfo.images && $data.noteInfo.images.length > 1 || $data.noteInfo.imgs && $data.noteInfo.imgs.length > 1 ? {
    w: common_vendor.t($data.swiperIdx + 1),
    x: common_vendor.t($data.noteInfo.type == 1 ? $data.noteInfo.imgs.length : $data.noteInfo.images.length),
    y: "calc(" + ($data.statusBarHeight + $data.titleBarHeight) + "px + 20rpx)"
  } : {}, {
    z: $data.noteInfo.images && $data.noteInfo.images.length > 1 || $data.noteInfo.imgs && $data.noteInfo.imgs.length > 1
  }, $data.noteInfo.images && $data.noteInfo.images.length > 1 || $data.noteInfo.imgs && $data.noteInfo.imgs.length > 1 ? {
    A: common_vendor.f($data.noteInfo.type == 1 ? $data.noteInfo.imgs : $data.noteInfo.images, (item, index, i0) => {
      return {
        a: index,
        b: common_vendor.n(index == $data.swiperIdx ? "active" : "")
      };
    }),
    B: "calc(100% / " + ($data.noteInfo.type == 1 ? $data.noteInfo.imgs.length : $data.noteInfo.images.length) + " - 5px)"
  } : {}) : {}, {
    C: $options.isVideoNote && $data.noteInfo.video
  }, $options.isVideoNote && $data.noteInfo.video ? {
    D: $data.noteInfo.video,
    E: $data.noteInfo.video_cover
  } : {}, {
    F: $options.isAudioNote
  }, $options.isAudioNote ? common_vendor.e({
    G: $data.noteInfo.audio_cover || "/static/img/audio_default_cover.png",
    H: $data.noteInfo.audio_cover || "/static/img/audio_default_cover.png",
    I: common_assets._imports_2$5,
    J: common_vendor.t($data.noteInfo.audio_title || "未知音频"),
    K: common_vendor.t($data.noteInfo.content || "无描述信息"),
    L: $data.noteInfo.audio
  }, $data.noteInfo.audio ? common_vendor.e({
    M: $data.bgAudioStatus
  }, $data.bgAudioStatus ? {
    N: common_vendor.p({
      color: "#fff"
    })
  } : {
    O: common_assets._imports_3$11
  }, {
    P: common_vendor.o((...args) => $options.audioBgClick && $options.audioBgClick(...args))
  }) : {}) : {}, {
    Q: common_vendor.t($data.noteInfo.content),
    R: $data.noteInfo.topic_info && $data.noteInfo.topic_info.length > 0 || $options.hasCircle() || $data.noteInfo.goods_info || $data.noteInfo.activity_name
  }, $data.noteInfo.topic_info && $data.noteInfo.topic_info.length > 0 || $options.hasCircle() || $data.noteInfo.goods_info || $data.noteInfo.activity_name ? common_vendor.e({
    S: $data.noteInfo.location_name
  }, $data.noteInfo.location_name ? {
    T: common_assets._imports_5$2,
    U: common_vendor.t($data.noteInfo.location_name),
    V: common_vendor.o((...args) => $options.openLocationClick && $options.openLocationClick(...args))
  } : {}, {
    W: common_vendor.f($data.noteInfo.topic_info, (topic, topicIndex, i0) => {
      return {
        a: topic.icon || "/static/img/topic_icon.png",
        b: common_vendor.t(topic.title),
        c: "topic-" + topicIndex,
        d: "topic/details?id=" + topic.id,
        e: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), "topic-" + topicIndex)
      };
    }),
    X: $options.hasCircle()
  }, $options.hasCircle() ? {
    Y: $options.getCircleAvatar(),
    Z: common_vendor.t($options.getCircleName()),
    aa: "note/circle?id=" + $options.getCircleId(),
    ab: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args))
  } : {}, {
    ac: $data.noteInfo.activity_name
  }, $data.noteInfo.activity_name ? {
    ad: $data.noteInfo.activity_img,
    ae: common_vendor.t($data.noteInfo.activity_name),
    af: "activity/details?id=" + $data.noteInfo.activity_id,
    ag: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args))
  } : {}, {
    ah: $data.noteInfo.goods_info
  }, $data.noteInfo.goods_info ? {
    ai: $data.noteInfo.goods_info.image,
    aj: common_vendor.t($data.noteInfo.goods_info.name || $data.noteInfo.goods_info.store_name),
    ak: "goods/details?id=" + $data.noteInfo.goods_info.id,
    al: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args))
  } : {}) : {}, {
    am: $data.noteInfo.vote_info
  }, $data.noteInfo.vote_info ? common_vendor.e({
    an: common_assets._imports_3$9,
    ao: common_vendor.t($data.noteInfo.vote_info.vote.title),
    ap: !$data.noteInfo.vote_info.user_selected
  }, !$data.noteInfo.vote_info.user_selected ? {
    aq: common_vendor.f($data.noteInfo.vote_info.options, (option, idx, i0) => {
      return {
        a: common_vendor.t(option.option_text),
        b: option.id || idx,
        c: common_vendor.o(($event) => $options.onVote(option.id), option.id || idx)
      };
    })
  } : {
    ar: common_vendor.f($data.noteInfo.vote_info.options, (option, idx, i0) => {
      return common_vendor.e({
        a: $data.noteInfo.vote_info.user_selected === option.id
      }, $data.noteInfo.vote_info.user_selected === option.id ? {
        b: common_assets._imports_4$2
      } : {}, {
        c: common_vendor.t(option.option_text),
        d: common_vendor.t(option.percent),
        e: option.percent + "%",
        f: $data.noteInfo.vote_info.user_selected === option.id ? "#ffd600" : "#eaeaea",
        g: option.id || idx
      });
    })
  }, {
    as: common_vendor.t($options.votePeopleText)
  }) : {}, {
    at: common_vendor.t($data.noteInfo.create_time),
    av: common_vendor.t($data.noteInfo.province),
    aw: common_vendor.t($data.noteInfo.views),
    ax: common_vendor.o(($event) => $options.shareClick(true)),
    ay: common_assets._imports_3$10,
    az: $data.statusBarHeight + $data.titleBarHeight + "px",
    aA: common_vendor.t($data.noteInfo.comments ? "评论 " + $data.noteInfo.comments : "暂无评论"),
    aB: $data.cType == 0 ? "6rpx" : "74rpx",
    aC: $data.cType == 0 ? "#000" : "#999",
    aD: common_vendor.o(($event) => $options.commentClick(0)),
    aE: $data.cType == 1 ? "#000" : "#999",
    aF: common_vendor.o(($event) => $options.commentClick(1)),
    aG: $data.isEmpty
  }, $data.isEmpty ? {
    aH: common_assets._imports_3$1
  } : {
    aI: common_vendor.f($data.commentList, (item, index, i0) => {
      return common_vendor.e({
        a: "1af14847-1-" + i0,
        b: common_vendor.p({
          src: item.avatar || "/static/img/avatar_default.png"
        }),
        c: "user/details?id=" + item.uid,
        d: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), index),
        e: $data.noteInfo.uid == item.uid
      }, $data.noteInfo.uid == item.uid ? {} : $data.userId == item.uid ? {} : {}, {
        f: $data.userId == item.uid,
        g: common_vendor.t(item.nickname || "用户"),
        h: !item.delete_time
      }, !item.delete_time ? common_vendor.e({
        i: item.is_like == 1 ? "/static/img/dz1.png" : "/static/img/dz.png",
        j: item.likes > 0
      }, item.likes > 0 ? {
        k: common_vendor.t(item.likes)
      } : {}, {
        l: common_vendor.o(($event) => $options.toggleCommentLike(item.id, item.is_like), index)
      }) : {}, {
        m: "user/details?id=" + item.uid,
        n: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), index),
        o: !item.delete_time
      }, !item.delete_time ? {
        p: $options.parseEmojiContent(item.content),
        q: common_vendor.o((...args) => $options.onEmojiClick && $options.onEmojiClick(...args), index)
      } : {}, {
        r: item.image && !item.delete_time
      }, item.image && !item.delete_time ? {
        s: item.image,
        t: common_vendor.o(($event) => $options.previewCommentImage(item.image), index)
      } : {}, {
        v: common_vendor.n((item.status != 5 || item.delete_time) && "db"),
        w: common_vendor.o(($event) => $options.handleCommentClick($event, 1, item.uid, item.id, item.nickname || "用户", index, -1), index),
        x: common_vendor.t(item.create_time),
        y: common_vendor.t(item.province || ""),
        z: !item.delete_time
      }, !item.delete_time ? {
        A: common_vendor.o(($event) => $options.handleCommentClick($event, 1, item.uid, item.id, item.nickname || "用户", index, -1), index)
      } : {}, {
        B: $data.userId == item.uid && item.status == 5 && !item.delete_time
      }, $data.userId == item.uid && item.status == 5 && !item.delete_time ? {
        C: common_vendor.o((...args) => $options.delComment && $options.delComment(...args), index),
        D: item.id,
        E: index
      } : {}, {
        F: item.replies && item.replies.length > 0
      }, item.replies && item.replies.length > 0 ? {
        G: common_vendor.f($options.sortRepliesByTime(item.replies), (v, i, i1) => {
          return common_vendor.e({
            a: "1af14847-2-" + i0 + "-" + i1,
            b: common_vendor.p({
              src: v.avatar || "/static/img/avatar_default.png"
            }),
            c: "user/details?id=" + v.uid,
            d: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), i),
            e: $data.noteInfo.uid == v.uid
          }, $data.noteInfo.uid == v.uid ? {} : $data.userId == v.uid ? {} : {}, {
            f: $data.userId == v.uid,
            g: common_vendor.t(v.nickname || "用户"),
            h: "user/details?id=" + v.uid,
            i: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), i),
            j: v.reply_id && v.reply_id !== item.id
          }, v.reply_id && v.reply_id !== item.id ? {
            k: common_vendor.t(v.reply_nickname || "用户"),
            l: "user/details?id=" + v.reply_uid,
            m: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), i)
          } : {}, {
            n: !v.delete_time
          }, !v.delete_time ? common_vendor.e({
            o: v.is_like == 1 ? "/static/img/dz1.png" : "/static/img/dz.png",
            p: v.likes > 0
          }, v.likes > 0 ? {
            q: common_vendor.t(v.likes)
          } : {}, {
            r: common_vendor.o(($event) => $options.toggleCommentLike(v.id, v.is_like), i)
          }) : {}, {
            s: !v.delete_time && !v.is_system_message
          }, !v.delete_time && !v.is_system_message ? {
            t: $options.parseEmojiContent(v.content),
            v: common_vendor.o((...args) => $options.onEmojiClick && $options.onEmojiClick(...args), i)
          } : v.delete_time ? {} : v.is_system_message ? {
            y: common_vendor.t(v.content)
          } : {}, {
            w: v.delete_time,
            x: v.is_system_message,
            z: v.image && !v.delete_time
          }, v.image && !v.delete_time ? {
            A: v.image,
            B: common_vendor.o(($event) => $options.previewCommentImage(v.image), i)
          } : {}, {
            C: common_vendor.n((v.status != 5 || v.delete_time) && "db"),
            D: common_vendor.n(v.is_system_message && "system-message"),
            E: common_vendor.o(($event) => $options.handleCommentClick($event, 1, v.uid, item.id, v.nickname || "用户", index, $options.getReplyIndex(item.replies, v.id)), i),
            F: common_vendor.t(v.create_time),
            G: common_vendor.t(v.province || ""),
            H: !v.delete_time
          }, !v.delete_time ? {
            I: common_vendor.o(($event) => $options.handleCommentClick($event, 1, v.uid, item.id, v.nickname || "用户", index, $options.getReplyIndex(item.replies, v.id)), i)
          } : {}, {
            J: $data.userId == v.uid && v.status == 5 && !v.delete_time
          }, $data.userId == v.uid && v.status == 5 && !v.delete_time ? {
            K: common_vendor.o((...args) => $options.delComment && $options.delComment(...args), i),
            L: v.id,
            M: index,
            N: $options.getReplyIndex(item.replies, v.id)
          } : {}, {
            O: i
          });
        })
      } : {}, {
        H: item.reply_count > (item.replies ? item.replies.length : 0)
      }, item.reply_count > (item.replies ? item.replies.length : 0) ? common_vendor.e({
        I: item.loading_replies
      }, item.loading_replies ? {
        J: common_assets._imports_10$1
      } : common_vendor.e({
        K: !item.replies || item.replies.length === 0
      }, !item.replies || item.replies.length === 0 ? {
        L: common_vendor.t(item.reply_count)
      } : item.has_more_replies ? {
        N: common_vendor.t(item.replies.length),
        O: common_vendor.t(item.reply_count)
      } : {}, {
        M: item.has_more_replies
      }), {
        P: item.id,
        Q: index,
        R: common_vendor.o((...args) => $options.loadAllReplies && $options.loadAllReplies(...args), index)
      }) : {}, {
        S: index
      });
    })
  }, {
    aJ: $data.loadStatus != "no-more"
  }, $data.loadStatus != "no-more" ? {
    aK: common_vendor.p({
      status: $data.loadStatus
    })
  } : {}, {
    aL: !$data.isUser
  }, !$data.isUser ? {
    aM: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args))
  } : {
    aN: $data.userAvatar || "/static/img/avatar_default.png",
    aO: common_vendor.t($data.comtext ? $data.comtext : $data.comtips),
    aP: common_vendor.o(($event) => $options.handleCommentClick($event))
  }, {
    aQ: common_assets._imports_7$2,
    aR: common_vendor.t($data.noteInfo.comments ? $data.noteInfo.comments : ""),
    aS: common_vendor.o(($event) => $options.handleCommentClick($event)),
    aT: $data.noteInfo.is_like == 1
  }, $data.noteInfo.is_like == 1 ? {
    aU: common_assets._imports_8$2
  } : {
    aV: common_assets._imports_9$2
  }, {
    aW: $data.noteInfo.likes < 1e4
  }, $data.noteInfo.likes < 1e4 ? {
    aX: common_vendor.t($data.noteInfo.likes ? $data.noteInfo.likes : "")
  } : {
    aY: common_vendor.t($data.noteInfo.like_count_str)
  }, {
    aZ: common_vendor.o((...args) => $options.likeDynamic && $options.likeDynamic(...args)),
    ba: common_assets._imports_10,
    bb: common_vendor.o(($event) => $options.shareClick(true)),
    bc: $data.isComment
  }, $data.isComment ? {
    bd: common_vendor.o((...args) => $options.closeComment && $options.closeComment(...args))
  } : {}, {
    be: common_vendor.sr("commentInput", "1af14847-4"),
    bf: common_vendor.o($options.closeComment),
    bg: common_vendor.o($options.handleCommentSubmit),
    bh: common_vendor.p({
      show: $data.isComment,
      placeholder: $data.comtips,
      focus: $data.isFocus
    }),
    bi: common_vendor.o($options.closeShare),
    bj: common_vendor.o($options.handleEdit),
    bk: common_vendor.o($options.handleDelete),
    bl: common_vendor.o($options.handleReport),
    bm: common_vendor.o($options.handleDislike),
    bn: common_vendor.p({
      show: $data.isShareVisible,
      noteInfo: $data.noteInfo,
      userId: $data.userId
    }),
    bo: common_vendor.t($data.tipsTitle),
    bp: common_vendor.sr("tipsPopup", "1af14847-6"),
    bq: common_vendor.p({
      type: "top",
      ["mask-background-color"]: "rgba(0, 0, 0, 0)"
    }),
    br: $data.previewEmojiData
  }, $data.previewEmojiData ? {
    bs: $data.previewEmojiData.url,
    bt: common_vendor.o(($event) => $data.previewEmojiData = null)
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 7;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/note/details.js.map
