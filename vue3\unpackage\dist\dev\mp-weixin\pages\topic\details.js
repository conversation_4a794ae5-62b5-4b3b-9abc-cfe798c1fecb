"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_social = require("../../api/social.js");
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (lazyImage + waterfall + cardGg + shareComponent + _easycom_uni_popup)();
}
const lazyImage = () => "../../components/lazyImage/lazyImage.js";
const waterfall = () => "../../components/waterfall/waterfall.js";
const cardGg = () => "../../components/card-gg/card-gg.js";
const shareComponent = () => "../../components/share/index.js";
const _sfc_main = /* @__PURE__ */ Object.assign({
  name: "TopicDetails"
}, {
  __name: "details",
  setup(__props) {
    const store = common_vendor.useStore();
    const statusBarHeight = common_vendor.computed(() => store.state.statusBarHeight || 20);
    const titleBarHeight = common_vendor.computed(() => store.state.titleBarHeight || 44);
    const userInfo = common_vendor.computed(() => store.state.app.userInfo);
    const userId = common_vendor.computed(() => {
      var _a;
      return ((_a = userInfo.value) == null ? void 0 : _a.uid) || 0;
    });
    const navbarTrans = common_vendor.ref(0);
    const barList = common_vendor.ref(["最新", "热门"]);
    const barIdx = common_vendor.ref(0);
    const topicId = common_vendor.ref(0);
    const topicInfo = common_vendor.reactive({
      id: 0,
      title: "",
      description: "",
      cover_image: "",
      post_count: 0,
      view_count: 0
    });
    const list = common_vendor.ref([]);
    const page = common_vendor.ref(1);
    const limit = common_vendor.ref(10);
    const totalCount = common_vendor.ref(0);
    const isThrottling = common_vendor.ref(true);
    const isEmpty = common_vendor.ref(false);
    const loadStatus = common_vendor.ref("more");
    const tipsTitle = common_vendor.ref("");
    const isWaterfall = common_vendor.ref(false);
    const showLoading = common_vendor.ref(true);
    const loadingTimer = common_vendor.ref(null);
    const debounceTimer = common_vendor.ref(null);
    const showShare = common_vendor.ref(false);
    const topicShareInfo = common_vendor.computed(() => {
      return {
        id: topicInfo.id || topicId.value,
        title: topicInfo.title,
        content: topicInfo.description,
        image: topicInfo.cover_image,
        type: "topic",
        share_url: `/pages/topic/details?id=${topicId.value}`
      };
    });
    const initPage = (options) => {
      common_vendor.index.showShareMenu();
      if (options && options.id) {
        topicId.value = parseInt(options.id);
        loadTopicDetail();
        loadTopicDynamicList(true);
      } else {
        common_vendor.index.showToast({
          title: "话题ID不能为空",
          icon: "none"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }
    };
    const previewTopicImage = () => {
      if (topicInfo.cover_image) {
        common_vendor.index.previewImage({
          current: topicInfo.cover_image,
          urls: [topicInfo.cover_image]
        });
      }
    };
    const barClick = (e) => {
      const idx = parseInt(e.currentTarget.dataset.idx);
      if (barIdx.value === idx || !isThrottling.value)
        return;
      barIdx.value = idx;
      page.value = 1;
      list.value = [];
      isEmpty.value = false;
      loadStatus.value = "more";
      loadTopicDynamicList(true);
    };
    const loadTopicDetail = async () => {
      showLoading.value = true;
      try {
        const res = await api_social.getTopicDetail(topicId.value);
        showLoading.value = false;
        if (res.status === 200 && res.data) {
          Object.assign(topicInfo, {
            ...res.data,
            id: res.data.id || topicId.value
          });
        } else {
          common_vendor.index.showToast({
            title: res.msg || "获取话题详情失败",
            icon: "none"
          });
        }
      } catch (error) {
        showLoading.value = false;
        try {
          const res = await api_social.getTopicList({
            page: 1,
            limit: 50,
            keyword: ""
          });
          if (res.status === 200 && res.data && res.data.list) {
            const topic = res.data.list.find((item) => item.id == topicId.value);
            if (topic) {
              Object.assign(topicInfo, {
                ...topic,
                id: topic.id || topicId.value
              });
            } else {
              common_vendor.index.showToast({
                title: "话题不存在",
                icon: "none"
              });
            }
          } else {
            common_vendor.index.showToast({
              title: res.msg || "获取话题详情失败",
              icon: "none"
            });
          }
        } catch (listErr) {
          showLoading.value = false;
          common_vendor.index.showToast({
            title: "网络错误，请稍后重试",
            icon: "none"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 2e3);
        }
      }
    };
    const loadTopicDynamicList = async (isRefresh = false) => {
      if (!isThrottling.value)
        return;
      isThrottling.value = false;
      showLoading.value = true;
      if (loadingTimer.value) {
        clearTimeout(loadingTimer.value);
        loadingTimer.value = null;
      }
      loadingTimer.value = setTimeout(() => {
        showLoading.value = false;
      }, 800);
      try {
        const res = await api_social.getTopicDynamicList({
          topic_id: topicId.value,
          page: page.value,
          limit: limit.value,
          sort: barIdx.value === 0 ? "latest" : "hot"
        });
        if (loadingTimer.value) {
          clearTimeout(loadingTimer.value);
          loadingTimer.value = null;
        }
        showLoading.value = false;
        if (res.status === 200 && res.data) {
          const data = res.data;
          const newList = data.list || [];
          if (isRefresh || page.value === 1) {
            list.value = newList;
          } else {
            list.value = [...list.value, ...newList];
          }
          totalCount.value = data.total || 0;
          isEmpty.value = list.value.length === 0;
          const hasMore = page.value * limit.value < totalCount.value;
          loadStatus.value = hasMore ? "more" : "noMore";
          isThrottling.value = true;
          if (isRefresh) {
            common_vendor.index.stopPullDownRefresh();
          }
        } else {
          handleError(res.msg || "获取话题动态失败");
        }
      } catch (err) {
        if (loadingTimer.value) {
          clearTimeout(loadingTimer.value);
          loadingTimer.value = null;
        }
        showLoading.value = false;
        isThrottling.value = true;
        if (isRefresh) {
          common_vendor.index.stopPullDownRefresh();
        }
        if (page.value === 1) {
          isEmpty.value = true;
        }
        handleError(err, "获取话题动态失败，请稍后重试");
      }
    };
    const handleError = (error, defaultMessage = "操作失败") => {
      common_vendor.index.__f__("error", "at pages/topic/details.vue:409", "错误详情:", error);
      const message = (error == null ? void 0 : error.msg) || (error == null ? void 0 : error.message) || defaultMessage;
      common_vendor.index.showToast({
        title: message,
        icon: "none",
        duration: 2e3
      });
    };
    const navBack = () => {
      common_vendor.index.navigateBack();
    };
    const likeClick = (data) => {
      if (data && data.idx !== void 0) {
        list.value[data.idx] = data.item;
      }
    };
    const onCardUpdate = (data) => {
      if (data && data.idx !== void 0) {
        list.value[data.idx] = data.item;
      }
    };
    const closeShare = () => {
      showShare.value = false;
    };
    const handleShare = (type) => {
      common_vendor.index.__f__("log", "at pages/topic/details.vue:444", "分享话题:", type, topicInfo);
      common_vendor.index.showToast({
        title: "分享成功",
        icon: "success"
      });
      closeShare();
    };
    const handleDislike = () => {
      common_vendor.index.__f__("log", "at pages/topic/details.vue:454", "不喜欢话题:", topicInfo);
      common_vendor.index.showToast({
        title: "已标记为不感兴趣",
        icon: "success"
      });
      closeShare();
    };
    const handleReport = () => {
      common_vendor.index.__f__("log", "at pages/topic/details.vue:464", "举报话题:", topicInfo);
      common_vendor.index.showToast({
        title: "举报成功",
        icon: "success"
      });
      closeShare();
    };
    common_vendor.onLoad((options) => {
      initPage(options);
    });
    common_vendor.onPullDownRefresh(() => {
      page.value = 1;
      list.value = [];
      isEmpty.value = false;
      loadStatus.value = "more";
      loadTopicDynamicList(true);
    });
    common_vendor.onReachBottom(() => {
      if (loadStatus.value === "noMore" || !isThrottling.value)
        return;
      page.value++;
      loadStatus.value = "loading";
      loadTopicDynamicList();
    });
    common_vendor.onPageScroll((e) => {
      const scrollTop = e.scrollTop > 150 ? 150 : e.scrollTop;
      navbarTrans.value = scrollTop / 150;
    });
    common_vendor.onUnmounted(() => {
      if (loadingTimer.value) {
        clearTimeout(loadingTimer.value);
        loadingTimer.value = null;
      }
      if (debounceTimer.value) {
        clearTimeout(debounceTimer.value);
        debounceTimer.value = null;
      }
    });
    common_vendor.onShareAppMessage(() => {
      return {
        title: topicInfo.title || "话题分享",
        path: `/pages/topic/details?id=${topicId.value}`,
        imageUrl: topicInfo.cover_image || "/static/img/avatar.png"
      };
    });
    common_vendor.onShareTimeline(() => {
      return {
        title: topicInfo.title || "话题分享",
        query: "id=" + topicId.value,
        imageUrl: topicInfo.cover_image || "/static/img/avatar.png"
      };
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: titleBarHeight.value + "px",
        c: common_vendor.o(navBack),
        d: navbarTrans.value == 1
      }, navbarTrans.value == 1 ? {
        e: common_vendor.t(topicInfo.title || "话题详情")
      } : {}, {
        f: statusBarHeight.value + "px",
        g: "rgba(255, 255, 255," + navbarTrans.value + ")",
        h: topicInfo.cover_image
      }, topicInfo.cover_image ? {
        i: common_vendor.o(previewTopicImage),
        j: common_vendor.p({
          src: topicInfo.cover_image
        })
      } : {}, {
        k: common_assets._imports_1$20,
        l: !showLoading.value
      }, !showLoading.value ? {
        m: common_vendor.t(topicInfo.title || "话题加载中...")
      } : {}, {
        n: common_assets._imports_2$7,
        o: common_vendor.o(($event) => _ctx.shareClick(true)),
        p: !showLoading.value && topicInfo.description
      }, !showLoading.value && topicInfo.description ? {
        q: common_vendor.t(topicInfo.description)
      } : {}, {
        r: statusBarHeight.value + titleBarHeight.value + 160 + "rpx",
        s: common_vendor.f(barList.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item),
            b: index == barIdx.value ? "#000" : "#999",
            c: index == barIdx.value ? "28rpx" : "26rpx",
            d: common_vendor.n({
              active: index == barIdx.value
            }),
            e: index,
            f: common_vendor.o(barClick, index),
            g: index
          };
        }),
        t: !showLoading.value
      }, !showLoading.value ? {
        v: common_vendor.t(topicInfo.post_count || 0)
      } : {}, {
        w: !showLoading.value
      }, !showLoading.value ? {} : {}, {
        x: !showLoading.value
      }, !showLoading.value ? {
        y: common_vendor.t(topicInfo.view_count || 0)
      } : {}, {
        z: showLoading.value
      }, showLoading.value ? {} : {}, {
        A: showLoading.value
      }, showLoading.value ? {} : {}, {
        B: isEmpty.value
      }, isEmpty.value ? {
        C: common_assets._imports_3$1,
        D: common_vendor.t(topicInfo.title || "该话题")
      } : common_vendor.e({
        E: isWaterfall.value
      }, isWaterfall.value ? {
        F: common_vendor.p({
          note: list.value,
          page: page.value
        })
      } : {
        G: common_vendor.f(list.value, (item, index, i0) => {
          return {
            a: index,
            b: common_vendor.o(likeClick, index),
            c: common_vendor.o(onCardUpdate, index),
            d: "57f20d30-2-" + i0,
            e: common_vendor.p({
              item,
              idx: index
            })
          };
        })
      }, {
        H: common_vendor.n(isWaterfall.value ? "dynamic-box" : "")
      }), {
        I: list.value.length > 0 && loadStatus.value === "noMore"
      }, list.value.length > 0 && loadStatus.value === "noMore" ? {} : {}, {
        J: common_vendor.o(closeShare),
        K: common_vendor.o(handleShare),
        L: common_vendor.o(handleDislike),
        M: common_vendor.o(handleReport),
        N: common_vendor.p({
          show: showShare.value,
          noteInfo: topicShareInfo.value,
          userId: userId.value
        }),
        O: common_vendor.t(tipsTitle.value),
        P: common_vendor.sr("tipsPopup", "57f20d30-4"),
        Q: common_vendor.p({
          type: "top",
          ["mask-background-color"]: "rgba(0, 0, 0, 0)"
        })
      });
    };
  }
});
_sfc_main.__runtimeHooks = 7;
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/topic/details.js.map
