{"version": 3, "file": "details.js", "sources": ["pages/goods/details.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZ29vZHMvZGV0YWlscy52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 导航栏 -->\r\n    <view class=\"nav-box df\" :style=\"{'padding-top': statusBarHeight + 'px', 'background': 'rgba(255,255,255,'+ navbarTrans +')'}\">\r\n      <view class=\"nav-back df\" :style=\"{'height': titleBarHeight + 'px'}\" @tap=\"navBack\">\r\n        <image src=\"/static/img/back.png\" style=\"width:34rpx;height:34rpx\"></image>\r\n      </view>\r\n      <view v-if=\"navbarTrans == 1\" class=\"nav-title ohto\">{{goodsInfo.name}}</view>\r\n    </view>\r\n    \r\n    <!-- 商品轮播图 -->\r\n    <view class=\"images-box df\">\r\n      <swiper class=\"images-swiper\" circular autoplay @change=\"imagesSwiper\" data-type=\"1\">\r\n        <swiper-item v-for=\"(item, index) in goodsInfo.imgs\" :key=\"index\" :data-idx=\"index\" data-type=\"1\" @tap=\"imgParamTap\">\r\n          <lazy-image :src=\"item\"></lazy-image>\r\n        </swiper-item>\r\n      </swiper>\r\n      \r\n      <!-- 轮播指示器 -->\r\n      <view v-if=\"goodsInfo.imgs.length\" class=\"indicator\">\r\n        <view v-for=\"(v, index) in goodsInfo.imgs\" :key=\"index\" \r\n              :class=\"['indicator-item', imgIdx == index && 'act']\"\r\n              :style=\"{'width': 100 / goodsInfo.imgs.length + '%'}\">\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 商品信息 -->\r\n    <view class=\"info-box\">\r\n      <!-- 标签 -->\r\n      <view class=\"tags\">\r\n        <view v-for=\"(tag, index) in goodsInfo.tags\" :key=\"index\" class=\"tag-item\">{{tag}}</view>\r\n      </view>\r\n      \r\n      <view class=\"title\">{{goodsInfo.name}}</view>\r\n      <view class=\"desc\">{{goodsInfo.intro}}</view>\r\n      \r\n      <view class=\"price\">\r\n        <money :price=\"goodsInfo.product[productIdx].price\"></money>\r\n        <view v-if=\"goodsInfo.product[productIdx].line_price\" class=\"price-line\" style=\"text-decoration:line-through\">\r\n          ¥{{goodsInfo.product[productIdx].line_price}}\r\n        </view>\r\n        <view class=\"price-line\">\r\n          {{goodsInfo.buy ? goodsInfo.buy + \"人已买\" : goodsInfo.cart + goodsInfo.browse + \"人想买\"}}\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 商品规格选择 -->\r\n    <view class=\"content\">\r\n      <view class=\"content-title\">规格</view>\r\n      <scroll-view scroll-x=\"true\">\r\n        <view class=\"specs-scroll\">\r\n          <view v-for=\"(item, index) in goodsInfo.product\" :key=\"index\" \r\n                class=\"specs-item\" \r\n                :style=\"{'border-color': productIdx == index ? '#000' : '#f8f8f8'}\"\r\n                @tap=\"() => productIdx = index\">\r\n            <view class=\"fd df\" :data-idx=\"index\" data-type=\"2\" @tap.stop=\"imgParamTap\">\r\n              <image src=\"/static/img/fd.png\" style=\"width:22rpx;height:22rpx\"></image>\r\n            </view>\r\n            <image class=\"img\" :src=\"item.img\" mode=\"aspectFill\"></image>\r\n            <view class=\"name\">\r\n              <view>{{item.name}}</view>\r\n              <view style=\"margin-top:10rpx\">¥ {{item.price}}</view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n    \r\n    <!-- 数量选择 -->\r\n    <view class=\"content\">\r\n      <view class=\"content-title\">数量</view>\r\n      <view class=\"quantity-box df\">\r\n        <view class=\"quantity-btn\" \r\n              :style=\"{'color': quantity > 1 ? '#000' : '#ccc'}\" \r\n              @tap=\"quantityBtn\" \r\n              data-type=\"0\">－</view>\r\n        <input @blur=\"quantityBtn\" \r\n               data-type=\"2\" \r\n               type=\"number\" \r\n               maxlength=\"4\" \r\n               v-model=\"quantity\" />\r\n        <view class=\"quantity-btn\" \r\n              :style=\"{'color': quantity < goodsInfo.product[productIdx].stock ? '#000' : '#ccc'}\" \r\n              @tap=\"quantityBtn\" \r\n              data-type=\"1\">＋</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 评价和商品详情 -->\r\n    <view class=\"content\">\r\n      <view class=\"content-title\">商品详情</view>\r\n      \r\n      <!-- 评价区域 -->\r\n      <view v-if=\"goodsInfo.comment > 0\" class=\"evaluate df\" @tap=\"toEvaluate\">\r\n        <view>评价（{{goodsInfo.comment}}）</view>\r\n        <view class=\"df\">\r\n          <!-- 用户头像 -->\r\n          <view v-if=\"goodsInfo.comment_user.length > 0\" class=\"cu-img-group\">\r\n            <view v-for=\"(img, index) in goodsInfo.comment_user\" :key=\"index\" class=\"cu-img\">\r\n              <image :src=\"img\" mode=\"aspectFill\"></image>\r\n            </view>\r\n          </view>\r\n          <image class=\"effect\" src=\"/static/img/y.png\"></image>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 商品详情 -->\r\n      <rich-text :nodes=\"goodsInfo.details\"></rich-text>\r\n    </view>\r\n    \r\n    <!-- 底部操作栏 -->\r\n    <view class=\"footer-box bUp df\">\r\n      <view class=\"footer-item df\">\r\n        <!-- 客服按钮 -->\r\n        <button class=\"icon-box df\"\r\n                open-type=\"contact\"\r\n                :send-message-title=\"goodsInfo.name\"\r\n                :send-message-path=\"'/pages/goods/details?id=' + goodsInfo.id\"\r\n                :send-message-img=\"goodsInfo.imgs[0]\"\r\n                :show-message-card=\"true\">\r\n          <image src=\"/static/img/kf.png\"></image>\r\n          <text>客服</text>\r\n        </button>\r\n        \r\n        <!-- 购物车按钮 -->\r\n        <button class=\"icon-box df\" @tap=\"toCart\">\r\n          <image src=\"/static/img/gwc1.png\"></image>\r\n          <text>购物车</text>\r\n          <view v-if=\"goodsInfo.cart_count\" class=\"badge\">\r\n            {{goodsInfo.cart_count > 99 ? '99+' : goodsInfo.cart_count}}\r\n          </view>\r\n        </button>\r\n        \r\n        <!-- 购买按钮区域 -->\r\n        <view v-if=\"goodsInfo.type == 2\" class=\"btn df\">\r\n          <view @tap=\"taobaoClick\" class=\"bg1 df\" style=\"width:100%;justify-content:center\">\r\n            <image src=\"/static/img/tb.png\" style=\"width:40rpx;height:40rpx\"></image>\r\n            <text style=\"margin-left:10rpx\">复制优惠链接</text>\r\n          </view>\r\n        </view>\r\n        <view v-else class=\"btn df\">\r\n          <view @tap=\"addCartClick\">加入购物车</view>\r\n          <view @tap=\"buyNowClick\" :class=\"['bg1', goodsInfo.product[productIdx].stock <= 0 && 'bg2']\">\r\n            {{goodsInfo.product[productIdx].stock <= 0 ? '暂无库存' : '立即购买'}}\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 提示弹窗 -->\r\n    <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\r\n      <view class=\"tips-box df\">\r\n        <view class=\"tips-item\">{{tipsTitle}}</view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport lazyImage from '@/components/lazyImage/lazyImage.vue'\r\nimport money from '@/components/money/money.vue'\r\n\r\nconst app = getApp()\r\n\r\nexport default {\r\n  components: {\r\n    lazyImage,\r\n    money\r\n  },\r\n  data() {\r\n    return {\r\n      statusBarHeight: app.globalData.statusBarHeight || 20,\r\n      titleBarHeight: app.globalData.titleBarHeight || 44,\r\n      navbarTrans: 0,\r\n      goodsInfo: {\r\n        id: 1,\r\n        name: \"智能手表\",\r\n        intro: \"支持心率监测、睡眠监测、消息提醒等多种功能\",\r\n        imgs: [\r\n          \"/static/img/avatar.png\",\r\n          \"/static/img/avatar.png\",\r\n          \"/static/img/avatar.png\"\r\n        ],\r\n        details: \"<p>这是一款功能强大的智能手表，支持心率监测、睡眠监测、消息提醒等多种功能。</p>\",\r\n        buy: 1250,\r\n        view: 3560,\r\n        cart: 80,\r\n        comment: 35,\r\n        tags: [\"新品\", \"热卖\", \"质保\"],\r\n        comment_user: [\r\n          \"/static/img/avatar.png\",\r\n          \"/static/img/avatar.png\",\r\n          \"/static/img/avatar.png\"\r\n        ],\r\n        product: [\r\n          {\r\n            id: 101,\r\n            name: \"黑色标准版\",\r\n            img: \"/static/img/avatar.png\",\r\n            price: \"299.00\",\r\n            line_price: \"399.00\",\r\n            stock: 120\r\n          },\r\n          {\r\n            id: 102,\r\n            name: \"白色标准版\",\r\n            img: \"/static/img/avatar.png\",\r\n            price: \"299.00\",\r\n            line_price: \"399.00\",\r\n            stock: 85\r\n          },\r\n          {\r\n            id: 103,\r\n            name: \"蓝色豪华版\",\r\n            img: \"/static/img/avatar.png\",\r\n            price: \"399.00\",\r\n            line_price: \"499.00\",\r\n            stock: 0\r\n          }\r\n        ],\r\n        cart_count: 2,\r\n        type: 1\r\n      },\r\n      imgIdx: 0,\r\n      productIdx: 0,\r\n      quantity: 1,\r\n      tipsTitle: \"\"\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    // 设置分享菜单 - 仅在小程序环境中有效\r\n    // #ifdef MP\r\n    uni.showShareMenu({\r\n      withShareTicket: true,\r\n      menus: ['shareAppMessage', 'shareTimeline']\r\n    });\r\n    // #endif\r\n    \r\n    // 获取系统信息\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    this.statusBarHeight = systemInfo.statusBarHeight || 20;\r\n    this.titleBarHeight = 44;\r\n    \r\n    // 如果有商品ID则加载商品详情\r\n    if (options.id && options.id > 0) {\r\n      this.goodsInfo.id = options.id;\r\n      // 实际应用中这里应该调用接口\r\n    }\r\n  },\r\n  methods: {\r\n    // 添加到购物车\r\n    addCartClick() {\r\n      this.goodsInfo.cart_count = parseInt(this.goodsInfo.cart_count) + 1;\r\n      this.opTipsPopup(\"共 \" + this.quantity + \" 件商品已经为您加入购物车 🎉\");\r\n      app.globalData.isCenterPage = true;\r\n    },\r\n    \r\n    // 立即购买\r\n    buyNowClick() {\r\n      if(this.goodsInfo.product[this.productIdx].stock > 0) {\r\n        uni.navigateTo({\r\n          url: \"/pages/order/settlement?type=1&pid=\" + this.goodsInfo.product[this.productIdx].id + \"&quantity=\" + this.quantity\r\n        });\r\n      } else {\r\n        this.opTipsPopup(\"该款式已售罄暂时无法购买，请稍后重试！\");\r\n      }\r\n    },\r\n    \r\n    // 数量选择\r\n    quantityBtn(e) {\r\n      let type = e.currentTarget.dataset.type;\r\n      \r\n      if(type == 0 && parseInt(this.quantity) <= 1) return;\r\n      \r\n      if(parseInt(this.quantity) > this.goodsInfo.product[this.productIdx].stock) {\r\n        this.quantity = this.goodsInfo.product[this.productIdx].stock;\r\n        this.opTipsPopup(\"该款式最多可购买 \" + this.quantity + \" 件！\");\r\n      } else if(this.goodsInfo.product[this.productIdx].stock && this.quantity && this.quantity != 0) {\r\n        if(type == 0) {\r\n          this.quantity = parseInt(this.quantity) - 1;\r\n        }\r\n        if(type == 1 && parseInt(this.quantity) < this.goodsInfo.product[this.productIdx].stock) {\r\n          this.quantity = parseInt(this.quantity) + 1;\r\n        }\r\n      } else {\r\n        this.quantity = 1;\r\n      }\r\n    },\r\n    \r\n    // 淘宝链接\r\n    taobaoClick() {\r\n      uni.setClipboardData({\r\n        data: \"https://example.com/shopping\",\r\n        success: function() {\r\n          uni.hideToast();\r\n        }\r\n      });\r\n      this.opTipsPopup(\"复制成功，打开手机淘宝即可优惠购！\");\r\n    },\r\n    \r\n    // 图片点击\r\n    imgParamTap(e) {\r\n      let type = e.currentTarget.dataset.type;\r\n      let idx = e.currentTarget.dataset.idx;\r\n      let current = \"\";\r\n      let urls = [];\r\n      \r\n      if(type == 1) {\r\n        current = this.goodsInfo.imgs[idx];\r\n        urls = this.goodsInfo.imgs;\r\n      } else {\r\n        current = this.goodsInfo.product[idx].img;\r\n        urls = [current];\r\n      }\r\n      \r\n      uni.previewImage({\r\n        current: current,\r\n        urls: urls\r\n      });\r\n    },\r\n    \r\n    // 轮播图切换\r\n    imagesSwiper(e) {\r\n      this.imgIdx = e.detail.current;\r\n    },\r\n    \r\n    // 进入购物车\r\n    toCart() {\r\n      uni.navigateTo({\r\n        url: \"/pages/goods/cart\"\r\n      });\r\n    },\r\n    \r\n    // 进入评价页\r\n    toEvaluate() {\r\n      uni.navigateTo({\r\n        url: \"/pages/goods/evaluate?id=\" + this.goodsInfo.id + \"&name=\" + this.goodsInfo.name + \"&count=\" + this.goodsInfo.comment + \"&img=\" + this.goodsInfo.imgs[0]\r\n      });\r\n    },\r\n    \r\n    // 返回\r\n    navBack() {\r\n      if (getCurrentPages().length > 1) {\r\n        uni.navigateBack();\r\n      } else {\r\n        uni.switchTab({\r\n          url: \"/pages/tabbar/shop\"\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 显示提示\r\n    opTipsPopup(msg, isBack = false) {\r\n      this.tipsTitle = msg;\r\n      this.$refs.tipsPopup.open();\r\n      \r\n      setTimeout(() => {\r\n        this.$refs.tipsPopup.close();\r\n        if (isBack) {\r\n          setTimeout(() => {\r\n            this.navBack();\r\n          }, 300);\r\n        }\r\n      }, 2000);\r\n    }\r\n  },\r\n  onPageScroll(e) {\r\n    // 计算导航栏透明度 (0-1)\r\n    let t = (e.scrollTop > 150 ? 150 : e.scrollTop) / 150;\r\n    this.navbarTrans = t;\r\n  },\r\n  onShareAppMessage() {\r\n    return {\r\n      title: this.goodsInfo.name,\r\n      imageUrl: this.goodsInfo.imgs[0],\r\n      path: \"/pages/goods/details?id=\" + this.goodsInfo.id\r\n    };\r\n  },\r\n  onShareTimeline() {\r\n    return {\r\n      title: this.goodsInfo.name,\r\n      imageUrl: this.goodsInfo.imgs[0],\r\n      query: \"id=\" + this.goodsInfo.id\r\n    };\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.container{padding-bottom:320rpx}\r\n.nav-box{position:fixed;top:0;left:0;width:100%;z-index:99;box-sizing:border-box}\r\n.nav-box .nav-back{padding:0 30rpx;width:34rpx;height:100%}\r\n.nav-box .nav-title{max-width:60%;font-size:32rpx;font-weight:700}\r\n.images-box{width:100%;flex-direction:column;position:relative}\r\n.images-box .images-swiper{width:100%;height:750rpx;background:#f8f8f8}\r\n.images-box .indicator{position:absolute;bottom:30rpx;display:flex;width:375rpx;height:6rpx;background:rgba(255,255,255,.3);border-radius:6rpx}\r\n.indicator .indicator-item{height:6rpx;border-radius:6rpx}\r\n.indicator .act{background:#fff!important}\r\n.info-box{width:calc(100% - 60rpx);padding:30rpx}\r\n.info-box .title{width:100%;color:#000;font-size:36rpx;font-weight:700}\r\n.info-box .desc{margin-top:10rpx;color:#444;font-size:24rpx;font-weight:500}\r\n.info-box .price{margin-top:20rpx;display:flex;align-items:flex-end}\r\n.price .price-line{margin-left:20rpx;color:#999;font-size:22rpx;line-height:22rpx}\r\n.info-box .tags{padding-bottom:10rpx;width:100%;display:flex;flex-wrap:wrap}\r\n.tags .tag-item{margin:0 10rpx 10rpx 0;height:40rpx;padding:0 12rpx;line-height:40rpx;font-size:18rpx;font-weight:500;background:#f8f8f8;border-radius:8rpx}\r\n.content{width:calc(100% - 60rpx);padding:0 30rpx}\r\n.content .content-title{padding:30rpx 0;color:#999;font-size:24rpx;font-weight:700}\r\n.content .specs-scroll{width:100%;display:flex}\r\n.specs-scroll .specs-item{flex-shrink:0;margin-right:20rpx;background:#fff;width:200rpx;border-radius:8rpx;border-width:1px;border-style:solid;position:relative;overflow:hidden}\r\n.specs-item .fd{position:absolute;z-index:1;top:10rpx;right:10rpx;width:48rpx;height:48rpx;justify-content:center;background:rgba(0,0,0,.3);border-radius:50%}\r\n.specs-item .img{width:200rpx;height:200rpx;display:block}\r\n.specs-item .name{width:calc(100% - 40rpx);margin:20rpx;line-height:30rpx;text-align:center;font-size:20rpx;font-weight:500}\r\n.quantity-box{width:240rpx;height:80rpx;line-height:80rpx;border-radius:40rpx;border:1px solid #f5f5f5;font-size:32rpx;font-weight:700;text-align:center}\r\n.quantity-box input{color:#000;width:80rpx;height:80rpx;line-height:80rpx}\r\n.quantity-box .quantity-btn{width:80rpx;height:80rpx;line-height:80rpx}\r\n.evaluate{margin-bottom:30rpx;width:calc(100% - 60rpx);padding:30rpx;font-size:26rpx;font-weight:700;border-radius:8rpx;background:#f8f8f8;justify-content:space-between}\r\n.evaluate image{width:30rpx;height:30rpx}\r\n.cu-img-group{direction:ltr;unicode-bidi:bidi-override;display:inline-block;margin-right:20rpx}\r\n.cu-img-group .cu-img{width:48rpx;height:48rpx;display:inline-flex;position:relative;margin-left:-24rpx;border:4rpx solid #f8f8f8;background:#f5f5f5;vertical-align:middle;border-radius:50%}\r\n.cu-img-group .cu-img image{width:100%;height:100%;border-radius:50%}\r\n.footer-box{position:fixed;left:0;right:0;bottom:0;z-index:99;border-top:1px solid #f8f8f8;background:rgba(255,255,255,.95);box-sizing:border-box}\r\n.footer-box .footer-item{width:calc(100% - 20rpx);padding:30rpx 10rpx;height:100rpx;justify-content:space-between;padding-bottom:max(env(safe-area-inset-bottom),30rpx)}\r\n.footer-item .icon-box{margin:0;width:82rpx!important;height:100rpx;padding:20rpx 0;flex-direction:column;justify-content:space-between;background:transparent;position:relative}\r\n.footer-item .icon-box image{width:42rpx;height:42rpx}\r\n.footer-item .icon-box text{font-size:14rpx;line-height:14rpx;font-weight:700}\r\n.footer-item .icon-box .badge{position:absolute;top:0;right:0;min-width:30rpx;height:30rpx;line-height:30rpx;text-align:center;font-size:18rpx;font-weight:700;color:#fff;background:#000;border-radius:30rpx;border:4rpx solid #fff}\r\n.footer-item .btn{width:calc(100% - 204rpx);padding:0 20rpx;justify-content:space-between}\r\n.footer-item .btn view{width:calc(50% - 15rpx);height:100rpx;line-height:100rpx;text-align:center;font-size:24rpx;font-weight:700;color:#000;background:#fff;border-radius:60rpx;border:1px solid #000}\r\n.footer-item .btn .bg1{color:#fff;background:#000}\r\n.footer-item .btn .bg2{color:#999;background:#f5f5f5;border:1px solid #f5f5f5}\r\n.tips-box{width:100%;padding:30rpx 0;background:#fff;justify-content:center}\r\n.tips-box .tips-item{color:#333;font-size:28rpx;font-weight:700}\r\n.df{display:flex;align-items:center}\r\n.ohto{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}\r\n</style>", "import MiniProgramPage from 'Z:/WWW/shejiao/vue3/pages/goods/details.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAiKA,MAAA,YAAA,MAAA;AACA,MAAA,QAAA,MAAA;AAEA,MAAA,MAAA,OAAA;AAEA,MAAA,YAAA;AAAA;IAEI;AAAA;;EAGF,OAAA;AACE,WAAA;AAAA;;;;;;;;;;;;;QAcI,KAAA;AAAA,QACA,MAAA;AAAA,QACA,MAAA;AAAA;QAEA,MAAA,CAAA,MAAA,MAAA,IAAA;AAAA;;;;;QAMA,SAAA;AAAA,UACE;AAAA,YACE,IAAA;AAAA;;YAGA,OAAA;AAAA,YACA,YAAA;AAAA,YACA,OAAA;AAAA;UAEF;AAAA,YACE,IAAA;AAAA;;YAGA,OAAA;AAAA,YACA,YAAA;AAAA,YACA,OAAA;AAAA;UAEF;AAAA,YACE,IAAA;AAAA;;YAGA,OAAA;AAAA,YACA,YAAA;AAAA,YACA,OAAA;AAAA,UACF;AAAA;;;;MAKJ,QAAA;AAAA;;;IAIF;AAAA;EAEF,OAAA,SAAA;AAGEA,kBAAAA,MAAA,cAAA;AAAA,MACE,iBAAA;AAAA,MACA,OAAA,CAAA,mBAAA,eAAA;AAAA,IACF,CAAA;AAIA,UAAA,aAAAA,oBAAA;;;;;IAQA;AAAA;EAEF,SAAA;AAAA;AAAA,IAEE,eAAA;AACE,WAAA,UAAA,aAAA,SAAA,KAAA,UAAA,UAAA,IAAA;;AAEA,UAAA,WAAA,eAAA;AAAA;;;;AAMEA,sBAAAA,MAAA,WAAA;AAAA;QAEA,CAAA;AAAA;AAEA,aAAA,YAAA,qBAAA;AAAA,MACF;AAAA;;IAIF,YAAA,GAAA;AACE,UAAA,OAAA,EAAA,cAAA,QAAA;AAEA,UAAA,QAAA,KAAA,SAAA,KAAA,QAAA,KAAA;AAAA;AAEA,UAAA,SAAA,KAAA,QAAA,IAAA,KAAA,UAAA,QAAA,KAAA,UAAA,EAAA,OAAA;;;MAGA,WAAA,KAAA,UAAA,QAAA,KAAA,UAAA,EAAA,SAAA,KAAA,YAAA,KAAA,YAAA,GAAA;;AAEI,eAAA,WAAA,SAAA,KAAA,QAAA,IAAA;AAAA,QACF;AACA,YAAA,QAAA,KAAA,SAAA,KAAA,QAAA,IAAA,KAAA,UAAA,QAAA,KAAA,UAAA,EAAA,OAAA;AACE,eAAA,WAAA,SAAA,KAAA,QAAA,IAAA;AAAA,QACF;AAAA;AAEA,aAAA,WAAA;AAAA,MACF;AAAA;;;AAKAA,oBAAAA,MAAA,iBAAA;AAAA,QACE,MAAA;AAAA,QACA,SAAA,WAAA;AACEA,wBAAA,MAAA,UAAA;AAAA,QACF;AAAA,MACF,CAAA;AACA,WAAA,YAAA,mBAAA;AAAA;;IAIF,YAAA,GAAA;AACE,UAAA,OAAA,EAAA,cAAA,QAAA;AACA,UAAA,MAAA,EAAA,cAAA,QAAA;;;;AAKE,kBAAA,KAAA,UAAA,KAAA,GAAA;;;AAGA,kBAAA,KAAA,UAAA,QAAA,GAAA,EAAA;AACA,eAAA,CAAA,OAAA;AAAA,MACF;AAEAA,oBAAAA,MAAA,aAAA;AAAA,QACE;AAAA,QACA;AAAA,MACF,CAAA;AAAA;;IAIF,aAAA,GAAA;;;;IAKA,SAAA;AACEA,oBAAAA,MAAA,WAAA;AAAA;MAEA,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA,8BAAA,KAAA,UAAA,KAAA,WAAA,KAAA,UAAA,OAAA,YAAA,KAAA,UAAA,UAAA,UAAA,KAAA,UAAA,KAAA,CAAA;AAAA,MACF,CAAA;AAAA;;IAIF,UAAA;AACE,UAAA,gBAAA,EAAA,SAAA,GAAA;AACEA,sBAAA,MAAA,aAAA;AAAA;AAEAA,sBAAAA,MAAA,UAAA;AAAA;QAEA,CAAA;AAAA,MACF;AAAA;;;AAKA,WAAA,YAAA;;AAGA,iBAAA,MAAA;;;AAGI,qBAAA,MAAA;AACE,iBAAA,QAAA;AAAA,UACF,GAAA,GAAA;AAAA,QACF;AAAA,MACF,GAAA,GAAA;AAAA,IACF;AAAA;EAEF,aAAA,GAAA;AAEE,QAAA,KAAA,EAAA,YAAA,MAAA,MAAA,EAAA,aAAA;AACA,SAAA,cAAA;AAAA;EAEF,oBAAA;AACE,WAAA;AAAA;MAEE,UAAA,KAAA,UAAA,KAAA,CAAA;AAAA;;;EAIJ,kBAAA;AACE,WAAA;AAAA;MAEE,UAAA,KAAA,UAAA,KAAA,CAAA;AAAA;;EAGJ;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClYA,GAAG,WAAW,eAAe;"}