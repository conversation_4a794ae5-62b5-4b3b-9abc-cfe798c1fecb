{"version": 3, "file": "my-tree-hole.js", "sources": ["pages/note/my-tree-hole.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbm90ZS9teS10cmVlLWhvbGUudnVl"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"nav-bar bfw\" :style=\"{'padding-top': statusBarHeight + 'px'}\">\n      <view class=\"bar-box df\" :style=\"{'height': titleBarHeight + 'px', 'width': '100%'}\">\n        <view class=\"bar-back df\" @tap=\"goBack\">\n          <image src=\"/static/img/z.png\" style=\"width:34rpx;height:34rpx\"></image>\n        </view>\n        <view class=\"bar-title ohto\">我的纸条</view>\n        <view class=\"notification-btn df\">\n          <text class=\"notification-icon\">🔔</text>\n        </view>\n      </view>\n      <view class=\"nav-box df\">\n        <view\n          v-for=\"(item, index) in barList\"\n          :key=\"index\"\n          class=\"nav-item df\"\n          @tap=\"switchTab\"\n          :data-idx=\"index\">\n          <text :style=\"{'color': index == currentTab ? '#000' : '#999', 'font-size': index == currentTab ? '28rpx' : '26rpx'}\">\n            {{ item }}\n          </text>\n          <view :style=\"{'opacity': index == currentTab ? 1 : 0}\" class=\"nav-line\"></view>\n        </view>\n      </view>\n    </view>\n\n    <view class=\"content-box\" :style=\"{'margin-top': 'calc(' + (statusBarHeight + titleBarHeight) + 'px + 90rpx)'}\">\n      <!-- 自定义加载指示器 -->\n      <view v-if=\"loading\" class=\"loading-container\">\n        <view class=\"loading-indicator\"></view>\n      </view>\n\n      <emptyPage\n        v-if=\"isEmpty\"\n        :title=\"currentTab == 0 ? '还没有发布过纸条' : '还没有抽到纸条'\"\n        description=\"空空如也，等待探索\"\n        :buttonText=\"currentTab == 0 ? '去发布纸条' : '去抽取纸条'\"\n        @buttonClick=\"goToMain\"\n      />\n\n      <block v-else>\n        <view class=\"paper-list\">\n          <view\n            class=\"paper-item\"\n            v-for=\"item in currentPaperList\"\n            :key=\"item.id\"\n            @click=\"viewPaperDetail(item)\"\n          >\n            <!-- 用户信息 -->\n            <view class=\"user-info\">\n              <view class=\"avatar\">\n                <image :src=\"item.avatar || '/static/default-avatar.png'\" class=\"avatar-img\"></image>\n              </view>\n              <view class=\"user-details\">\n                <view class=\"username\">{{item.nickname}}</view>\n                <view class=\"user-meta\">\n                  <text class=\"gender-icon\">{{getGenderIcon(item.sex)}}</text>\n                  <text class=\"age\">{{item.age}} · {{item.location}}</text>\n                </view>\n              </view>\n              <view class=\"type-tag\" :class=\"'type-' + item.type\">\n                <text class=\"type-icon\">{{getTypeIcon(item.type)}}</text>\n                <text class=\"type-text\">{{getTypeText(item.type)}}</text>\n              </view>\n            </view>\n\n            <!-- 纸条内容 -->\n            <view class=\"paper-content\">\n              <text class=\"content-text\">{{item.content}}</text>\n            </view>\n\n            <!-- 状态信息 -->\n            <view class=\"paper-status\">\n              <text class=\"status-text\">{{getStatusText(item)}}</text>\n              <text class=\"time-text\">{{formatTime(item.create_time)}}</text>\n            </view>\n\n            <!-- 回应统计 -->\n            <view class=\"response-info\" @click.stop=\"viewResponses(item)\">\n              <text class=\"response-text\">共{{item.response_count}}条回应</text>\n              <text class=\"arrow-icon\">›</text>\n            </view>\n\n            <!-- 回应按钮 -->\n            <view class=\"reply-btn\" @click.stop=\"quickReply(item)\">\n              <text class=\"reply-text\">回应</text>\n            </view>\n          </view>\n        </view>\n      </block>\n\n      <!-- 底部加载状态显示 -->\n      <view v-if=\"currentPaperList.length > 0 && loadStatus === 'noMore'\" style=\"text-align: center; padding: 20rpx 0; color: #999; font-size: 24rpx;\">\n        没有更多数据了\n      </view>\n    </view>\n\n    <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\n      <view class=\"tips-box df\">\n        <view class=\"tips-item bfh\">{{ tipsTitle }}</view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\nimport { getMyTreeHoleBoxList, getMyDrawnBoxList } from '@/api/social.js'\nimport { checkLogin, toLogin } from '@/libs/login.js'\nimport emptyPage from '@/components/emptyPage/emptyPage.vue'\n\nexport default {\n  name: 'MyTreeHole',\n  components: {\n    emptyPage\n  },\n  data() {\n    return {\n      statusBarHeight: this.$store.state.statusBarHeight || 20,\n      titleBarHeight: this.$store.state.titleBarHeight || 44,\n      barList: ['我的纸条', '抽到的纸条'],\n      currentTab: 1, // 0-我的纸条 1-抽到的纸条\n      myPapers: [], // 我发布的纸条\n      drawnPapers: [], // 我抽到的纸条\n      loading: false,\n      isEmpty: false,\n      loadStatus: 'more',\n      tipsTitle: '',\n      paperList: [ // 模拟数据，用于测试\n        {\n          id: 1,\n          type: 4, // 语音纸条\n          content: '',\n          nickname: '幽默的麻雀',\n          avatar: '',\n          sex: 1,\n          age: 19,\n          location: 'Sub',\n          response_count: 1,\n          create_time: '2024-01-20 21:09',\n          status: 'received', // received-已收到 sent-已发送\n          voice_duration: 23\n        },\n        {\n          id: 2,\n          type: 1, // 问题\n          content: '又爱违者的暂哥滴落我哦',\n          nickname: '优雅的巨蜥',\n          avatar: '',\n          sex: 1,\n          age: 24,\n          location: 'Sub',\n          response_count: 7,\n          create_time: '2024-01-20 16:55',\n          status: 'received',\n          received_count: 53\n        },\n        {\n          id: 3,\n          type: 1, // 问题\n          content: '有没有资深玩家，做过提高身体阅值的系统脱敏训练，我过相关经验的留言，请简单描述，谢谢！',\n          nickname: '忧伤的鲨鱼',\n          avatar: '',\n          sex: 1,\n          age: 37,\n          location: 'Dom',\n          response_count: 6,\n          create_time: '2024-01-20 15:54',\n          status: 'received',\n          received_count: 91\n        }\n      ]\n    }\n  },\n  computed: {\n    // 当前显示的纸条列表\n    currentPaperList() {\n      if (this.currentTab === 0) {\n        return this.myPapers.length > 0 ? this.myPapers : []\n      } else {\n        return this.drawnPapers.length > 0 ? this.drawnPapers : []\n      }\n    }\n  },\n  onLoad() {\n    // 检查登录状态\n    if (!this.checkLoginStatus()) {\n      return\n    }\n    this.loadData()\n  },\n  onShow() {\n    // 每次显示页面时检查登录状态\n    if (!this.checkLoginStatus()) {\n      return\n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack()\n    },\n\n    async loadData() {\n      if (this.currentTab === 0) {\n        await this.loadMyPapers()\n      } else {\n        await this.loadDrawnPapers()\n      }\n    },\n\n    async loadMyPapers() {\n      console.log('=== 加载我的纸条 ===')\n      this.loading = true\n\n      try {\n        const params = {\n          page: 1,\n          limit: 20\n        }\n        console.log('请求参数:', params)\n\n        const result = await getMyTreeHoleBoxList(params)\n        console.log('我的纸条API响应:', result)\n\n        if (result.status === 200 && result.data) {\n          this.myPapers = result.data.list || []\n          this.isEmpty = this.myPapers.length === 0\n          console.log('我的纸条数据:', this.myPapers)\n\n          if (this.myPapers.length > 0) {\n            uni.showToast({\n              title: `加载了${this.myPapers.length}条纸条`,\n              icon: 'none'\n            })\n          }\n        } else {\n          this.isEmpty = true\n          throw new Error(result.msg || '获取我的纸条失败')\n        }\n      } catch (error) {\n        console.error('加载我的纸条失败:', error)\n        uni.showToast({\n          title: error.message || '加载失败',\n          icon: 'none'\n        })\n      } finally {\n        this.loading = false\n      }\n    },\n\n    async loadDrawnPapers() {\n      console.log('=== 加载抽到的纸条 ===')\n      this.loading = true\n\n      try {\n        const params = {\n          page: 1,\n          limit: 20\n        }\n        console.log('请求参数:', params)\n\n        const result = await getMyDrawnBoxList(params)\n        console.log('抽到的纸条API响应:', result)\n\n        if (result.status === 200 && result.data) {\n          this.drawnPapers = result.data.list || []\n          this.isEmpty = this.drawnPapers.length === 0\n          console.log('抽到的纸条数据:', this.drawnPapers)\n\n          if (this.drawnPapers.length > 0) {\n            uni.showToast({\n              title: `加载了${this.drawnPapers.length}条纸条`,\n              icon: 'none'\n            })\n          }\n        } else {\n          this.isEmpty = true\n          throw new Error(result.msg || '获取抽到的纸条失败')\n        }\n      } catch (error) {\n        console.error('加载抽到的纸条失败:', error)\n        uni.showToast({\n          title: error.message || '加载失败',\n          icon: 'none'\n        })\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    async switchTab(e) {\n      // 获取点击的标签索引\n      const clickIdx = parseInt(e.currentTarget.dataset.idx);\n\n      // 如果点击的是当前选中的标签，不重复加载\n      if (clickIdx === this.currentTab) {\n        return;\n      }\n\n      this.currentTab = clickIdx;\n      this.isEmpty = false;\n      await this.loadData();\n    },\n    \n    getGenderIcon(sex) {\n      return sex === 1 ? '♂' : sex === 2 ? '♀' : '⚪'\n    },\n    \n    getTypeIcon(type) {\n      const icons = {\n        1: '❓',\n        2: '🤫', \n        3: '🌠',\n        4: '🎵'\n      }\n      return icons[type] || '📝'\n    },\n    \n    getTypeText(type) {\n      const texts = {\n        1: '问题咨询',\n        2: '秘密',\n        3: '心愿',\n        4: '语音纸条'\n      }\n      return texts[type] || '纸条'\n    },\n    \n    getStatusText(item) {\n      if (this.currentTab === 0) {\n        // 我的纸条\n        return `已被${item.received_count || 0}人抽取`\n      } else {\n        // 抽到的纸条\n        return `已被${item.received_count || 0}人抽取`\n      }\n    },\n    \n    formatTime(timeStr) {\n      const now = new Date()\n      const time = new Date(timeStr)\n      const diff = now - time\n      const minutes = Math.floor(diff / (1000 * 60))\n\n      if (minutes < 60) {\n        return `${minutes}分钟前`\n      } else if (minutes < 1440) {\n        return `${Math.floor(minutes / 60)}小时前`\n      } else {\n        return `${Math.floor(minutes / 1440)}天前`\n      }\n    },\n\n    // 统一错误处理\n    handleError(error, defaultMessage = '操作失败') {\n      console.error('错误处理:', error)\n\n      let message = defaultMessage\n\n      if (typeof error === 'string') {\n        message = error\n      } else if (error && typeof error === 'object') {\n        if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network')) {\n          message = '网络连接异常，请检查网络设置'\n        } else if (error.code === 'TIMEOUT' || error.message?.includes('timeout')) {\n          message = '请求超时，请稍后重试'\n        } else {\n          message = error.msg || error.message || error.data?.msg || defaultMessage\n        }\n      }\n\n      uni.showToast({\n        title: message,\n        icon: 'none',\n        duration: 2000\n      })\n\n      return message\n    },\n\n    // 检查登录状态\n    checkLoginStatus() {\n      const isLoggedIn = checkLogin() && this.$store.state.app.token\n\n      if (!isLoggedIn) {\n        uni.showModal({\n          title: '提示',\n          content: '请先登录后查看纸条',\n          confirmText: '去登录',\n          cancelText: '返回',\n          success: (res) => {\n            if (res.confirm) {\n              toLogin()\n            } else {\n              uni.navigateBack()\n            }\n          }\n        })\n        return false\n      }\n\n      return true\n    },\n\n    viewPaperDetail(item) {\n      console.log('查看纸条详情:', item)\n      console.log('当前tab:', this.currentTab === 0 ? '我的纸条' : '抽到的纸条')\n\n      if (!item.id) {\n        uni.showToast({\n          title: '纸条ID不存在',\n          icon: 'none'\n        })\n        return\n      }\n\n      console.log('跳转到详情页面，ID:', item.id)\n\n      // 跳转到详情页面\n      uni.navigateTo({\n        url: `/pages/note/detail?id=${item.id}`\n      })\n    },\n\n    viewResponses(item) {\n      // 查看回应列表 - 跳转到详情页面\n      uni.navigateTo({\n        url: `/pages/note/paper-detail?id=${item.id}&type=${item.type}`\n      })\n    },\n\n    quickReply(item) {\n      // 快捷回应 - 跳转到弹窗\n      uni.navigateTo({\n        url: `/pages/note/tree-hole-detail?id=${item.id}&type=${item.type}`\n      })\n    },\n    \n    goToMain() {\n      uni.navigateTo({\n        url: '/pages/note/manghe'\n      })\n    },\n\n    // 显示提示信息\n    opTipsPopup(msg) {\n      this.tipsTitle = msg;\n      this.$refs.tipsPopup.open();\n      setTimeout(() => {\n        this.$refs.tipsPopup.close();\n      }, 2000);\n    }\n  }\n}\n</script>\n\n<style>\n.nav-bar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  z-index: 99;\n  box-sizing: border-box;\n}\n.bar-box .bar-back {\n  padding: 0 30rpx;\n  width: 34rpx;\n  height: 100%;\n}\n.bar-box .bar-title {\n  max-width: 60%;\n  font-size: 32rpx;\n  font-weight: 700;\n  flex: 1;\n  text-align: center;\n}\n.nav-box {\n  width: 100%;\n  height: 80rpx;\n}\n.nav-box .nav-item {\n  padding: 0 30rpx;\n  height: 100%;\n  flex-direction: column;\n  justify-content: center;\n  position: relative;\n}\n.nav-box .nav-item text {\n  font-weight: 700;\n  transition: all .3s ease-in-out;\n}\n.nav-box .nav-line {\n  position: absolute;\n  bottom: 12rpx;\n  width: 18rpx;\n  height: 6rpx;\n  border-radius: 6rpx;\n  background: #000;\n  transition: opacity .3s ease-in-out;\n}\n.content-box {\n  width: calc(100% - 60rpx);\n  padding: 30rpx;\n}\n\n.notification-btn {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.notification-icon {\n  font-size: 32rpx;\n}\n\n/* 加载中状态样式 */\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 60rpx;\n  margin-bottom: 20rpx;\n}\n.loading-indicator {\n  width: 30rpx;\n  height: 30rpx;\n  border: 3rpx solid #f3f3f3;\n  border-top: 3rpx solid #000;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.paper-list {\n  width: 100%;\n}\n\n/* 纸条项 */\n.paper-item {\n  background: #fff;\n  border-radius: 24rpx;\n  padding: 32rpx;\n  margin-bottom: 24rpx;\n  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n\n/* 用户信息 */\n.user-info {\n  display: flex;\n  align-items: center;\n  gap: 24rpx;\n  margin-bottom: 24rpx;\n}\n\n.avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  overflow: hidden;\n  background: #f0f0f0;\n}\n\n.avatar-img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.user-details {\n  flex: 1;\n}\n\n.username {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.user-meta {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n}\n\n.gender-icon {\n  color: #666;\n  font-size: 24rpx;\n}\n\n.age {\n  color: #666;\n  font-size: 24rpx;\n}\n\n.type-tag {\n  padding: 12rpx 20rpx;\n  border-radius: 20rpx;\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n}\n\n.type-1 { background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%); }\n.type-2 { background: linear-gradient(135deg, #EC4899 0%, #BE185D 100%); }\n.type-3 { background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%); }\n.type-4 { background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%); }\n\n.type-icon,\n.type-text {\n  color: #fff;\n  font-size: 24rpx;\n  font-weight: 500;\n}\n\n/* 纸条内容 */\n.paper-content {\n  margin-bottom: 24rpx;\n}\n\n.content-text {\n  color: #333;\n  font-size: 28rpx;\n  line-height: 1.6;\n}\n\n/* 状态信息 */\n.paper-status {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24rpx;\n}\n\n.status-text {\n  color: #999;\n  font-size: 24rpx;\n}\n\n.time-text {\n  color: #999;\n  font-size: 24rpx;\n}\n\n/* 回应信息 */\n.response-info {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 20rpx 0;\n  border-top: 1rpx solid #f0f0f0;\n  margin-bottom: 24rpx;\n}\n\n.response-text {\n  color: #333;\n  font-size: 28rpx;\n}\n\n.arrow-icon {\n  color: #999;\n  font-size: 32rpx;\n}\n\n/* 回应按钮 */\n.reply-btn {\n  width: 100%;\n  height: 72rpx;\n  background: #f8f9fa;\n  border-radius: 36rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.reply-btn:active {\n  background: #e9ecef;\n  transform: scale(0.98);\n}\n\n.reply-text {\n  color: #666;\n  font-size: 28rpx;\n}\n\n/* 加载状态 */\n.loading-state {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 40rpx;\n  color: #666;\n  font-size: 28rpx;\n}\n\n/* 空状态 */\n.empty-box {\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 0;\n}\n.empty-box image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 30rpx;\n}\n.empty-box .e1 {\n  font-size: 28rpx;\n  font-weight: bold;\n  margin-bottom: 10rpx;\n}\n.empty-box .e2 {\n  font-size: 24rpx;\n  color: #999;\n  margin-bottom: 30rpx;\n}\n\n.empty-btn {\n  padding: 24rpx 48rpx;\n  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);\n  border-radius: 48rpx;\n  box-shadow: 0 4rpx 16rpx rgba(139, 92, 246, 0.3);\n}\n\n.empty-btn-text {\n  color: #fff;\n  font-size: 28rpx;\n  font-weight: 500;\n}\n\n.df {\n  display: flex;\n  align-items: center;\n}\n.bfw {\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n  background: rgba(255,255,255,.8);\n}\n.bfh {\n  background: #000;\n  color: #fff;\n  padding: 20rpx 40rpx;\n  border-radius: 12rpx;\n  font-size: 24rpx;\n  font-weight: 700;\n}\n.tips-box {\n  justify-content: center;\n  width: 100%;\n}\n.ohto {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n</style>\n", "import MiniProgramPage from 'Z:/WWW/shejiao/vue3/pages/note/my-tree-hole.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "getMyTreeHoleBoxList", "getMyDrawnBoxList", "checkLogin", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;AA6GA,MAAK,YAAa,MAAW;AAE7B,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB,KAAK,OAAO,MAAM,mBAAmB;AAAA,MACtD,gBAAgB,KAAK,OAAO,MAAM,kBAAkB;AAAA,MACpD,SAAS,CAAC,QAAQ,OAAO;AAAA,MACzB,YAAY;AAAA;AAAA,MACZ,UAAU,CAAE;AAAA;AAAA,MACZ,aAAa,CAAE;AAAA;AAAA,MACf,SAAS;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,WAAW;AAAA;AAAA,QACT;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA;AAAA,UACN,SAAS;AAAA,UACT,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL,UAAU;AAAA,UACV,gBAAgB;AAAA,UAChB,aAAa;AAAA,UACb,QAAQ;AAAA;AAAA,UACR,gBAAgB;AAAA,QACjB;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA;AAAA,UACN,SAAS;AAAA,UACT,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL,UAAU;AAAA,UACV,gBAAgB;AAAA,UAChB,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,gBAAgB;AAAA,QACjB;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA;AAAA,UACN,SAAS;AAAA,UACT,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL,UAAU;AAAA,UACV,gBAAgB;AAAA,UAChB,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,gBAAgB;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA;AAAA,IAER,mBAAmB;AACjB,UAAI,KAAK,eAAe,GAAG;AACzB,eAAO,KAAK,SAAS,SAAS,IAAI,KAAK,WAAW,CAAC;AAAA,aAC9C;AACL,eAAO,KAAK,YAAY,SAAS,IAAI,KAAK,cAAc,CAAC;AAAA,MAC3D;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAEP,QAAI,CAAC,KAAK,oBAAoB;AAC5B;AAAA,IACF;AACA,SAAK,SAAS;AAAA,EACf;AAAA,EACD,SAAS;AAEP,QAAI,CAAC,KAAK,oBAAoB;AAC5B;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA,IAED,MAAM,WAAW;AACf,UAAI,KAAK,eAAe,GAAG;AACzB,cAAM,KAAK,aAAa;AAAA,aACnB;AACL,cAAM,KAAK,gBAAgB;AAAA,MAC7B;AAAA,IACD;AAAA,IAED,MAAM,eAAe;AACnBA,oBAAAA,MAAY,MAAA,OAAA,sCAAA,gBAAgB;AAC5B,WAAK,UAAU;AAEf,UAAI;AACF,cAAM,SAAS;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AACAA,sBAAAA,MAAY,MAAA,OAAA,sCAAA,SAAS,MAAM;AAE3B,cAAM,SAAS,MAAMC,WAAoB,qBAAC,MAAM;AAChDD,sBAAAA,MAAA,MAAA,OAAA,sCAAY,cAAc,MAAM;AAEhC,YAAI,OAAO,WAAW,OAAO,OAAO,MAAM;AACxC,eAAK,WAAW,OAAO,KAAK,QAAQ,CAAC;AACrC,eAAK,UAAU,KAAK,SAAS,WAAW;AACxCA,iFAAY,WAAW,KAAK,QAAQ;AAEpC,cAAI,KAAK,SAAS,SAAS,GAAG;AAC5BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,MAAM,KAAK,SAAS,MAAM;AAAA,cACjC,MAAM;AAAA,aACP;AAAA,UACH;AAAA,eACK;AACL,eAAK,UAAU;AACf,gBAAM,IAAI,MAAM,OAAO,OAAO,UAAU;AAAA,QAC1C;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,sCAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA,IAED,MAAM,kBAAkB;AACtBA,oBAAAA,MAAY,MAAA,OAAA,sCAAA,iBAAiB;AAC7B,WAAK,UAAU;AAEf,UAAI;AACF,cAAM,SAAS;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AACAA,sBAAAA,MAAY,MAAA,OAAA,sCAAA,SAAS,MAAM;AAE3B,cAAM,SAAS,MAAME,WAAiB,kBAAC,MAAM;AAC7CF,sBAAAA,MAAA,MAAA,OAAA,sCAAY,eAAe,MAAM;AAEjC,YAAI,OAAO,WAAW,OAAO,OAAO,MAAM;AACxC,eAAK,cAAc,OAAO,KAAK,QAAQ,CAAC;AACxC,eAAK,UAAU,KAAK,YAAY,WAAW;AAC3CA,wBAAA,MAAA,MAAA,OAAA,sCAAY,YAAY,KAAK,WAAW;AAExC,cAAI,KAAK,YAAY,SAAS,GAAG;AAC/BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,MAAM,KAAK,YAAY,MAAM;AAAA,cACpC,MAAM;AAAA,aACP;AAAA,UACH;AAAA,eACK;AACL,eAAK,UAAU;AACf,gBAAM,IAAI,MAAM,OAAO,OAAO,WAAW;AAAA,QAC3C;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,sCAAA,cAAc,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA,IAED,MAAM,UAAU,GAAG;AAEjB,YAAM,WAAW,SAAS,EAAE,cAAc,QAAQ,GAAG;AAGrD,UAAI,aAAa,KAAK,YAAY;AAChC;AAAA,MACF;AAEA,WAAK,aAAa;AAClB,WAAK,UAAU;AACf,YAAM,KAAK;IACZ;AAAA,IAED,cAAc,KAAK;AACjB,aAAO,QAAQ,IAAI,MAAM,QAAQ,IAAI,MAAM;AAAA,IAC5C;AAAA,IAED,YAAY,MAAM;AAChB,YAAM,QAAQ;AAAA,QACZ,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,aAAO,MAAM,IAAI,KAAK;AAAA,IACvB;AAAA,IAED,YAAY,MAAM;AAChB,YAAM,QAAQ;AAAA,QACZ,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,aAAO,MAAM,IAAI,KAAK;AAAA,IACvB;AAAA,IAED,cAAc,MAAM;AAClB,UAAI,KAAK,eAAe,GAAG;AAEzB,eAAO,KAAK,KAAK,kBAAkB,CAAC;AAAA,aAC/B;AAEL,eAAO,KAAK,KAAK,kBAAkB,CAAC;AAAA,MACtC;AAAA,IACD;AAAA,IAED,WAAW,SAAS;AAClB,YAAM,MAAM,oBAAI,KAAK;AACrB,YAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,YAAM,OAAO,MAAM;AACnB,YAAM,UAAU,KAAK,MAAM,QAAQ,MAAO,GAAG;AAE7C,UAAI,UAAU,IAAI;AAChB,eAAO,GAAG,OAAO;AAAA,MACnB,WAAW,UAAU,MAAM;AACzB,eAAO,GAAG,KAAK,MAAM,UAAU,EAAE,CAAC;AAAA,aAC7B;AACL,eAAO,GAAG,KAAK,MAAM,UAAU,IAAI,CAAC;AAAA,MACtC;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,OAAO,iBAAiB,QAAQ;;AAC1CA,oBAAAA,MAAc,MAAA,SAAA,sCAAA,SAAS,KAAK;AAE5B,UAAI,UAAU;AAEd,UAAI,OAAO,UAAU,UAAU;AAC7B,kBAAU;AAAA,MACZ,WAAW,SAAS,OAAO,UAAU,UAAU;AAC7C,YAAI,MAAM,SAAS,qBAAmB,WAAM,YAAN,mBAAe,SAAS,aAAY;AACxE,oBAAU;AAAA,QACZ,WAAW,MAAM,SAAS,eAAa,WAAM,YAAN,mBAAe,SAAS,aAAY;AACzE,oBAAU;AAAA,eACL;AACL,oBAAU,MAAM,OAAO,MAAM,aAAW,WAAM,SAAN,mBAAY,QAAO;AAAA,QAC7D;AAAA,MACF;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,OACX;AAED,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,mBAAmB;AACjB,YAAM,aAAaG,WAAAA,gBAAgB,KAAK,OAAO,MAAM,IAAI;AAEzD,UAAI,CAAC,YAAY;AACfH,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACfI,iCAAQ;AAAA,mBACH;AACLJ,4BAAAA,MAAI,aAAa;AAAA,YACnB;AAAA,UACF;AAAA,SACD;AACD,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACR;AAAA,IAED,gBAAgB,MAAM;AACpBA,oBAAAA,MAAY,MAAA,OAAA,sCAAA,WAAW,IAAI;AAC3BA,0BAAY,MAAA,OAAA,sCAAA,UAAU,KAAK,eAAe,IAAI,SAAS,OAAO;AAE9D,UAAI,CAAC,KAAK,IAAI;AACZA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEAA,oBAAA,MAAA,MAAA,OAAA,sCAAY,eAAe,KAAK,EAAE;AAGlCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,yBAAyB,KAAK,EAAE;AAAA,OACtC;AAAA,IACF;AAAA,IAED,cAAc,MAAM;AAElBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,+BAA+B,KAAK,EAAE,SAAS,KAAK,IAAI;AAAA,OAC9D;AAAA,IACF;AAAA,IAED,WAAW,MAAM;AAEfA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,mCAAmC,KAAK,EAAE,SAAS,KAAK,IAAI;AAAA,OAClE;AAAA,IACF;AAAA,IAED,WAAW;AACTA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACF;AAAA;AAAA,IAGD,YAAY,KAAK;AACf,WAAK,YAAY;AACjB,WAAK,MAAM,UAAU;AACrB,iBAAW,MAAM;AACf,aAAK,MAAM,UAAU;MACtB,GAAE,GAAI;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpcA,GAAG,WAAW,eAAe;"}