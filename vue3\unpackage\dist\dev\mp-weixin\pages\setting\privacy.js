"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_social = require("../../api/social.js");
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (navbar + _easycom_uni_popup)();
}
const navbar = () => "../../components/navbar/navbar.js";
const _sfc_main = {
  __name: "privacy",
  setup(__props) {
    const store = common_vendor.useStore();
    const statusBarHeight = common_vendor.computed(() => store.state.statusBarHeight || 20);
    const titleBarHeight = common_vendor.computed(() => store.state.titleBarHeight || 44);
    const privacySettings = common_vendor.reactive({
      showFans: true,
      showFollows: true,
      showVisitors: true,
      allowSearch: true
    });
    const flowSettings = common_vendor.reactive({
      dynamicFlow: false,
      circleFlow: false
    });
    const uiState = common_vendor.reactive({
      tipsTitle: "",
      privacyLoading: false,
      flowLoading: false
    });
    const tipsPopup = common_vendor.ref(null);
    const debounceTimers = common_vendor.reactive({
      privacy: null,
      flow: null
    });
    const debounce = (func, delay = 300, timerKey = "default") => {
      if (debounceTimers[timerKey]) {
        clearTimeout(debounceTimers[timerKey]);
      }
      debounceTimers[timerKey] = setTimeout(() => {
        func();
        debounceTimers[timerKey] = null;
      }, delay);
    };
    const handleError = (error, context = "操作") => {
      common_vendor.index.__f__("error", "at pages/setting/privacy.vue:166", `${context}失败:`, error);
      let message = `${context}失败，请稍后重试`;
      if (error.message) {
        message = error.message;
      } else if (error.msg) {
        message = error.msg;
      }
      showTip(message);
    };
    const showTip = (msg) => {
      uiState.tipsTitle = msg;
      tipsPopup.value.open();
      setTimeout(() => {
        tipsPopup.value.close();
      }, 2e3);
    };
    const getUserInfoFromCache = () => {
      try {
        let userInfo = common_vendor.index.getStorageSync("USER_INFO") || {};
        if (typeof userInfo === "string") {
          userInfo = JSON.parse(userInfo);
        }
        return typeof userInfo === "object" && userInfo !== null ? userInfo : {};
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/setting/privacy.vue:201", "解析USER_INFO缓存失败:", e);
        return {};
      }
    };
    const safeJsonParse = (data, defaultValue = {}) => {
      try {
        if (typeof data === "string") {
          return JSON.parse(data);
        } else if (typeof data === "object" && data !== null) {
          return data;
        }
        return defaultValue;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/setting/privacy.vue:216", "JSON解析失败:", e);
        return defaultValue;
      }
    };
    const loadFromCache = () => {
      try {
        const userInfo = getUserInfoFromCache();
        common_vendor.index.__f__("log", "at pages/setting/privacy.vue:226", "从缓存加载用户信息:", userInfo);
        if (userInfo.privacy_settings) {
          const cachedPrivacySettings = safeJsonParse(userInfo.privacy_settings, {});
          if (Object.keys(cachedPrivacySettings).length > 0) {
            Object.assign(privacySettings, cachedPrivacySettings);
            common_vendor.index.__f__("log", "at pages/setting/privacy.vue:235", "成功加载隐私设置:", privacySettings);
          }
        } else {
          common_vendor.index.__f__("log", "at pages/setting/privacy.vue:238", "缓存中没有隐私设置，使用默认值");
        }
        if (userInfo.flow_settings) {
          const cachedFlowSettings = safeJsonParse(userInfo.flow_settings, {});
          if (Object.keys(cachedFlowSettings).length > 0) {
            common_vendor.index.__f__("log", "at pages/setting/privacy.vue:246", "读取到的瀑布流设置:", cachedFlowSettings);
            const normalizedSettings = {
              dynamicFlow: Boolean(cachedFlowSettings.dynamicFlow),
              circleFlow: Boolean(cachedFlowSettings.circleFlow)
            };
            Object.assign(flowSettings, normalizedSettings);
            common_vendor.index.__f__("log", "at pages/setting/privacy.vue:256", "成功加载瀑布流设置:", flowSettings);
          }
        } else {
          common_vendor.index.__f__("log", "at pages/setting/privacy.vue:259", "缓存中没有瀑布流设置，使用默认值:", flowSettings);
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/setting/privacy.vue:262", "从缓存加载配置出错:", e);
      }
    };
    const updateLocalCache = (settingType, data) => {
      try {
        const userInfo = getUserInfoFromCache();
        common_vendor.index.__f__("log", "at pages/setting/privacy.vue:271", `更新${settingType}缓存`);
        let normalizedData = { ...data };
        if (settingType === "flow_settings") {
          normalizedData = {
            dynamicFlow: Boolean(data.dynamicFlow),
            circleFlow: Boolean(data.circleFlow)
          };
          common_vendor.index.__f__("log", "at pages/setting/privacy.vue:282", "标准化后的flow_settings:", normalizedData);
        }
        userInfo[settingType] = normalizedData;
        common_vendor.index.setStorageSync("USER_INFO", userInfo);
        common_vendor.index.__f__("log", "at pages/setting/privacy.vue:290", `${settingType}缓存更新完成`);
        common_vendor.index.$emit("userInfoUpdated", {
          type: settingType,
          data: userInfo
        });
        if (settingType === "flow_settings") {
          try {
            store.commit("UPDATE_FLOW_SETTINGS", normalizedData);
            common_vendor.index.__f__("log", "at pages/setting/privacy.vue:302", "全局状态更新完成:", normalizedData);
          } catch (error) {
            common_vendor.index.__f__("warn", "at pages/setting/privacy.vue:304", "更新全局状态失败:", error.message);
          }
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/setting/privacy.vue:308", "更新本地缓存失败:", e);
      }
    };
    const getSettingDisplayName = (type, isEnabled) => {
      const names = {
        showVisitors: "访客查看",
        allowSearch: "搜索发现",
        showFans: "粉丝查看",
        showFollows: "关注查看",
        dynamicFlow: "动态瀑布流",
        circleFlow: "圈子瀑布流"
      };
      const status = isEnabled ? "开启" : "关闭";
      return `${names[type] || type}已${status}`;
    };
    const switchPrivacy = (type) => {
      if (uiState.privacyLoading)
        return;
      debounce(() => {
        uiState.privacyLoading = true;
        const updateData = { ...privacySettings };
        updateData[type] = !updateData[type];
        Object.assign(privacySettings, updateData);
        updateLocalCache("privacy_settings", privacySettings);
        const requestData = {
          privacy_settings: updateData
        };
        api_social.updatePrivacySettings(requestData).then((res) => {
          if (res.code === 200 && res.data) {
            if (JSON.stringify(privacySettings) !== JSON.stringify(res.data)) {
              Object.assign(privacySettings, res.data);
              updateLocalCache("privacy_settings", privacySettings);
            }
            showTip(getSettingDisplayName(type, privacySettings[type]));
          }
        }).catch((error) => {
          handleError(error, "隐私设置更新");
        }).finally(() => {
          uiState.privacyLoading = false;
        });
      }, 300, "privacy");
    };
    const switchFlow = (type) => {
      if (uiState.flowLoading)
        return;
      debounce(() => {
        uiState.flowLoading = true;
        const updateData = { ...flowSettings };
        updateData[type] = !Boolean(updateData[type]);
        common_vendor.index.__f__("log", "at pages/setting/privacy.vue:381", "瀑布流设置变更:", type, "从", flowSettings[type], "到", updateData[type]);
        Object.assign(flowSettings, updateData);
        updateLocalCache("flow_settings", flowSettings);
        const requestData = {
          flow_settings: updateData
        };
        api_social.updateFlowSettings(requestData).then((res) => {
          if (res.code === 200 && res.data) {
            if (JSON.stringify(flowSettings) !== JSON.stringify(res.data)) {
              common_vendor.index.__f__("log", "at pages/setting/privacy.vue:399", "从服务器获取到不同的瀑布流设置，更新本地缓存");
              const serverData = { ...res.data };
              if (typeof serverData.dynamicFlow === "undefined") {
                serverData.dynamicFlow = false;
              }
              if (typeof serverData.circleFlow === "undefined") {
                serverData.circleFlow = false;
              }
              serverData.dynamicFlow = Boolean(serverData.dynamicFlow);
              serverData.circleFlow = Boolean(serverData.circleFlow);
              Object.assign(flowSettings, serverData);
              updateLocalCache("flow_settings", flowSettings);
            }
            showTip(getSettingDisplayName(type, flowSettings[type]));
          }
        }).catch((error) => {
          handleError(error, "瀑布流设置更新");
        }).finally(() => {
          uiState.flowLoading = false;
        });
      }, 300, "flow");
    };
    const clearTimers = () => {
      Object.keys(debounceTimers).forEach((key) => {
        if (debounceTimers[key]) {
          clearTimeout(debounceTimers[key]);
          debounceTimers[key] = null;
        }
      });
    };
    common_vendor.onMounted(() => {
      loadFromCache();
    });
    common_vendor.onUnmounted(() => {
      clearTimers();
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          bg: 1
        }),
        b: statusBarHeight.value + titleBarHeight.value + "px",
        c: common_assets._imports_8$4,
        d: privacySettings.showFans,
        e: common_vendor.o(($event) => switchPrivacy("showFans")),
        f: common_assets._imports_4$5,
        g: privacySettings.showFollows,
        h: common_vendor.o(($event) => switchPrivacy("showFollows")),
        i: common_assets._imports_6$3,
        j: privacySettings.showVisitors,
        k: common_vendor.o(($event) => switchPrivacy("showVisitors")),
        l: common_assets._imports_7,
        m: privacySettings.allowSearch,
        n: common_vendor.o(($event) => switchPrivacy("allowSearch")),
        o: common_assets._imports_4$6,
        p: flowSettings.dynamicFlow,
        q: common_vendor.o(($event) => switchFlow("dynamicFlow")),
        r: common_assets._imports_5$6,
        s: flowSettings.circleFlow,
        t: common_vendor.o(($event) => switchFlow("circleFlow")),
        v: common_assets._imports_6$4,
        w: common_assets._imports_1$4,
        x: common_vendor.t(uiState.tipsTitle),
        y: common_vendor.sr(tipsPopup, "0f600d97-1", {
          "k": "tipsPopup"
        }),
        z: common_vendor.p({
          type: "top",
          ["mask-background-color"]: "rgba(0, 0, 0, 0)"
        })
      };
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/setting/privacy.js.map
