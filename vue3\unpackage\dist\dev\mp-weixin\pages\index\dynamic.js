"use strict";
const common_vendor = require("../../common/vendor.js");
const api_social = require("../../api/social.js");
const libs_login = require("../../libs/login.js");
const stores_user = require("../../stores/user.js");
const common_assets = require("../../common/assets.js");
const lazyImage = () => "../../components/lazyImage/lazyImage.js";
const uniLoadMore = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const waterfall = () => "../../components/waterfall/waterfall.js";
const cardGg = () => "../../components/card-gg/card-gg.js";
const tabbar = () => "../../components/tabbar/tabbar.js";
const emptyPage = () => "../../components/emptyPage/emptyPage.js";
const _sfc_main = {
  components: {
    lazyImage,
    uniLoadMore,
    waterfall,
    cardGg,
    tabbar,
    emptyPage
  },
  data() {
    var _a, _b, _c, _d;
    return {
      userStore: stores_user.useUserStore(),
      statusBarHeight: ((_b = (_a = this.$store) == null ? void 0 : _a.state) == null ? void 0 : _b.statusBarHeight) || 20,
      titleBarHeight: ((_d = (_c = this.$store) == null ? void 0 : _c.state) == null ? void 0 : _d.titleBarHeight) || 44,
      currentMsg: 0,
      userCity: "",
      userUid: 0,
      userLatitude: 0,
      userLongitude: 0,
      navList: ["关注", "推荐", "附近"],
      navIdx: 1,
      list: [],
      // 优化后的缓存系统
      cachedData: {
        0: { list: [], page: 1, hasMore: true, lastUpdate: 0, renderItems: 15, totalCount: 0 },
        1: { list: [], page: 1, hasMore: true, lastUpdate: 0, renderItems: 15, totalCount: 0 },
        2: { list: [], page: 1, hasMore: true, lastUpdate: 0, renderItems: 15, totalCount: 0 }
      },
      circle: [],
      // 圈子数据
      circleLoaded: false,
      // 圈子是否已加载
      page: 1,
      totalCount: 0,
      // 总数据量
      isEmpty: false,
      loadStatus: "more",
      isWaterfall: false,
      contentHeight: 0,
      // 加载状态管理 - 参考center.vue
      loadingStates: {
        refresh: false,
        loadMore: false,
        initial: false
      },
      // 防抖定时器 - 提升流畅度
      requestTimers: {
        loadData: null
      },
      // 简化的加载状态管理
      isLoading: false,
      isRefreshing: false,
      isSwitching: false,
      // 优化渲染性能
      renderItems: 15,
      renderStep: 10,
      maxRenderItems: 100,
      // 基础状态
      _firstLoad: true,
      _lastLoginState: null,
      _navigateLock: false,
      // 缓存配置
      cacheConfig: {
        maxAge: 5 * 60 * 1e3,
        // 5分钟缓存过期
        maxItems: 50,
        // 每个tab最多缓存50条
        preloadThreshold: 3
        // 剩余3条时预加载
      },
      // 性能优化
      debounceTimer: null,
      scrollTimer: null,
      scrollRAF: null,
      // 格式化缓存
      _formattedCache: null,
      _lastListLength: 0,
      _reachEndThrottle: false,
      // 性能监控
      _performanceStart: 0
    };
  },
  mounted() {
    this.calcContentHeight();
    this.initPlatformFeatures();
    this.getUserLocation();
  },
  beforeDestroy() {
  },
  async onLoad() {
    this.calcContentHeight();
    common_vendor.index.$on("flowSettingsUpdated", this.handleFlowSettingsUpdate);
    common_vendor.index.$on("loginStateChanged", this.handleLoginStateChanged);
    this.initUserInfoFromCache();
    this._firstLoad = true;
    this._lastLoginState = this.isLogin;
    common_vendor.index.setNavigationBarColor({
      frontColor: "#000000",
      backgroundColor: "#ffffff",
      animation: { duration: 400, timingFunc: "easeIn" }
    });
    this.$nextTick(() => {
      this.initializePageData().catch((err) => {
        this.handleInitError();
      });
    });
  },
  onShow() {
    const token = this.userStore.token;
    const isLoggedIn = this.isLogin && token;
    if (this._lastLoginState !== isLoggedIn) {
      this._lastLoginState = isLoggedIn;
      this.handleLoginStateChange(isLoggedIn);
    }
    if (isLoggedIn) {
      const userInfo = this.getUserInfoFromCache();
      this.currentMsg = userInfo.service_num || 0;
    }
    this.checkCacheExpiry();
    common_vendor.index.setNavigationBarColor({
      frontColor: "#000000",
      backgroundColor: "#ffffff",
      animation: { duration: 400, timingFunc: "easeIn" }
    });
  },
  onPullDownRefresh() {
    this.page = 1;
    this.loadStatus = "more";
    this.isEmpty = false;
    this.isRefreshing = true;
    this.loadData(true).catch(() => {
      common_vendor.index.showToast({
        title: "刷新失败，请稍后再试",
        icon: "none",
        duration: 2e3
      });
    }).finally(() => {
      setTimeout(() => {
        this.isRefreshing = false;
        common_vendor.index.stopPullDownRefresh();
      }, 300);
    });
  },
  onReachBottom() {
    if (this.isLoading || this.loadingStates.loadMore || this.loadStatus === "noMore") {
      return;
    }
    const isLoggedIn = this.isLogin && this.$store.state.app.token;
    if (this.navIdx === 0 && !isLoggedIn) {
      return;
    }
    const canLoadMore = this.list.length > 0 && (this.totalCount === 0 || this.list.length < this.totalCount) && this.loadStatus !== "noMore";
    if (canLoadMore) {
      this.loadingStates.loadMore = true;
      this.loadStatus = "loading";
      const currentPage = this.page;
      this.page++;
      this.loadData(false, true).catch((err) => {
        this.page = currentPage;
        this.loadStatus = "more";
        if (err.message !== "正在加载中") {
          this.showErrorToast("加载更多失败，请稍后再试", 2e3);
        }
      }).finally(() => {
        this.loadingStates.loadMore = false;
      });
    } else if (this.list.length >= this.totalCount && this.list.length > 0) {
      this.loadStatus = "noMore";
    }
  },
  onShareAppMessage() {
    return {
      title: this.$store.state.appXx[1] || "小程序示例",
      imageUrl: this.$store.state.appXx[2] || "/static/img/avatar.png"
    };
  },
  onShareTimeline() {
    return {
      title: this.$store.state.appXx[1] || "小程序示例",
      imageUrl: this.$store.state.appXx[2] || "/static/img/avatar.png"
    };
  },
  onUnload() {
    try {
      if (common_vendor.index.$off) {
        common_vendor.index.$off("flowSettingsUpdated", this.handleFlowSettingsUpdate);
        common_vendor.index.$off("loginStateChanged", this.handleLoginStateChanged);
      }
      if (this.cleanupPlatformFeatures) {
        this.cleanupPlatformFeatures();
      }
      if (this.resetLoadingState) {
        this.resetLoadingState();
      }
      if (this.clearTimers) {
        this.clearTimers();
      }
      if (this.performMemoryCleanup) {
        this.performMemoryCleanup();
      }
      this.cachedData = {};
      this._formattedCache = null;
    } catch (error) {
    }
  },
  methods: {
    toLogin() {
      libs_login.toLogin();
    },
    // 初始化页面数据
    async initializePageData() {
      try {
        this.isLoading = true;
        this.loadFlowSettings();
        this.getUserLocation();
        await this.loadData(true);
      } catch (error) {
        throw error;
      } finally {
        this.isLoading = false;
      }
    },
    // 处理初始化错误
    handleInitError() {
      this.isEmpty = true;
      this.loadStatus = "more";
      common_vendor.index.showModal({
        title: "加载失败",
        content: "页面初始化失败，是否重试？",
        confirmText: "重试",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            this.initializePageData();
          }
        }
      });
    },
    // 处理登录状态变化
    handleLoginStateChange(isLoggedIn) {
      if (isLoggedIn) {
        if (this.navIdx === 0) {
          this.clearTabCache(0);
          this.page = 1;
          this.isEmpty = false;
          this.loadStatus = "loading";
          this.loadData(true);
        }
      } else {
        if (this.navIdx === 0) {
          this.list = [];
          this.isEmpty = false;
          this.loadStatus = "more";
        }
      }
    },
    // 检查缓存过期
    checkCacheExpiry() {
      const now = Date.now();
      const currentCache = this.cachedData[this.navIdx];
      if (currentCache && currentCache.lastUpdate) {
        const age = now - currentCache.lastUpdate;
        if (age > this.cacheConfig.maxAge) {
          this.refreshCurrentTab(true);
        }
      }
    },
    // 刷新当前tab
    async refreshCurrentTab(silent = false) {
      if (this.loadingStates.refresh && !silent)
        return;
      try {
        this.loadingStates.refresh = true;
        this.page = 1;
        await this.loadData(true);
      } catch (error) {
      } finally {
        this.loadingStates.refresh = false;
      }
    },
    // 清空指定tab缓存
    clearTabCache(tabIndex) {
      if (this.cachedData[tabIndex]) {
        this.cachedData[tabIndex] = {
          list: [],
          page: 1,
          hasMore: true,
          lastUpdate: 0,
          renderItems: 15
        };
      }
    },
    getUserSocialData() {
      if (!this.isLogin || !this.$store.state.app.token) {
        this.currentMsg = 0;
        this.userUid = 0;
        return Promise.resolve();
      }
      const userInfo = this.getUserInfoFromCache();
      this.currentMsg = userInfo.service_num || 0;
      this.userUid = this.userStore.uid || 0;
      return Promise.resolve(userInfo);
    },
    handleLoginStateChanged(isLoggedIn) {
      if (isLoggedIn) {
        this.userUid = this.$store.state.app.uid || 0;
        const userInfo = this.getUserInfoFromCache();
        if (userInfo) {
          this.userCity = userInfo.residence_name || userInfo.city || "广州";
          this.userAvatar = userInfo.avatar || "/static/img/avatar.png";
          this.currentMsg = userInfo.service_num || 0;
        }
        this.refreshFlowSettings().then(() => {
          if (this.navIdx === 0) {
            this.cachedData[0] = { list: [], page: 1, hasMore: true, lastUpdate: 0, renderItems: 15, totalCount: 0 };
            this.page = 1;
            this.isEmpty = false;
            this.loadStatus = "loading";
            this.loadData(true);
          }
        }).catch(() => {
        });
      } else if (!isLoggedIn && this.navIdx === 0) {
        this.list = [];
        this.isEmpty = false;
        this.isWaterfall = false;
        this.currentMsg = 0;
      }
    },
    calcContentHeight() {
      const windowHeight = common_vendor.index.getSystemInfoSync().windowHeight;
      const navHeight = this.statusBarHeight + this.titleBarHeight;
      this.contentHeight = windowHeight - navHeight;
    },
    async swiperChange(e) {
      const current = e.detail.current;
      if (this.navIdx === current || this.isSwitching)
        return;
      try {
        this.isSwitching = true;
        this.saveCurrentTabData();
        this.navIdx = current;
        this.loadFlowSettings();
        const isLoggedIn = this.isLogin && this.$store.state.app.token;
        if (current === 0 && !isLoggedIn) {
          this.list = [];
          this.isEmpty = false;
          this.loadStatus = "more";
          return;
        }
        if (isLoggedIn) {
          this.initUserInfoFromCache();
        }
        if (current === 1 && !this.circleLoaded) {
          this.getHotCircles();
          this.circleLoaded = true;
        }
        const cachedData = this.getCachedData();
        if (cachedData && cachedData.list.length > 0) {
          this.list = cachedData.list;
          this.page = cachedData.page;
          this.totalCount = cachedData.totalCount || 0;
          this.isEmpty = false;
          this.loadStatus = cachedData.hasMore ? "more" : "noMore";
          this.renderItems = cachedData.renderItems || this.list.length;
          const now = Date.now();
          const age = now - cachedData.lastUpdate;
          if (age > this.cacheConfig.maxAge / 2) {
            this.refreshCurrentTab(true);
          }
        } else {
          this.page = 1;
          this.isEmpty = false;
          this.loadStatus = "loading";
          this.list = [];
          this.renderItems = 15;
          await this.loadData(true);
        }
      } catch (error) {
        this.handleTabSwitchError();
      } finally {
        this.isSwitching = false;
      }
    },
    // 保存当前tab数据
    saveCurrentTabData() {
      if (this.list.length > 0) {
        const cache = this.cachedData[this.navIdx];
        if (!cache || typeof cache !== "object" || Array.isArray(cache)) {
          this.cachedData[this.navIdx] = { list: [], page: 1, hasMore: true, lastUpdate: 0, renderItems: 15, totalCount: 0 };
        }
        const validCache = this.cachedData[this.navIdx];
        validCache.list = [...this.list];
        validCache.page = this.page;
        validCache.hasMore = this.loadStatus !== "noMore";
        validCache.lastUpdate = Date.now();
        validCache.renderItems = this.renderItems;
        validCache.totalCount = this.totalCount;
      }
    },
    // 处理tab切换错误
    handleTabSwitchError() {
      this.list = [];
      this.isEmpty = true;
      this.loadStatus = "more";
      common_vendor.index.showToast({
        title: "切换失败，请重试",
        icon: "none",
        duration: 2e3
      });
    },
    onScroll() {
      if (this.scrollRAF)
        return;
      this.scrollRAF = requestAnimationFrame(() => {
        this.checkInViewItems();
        this.scrollRAF = null;
      });
    },
    checkInViewItems() {
      if (!this.list || this.list.length === 0)
        return;
      if (this.list.length > this.renderItems) {
        const oldRenderItems = this.renderItems;
        this.renderItems = Math.min(this.list.length, this.renderItems + this.renderStep);
        if (oldRenderItems !== this.renderItems) {
          this._formattedCache = null;
        }
      }
    },
    cancelAllRequests() {
      this.requestQueue = [];
    },
    // 优化的数据加载方法 - 提升上拉加载流畅度
    async loadData(force = false, isLoadMore = false) {
      if (!force && (this.isLoading || this.isSwitching)) {
        return Promise.resolve(null);
      }
      const isLoggedIn = this.isLogin && this.$store.state.app.token;
      if (this.navIdx === 0 && !isLoggedIn) {
        this.list = [];
        this.isEmpty = false;
        this.loadStatus = "more";
        return Promise.resolve(null);
      }
      try {
        if (this.page === 1 && !isLoadMore) {
          this.isRefreshing = true;
          if (!this.isLoading) {
            this.showLoadingWithMessage();
          }
        }
        if (!isLoadMore) {
          this.loadStatus = "loading";
        }
        const cachedData = this.getCachedData();
        if (cachedData && !force && this.page === 1 && !isLoadMore) {
          this.list = cachedData.list;
          this.totalCount = cachedData.totalCount || 0;
          this.isEmpty = cachedData.list.length === 0;
          this.loadStatus = cachedData.hasMore ? "more" : "noMore";
          this.renderItems = cachedData.renderItems || this.list.length;
          return Promise.resolve(cachedData);
        }
        if (isLoggedIn && !isLoadMore) {
          this.refreshFlowSettings();
        }
        if (this.requestTimers.loadData) {
          clearTimeout(this.requestTimers.loadData);
        }
        const delay = isLoadMore ? 50 : 100;
        await new Promise((resolve) => {
          this.requestTimers.loadData = setTimeout(resolve, delay);
        });
        let result;
        if (this.navIdx === 0) {
          const userInfo = this.getUserInfoFromCache();
          this.userUid = userInfo.uid || 0;
          result = await this.dynamicFollow("follow", force);
        } else if (this.navIdx === 1) {
          if (!this.circleLoaded) {
            this.getHotCircles();
            this.circleLoaded = true;
          }
          this.userUid = isLoggedIn ? this.$store.state.app.uid || 0 : 0;
          result = this.isWaterfall ? await this.dynamicRecommendWaterfall(force) : await this.dynamicRecommend(force);
        } else if (this.navIdx === 2) {
          this.userUid = isLoggedIn ? this.$store.state.app.uid || 0 : 0;
          result = this.isWaterfall ? await this.dynamicRecommendWaterfall(force) : await this.dynamicFollow("nearby", force);
        }
        this._formattedCache = null;
        this.updateCache(result);
        return result;
      } catch (error) {
        this.handleLoadError(error);
        throw error;
      } finally {
        if (!isLoadMore) {
          this.isRefreshing = false;
          common_vendor.index.hideLoading();
        }
      }
    },
    // 获取缓存数据
    getCachedData() {
      const cache = this.cachedData[this.navIdx];
      if (!cache || typeof cache !== "object" || Array.isArray(cache) || !cache.list || !cache.list.length) {
        return null;
      }
      const now = Date.now();
      const age = now - (cache.lastUpdate || 0);
      if (age > this.cacheConfig.maxAge) {
        return null;
      }
      return cache;
    },
    // 更新缓存
    updateCache(data) {
      if (!data)
        return;
      const cache = this.cachedData[this.navIdx];
      if (!cache || typeof cache !== "object" || Array.isArray(cache)) {
        this.cachedData[this.navIdx] = { list: [], page: 1, hasMore: true, lastUpdate: 0, renderItems: 15, totalCount: 0 };
      }
      const validCache = this.cachedData[this.navIdx];
      validCache.lastUpdate = Date.now();
      if (this.page === 1) {
        validCache.list = [...this.list];
        validCache.page = 1;
      } else {
        validCache.list = [...this.list];
        validCache.page = this.page;
      }
      validCache.hasMore = this.loadStatus !== "noMore";
      validCache.renderItems = this.renderItems;
      if (validCache.list.length > this.cacheConfig.maxItems) {
        validCache.list = validCache.list.slice(0, this.cacheConfig.maxItems);
        validCache.renderItems = Math.min(validCache.renderItems, validCache.list.length);
      }
    },
    // 增强的错误处理
    handleLoadError(error) {
      this.loadStatus = "more";
      if (this.page === 1) {
        this.isEmpty = true;
        this.list = [];
      }
      const errorMessage = this.getErrorMessage(error);
      if (error !== "loadCancelled") {
        setTimeout(() => {
          common_vendor.index.showModal({
            title: "加载失败",
            content: errorMessage,
            confirmText: "重试",
            cancelText: "取消",
            success: (res) => {
              if (res.confirm) {
                this.retryLoad();
              }
            }
          });
        }, 300);
      }
    },
    // 获取错误信息
    getErrorMessage(error) {
      if (typeof error === "string") {
        return error;
      }
      if (error == null ? void 0 : error.msg) {
        return error.msg;
      }
      if (error == null ? void 0 : error.code) {
        switch (error.code) {
          case "NETWORK_ERROR":
            return "网络连接异常，请检查网络设置";
          case "TIMEOUT":
            return "请求超时，请稍后重试";
          case "SERVER_ERROR":
            return "服务器异常，请稍后重试";
          default:
            return "加载失败，请重试";
        }
      }
      if (error == null ? void 0 : error.status) {
        if (error.status === 400) {
          return error.msg || "请求参数错误";
        } else if (error.status === 401) {
          return "登录已过期，请重新登录";
        } else if (error.status === 403) {
          return "没有权限访问";
        } else if (error.status === 404) {
          return "请求的资源不存在";
        } else if (error.status >= 500) {
          return "服务器内部错误";
        }
      }
      return (error == null ? void 0 : error.message) || "无法获取数据，请检查网络连接";
    },
    // 重试加载
    retryLoad() {
      this.resetLoadingState();
      setTimeout(() => {
        this.loadData(true).catch((err) => {
          common_vendor.index.showToast({
            title: "重试失败，请稍后再试",
            icon: "none",
            duration: 2e3
          });
        });
      }, 500);
    },
    likeClick(data) {
      if (data && typeof data.index !== "undefined" && data.id) {
        const listItem = this.list.find((item) => item.id === data.id);
        if (listItem) {
          listItem.is_like = data.isLike ? 1 : 0;
          if (data.isLike) {
            listItem.likes = (parseInt(listItem.likes) || 0) + 1;
          } else {
            listItem.likes = Math.max(0, (parseInt(listItem.likes) || 0) - 1);
          }
          listItem._formatted = false;
          this._formattedCache = null;
          this.$nextTick(() => {
            this.$forceUpdate();
          });
        }
      }
    },
    followClick(data) {
      if (data && data.uid) {
        this.list.forEach((item) => {
          if (item.uid === data.uid) {
            if (item.user_info) {
              item.user_info.is_follow = data.is_follow;
              item.user_info.is_mutual_follow = data.is_mutual || 0;
            }
            if (item.user) {
              item.user.is_follow = data.is_follow;
              item.user.is_mutual_follow = data.is_mutual || 0;
            }
          }
        });
        this._formattedCache = null;
        this.$nextTick(() => {
          this.$forceUpdate();
        });
      }
    },
    waterfallLikeClick(data) {
      this.likeClick(data);
    },
    navigateToFun(e) {
      if (!e || !e.currentTarget || !e.currentTarget.dataset || !e.currentTarget.dataset.url) {
        return;
      }
      if (this._navigateLock)
        return;
      this._navigateLock = true;
      const url = "/pages/" + e.currentTarget.dataset.url;
      common_vendor.index.navigateTo({
        url,
        complete: () => {
          setTimeout(() => {
            this._navigateLock = false;
          }, 500);
        }
      });
    },
    loadFlowSettings() {
      try {
        const userInfo = this.getUserInfoFromCache();
        let flowSettings = userInfo.flow_settings;
        if (!flowSettings) {
          flowSettings = { dynamicFlow: false, circleFlow: false };
        } else if (typeof flowSettings === "string") {
          try {
            let jsonStr = flowSettings.trim();
            jsonStr = jsonStr.replace(/\\"/g, '"');
            if (jsonStr.startsWith('"') && jsonStr.endsWith('"') || jsonStr.startsWith("'") && jsonStr.endsWith("'")) {
              jsonStr = jsonStr.slice(1, -1);
            }
            flowSettings = JSON.parse(jsonStr);
          } catch (e) {
            flowSettings = { dynamicFlow: false, circleFlow: false };
          }
        } else if (typeof flowSettings !== "object" || flowSettings === null) {
          flowSettings = { dynamicFlow: false, circleFlow: false };
        }
        if (!flowSettings.hasOwnProperty("dynamicFlow")) {
          flowSettings.dynamicFlow = false;
        }
        const oldWaterfall = this.isWaterfall;
        if (this.navIdx === 0) {
          this.isWaterfall = false;
        } else {
          this.isWaterfall = flowSettings.dynamicFlow === true;
        }
        if (oldWaterfall !== this.isWaterfall) {
          this.$forceUpdate();
        }
        return flowSettings;
      } catch (e) {
        this.isWaterfall = false;
        return { dynamicFlow: false, circleFlow: false };
      }
    },
    refreshFlowSettings() {
      return new Promise((resolve) => {
        try {
          this.loadFlowSettings();
          resolve(true);
        } catch (error) {
          this.isWaterfall = false;
          resolve(false);
        }
      });
    },
    dynamicRecommend(forceLoad = false) {
      if (!forceLoad && this.isLoading) {
        return Promise.reject("正在加载中");
      }
      this.isLoading = true;
      const params = {
        page: this.page,
        limit: 10,
        // 添加limit参数
        type: "hot"
      };
      return api_social.getDynamicList(params).then((res) => {
        common_vendor.index.hideLoading();
        this.isLoading = false;
        if (res.status === 200) {
          const list = res.data.list || [];
          if (this.page === 1) {
            this.list = list;
          } else {
            this.list.push(...list);
          }
          if (res.data.count !== void 0) {
            this.totalCount = res.data.count;
          }
          if (list.length === 0) {
            this.loadStatus = "noMore";
          } else if (this.totalCount > 0 && this.list.length >= this.totalCount) {
            this.loadStatus = "noMore";
          } else if (list.length < 10) {
            this.loadStatus = "noMore";
          } else {
            this.loadStatus = "more";
          }
          this.isEmpty = list.length === 0 && this.page === 1;
          if (this.list.length > this.renderItems) {
            this.renderItems = this.list.length;
          }
          this.renderMode();
          return res.data;
        } else {
          this.handleApiError(res);
          return Promise.reject(res.msg || "获取推荐失败");
        }
      }).catch((err) => {
        this.handleApiError(err);
        return Promise.reject(err);
      });
    },
    dynamicRecommendWaterfall(forceLoad = false) {
      if (!forceLoad && this.isLoading) {
        return Promise.reject("正在加载中");
      }
      this.isLoading = true;
      const params = {
        page: this.page,
        limit: 10,
        // 添加limit参数
        type: "hot",
        waterfall: 1
      };
      return api_social.getDynamicList(params).then((res) => {
        common_vendor.index.hideLoading();
        this.isLoading = false;
        if (res.status === 200) {
          const list = res.data.list || [];
          if (this.page === 1) {
            this.list = list;
          } else {
            this.list.push(...list);
          }
          if (res.data.count !== void 0) {
            this.totalCount = res.data.count;
          }
          if (list.length === 0) {
            this.loadStatus = "noMore";
          } else if (this.totalCount > 0 && this.list.length >= this.totalCount) {
            this.loadStatus = "noMore";
          } else if (list.length < 10) {
            this.loadStatus = "noMore";
          } else {
            this.loadStatus = "more";
          }
          this.isEmpty = list.length === 0 && this.page === 1;
          if (this.list.length > this.renderItems) {
            this.renderItems = this.list.length;
          }
          this.renderMode();
          return res.data;
        } else {
          this.loadStatus = "more";
          if (this.page === 1) {
            this.isEmpty = true;
            this.list = [];
          }
          common_vendor.index.showToast({
            title: res.msg || "获取推荐失败",
            icon: "none",
            duration: 2e3
          });
          return Promise.reject(res.msg || "获取瀑布流数据失败");
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        this.isLoading = false;
        this.loadStatus = "more";
        if (this.page === 1) {
          this.isEmpty = true;
          this.list = [];
        }
        common_vendor.index.showToast({
          title: "网络连接异常，请检查网络设置",
          icon: "none",
          duration: 3e3
        });
        return Promise.reject(err);
      });
    },
    dynamicFollow(type = "follow", forceLoad = false) {
      if (!forceLoad && this.isLoading) {
        return Promise.resolve(null);
      }
      this.isLoading = true;
      const params = {
        page: this.page,
        limit: 10,
        // 添加limit参数
        type
      };
      if (type === "nearby") {
        params.longitude = this.userLongitude || 113.26436;
        params.latitude = this.userLatitude || 23.12908;
      }
      return api_social.getDynamicList(params).then((res) => {
        this.isLoading = false;
        common_vendor.index.hideLoading();
        if (res.status === 200) {
          const list = res.data.list || [];
          if (this.page === 1) {
            this.list = list;
          } else {
            this.list.push(...list);
          }
          if (res.data.count !== void 0) {
            this.totalCount = res.data.count;
          }
          if (list.length === 0) {
            this.loadStatus = "noMore";
          } else if (this.totalCount > 0 && this.list.length >= this.totalCount) {
            this.loadStatus = "noMore";
          } else if (list.length < 10) {
            this.loadStatus = "noMore";
          } else {
            this.loadStatus = "more";
          }
          this.isEmpty = list.length === 0 && this.page === 1;
          if (this.list.length > this.renderItems) {
            this.renderItems = this.list.length;
          }
          this.renderMode();
          return res.data;
        } else {
          this.loadStatus = "more";
          if (this.page === 1) {
            this.isEmpty = true;
            this.list = [];
          }
          common_vendor.index.showToast({
            title: res.msg || "获取动态失败",
            icon: "none",
            duration: 2e3
          });
          return Promise.reject(res.msg || "获取动态失败");
        }
      }).catch((err) => {
        this.isLoading = false;
        common_vendor.index.hideLoading();
        this.loadStatus = "more";
        if (this.page === 1) {
          this.isEmpty = true;
          this.list = [];
        }
        if (err !== "loadCancelled") {
          common_vendor.index.showToast({
            title: "网络连接异常，请检查网络设置",
            icon: "none",
            duration: 3e3
          });
        }
        return Promise.reject(err);
      });
    },
    renderMode() {
      if (!this.renderItems || this.renderItems < 10) {
        this.renderItems = 10;
      }
      if (!this.renderStep) {
        this.renderStep = 5;
      }
      if (!this.list || this.list.length === 0) {
        return;
      }
      this.renderItems = Math.min(this.list.length, this.renderItems);
    },
    onReachEnd() {
      if (this._reachEndThrottle)
        return;
      this._reachEndThrottle = true;
      setTimeout(() => {
        this._reachEndThrottle = false;
      }, 300);
      if (this.isLoading || this.isRefreshing || this.loadStatus === "noMore") {
        return;
      }
      const isLoggedIn = this.isLogin && this.$store.state.app.token;
      if (this.navIdx === 0 && !isLoggedIn) {
        return;
      }
      const remainingItems = this.list.length - this.renderItems;
      if (remainingItems > this.cacheConfig.preloadThreshold) {
        return;
      }
      if (isLoggedIn && !this.userUid) {
        this.initUserInfoFromCache();
      }
      const currentPage = this.page;
      this.page++;
      this.loadStatus = "loading";
      this.$nextTick(() => {
        this.loadData(true).catch((error) => {
          this.page = currentPage;
          this.loadStatus = "more";
          common_vendor.index.showToast({
            title: "加载失败，请重试",
            icon: "none",
            duration: 2e3
          });
        });
      });
    },
    // 显示错误提示
    showErrorToast(message, duration = 2e3) {
      common_vendor.index.showToast({
        title: message,
        icon: "none",
        duration
      });
    },
    formatDynamicData(data) {
      if (!data || typeof data !== "object" || data._formatted) {
        return data;
      }
      const result = Object.assign({}, data);
      const id = result.id || Date.now() + Math.floor(Math.random() * 1e3);
      const type = this.determineContentType(result);
      const user = this.formatUserInfo(result);
      const user_id = result.uid || result.user_id || (user == null ? void 0 : user.id) || 0;
      Object.assign(result, {
        id,
        type,
        user,
        user_id,
        uid: user_id,
        like_count: result.like_count || result.likes || 0,
        likes: result.likes || result.like_count || 0,
        comment_count: result.comment_count || result.comments || 0,
        create_time_str: result.create_time_str || result.time_str || result.create_time || "刚刚",
        province: result.province || "",
        browse: result.browse || result.view_count || result.views || 0,
        is_like: result.is_like ? 1 : 0
      });
      result.like_count_str = String(result.like_count);
      result.comment_count_str = String(result.comment_count);
      this.formatMediaContent(result);
      if (result.adds_name) {
        result.lat = result.lat || 0;
        result.lng = result.lng || 0;
      }
      result.circle_id = result.circle_id || 0;
      result.activity_id = result.activity_id || 0;
      result.order_id = result.order_id || 0;
      if (result.comment && typeof result.comment === "object") {
        if (!result.comment.user_name && result.comment.user) {
          result.comment.user_name = result.comment.user.name || "";
        }
      }
      result._formatted = true;
      return result;
    },
    // 确定内容类型
    determineContentType(data) {
      if (data.type !== void 0) {
        return data.type;
      }
      if (data.video || data.video_url) {
        return 3;
      } else if (data.audio) {
        return 4;
      } else if (data.images && Array.isArray(data.images) && data.images.length > 0 || data.imgs && Array.isArray(data.imgs) && data.imgs.length > 0) {
        return 2;
      } else {
        return 1;
      }
    },
    // 格式化用户信息
    formatUserInfo(data) {
      var _a, _b;
      if (data.user) {
        return data.user;
      }
      return {
        id: data.uid || data.user_id || 0,
        avatar: ((_a = data.user_info) == null ? void 0 : _a.avatar) || data.user_avatar || "/static/img/avatar.png",
        name: ((_b = data.user_info) == null ? void 0 : _b.nickname) || data.user_name || "用户",
        gender: data.user_gender || 1,
        age: data.user_age || ""
      };
    },
    // 格式化媒体内容
    formatMediaContent(result) {
      if (result.type === 2) {
        this.formatImageContent(result);
      } else if (result.type === 3) {
        this.formatVideoContent(result);
      } else if (result.type === 4) {
        this.formatAudioContent(result);
      }
    },
    // 格式化图片内容
    formatImageContent(result) {
      if (!result.imgs || !Array.isArray(result.imgs)) {
        result.imgs = [];
        if (result.images && Array.isArray(result.images)) {
          result.imgs = result.images.map((url) => {
            return typeof url === "string" ? { url } : url;
          });
        }
        if (result.img && result.img.url) {
          const found = result.imgs.some((img) => img.url === result.img.url);
          if (!found) {
            result.imgs.unshift(result.img);
          }
        }
      }
      result.imgs = result.imgs.map((img) => {
        if (typeof img === "string") {
          return { url: img, wide: 800, high: 600 };
        }
        return {
          url: img.url || "",
          wide: img.wide || 800,
          high: img.high || 600
        };
      });
      result.img_count = result.imgs.length;
    },
    // 格式化视频内容
    formatVideoContent(result) {
      if (!result.video || typeof result.video === "string") {
        result.video = {
          url: result.video || result.video_url || "",
          cover: result.video_cover || "",
          wide: result.video_width || 720,
          high: result.video_height || 1280
        };
      }
    },
    // 格式化音频内容
    formatAudioContent(result) {
      if (!result.audio) {
        result.audio = {
          name: result.audio_name || "未知音频",
          intro: result.audio_intro || "暂无简介",
          cover: result.audio_cover || "/static/img/default_bg.jpg"
        };
      }
    },
    touchStart(e) {
      this.touchStartY = e.touches[0].pageY;
    },
    touchMove(e) {
      e.stopPropagation();
    },
    navClick(e) {
      var _a;
      if (!((_a = e == null ? void 0 : e.currentTarget) == null ? void 0 : _a.dataset))
        return;
      const index = parseInt(e.currentTarget.dataset.idx || 0);
      if (this.navIdx === index || this.isSwitching)
        return;
      this.switchToTab(index);
    },
    // 切换到指定标签页
    async switchToTab(index) {
      try {
        this.isSwitching = true;
        this.saveCurrentTabData();
        this.navIdx = index;
        this._formattedCache = null;
        this.resetTabState();
        if (index === 0 && !this.isLogin) {
          this.isEmpty = false;
          this.loadStatus = "more";
          return;
        }
        if (index === 1 && !this.circleLoaded) {
          this.getHotCircles();
          this.circleLoaded = true;
        }
        this.loadFlowSettings();
        await this.loadData(true);
      } catch (error) {
      } finally {
        this.isSwitching = false;
      }
    },
    // 重置标签页状态
    resetTabState() {
      this.page = 1;
      this.isEmpty = false;
      this.loadStatus = "loading";
      this.list = [];
      this.renderItems = 15;
    },
    clearCache(tabIndex = null) {
      if (tabIndex !== null && typeof tabIndex === "number") {
        this.cachedData[tabIndex] = {
          list: [],
          page: 1,
          hasMore: true,
          lastUpdate: 0,
          renderItems: 15
        };
      } else {
        this.cachedData = {
          0: { list: [], page: 1, hasMore: true, lastUpdate: 0, renderItems: 15 },
          1: { list: [], page: 1, hasMore: true, lastUpdate: 0, renderItems: 15 },
          2: { list: [], page: 1, hasMore: true, lastUpdate: 0, renderItems: 15 }
        };
      }
    },
    // 处理用户信息更新
    handleUserInfoUpdate() {
      this.loadFlowSettings();
      if (this.list.length === 0) {
        this.loadData(true);
      }
    },
    // 简化的处理瀑布流设置更新
    handleFlowSettingsUpdate(settings) {
      const oldWaterfall = this.isWaterfall;
      if (this.navIdx === 0) {
        this.isWaterfall = false;
      } else {
        this.isWaterfall = settings.dynamicFlow === true;
      }
      if (oldWaterfall !== this.isWaterfall) {
        this.clearTabCache(this.navIdx);
        this.resetLoadingState();
        this.page = 1;
        this.renderItems = 15;
        this.loadData(true).catch((err) => {
        });
      }
    },
    // 添加检查微信环境的方法
    // 获取并解析用户信息缓存的统一方法
    getUserInfoFromCache() {
      let userInfo = common_vendor.index.getStorageSync("USER_INFO") || {};
      if (typeof userInfo === "string") {
        try {
          userInfo = JSON.parse(userInfo);
        } catch (e) {
          userInfo = {};
        }
      }
      return userInfo;
    },
    // 添加一个新方法，用于从缓存初始化用户信息
    initUserInfoFromCache() {
      this.userUid = this.$store.state.app.uid || 0;
      const userInfo = this.getUserInfoFromCache();
      this.userCity = userInfo.residence_name || userInfo.city || "广州";
      this.userAvatar = userInfo.avatar || "/static/img/avatar.png";
      this.currentMsg = userInfo.service_num || 0;
      return userInfo;
    },
    // 添加刷新用户信息的方法
    refreshUserInfo() {
      const userInfo = this.getUserInfoFromCache();
      this.userAvatar = userInfo.avatar || "/static/img/avatar.png";
      this.userCity = userInfo.residence_name || userInfo.city || "广州";
      this.userUid = this.$store.state.app.uid || 0;
      this.currentMsg = userInfo.service_num || 0;
    },
    // 重置加载状态
    resetLoadingState() {
      this.isLoading = false;
      this.isRefreshing = false;
      this.isSwitching = false;
      common_vendor.index.hideLoading();
    },
    // 显示带消息的加载提示
    showLoadingWithMessage() {
      const messages = [
        "加载中...",
        "获取最新内容...",
        "正在为您推荐...",
        "加载精彩内容..."
      ];
      const randomMessage = messages[Math.floor(Math.random() * messages.length)];
      common_vendor.index.showLoading({
        title: randomMessage,
        mask: true
      });
    },
    // 显示成功提示
    showSuccessToast(message = "操作成功") {
      common_vendor.index.showToast({
        title: message,
        icon: "success",
        duration: 1500
      });
    },
    // 显示错误提示
    showErrorToast(message = "操作失败") {
      common_vendor.index.showToast({
        title: message,
        icon: "none",
        duration: 2e3
      });
    },
    // 显示网络错误提示
    showNetworkError() {
      common_vendor.index.showModal({
        title: "网络异常",
        content: "网络连接不稳定，请检查网络设置后重试",
        confirmText: "重试",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            this.retryLoad();
          }
        }
      });
    },
    // 初始化平台特性
    initPlatformFeatures() {
      this.initMiniProgramFeatures();
    },
    initMiniProgramFeatures() {
      this.optimizeMiniProgramPerformance();
    },
    optimizeMiniProgramPerformance() {
      this.renderItems = Math.min(this.renderItems, 20);
      this.renderStep = Math.min(this.renderStep, 5);
    },
    // 清理平台特性
    cleanupPlatformFeatures() {
    },
    // 清理定时器和内存
    clearTimers() {
      try {
        if (this.debounceTimer) {
          clearTimeout(this.debounceTimer);
          this.debounceTimer = null;
        }
        if (this.scrollTimer) {
          clearTimeout(this.scrollTimer);
          this.scrollTimer = null;
        }
        if (this.scrollRAF) {
          if (typeof cancelAnimationFrame !== "undefined") {
            cancelAnimationFrame(this.scrollRAF);
          }
          this.scrollRAF = null;
        }
      } catch (error) {
      }
    },
    // 内存回收机制
    performMemoryCleanup() {
      try {
        this._formattedCache = null;
        if (this.cachedData && this.cacheConfig) {
          const now = Date.now();
          Object.keys(this.cachedData).forEach((key) => {
            const cache = this.cachedData[key];
            if (cache && cache.lastUpdate && now - cache.lastUpdate > this.cacheConfig.maxAge * 2) {
              cache.list = [];
              cache.lastUpdate = 0;
            }
          });
        }
      } catch (error) {
      }
    },
    // 获取热门圈子
    getHotCircles() {
      api_social.getHotCircles().then((res) => {
        if (res.status === 200 && res.data) {
          this.circle = res.data.map((item) => {
            return {
              id: item.id,
              circle_name: item.circle_name || item.name,
              circle_avatar: item.circle_avatar || item.avatar,
              name: item.circle_name || item.name,
              // 兼容字段
              avatar: item.circle_avatar || item.avatar,
              // 兼容字段
              is_hot: item.is_hot || false,
              is_new: item.is_new || false,
              dynamic_count: item.dynamic_count || 0,
              member_count: item.member_count || 0,
              user_count: item.member_count || 0,
              // 兼容字段
              circle_description: item.circle_description || ""
            };
          });
        } else {
          this.circle = [];
        }
      }).catch(() => {
        this.circle = [];
      });
    },
    // 添加统一的API错误处理方法
    handleApiError(err) {
      common_vendor.index.hideLoading();
      this.isLoading = false;
      this.loadStatus = "more";
      if (this.page === 1) {
        this.isEmpty = true;
        this.list = [];
      }
      common_vendor.index.showToast({
        title: err.msg || "网络连接异常，请检查网络设置",
        icon: "none",
        duration: 2e3
      });
    },
    // 获取用户位置信息 - 参考order_confirm页面的实现
    getUserLocation() {
      const cachedLatitude = common_vendor.index.getStorageSync("user_latitude") || common_vendor.index.getStorageSync("residence_lat");
      const cachedLongitude = common_vendor.index.getStorageSync("user_longitude") || common_vendor.index.getStorageSync("residence_lng");
      if (cachedLatitude && cachedLongitude) {
        this.userLatitude = parseFloat(cachedLatitude);
        this.userLongitude = parseFloat(cachedLongitude);
        const cachedAddress = common_vendor.index.getStorageSync("residence_name");
        if (cachedAddress) {
          this.userCity = cachedAddress;
        } else {
          this.getAddressFromLocation(this.userLatitude, this.userLongitude);
        }
        return;
      }
      this.requestLocation();
    },
    // 请求位置信息 - 参考order_confirm的实现方式
    requestLocation() {
      this.fallbackToUniLocation();
    },
    // 使用uni.getLocation获取位置
    fallbackToUniLocation() {
      common_vendor.index.getLocation({
        type: "wgs84",
        geocode: true,
        // 启用逆地理编码
        success: (res) => {
          this.handleLocationSuccess(res);
        },
        fail: (err) => {
          common_vendor.index.__f__("log", "at pages/index/dynamic.vue:2137", "uni.getLocation失败:", err);
          this.handleLocationError(err);
        }
      });
    },
    // 处理定位成功
    handleLocationSuccess(res) {
      this.userLatitude = res.latitude;
      this.userLongitude = res.longitude;
      common_vendor.index.setStorageSync("user_latitude", res.latitude);
      common_vendor.index.setStorageSync("user_longitude", res.longitude);
      common_vendor.index.setStorageSync("residence_lat", res.latitude);
      common_vendor.index.setStorageSync("residence_lng", res.longitude);
      if (res.address) {
        this.userCity = res.address.city || res.address.district || res.address.province || "";
        if (this.userCity) {
          common_vendor.index.setStorageSync("residence_name", this.userCity);
        }
      } else {
        this.getAddressFromLocation(res.latitude, res.longitude);
      }
    },
    // 处理定位失败
    handleLocationError(err) {
      common_vendor.index.__f__("log", "at pages/index/dynamic.vue:2169", "定位失败:", err);
      if (err.errMsg) {
        if (err.errMsg.includes("auth deny"))
          ;
        else if (err.errMsg.includes("timeout"))
          ;
        else if (err.errMsg.includes("fail"))
          ;
      }
      this.setDefaultLocation();
    },
    // 设置默认位置（广州）
    setDefaultLocation() {
      this.userLatitude = 23.12908;
      this.userLongitude = 113.26436;
      this.userCity = "广东省广州市";
      common_vendor.index.setStorageSync("residence_lat", this.userLatitude);
      common_vendor.index.setStorageSync("residence_lng", this.userLongitude);
      common_vendor.index.setStorageSync("residence_name", this.userCity);
    },
    // 强制刷新位置信息
    refreshLocation() {
      common_vendor.index.removeStorageSync("residence_lat");
      common_vendor.index.removeStorageSync("residence_lng");
      common_vendor.index.removeStorageSync("residence_name");
      this.getUserLocation();
    },
    // 根据经纬度获取地址信息（逆地理编码）
    getAddressFromLocation(latitude, longitude) {
      common_vendor.index.request({
        url: "https://apis.map.qq.com/ws/geocoder/v1/",
        data: {
          location: `${latitude},${longitude}`,
          key: "F7LBZ-NLU6D-6524Z-PK6ZQ-D47AJ-KRB2I",
          // 腾讯地图API key
          get_poi: 0
        },
        success: (res) => {
          if (res.data.status === 0 && res.data.result) {
            const result = res.data.result;
            const province = result.ad_info.province;
            const city = result.ad_info.city;
            const district = result.ad_info.district;
            let fullAddress = "";
            if (province && city) {
              if (province === city) {
                fullAddress = province + (district || "");
              } else {
                fullAddress = province + city + (district || "");
              }
            } else {
              fullAddress = result.address || "未知位置";
            }
            this.userCity = fullAddress;
            common_vendor.index.setStorageSync("residence_name", fullAddress);
          } else {
            this.setFallbackAddress();
          }
        },
        fail: () => {
          this.setFallbackAddress();
        }
      });
    },
    // 设置备用地址
    setFallbackAddress() {
      const defaultAddress = "广东省广州市";
      this.userCity = defaultAddress;
    },
    // 处理标签更新
    handleTagsUpdate({ tags, userId }) {
      if (this.displayItems && this.displayItems.length > 0) {
        this.displayItems = this.displayItems.map((item) => {
          if (item.user_id === userId || item.id === userId) {
            return {
              ...item,
              interest_tags: tags
            };
          }
          return item;
        });
      }
      this.loadData(true);
    },
    fetchList() {
      this.page = 1;
      this.loadData(true);
    },
    onCardUpdate({ vote_info, idx }) {
      if (this.list[idx]) {
        this.list[idx].vote_info = vote_info;
      }
    }
  },
  computed: {
    isLogin() {
      return this.userStore.isLoggedIn;
    },
    // 优化后的显示数据计算，使用缓存避免重复格式化
    displayItems() {
      if (!this._formattedCache || this._formattedCache.length !== this.list.length) {
        this._formattedCache = this.list.map((item) => {
          if (!item._formatted) {
            return this.formatDynamicData(item);
          }
          return item;
        });
      }
      return this._formattedCache.slice(0, this.renderItems);
    }
  },
  watch: {
    // 监听isWaterfall变化，清除缓存让Vue自动重新渲染
    isWaterfall() {
      this._formattedCache = null;
    }
  }
};
if (!Array) {
  const _component_emptyPage = common_vendor.resolveComponent("emptyPage");
  const _component_card_gg = common_vendor.resolveComponent("card-gg");
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  const _component_waterfall = common_vendor.resolveComponent("waterfall");
  const _component_tabbar = common_vendor.resolveComponent("tabbar");
  (_component_emptyPage + _component_card_gg + _easycom_uni_load_more2 + _component_waterfall + _component_tabbar)();
}
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
if (!Math) {
  _easycom_uni_load_more();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.navList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item),
        b: $data.navIdx === index
      }, $data.navIdx === index ? {} : {}, {
        c: index,
        d: $data.navIdx === index ? 1 : "",
        e: index,
        f: "tab" + index,
        g: common_vendor.o((...args) => $options.navClick && $options.navClick(...args), index)
      });
    }),
    b: "tab" + $data.navIdx,
    c: $data.titleBarHeight + "px",
    d: $data.statusBarHeight + "px",
    e: !$options.isLogin && $data.navIdx === 0
  }, !$options.isLogin && $data.navIdx === 0 ? {
    f: common_vendor.o($options.toLogin),
    g: common_vendor.p({
      title: "登录后浏览关注内容",
      description: "登录账号，查看关注的好友动态",
      buttonText: "立即登录"
    })
  } : $data.isEmpty && $data.navIdx === 0 ? {
    i: common_vendor.p({
      title: "暂无关注内容",
      description: "去探索，发现新朋友"
    })
  } : !$data.isEmpty && $data.list.length > 0 ? {
    k: common_vendor.f($options.displayItems, (item, index, i0) => {
      return {
        a: common_vendor.o($options.likeClick, index),
        b: common_vendor.o($options.followClick, index),
        c: common_vendor.o($options.onCardUpdate, index),
        d: "3bce33d0-2-" + i0,
        e: common_vendor.p({
          item,
          idx: index
        }),
        f: index
      };
    }),
    l: common_vendor.p({
      status: $data.loadStatus
    })
  } : {}, {
    h: $data.isEmpty && $data.navIdx === 0,
    j: !$data.isEmpty && $data.list.length > 0,
    m: common_vendor.o((...args) => $options.onReachEnd && $options.onReachEnd(...args)),
    n: common_vendor.o((...args) => $options.onScroll && $options.onScroll(...args)),
    o: $data.navIdx === 1 && $data.circle.length > 0
  }, $data.navIdx === 1 && $data.circle.length > 0 ? {
    p: common_vendor.f($data.circle, (item, index, i0) => {
      return common_vendor.e({
        a: item.circle_avatar || item.avatar,
        b: item.is_official == 1
      }, item.is_official == 1 ? {} : item.is_hot == 1 ? {} : {}, {
        c: item.is_hot == 1,
        d: common_vendor.t(item.circle_name || item.name),
        e: item.dynamic_count
      }, item.dynamic_count ? {
        f: common_vendor.t(item.dynamic_count)
      } : item.member_count || item.user_count ? {
        h: common_vendor.t(item.member_count || item.user_count)
      } : {}, {
        g: item.member_count || item.user_count,
        i: index,
        j: "note/circle?id=" + item.id,
        k: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), index)
      });
    }),
    q: common_assets._imports_5,
    r: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args))
  } : {}, {
    s: $data.isEmpty && $data.navIdx === 1
  }, $data.isEmpty && $data.navIdx === 1 ? {
    t: common_assets._imports_3$1
  } : !$data.isEmpty && $data.list.length > 0 ? common_vendor.e({
    w: $data.isWaterfall
  }, $data.isWaterfall ? {
    x: common_vendor.o($options.waterfallLikeClick),
    y: common_vendor.o($options.followClick),
    z: common_vendor.p({
      activity: [],
      note: $options.displayItems,
      page: $data.page
    })
  } : {
    A: common_vendor.f($options.displayItems, (item, index, i0) => {
      return {
        a: common_vendor.o($options.likeClick, index),
        b: common_vendor.o($options.followClick, index),
        c: common_vendor.o($options.fetchList, index),
        d: "3bce33d0-5-" + i0,
        e: common_vendor.p({
          item,
          idx: index
        }),
        f: index
      };
    })
  }, {
    B: common_vendor.p({
      status: $data.loadStatus
    }),
    C: common_vendor.n($data.isWaterfall ? "dynamic-box" : "")
  }) : {}, {
    v: !$data.isEmpty && $data.list.length > 0,
    D: common_vendor.o((...args) => $options.onReachEnd && $options.onReachEnd(...args)),
    E: common_vendor.o((...args) => $options.onScroll && $options.onScroll(...args)),
    F: $data.isEmpty && $data.navIdx === 2
  }, $data.isEmpty && $data.navIdx === 2 ? {
    G: common_vendor.p({
      title: "附近暂无内容",
      description: "去发笔记，让大家看到你"
    })
  } : !$data.isEmpty && $data.list.length > 0 ? common_vendor.e({
    I: $data.isWaterfall
  }, $data.isWaterfall ? {
    J: common_vendor.o($options.waterfallLikeClick),
    K: common_vendor.o($options.followClick),
    L: common_vendor.p({
      activity: [],
      note: $options.displayItems,
      page: $data.page
    })
  } : {
    M: common_vendor.f($options.displayItems, (item, index, i0) => {
      return {
        a: common_vendor.o($options.likeClick, index),
        b: common_vendor.o($options.followClick, index),
        c: common_vendor.o($options.fetchList, index),
        d: "3bce33d0-9-" + i0,
        e: common_vendor.p({
          item,
          idx: index
        }),
        f: index
      };
    })
  }, {
    N: common_vendor.p({
      status: $data.loadStatus
    }),
    O: common_vendor.n($data.isWaterfall ? "dynamic-box" : "")
  }) : {}, {
    H: !$data.isEmpty && $data.list.length > 0,
    P: common_vendor.o((...args) => $options.onReachEnd && $options.onReachEnd(...args)),
    Q: common_vendor.o((...args) => $options.onScroll && $options.onScroll(...args)),
    R: $data.navIdx,
    S: common_vendor.o((...args) => $options.swiperChange && $options.swiperChange(...args)),
    T: $data.contentHeight + "px",
    U: $data.statusBarHeight + $data.titleBarHeight + "px",
    V: common_vendor.p({
      currentPage: 1,
      currentMsg: $data.currentMsg
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/dynamic.js.map
