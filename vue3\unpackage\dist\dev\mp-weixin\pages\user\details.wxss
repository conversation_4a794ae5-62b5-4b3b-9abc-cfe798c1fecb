
.nav-box{
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  width: 100%;
  box-sizing: border-box;
}
.nav-box .nav-back{
  padding: 0 30rpx;
  width: 34rpx;
}
.nav-box .nav-title{
  max-width: 60%;
  font-size: 32rpx;
  font-weight: 700;
}
.user-box{
  width: calc(100% - 60rpx);
  padding: 60rpx 30rpx;
  color: #fff;
  position: relative;
  overflow: hidden;
}
.user-box .user-img, .user-box .user-bg{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.user-box .user-bg{
  z-index: -1;
  background: rgba(0, 0, 0, .5);
}
.user-box .user-top{
  width: 100%;
  justify-content: space-between;
}
.user-top .avatar{
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  background: #fff;
  border: 2px solid #f5f5f5;
  overflow: hidden;
}
.user-top .btn{
  padding: 0 30rpx;
  height: 64rpx;
  line-height: 64rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: 700;
  color: #000;
  background: #fff;
}
.user-top .active{
  color: rgba(255, 255, 255, .52);
  background: rgba(255, 255, 255, .1);
}
.user-top .mutual{
  color: #576b95;
  background: rgba(255, 255, 255, .2);
}
.user-box .user-name{
  margin: 20rpx 0 10rpx;
  width: 100%;
  font-size: 34rpx;
  font-weight: 700;
}
.user-box .user-intro{
  width: 100%;
  word-break: break-word;
  white-space: pre-line;
}
.user-box .user-intro text{
  color: #ccc;
  font-size: 24rpx;
  font-weight: 400;
}
.user-box .user-tag{
  margin: 20rpx 0;
  width: 100%;
}
.user-tag .tag-item{
  margin-right: 16rpx;
  height: 44rpx;
  padding: 0 14rpx;
  border-radius: 8rpx;
  background: rgba(255, 255, 255, .1);
  font-weight: 500;
  font-size: 20rpx;
  justify-content: center;
}
.user-tag .tag-item image{
  width: 24rpx;
  height: 24rpx;
}
.user-num .num-item{
  margin-right: 30rpx;
  font-size: 20rpx;
  font-weight: 300;
  color: #ccc;
}
.user-num .num-item .t1{
  color: #fff;
  font-size: 28rpx;
  font-weight: 700;
  margin-right: 6rpx;
}
.content-box{
  margin-top: -30rpx;
  background: #fff;
  padding: 30rpx 0;
  border-radius: 30rpx 30rpx 0 0;
}
.block-box .block-title{
  padding: 0 30rpx;
  font-size: 26rpx;
  font-weight: 700;
}
.block-box .circle-box{
  width: calc(100% - 20rpx);
  padding: 30rpx 10rpx;
  display: flex;
}
.circle-box .circle-item{
  flex-shrink: 0;
  margin: 0 10rpx;
  flex-direction: column;
  justify-content: center;
}
.circle-item .circle-avatar{
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: #f8f8f8;
}
.circle-item .circle-name{
  margin-top: 15rpx;
  width: 120rpx;
  color: #999;
  font-size: 20rpx;
  text-align: center;
}
.bar-box{
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  z-index: 99;
  margin-top: -1px;
  width: 100%;
  height: 80rpx;
  background: #fff;
}
.bar-box .bar-item{
  padding: 0 30rpx;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  position: relative;
}
.bar-box .bar-item text{
  font-weight: 700;
  transition: all .3s ease-in-out;
}
.bar-item .bar-line{
  position: absolute;
  bottom: 12rpx;
  width: 18rpx;
  height: 6rpx;
  border-radius: 6rpx;
  background: #000;
  transition: opacity .3s ease-in-out;
}
.content-box .dynamic-box{
  width: calc(100% - 16rpx);
  padding: 22rpx 8rpx 0;
}
.loading-box {
  width: 100%;
  padding: 60rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.load-more-box {
  width: 100%;
  padding: 30rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.empty-box{
  width: 100%;
  padding: 100rpx 0;
  flex-direction: column;
}
.empty-box image{
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 30rpx;
}
.empty-box .e1{
  font-size: 30rpx;
  font-weight: 700;
}
.empty-box .e2{
  margin-top: 10rpx;
  color: #999;
  font-size: 26rpx;
}
.error-box{
  width: 100%;
  padding: 100rpx 0;
  flex-direction: column;
}
.error-box image{
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 30rpx;
}
.error-box .e1{
  font-size: 30rpx;
  font-weight: 700;
  color: #333;
}
.error-box .e2{
  margin-top: 10rpx;
  color: #999;
  font-size: 26rpx;
  text-align: center;
  padding: 0 60rpx;
}
.error-box .retry-btn{
  margin-top: 40rpx;
  width: 200rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 700;
  color: #fff;
  background: #007aff;
  border-radius: 40rpx;
}
.tips-box{
  justify-content: center;
  width: 100%;
}
.tips-box .tips-item{
  background: #000;
  color: #fff;
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 700;
}
.df{
  display: flex;
  align-items: center;
}
.ohto{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.xwb{
  filter: invert(1);
}
.background-carousel {
  position: relative;
  width: 100%;
  height: 100%;
}
.carousel-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}
.carousel-item.active {
  opacity: 1;
}
.carousel-indicators {
  position: absolute;
  bottom: 20rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
}
.indicator {
  width: 12rpx;
  height: 4rpx;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 2rpx;
  margin: 0 4rpx;
  cursor: pointer;
}
.indicator.active {
  background-color: #fff;
}
.default-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.like-popup{
  width: 400rpx;
  background: #fff;
  padding: 30rpx;
  border-radius: 30rpx;
  overflow: hidden;
}
.like-popup .like-img{
  margin: 0 40rpx;
  width: 320rpx;
  height: 200rpx;
}
.like-popup .like-content{
  margin: 20rpx 0 40rpx;
  width: 100%;
  color: #333;
  font-size: 26rpx;
  text-align: center;
}
.like-popup .like-btn{
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 700;
  color: #fff;
  background: #000;
  border-radius: 16rpx;
}
