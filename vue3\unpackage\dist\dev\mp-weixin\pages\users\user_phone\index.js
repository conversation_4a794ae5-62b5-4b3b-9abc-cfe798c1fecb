"use strict";
const common_vendor = require("../../../common/vendor.js");
const mixins_SendVerifyCode = require("../../../mixins/SendVerifyCode.js");
const api_api = require("../../../api/api.js");
const libs_login = require("../../../libs/login.js");
const mixins_color = require("../../../mixins/color.js");
const common_assets = require("../../../common/assets.js");
const Verify = () => "../components/verify/index.js";
const authorize = () => "../../../components/Authorize.js";
const _sfc_main = {
  mixins: [mixins_SendVerifyCode.sendVerifyCode, mixins_color.colors],
  components: {
    authorize,
    Verify
  },
  data() {
    return {
      phone: "",
      captcha: "",
      isAuto: false,
      //没有授权的不会自动授权
      isShowAuth: false,
      //是否隐藏授权
      key: "",
      authKey: "",
      type: 0
    };
  },
  computed: common_vendor.mapGetters(["isLogin"]),
  onLoad(options) {
    if (this.isLogin) {
      api_api.verifyCode().then((res) => {
        this.$set(this, "key", res.data.key);
      });
      this.authKey = options.key || "";
      this.url = options.url || "";
    } else {
      libs_login.toLogin();
    }
    this.type = options.type || 0;
  },
  methods: {
    onLoadFun: function() {
    },
    // 授权关闭
    authColse: function(e) {
      this.isShowAuth = e;
    },
    editPwd: function() {
      let that = this;
      if (!that.phone)
        return that.$util.Tips({
          title: that.$t(`请填写手机号码`)
        });
      if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.phone))
        return that.$util.Tips({
          title: that.$t(`请输入正确的手机号码`)
        });
      if (!that.captcha)
        return that.$util.Tips({
          title: that.$t(`请填写验证码`)
        });
      if (this.type == 0) {
        api_api.bindingUserPhone({
          phone: that.phone,
          captcha: that.captcha
        }).then((res) => {
          if (res.data !== void 0 && res.data.is_bind) {
            common_vendor.index.showModal({
              title: that.$t(`是否绑定账号`),
              content: res.msg,
              confirmText: that.$t(`绑定`),
              success(res2) {
                if (res2.confirm) {
                  api_api.bindingUserPhone({
                    phone: that.phone,
                    captcha: that.captcha,
                    step: 1
                  }).then((res3) => {
                    return that.$util.Tips({
                      title: res3.msg,
                      icon: "success"
                    }, {
                      tab: 5,
                      url: "/pages/users/user_info/index"
                    });
                  }).catch((err) => {
                    return that.$util.Tips({
                      title: err
                    });
                  });
                } else if (res2.cancel) {
                  return that.$util.Tips({
                    title: that.$t(`您已取消绑定！`)
                  }, {
                    tab: 5,
                    url: "/pages/users/user_info/index"
                  });
                }
              }
            });
          } else
            return that.$util.Tips({
              title: that.$t(`绑定成功`),
              icon: "success"
            }, {
              tab: 5,
              url: "/pages/users/user_info/index"
            });
        }).catch((err) => {
          return that.$util.Tips({
            title: err
          });
        });
      } else {
        api_api.updatePhone({
          phone: that.phone,
          captcha: that.captcha
        }).then((res) => {
          return that.$util.Tips({
            title: res.msg,
            icon: "success"
          }, {
            tab: 5,
            url: "/pages/users/user_info/index"
          });
        }).catch((error) => {
          return that.$util.Tips({
            title: error
          });
        });
      }
    },
    success(data) {
      this.$refs.verify.hide();
      let that = this;
      api_api.verifyCode().then((res) => {
        api_api.registerVerify(that.phone, "reset", res.data.key, this.captchaType, data.captchaVerification).then((res2) => {
          that.$util.Tips({
            title: res2.msg
          });
          that.sendCode();
        }).catch((err) => {
          return that.$util.Tips({
            title: err
          });
        });
      });
    },
    /**
     * 发送验证码
     *
     */
    async code() {
      let that = this;
      if (!that.phone)
        return that.$util.Tips({
          title: that.$t(`请填写手机号码`)
        });
      if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.phone))
        return that.$util.Tips({
          title: that.$t(`请输入正确的手机号码`)
        });
      this.$refs.verify.show();
      return;
    }
  }
};
if (!Array) {
  const _component_navbar = common_vendor.resolveComponent("navbar");
  const _component_Verify = common_vendor.resolveComponent("Verify");
  (_component_navbar + _component_Verify)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_assets._imports_0$17,
    b: common_assets._imports_0$18,
    c: _ctx.$t(`填写手机号码`),
    d: $data.phone,
    e: common_vendor.o(($event) => $data.phone = $event.detail.value),
    f: common_assets._imports_1$18,
    g: _ctx.$t(`填写验证码`),
    h: $data.captcha,
    i: common_vendor.o(($event) => $data.captcha = $event.detail.value),
    j: common_vendor.t(_ctx.text),
    k: common_vendor.n(_ctx.disabled ? "active" : ""),
    l: common_vendor.o((...args) => $options.code && $options.code(...args)),
    m: common_vendor.t(_ctx.$t(`确认绑定`)),
    n: common_vendor.o((...args) => $options.editPwd && $options.editPwd(...args)),
    o: common_vendor.sr("verify", "2b9f9274-1"),
    p: common_vendor.o($options.success),
    q: common_vendor.p({
      captchaType: _ctx.captchaType,
      imgSize: {
        width: "330px",
        height: "155px"
      }
    }),
    r: common_vendor.s(_ctx.colorStyle)
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/users/user_phone/index.js.map
