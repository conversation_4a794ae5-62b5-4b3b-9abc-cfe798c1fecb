// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

/**
 * 用户信息管理混入
 * 提供统一的用户信息管理方法
 */

import userManager from '@/utils/userManager.js'
import { useUserStore } from '@/stores/user.js'

export default {
  data() {
    return {
      // 统一的用户信息对象
      unifiedUserInfo: {
        uid: 0,
        nickname: '',
        avatar: '',
        phone: '',
        follow_count: 0,
        fans_count: 0,
        like_count: 0,
        visitor_count: 0,
        visitor_badge: 0,
        vip_status: 0,
        is_money_level: 0,
        svip_open: 0
      }
    }
  },

  computed: {
    // 用户是否已登录
    isUserLoggedIn() {
      const status = userManager.checkLoginStatus()
      return status.valid
    },

    // 用户ID
    currentUserId() {
      return this.unifiedUserInfo.uid || 0
    },

    // 用户昵称
    currentUserNickname() {
      return this.unifiedUserInfo.nickname || '未登录'
    },

    // 用户头像
    currentUserAvatar() {
      return this.unifiedUserInfo.avatar || '/static/img/avatar.png'
    },

    // 是否为VIP用户
    isVipUser() {
      return this.unifiedUserInfo.vip_status === 1
    }
  },

  created() {
    // 初始化用户管理工具
    if (this.$store) {
      userManager.init(this.$store)
    }
    
    // 加载用户信息
    this.loadUnifiedUserInfo()
  },

  methods: {
    /**
     * 加载统一的用户信息
     */
    loadUnifiedUserInfo() {
      try {
        const { userInfo, isLogin } = userManager.getUserInfo()
        
        if (isLogin && userInfo.nickname) {
          this.unifiedUserInfo = { ...this.unifiedUserInfo, ...userInfo }
          console.log('混入：用户信息已加载', {
            uid: this.unifiedUserInfo.uid,
            nickname: this.unifiedUserInfo.nickname
          })
        } else {
          console.log('混入：用户未登录或信息不完整')
        }
      } catch (error) {
        console.error('混入：加载用户信息失败', error)
      }
    },

    /**
     * 更新用户信息
     */
    updateUnifiedUserInfo(newUserInfo) {
      try {
        const success = userManager.updateUserInfo(newUserInfo)
        
        if (success) {
          // 重新加载用户信息
          this.loadUnifiedUserInfo()
          
          // 触发全局事件
          uni.$emit('userInfoUpdated', this.unifiedUserInfo)
          
          return true
        }
        return false
      } catch (error) {
        console.error('混入：更新用户信息失败', error)
        return false
      }
    },

    /**
     * 设置登录状态
     */
    setUnifiedLoginStatus(token, userInfo = null) {
      try {
        const success = userManager.setLoginStatus(token, userInfo)
        
        if (success) {
          this.loadUnifiedUserInfo()
          uni.$emit('loginStateChanged', true)
          return true
        }
        return false
      } catch (error) {
        console.error('混入：设置登录状态失败', error)
        return false
      }
    },

    /**
     * 统一登出
     */
    unifiedLogout() {
      try {
        const success = userManager.logout()
        
        if (success) {
          // 重置页面用户信息
          this.unifiedUserInfo = {
            uid: 0,
            nickname: '',
            avatar: '',
            phone: '',
            follow_count: 0,
            fans_count: 0,
            like_count: 0,
            visitor_count: 0,
            visitor_badge: 0,
            vip_status: 0,
            is_money_level: 0,
            svip_open: 0
          }
          
          // 触发全局事件
          uni.$emit('loginStateChanged', false)
          uni.$emit('userInfoUpdated', this.unifiedUserInfo)
          
          return true
        }
        return false
      } catch (error) {
        console.error('混入：登出失败', error)
        return false
      }
    },

    /**
     * 检查登录状态
     */
    checkUnifiedLoginStatus(showToast = false) {
      const status = userManager.checkLoginStatus()
      
      if (!status.valid && showToast) {
        uni.showToast({
          title: '请先登录',
          icon: 'none',
          duration: 2000
        })
      }
      
      return status.valid
    },

    /**
     * 同步所有数据源
     */
    syncUnifiedUserInfo() {
      try {
        userManager.syncAllSources()
        this.loadUnifiedUserInfo()
      } catch (error) {
        console.error('混入：同步用户信息失败', error)
      }
    },

    /**
     * 获取用户统计信息的格式化文本
     */
    getFormattedUserStats() {
      return {
        likeCount: this.unifiedUserInfo.like_count > 999 
          ? (this.unifiedUserInfo.like_count / 1000).toFixed(1) + 'k'
          : this.unifiedUserInfo.like_count.toString(),
        followCount: this.unifiedUserInfo.follow_count.toString(),
        fansCount: this.unifiedUserInfo.fans_count.toString(),
        visitorCount: this.unifiedUserInfo.visitor_count.toString()
      }
    }
  },

  // 监听全局事件
  mounted() {
    // 监听用户信息更新事件
    uni.$on('userInfoUpdated', this.handleGlobalUserInfoUpdate)
    uni.$on('loginStateChanged', this.handleGlobalLoginStateChange)
  },

  beforeDestroy() {
    // 移除事件监听
    uni.$off('userInfoUpdated', this.handleGlobalUserInfoUpdate)
    uni.$off('loginStateChanged', this.handleGlobalLoginStateChange)
  },

  // 全局事件处理方法
  methods: {
    handleGlobalUserInfoUpdate() {
      this.loadUnifiedUserInfo()
    },

    handleGlobalLoginStateChange(isLogin) {
      if (isLogin) {
        this.loadUnifiedUserInfo()
      } else {
        this.unifiedLogout()
      }
    }
  }
}
