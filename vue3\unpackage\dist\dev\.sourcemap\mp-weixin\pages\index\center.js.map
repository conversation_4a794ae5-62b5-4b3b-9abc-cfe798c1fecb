{"version": 3, "file": "center.js", "sources": ["pages/index/center.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvY2VudGVyLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"container\" :class=\"{'no-scroll': showSidebar}\">\n    <view class=\"nav-box\" :style=\"{'padding-top': statusBarHeight + 'px', 'background': 'rgba(255,255,255,'+ navbarTrans +')'}\">\n      <view class=\"nav-item df\" :style=\"{'height': titleBarHeight + 'px'}\">\n        <view class=\"nav-menu-btn\" @tap=\"toggleSidebar\">\n          <image src=\"/static/img/menu.png\"></image>\n        </view>\n        <view class=\"ohto df\" :style=\"{'transform': 'translateY('+ ((1-navbarTrans) * 30) +'px)', 'opacity': navbarTrans}\">\n          <image :src=\"userInfo.avatar\" mode=\"aspectFill\" class=\"nav-user-avatar\"></image>\n          <text>{{userInfo.nickname}}</text>\n        </view>\n      </view>\n    </view>\n    <view class=\"user-box\" :style=\"{'padding-top': statusBarHeight + titleBarHeight + 'px'}\">\n      <view class=\"user-bg\"></view>\n      <view class=\"user-img\" style=\"z-index:-2\">\n        <lazy-image :src=\"userInfo.avatar || '/static/img/avatar.png'\" mode=\"aspectFill\"></lazy-image>\n      </view>\n      <view class=\"user-top df\" data-url=\"center/means\" @tap=\"navigateToFun\" style=\"position: relative; padding-right: 40rpx; padding-top: 40rpx;\">\n        <view class=\"avatar-wrapper\">\n          <view class=\"avatar\">\n            <lazy-image :src=\"userInfo.avatar\"></lazy-image>\n\n          </view>\n        </view>\n        <view class=\"user-info\">\n          <view class=\"user-name-row df\">\n            <text class=\"user-name-text\">{{userInfo.nickname}}</text>\n            <view class=\"status-icon vip-icon\" v-if=\"userInfo.is_money_level> 0 && userInfo.svip_open\">\n              <image src=\"/static/img/svip.gif\"></image>\n            </view>\n            <view class=\"status-icon verified-icon\" v-if=\"userInfo.is_verified\">\n              <image src=\"/static/img/rz.png\"></image>\n            </view>\n          </view>\n          <view class=\"user-id-row df\">\n            <view v-if=\"userInfo.sex != 2\" class=\"sex-icon df\">\n              <image :src=\"userInfo.sex == 1 ? '/static/img/nan.png' : '/static/img/nv.png'\"></image>\n            </view>\n            <text class=\"user-id\">ID: {{userInfo.user_id_number}}</text>\n          </view>\n        </view>\n        <view class=\"right-arrow\">\n          <image src=\"/static/img/x.png\" mode=\"aspectFit\"></image>\n        </view>\n      </view>\n\n      <view class=\"user-num-wrap df\">\n        <view class=\"user-num df\">\n          <view class=\"num-item df\" @tap=\"toFollowList(0)\">\n            <text class=\"t1\">{{userInfo.follow_count}}</text>\n            <text class=\"t2\">关注</text>\n          </view>\n          <view class=\"num-item df\" @tap=\"toFollowList(1)\">\n            <text class=\"t1\">{{userInfo.fans_count}}</text>\n            <text class=\"t2\">粉丝</text>\n          </view>\n          <view class=\"num-item df\" @tap=\"likePopupClick(true)\">\n            <text class=\"t1\">{{userInfo.like_count_str}}</text>\n            <text class=\"t2\">获赞</text>\n          </view>\n          <view class=\"num-item df visitor-item\" data-url=\"center/visitor\" @tap=\"navigateToFun\">\n            <text class=\"t1\">{{userInfo.visitor_count}}</text>\n            <text class=\"t2\">访客</text>\n            <view v-if=\"userInfo.visitor_badge\" class=\"badge\">+{{userInfo.visitor_badge > 99 ? '99+' : userInfo.visitor_badge}}</view>\n          </view>\n        </view>\n      </view>\n      <view class=\"tag-wrapper\">\n        <view v-if=\"userInfo.interest_tags && userInfo.interest_tags.length\" class=\"user-tag df\">\n          <view v-for=\"(tag, index) in userInfo.interest_tags\" :key=\"index\" class=\"tag-item df\">\n            <text>{{tag}}</text>\n          </view>\n        </view>\n        <view v-else class=\"tag-empty\">\n          <text>添加兴趣标签，让大家更了解你</text>\n        </view>\n        <view class=\"user-actions df\">\n          <view class=\"btn-icon\" data-url=\"setting/index\" @tap=\"navigateToFun\">\n            <image src=\"/static/img/setting/104.png\"></image>\n          </view>\n        </view>\n      </view>\n      <view class=\"user-intro\" data-url=\"center/means\" @tap=\"navigateToFun\">\n        <text class=\"intro-text\" user-select=\"true\">{{userInfo.about_me ? userInfo.about_me : \"添加个人简介，让大家认识你...\"}}</text>\n        <view class=\"more-btn\">\n          <image src=\"/static/img/more.png\" mode=\"aspectFit\"></image>\n        </view>\n      </view>\n      \n\n    </view>\n    <scroll-view scroll-x=\"true\" class=\"user-block\">\n      <view class=\"block-box\">\n        <view v-if=\"userInfo.activity_count\" class=\"block-item df\" data-url=\"activity/index?type=1\" @tap=\"navigateToFun\">\n          <view class=\"block-title\" style=\"margin-right:68rpx\">\n            <view class=\"t1\">活动</view>\n            <view class=\"t2\">{{userInfo.activity_count ? '共' + userInfo.activity_count + '个活动' : '没有参加活动'}}</view>\n          </view>\n          <view class=\"cu-group df\">\n            <view class=\"cu-item\" :style=\"{'background': userInfo.activity_img ? '#CECECE' : '#000'}\">\n              <image v-if=\"!userInfo.activity_img\" class=\"icon\" src=\"/static/img/hd.png\"></image>\n              <image v-else class=\"img\" :src=\"userInfo.activity_img\" mode=\"aspectFill\"></image>\n            </view>\n            <view class=\"cu-lump1\"></view>\n            <view class=\"cu-lump2\"></view>\n          </view>\n          <image class=\"block-icon\" src=\"/static/img/x.png\"></image>\n        </view>\n        <view v-for=\"(item, index) in blockList\" :key=\"index\" class=\"block-item df\" :data-url=\"item.url\" @tap=\"navigateToFun\">\n          <view class=\"block-title\" style=\"margin-right:68rpx\">\n            <view class=\"t1\">{{item.name}}</view>\n            <view v-if=\"item.count\" class=\"t2\">\n              共{{item.count}}{{index == 0 ? '个圈子' : index == 1 ? '件商品' : '笔订单'}}\n            </view>\n            <view v-else class=\"t2\">\n              {{index == 0 ? '没有加入圈子' : index == 1 ? '购物车空空的' : '订单空空的'}}\n            </view>\n          </view>\n          <view class=\"cu-group df\">\n            <view class=\"cu-item\" :style=\"{'background': item.img ? '#CECECE' : '#000'}\">\n              <image v-if=\"!item.img\" class=\"icon\" :src=\"item.icon\"></image>\n              <image v-else class=\"img\" :src=\"item.img\" mode=\"aspectFill\"></image>\n            </view>\n            <view class=\"cu-lump1\"></view>\n            <view class=\"cu-lump2\"></view>\n          </view>\n          <image class=\"block-icon\" src=\"/static/img/x.png\"></image>\n        </view>\n        <view v-if=\"appCard || userInfo.card_count\" class=\"block-item df\" data-url=\"center/card?type=1\" @tap=\"navigateToFun\">\n          <view class=\"block-title\" style=\"margin-right:68rpx\">\n            <view class=\"t1\">卡券</view>\n            <view class=\"t2\">{{userInfo.card_count ? '共' + userInfo.card_count + '张卡券' : '暂无可用卡券'}}</view>\n          </view>\n          <view class=\"cu-group df\">\n            <view class=\"cu-item\" style=\"background:#000\">\n              <image class=\"icon\" src=\"/static/img/kq.png\"></image>\n            </view>\n            <view class=\"cu-lump1\"></view>\n            <view class=\"cu-lump2\"></view>\n          </view>\n          <image class=\"block-icon\" src=\"/static/img/x.png\"></image>\n        </view>\n        <view style=\"flex-shrink:0;width:15rpx;height:15rpx\"></view>\n      </view>\n    </scroll-view>\n    <view class=\"bar-box df\" :style=\"{'top': statusBarHeight + titleBarHeight - 1 + 'px'}\">\n      <view v-for=\"(item, index) in barList\" :key=\"index\" class=\"bar-item df\" @tap=\"barClick\" :data-idx=\"index\">\n        <text :style=\"{'color': index == barIdx ? '#000' : '#999', 'font-size': index == barIdx ? '28rpx' : '26rpx'}\">{{item}}</text>\n        <view :style=\"{'opacity': index == barIdx ? 1 : 0}\" class=\"bar-line\"></view>\n      </view>\n    </view>\n    <!-- 优化的加载状态显示 - 有缓存数据时不显示 -->\n    <view class=\"loading-indicator df\" :style=\"{'height': (!isThrottling && (loadStatus == 'loading' || loadingState.pullRefresh) && list.length === 0) ? '60rpx' : '0px'}\">\n      <uni-load-more v-if=\"!isThrottling && (loadStatus == 'loading' || loadingState.pullRefresh) && list.length === 0\" :status=\"'loading'\"></uni-load-more>\n    </view>\n\n    <view class=\"content-container\">\n      <!-- 空状态 -->\n      <view v-if=\"isEmpty && !loadingState.dynamicList\" class=\"empty-state df\">\n        <image src=\"/static/img/empty.png\"/>\n        <view class=\"empty-title\">{{getEmptyTitle()}}</view>\n        <view class=\"empty-subtitle\">{{getEmptySubtitle()}}</view>\n      </view>\n\n      <!-- 加载中状态 -->\n      <view v-else-if=\"loadingState.dynamicList && list.length === 0\" class=\"loading-state df\">\n        <uni-load-more :status=\"'loading'\"></uni-load-more>\n        <view class=\"loading-text\">正在加载内容...</view>\n      </view>\n\n      <!-- 内容列表 -->\n      <block v-else>\n        <block v-for=\"(item, index) in list\" :key=\"item.id || index\">\n          <card-gg v-if=\"barIdx == 1\" :item=\"item\" :idx=\"index\"></card-gg>\n          <card-wd v-else :item=\"item\" :idx=\"index\" :bar=\"barIdx\" @delback=\"delClick\"></card-wd>\n        </block>\n      </block>\n\n      <!-- 底部加载更多状态 -->\n      <uni-load-more v-if=\"list.length > 0\" :status=\"loadStatus\"></uni-load-more>\n    </view>\n    <uni-popup ref=\"likePopup\" class=\"r\">\n      <view class=\"like-popup\">\n        <image class=\"like-img\" src=\"/static/img/inset/like.png\" mode=\"aspectFill\"></image>\n        <view class=\"like-content\"><text>\"</text>{{userInfo.nickname}}<text>\"</text>共获得 {{userInfo.like_count || 0}} 个赞 </view>\n        <view class=\"like-btn\" @tap=\"likePopupClick(false)\">确认</view>\n      </view>\n    </uni-popup>\n    <tabbar :currentPage=\"4\" :currentMsg=\"currentMsg\" :userAvatar=\"userInfo.avatar\"></tabbar>\n    <view class=\"sidebar-menu\" :class=\"{'active': showSidebar}\">\n      <view class=\"sidebar-header\" :style=\"{'padding-top': statusBarHeight + 10 + 'px'}\">\n        <view class=\"sidebar-user-info\">\n          <image :src=\"userInfo.avatar || '/static/img/avatar.png'\" mode=\"aspectFill\" class=\"sidebar-avatar\"></image>\n          <view class=\"sidebar-user-details\">\n            <view class=\"sidebar-user-name\">{{userInfo.nickname}}</view>\n            <view class=\"user-status\">\n              <view class=\"status-item\" v-if=\"userInfo.is_money_level> 0 && userInfo.svip_open\">\n                <image src=\"/static/img/svip.gif\" class=\"status-icon\"></image>\n              </view>\n              <view class=\"status-item verified-tag\" v-if=\"userInfo.is_verified\">\n                <image src=\"/static/img/rz.png\" class=\"status-icon\"></image>\n                <text>已认证</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        <view class=\"close-btn df\" @tap=\"toggleSidebar\">\n          <image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx;\"></image>\n        </view>\n      </view>\n      <view class=\"member-card\">\n        <view class=\"member-status\">\n          <view v-if=\"userInfo.vip_status == 1\" class=\"member-label\">永久会员</view>\n          <view v-else-if=\"userInfo.vip_status == 3\" class=\"member-label\">\n            会员到期：{{ formatDate(userInfo.overdue_time) }}\n          </view>\n          <view v-else-if=\"userInfo.vip_status == -1\" class=\"member-label\">会员已过期</view>\n          <view v-else-if=\"userInfo.vip_status == 2\" class=\"member-label\">未开通会员</view>\n          <view v-if=\"userInfo.vip_status != 1\" class=\"member-price\" @tap=\"goToVipPage\">¥3.8续费</view>\n        </view>\n        <view class=\"member-benefits\">\n          <text class=\"member-rights\" @tap=\"goToVipPage\">会员权益 | 领取我的等级特权</text>\n        </view>\n        <view class=\"member-desc\">专属优惠，VIP低至¥88，畅听1年！</view>\n      </view>\n      <scroll-view scroll-y class=\"sidebar-scroll\">\n        <view class=\"sidebar-content\">\n          <view class=\"menu-section\">\n            <view class=\"section-title\">我的服务</view>\n            <view class=\"menu-grid\">\n            <view \n                v-for=\"(item, index) in sidebarMenu\" \n                :key=\"'menu-' + index\" \n                class=\"grid-item\" \n              :data-url=\"item.url\" \n              @tap=\"navigateToFun\">\n                <view class=\"grid-icon-wrapper\">\n                  <image :src=\"item.icon\" class=\"grid-icon\"></image>\n                  <view v-if=\"item.badge\" class=\"grid-badge\">{{item.badge}}</view>\n              </view>\n                <text class=\"grid-text\">{{item.name}}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </scroll-view>\n      <view class=\"sidebar-footer\">\n        <view class=\"bottom-nav df\">\n          <view class=\"bottom-nav-item df\" @tap=\"handleBottomNav('scan')\">\n            <view class=\"nav-icon-box df\">\n              <image src=\"/static/img/scan.png\" class=\"nav-icon\"></image>\n            </view>\n            <text class=\"nav-text\">扫一扫</text>\n          </view>\n          <view class=\"bottom-nav-item df\" @tap=\"handleBottomNav('help')\">\n            <view class=\"nav-icon-box df\">\n              <image src=\"/static/img/kf.png\" class=\"nav-icon\"></image>\n            </view>\n            <text class=\"nav-text\">帮助与客服</text>\n          </view>\n          <view class=\"bottom-nav-item df\" @tap=\"handleBottomNav('setting')\">\n            <view class=\"nav-icon-box df\">\n              <image src=\"/static/img/setting/104.png\" class=\"nav-icon\"></image>\n            </view>\n            <text class=\"nav-text\">设置</text>\n          </view>\n        </view>\n        <view class=\"copyright-text\">© {{currentYear}} 个人中心</view>\n      </view>\n    </view>\n    <view class=\"sidebar-mask\" v-if=\"showSidebar\" @tap=\"toggleSidebar\" @touchmove.stop.prevent @touchstart.stop @touchend.stop></view>\n  </view>\n</template>\n\n<script>\nimport lazyImage from '@/components/lazyImage/lazyImage'\nimport uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more'\nimport cardWd from '@/components/card-wd/card-wd'\nimport cardGg from '@/components/card-gg/card-gg'\nimport tabbar from '@/components/tabbar/tabbar'\nimport uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue'\nimport { getMenuList, setVisit } from '@/api/user.js'\nimport { getMyDynamicList, likeDynamic, deleteDynamic, getUserSocialInfo, getLikeDynamicList, getVisitorDetails } from '@/api/social.js'\nimport { toLogin } from '@/libs/login.js'\nimport { mapGetters } from \"vuex\"\n// 引入Pinia stores\nimport { useUserStore } from '@/stores/user.js'\nimport { useAppStore } from '@/stores/app.js'\nimport { useSocialStore } from '@/stores/social.js'\n// 引入统一用户管理工具\nimport userManager from '@/utils/userManager.js'\n\nexport default {\n  components: {\n    lazyImage,\n    uniLoadMore,\n    cardWd,\n    cardGg,\n    tabbar,\n    uniPopup\n  },\n  computed: {\n    ...mapGetters({\n      isLogin: 'isLogin'\n    })\n  },\n  data() {\n    return {\n      // Pinia stores实例\n      userStore: useUserStore(),\n      appStore: useAppStore(),\n      socialStore: useSocialStore(),\n\n      statusBarHeight: this.$store.state.statusBarHeight || 20,\n      titleBarHeight: this.$store.state.titleBarHeight || 44,\n      currentMsg: 0,\n      currentYear: new Date().getFullYear(),\n      scrollTop: 0,\n      navbarTrans: 0,\n      userInfo: {\n        avatar: \"\",\n        nickname: \"您还未登录哦~\",\n        follow_count: 0,\n        fans_count: 0,\n        like_count: 0,\n        like_count_str: \"0\",\n        visitor_count: 0,\n        visitor_badge: 0,\n        is_verified: 0,\n        is_money_level: 0,\n        svip_open: 0,\n        sex: 0,\n        user_id_number: \"\",\n        about_me: \"\",\n        interest_tags: [],\n        vip_status: 0,\n        overdue_time: \"\",\n        card_count: 0,\n        activity_count: 0,\n        activity_img: \"\"\n      },\n      blockList: [\n        {name: '圈子', img: '', icon: '/static/img/qz.png', url: 'center/circle?type=1', count: 0},\n        {name: '购物车', img: '', icon: '/static/img/gwc.png', url: 'goods/cart', count: 0},\n        {name: '订单', img: '', icon: '/static/img/dd.png', url: 'order/index', count: 0}\n      ],\n      barList: ['笔记', '赞过'],\n      barIdx: 0,\n      list: [],\n      page: 1,\n      totalCount: 0,\n      isEmpty: false,\n      loadStatus: 'more',\n      showSidebar: false,\n      sidebarMenu: [],\n      isLoading: false,\n      isThrottling: false,\n      appCard: true,\n\n      // 优化后的数据管理变量\n      lastRefreshTime: 0,\n      refreshInterval: 300000, // 5分钟刷新间隔\n\n      // 优化的加载状态管理 - 避免状态冲突，使用语义化命名\n      loadingState: {\n        userInfo: false,        // 用户信息加载状态\n        dynamicList: false,     // 动态列表加载状态\n        pullRefresh: false,     // 下拉刷新状态\n        loadMore: false         // 加载更多状态\n      },\n\n      // 简化的定时器管理\n      refreshTimer: null,\n\n      // 简化的缓存策略\n      cacheTimeout: 300000, // 5分钟统一缓存时间\n      lastRefreshTime: 0,   // 最后刷新时间\n\n      // tab数据缓存\n      tabDataCache: {\n        0: { data: [], timestamp: 0, totalCount: 0 }, // 笔记数据缓存\n        1: { data: [], timestamp: 0, totalCount: 0 }  // 点赞数据缓存\n      },\n\n      // 错误重试机制\n      retryConfig: {\n        maxRetries: 3,\n        retryDelay: 1000,\n        currentRetries: {\n          userInfo: 0,\n          dynamicList: 0\n        }\n      }\n    }\n  },\n  onPullDownRefresh() {\n    // 统一登录状态检查\n    if (!this.checkLoginStatus()) {\n      uni.stopPullDownRefresh();\n      return;\n    }\n    this.refreshData(true);\n  },\n  onLoad() {\n    this.navigationBarColor(0);\n\n    // 初始化统一用户管理工具\n    userManager.init(this.$store);\n\n    // 初始化Pinia stores\n    this.initPiniaStores();\n\n    // 注册事件监听\n    uni.$on('loginStateChanged', this.handleLoginStateChanged);\n    uni.$on('userInfoUpdated', this.handleUserInfoUpdate);\n\n    // 先加载缓存数据，提升首屏体验\n    this.loadUserFromCache();\n\n    // 检查登录状态\n    if (!this.checkLoginStatus(true)) {\n      return;\n    }\n\n    // 异步初始化页面数据\n    this.$nextTick(() => {\n      this.initPageData();\n    });\n  },\n  onUnload() {\n    uni.$off('userInfoUpdated', this.handleUserInfoUpdate);\n    uni.$off('loginStateChanged', this.handleLoginStateChanged);\n    \n    // 清除所有定时器\n    this.clearAllTimers();\n  },\n  onShow() {\n    this.navigationBarColor(0);\n\n    // 清理过期的tab数据缓存\n    this.cleanExpiredTabCache();\n\n    // 统一登录状态检查\n    if (!this.checkLoginStatus(true)) {\n      return;\n    }\n\n    // 检查是否需要刷新数据\n    if (this.isLogin) {\n      // 优化：使用防抖避免频繁刷新\n      this.debounceRefresh();\n\n      // 记录访问（异步执行，不阻塞主流程）\n      this.$nextTick(() => {\n        this.setVisit();\n      });\n    }\n  },\n  methods: {\n    // 统一用户信息管理 - 初始化stores\n    initPiniaStores() {\n      // 从本地存储初始化用户状态\n      this.userStore.initFromStorage()\n      this.appStore.initFromStorage()\n\n      // 统一用户信息数据源\n      this.syncUserInfoFromAllSources()\n\n      console.log('Pinia stores初始化完成:', {\n        用户登录状态: this.userStore.isLoggedIn,\n        用户昵称: this.userStore.displayName,\n        是否VIP: this.userStore.isVip,\n        主题模式: this.appStore.theme.mode\n      })\n    },\n\n    // 统一用户信息数据源\n    syncUserInfoFromAllSources() {\n      try {\n        // 使用统一用户管理工具同步所有数据源\n        userManager.syncAllSources()\n\n        // 获取同步后的用户信息\n        const { userInfo, isLogin } = userManager.getUserInfo()\n\n        // 更新页面数据\n        if (isLogin && userInfo.nickname) {\n          this.userInfo = { ...this.userInfo, ...userInfo }\n          console.log('页面用户信息已同步:', {\n            uid: userInfo.uid,\n            nickname: userInfo.nickname\n          })\n        }\n      } catch (error) {\n        console.error('同步用户信息失败:', error)\n      }\n    },\n\n    // 统一更新用户信息到所有数据源\n    updateUserInfoToAllSources(newUserInfo) {\n      try {\n        if (!newUserInfo || typeof newUserInfo !== 'object') {\n          console.warn('updateUserInfoToAllSources: 无效的用户信息', newUserInfo);\n          return;\n        }\n\n        // 使用统一用户管理工具更新\n        const success = userManager.updateUserInfo(newUserInfo);\n\n        if (success) {\n          // 获取更新后的用户信息\n          const { userInfo } = userManager.getUserInfo();\n          this.userInfo = { ...this.userInfo, ...userInfo };\n        }\n\n      } catch (error) {\n        console.error('统一更新用户信息失败:', error);\n      }\n    },\n\n    // Pinia使用示例 - 缓存动态数据\n    cacheDynamicDataToPinia() {\n      if (this.list.length > 0) {\n        const cacheType = 'personal'\n        const subType = this.barIdx === 0 ? 'notes' : 'likes'\n\n        // 缓存当前tab的数据到Pinia\n        this.socialStore.setDynamicCache(cacheType, subType, this.list, this.totalCount)\n\n        console.log('动态数据已缓存到Pinia:', {\n          类型: `${cacheType}.${subType}`,\n          数据量: this.list.length,\n          总数: this.totalCount\n        })\n      }\n    },\n\n    // Pinia使用示例 - 从缓存加载数据\n    loadDataFromPiniaCache() {\n      const cacheType = 'personal'\n      const subType = this.barIdx === 0 ? 'notes' : 'likes'\n\n      // 检查缓存是否有效\n      if (this.socialStore.isCacheValid(cacheType, subType)) {\n        const cache = this.socialStore.getDynamicCache(cacheType, subType)\n\n        this.list = [...cache.data]\n        this.totalCount = cache.totalCount\n        this.isEmpty = this.list.length === 0\n        this.loadStatus = this.list.length >= this.totalCount ? 'noMore' : 'more'\n\n        console.log('从Pinia缓存加载数据:', {\n          类型: `${cacheType}.${subType}`,\n          数据量: this.list.length,\n          缓存时间: new Date(cache.timestamp).toLocaleString()\n        })\n\n        return true\n      }\n\n      return false\n    },\n\n    // 统一登录状态检查\n    checkLoginStatus(redirectToLogin = false) {\n      const isLoggedIn = this.isLogin && this.$store.state.app.token;\n\n      // #ifdef H5 || APP-PLUS\n      if (!isLoggedIn && redirectToLogin) {\n        toLogin();\n        return false;\n      }\n      // #endif\n\n      return isLoggedIn;\n    },\n    \n    // 清除定时器\n    clearAllTimers() {\n      if (this.refreshTimer) {\n        clearTimeout(this.refreshTimer);\n        this.refreshTimer = null;\n      }\n    },\n\n    // 简化的刷新方法\n    debounceRefresh() {\n      if (this.refreshTimer) {\n        clearTimeout(this.refreshTimer);\n      }\n\n      this.refreshTimer = setTimeout(() => {\n        this.refreshData();\n      }, 300);\n    },\n\n    // 简化的数据刷新检查\n    checkAndRefreshData() {\n      if (!this.checkLoginStatus()) {\n        return;\n      }\n\n      const now = Date.now();\n\n      // 检查是否需要刷新（统一5分钟缓存）\n      if (now - this.lastRefreshTime > this.cacheTimeout) {\n        this.refreshData();\n      }\n    },\n    \n    async initPageData() {\n      try {\n        // 异步获取侧边栏菜单（优化：使用缓存）\n        this.getSidebarMenu().catch(err => {\n          console.log('获取侧边栏菜单失败:', err);\n        });\n\n        if (!this.isLogin) {\n          this.isEmpty = true;\n          this.list = [];\n          return;\n        }\n\n        // 并行加载用户信息和动态列表\n        await Promise.allSettled([\n          this.getUserSocialInfo(false),\n          this.userDynamic(false, false)\n        ]);\n\n      } catch (error) {\n        console.error('页面初始化失败:', error);\n        this.showErrorToast('页面加载失败，请稍后重试');\n      } finally {\n        // 异步记录访问\n        this.$nextTick(() => {\n          this.setVisit();\n        });\n      }\n    },\n\n    // 错误处理方法\n    handleUserInfoError(error) {\n      console.error('用户信息加载失败:', error);\n      // 用户信息加载失败时的降级处理\n      if (!this.userInfo.nickname || this.userInfo.nickname === \"您还未登录哦~\") {\n        this.userInfo.nickname = \"加载失败\";\n        this.userInfo.avatar = \"/static/img/avatar.png\";\n      }\n    },\n\n    handleDynamicListError(error) {\n      console.error('动态列表加载失败:', error);\n      // 动态列表加载失败时的处理\n      if (this.list.length === 0) {\n        this.isEmpty = true;\n        this.loadStatus = 'more';\n      }\n    },\n\n    showErrorToast(message, duration = 2000) {\n      uni.showToast({\n        title: message,\n        icon: 'none',\n        duration: duration\n      });\n    },\n    \n    loadUserFromCache() {\n      try {\n        // 使用统一的数据源同步方法\n        this.syncUserInfoFromAllSources()\n\n        // 如果同步后仍然没有用户信息，尝试从缓存恢复\n        if (!this.userInfo.uid || !this.userInfo.nickname) {\n          const cachedUserInfo = uni.getStorageSync('USER_INFO');\n          if (cachedUserInfo && typeof cachedUserInfo === 'object') {\n            this.userInfo = {...this.userInfo, ...cachedUserInfo};\n            this.userClick();\n          } else if (typeof cachedUserInfo === 'string') {\n            try {\n              const parsedInfo = JSON.parse(cachedUserInfo);\n              this.userInfo = {...this.userInfo, ...parsedInfo};\n              this.userClick();\n            } catch (e) {\n              console.error('解析缓存用户信息失败:', e);\n            }\n          }\n        } else {\n          this.userClick();\n        }\n      } catch (e) {\n        console.error('读取缓存用户信息失败:', e);\n      }\n    },\n\n    // 清理过期的tab数据缓存\n    cleanExpiredTabCache() {\n      const now = Date.now();\n      Object.keys(this.tabDataCache).forEach(key => {\n        const cache = this.tabDataCache[key];\n        if (cache.timestamp && (now - cache.timestamp > this.cacheTimeout)) {\n          this.tabDataCache[key] = { data: [], timestamp: 0, totalCount: 0 };\n        }\n      });\n    },\n    \n    async refreshData(isPullDown = false) {\n      // 防止重复刷新\n      if (this.loadingState.pullRefresh) {\n        return;\n      }\n\n      // 设置刷新状态\n      this.loadingState.pullRefresh = true;\n\n      try {\n        // 重置页码\n        this.page = 1;\n\n        // 串行执行，避免状态冲突\n        await this.getUserSocialInfo(false).catch(err => {\n          console.error('刷新用户信息失败:', err);\n        });\n\n        await this.userDynamic(true, false).catch(err => {\n          console.error('刷新动态列表失败:', err);\n        });\n\n        // 更新最后刷新时间\n        this.lastRefreshTime = Date.now();\n\n      } finally {\n        this.loadingState.pullRefresh = false;\n        if (isPullDown) {\n          uni.stopPullDownRefresh();\n        }\n      }\n    },\n    \n    handleLoginStateChanged(isLoggedIn) {\n      if (isLoggedIn) {\n        // 登录状态变更为已登录，刷新数据\n        this.getUserSocialInfo(true).catch(err => {\n          console.log('登录状态变更后刷新用户信息失败:', err);\n        });\n        this.userDynamic(false, true).catch(err => {\n          if (err.message !== '正在加载中') {\n            console.log('登录状态变更后刷新动态列表失败:', err);\n          }\n        });\n      } else {\n        // 登录状态变更为未登录，重置用户信息\n        this.resetUserInfo();\n      }\n    },\n    \n    resetUserInfo() {\n      // 使用统一用户管理工具重置\n      userManager.logout();\n\n      // 重置页面数据\n      this.userInfo = {\n        uid: 0,\n        avatar: '/static/img/avatar.png',\n        nickname: '未登录',\n        about_me: '',\n        follow_count: 0,\n        fans_count: 0,\n        like_count: 0,\n        like_count_str: '0',\n        visitor_count: 0,\n        visitor_badge: 0\n      };\n\n      // 重置数据状态\n      this.list = [];\n      this.isEmpty = true;\n      this.loadStatus = 'more';\n      this.page = 1;\n\n      // 重置加载状态\n      Object.keys(this.loadingState).forEach(key => {\n        this.loadingState[key] = false;\n      });\n\n      // 重置刷新时间\n      this.lastRefreshTime = 0;\n\n      console.log('用户信息已统一重置');\n    },\n    \n    handleApiError(err) {\n      if (err && (err.statusCode === 401 || err.code === 401 || err.status === 401)) {\n        uni.showToast({\n          title: '登录信息已过期，请重新登录',\n          icon: 'none',\n          duration: 2000\n        });\n        this.resetUserInfo();\n        setTimeout(() => toLogin(), 1500);\n        return true;\n      }\n      return false;\n    },\n    \n    async getUserSocialInfo(silent = false, retryCount = 0) {\n      // 防止重复请求\n      if (this.loadingState.userInfo || !this.isLogin) {\n        const errorMsg = '正在加载中或未登录';\n        if (!silent && this.loadingState.userInfo) {\n          this.showErrorToast('数据加载中，请稍候', 1500);\n        }\n        return Promise.reject(new Error(errorMsg));\n      }\n\n      // 设置加载状态\n      this.loadingState.userInfo = true;\n\n      // 非静默模式下显示加载提示\n      if (!silent) {\n        uni.showLoading({\n          title: '加载中...',\n          mask: false\n        });\n      }\n\n      try {\n        const res = await getUserSocialInfo();\n\n        // 更新请求时间\n        this.lastRefreshTime = Date.now();\n\n        if (res.status === 200 || res.code === 200) {\n          const socialData = res.data;\n\n          // 数据处理\n          if (socialData.like_count !== undefined) {\n            socialData.like_count_str = socialData.like_count > 999 ?\n              (socialData.like_count/1000).toFixed(1) + 'k' :\n              socialData.like_count.toString();\n          }\n\n          if (!socialData.uid && this.$store.state.app.userInfo?.uid) {\n            socialData.uid = this.$store.state.app.userInfo.uid;\n          }\n\n          // 统一更新用户信息到所有数据源\n          this.updateUserInfoToAllSources(socialData);\n          this.userClick();\n\n          if (socialData.service_num !== undefined) {\n            this.currentMsg = socialData.service_num;\n          }\n\n          // 重置重试计数\n          this.retryConfig.currentRetries.userInfo = 0;\n\n          return socialData;\n        } else {\n          throw new Error(res.msg || '获取用户信息失败');\n        }\n\n      } catch (err) {\n        // 处理API错误\n        if (this.handleApiError(err)) {\n          return Promise.reject(err);\n        }\n\n        // 重试机制\n        if (retryCount < this.retryConfig.maxRetries && !silent) {\n          console.log(`用户信息获取失败，正在重试 ${retryCount + 1}/${this.retryConfig.maxRetries}`);\n\n          await new Promise(resolve =>\n            setTimeout(resolve, this.retryConfig.retryDelay * (retryCount + 1))\n          );\n\n          return this.getUserSocialInfo(silent, retryCount + 1);\n        }\n\n        // 降级处理\n        if (this.$store.state.app.userInfo && !this.userInfo.nickname) {\n          this.userInfo = {...this.userInfo, ...this.$store.state.app.userInfo};\n          this.userClick();\n        }\n\n        throw err;\n\n      } finally {\n        this.loadingState.userInfo = false;\n        if (!silent) {\n          uni.hideLoading();\n        }\n      }\n    },\n    \n    getUserInfo(silent = false) {\n      return this.getUserSocialInfo(silent);\n    },\n    \n    async userDynamic(isRefresh = false, silent = false, retryCount = 0) {\n      // 检查登录状态\n      if (!this.checkLoginStatus()) {\n        this.isEmpty = true;\n        this.list = [];\n        this.loadStatus = 'more';\n        return Promise.reject(new Error('未登录状态'));\n      }\n\n      // 防止重复请求\n      if (this.loadingState.dynamicList) {\n        const errorMsg = '正在加载中';\n        if (!silent) {\n          this.showErrorToast('数据加载中，请稍候', 1500);\n        }\n        return Promise.reject(new Error(errorMsg));\n      }\n\n      // 设置加载状态\n      this.loadingState.dynamicList = true;\n\n      // 显示加载状态\n      if (!silent) {\n        this.loadStatus = 'loading';\n      }\n\n      // 重置页码\n      if (isRefresh) this.page = 1;\n\n      try {\n        const loginUserId = this.$store.state.app.uid || 0;\n        let apiCall;\n\n        // 根据当前tab选择对应的API\n        if (this.barIdx == 0) {\n          apiCall = getMyDynamicList({\n            page: this.page,\n            limit: 10\n          });\n        } else if (this.barIdx == 1) {\n          apiCall = getLikeDynamicList(loginUserId, {\n            page: this.page,\n            limit: 10\n          });\n        } else {\n          console.warn('未知的barIdx:', this.barIdx);\n          return;\n        }\n\n        const res = await apiCall;\n\n        // 只有非静默模式才更新加载状态\n        if (!silent) {\n          this.loadStatus = 'more';\n        }\n\n        if (res.status == 200 && res.data) {\n          if (res.data.list && res.data.list.length > 0) {\n            // 优化：使用更高效的数组操作\n            if (this.page == 1) {\n              this.list = res.data.list;\n            } else {\n              // 使用push.apply比concat更高效\n              this.list.push(...res.data.list);\n            }\n\n            if (res.data.count !== undefined) {\n              this.totalCount = res.data.count;\n            }\n            this.isEmpty = false;\n          } else if (this.page == 1) {\n            this.isEmpty = true;\n            this.list = [];\n          }\n\n          this.updateStoreState();\n\n          // 缓存当前tab的数据\n          if (this.page === 1) { // 只缓存第一页数据\n            this.tabDataCache[this.barIdx] = {\n              data: [...this.list],\n              timestamp: Date.now(),\n              totalCount: this.totalCount\n            };\n          }\n\n          // 重置重试计数\n          this.retryConfig.currentRetries.dynamicList = 0;\n\n          return res.data;\n        } else {\n          throw new Error(res.msg || '获取数据失败');\n        }\n\n      } catch (err) {\n        // 只有非静默模式才更新加载状态\n        if (!silent) {\n          this.loadStatus = 'more';\n        }\n\n        // 处理401错误\n        if (err && (err.statusCode === 401 || err.code === 401 || err.status === 401)) {\n          this.handleApiError({status: 401});\n          throw err;\n        }\n\n        // 重试机制\n        if (retryCount < this.retryConfig.maxRetries && !silent) {\n          console.log(`动态列表获取失败，正在重试 ${retryCount + 1}/${this.retryConfig.maxRetries}`);\n\n          await new Promise(resolve =>\n            setTimeout(resolve, this.retryConfig.retryDelay * (retryCount + 1))\n          );\n\n          return this.userDynamic(isRefresh, silent, retryCount + 1);\n        }\n\n        // 最终失败处理\n        if (this.page == 1 && !silent) {\n          this.isEmpty = true;\n          this.list = [];\n        }\n\n        this.updateStoreState();\n        throw err;\n\n      } finally {\n        this.loadingState.dynamicList = false;\n      }\n    },\n    \n    updateStoreState() {\n      // 通过 mutation 更新状态，而不是直接修改\n      try {\n        if (typeof this.$store.state.app.isCurrentMsg !== 'undefined') {\n          this.$store.commit('SET_CURRENT_MSG', true);\n        }\n        // 如果需要 isCenterPage 状态，需要先在 store 中定义对应的 mutation\n        // this.$store.commit('SET_CENTER_PAGE', false);\n      } catch (error) {\n        console.warn('更新 store 状态失败:', error.message);\n      }\n    },\n    \n    barClick(e) {\n      if (this.isThrottling || this.loadingState.dynamicList) return;\n\n      const newBarIdx = parseInt(e.currentTarget.dataset.idx);\n\n      if (newBarIdx === this.barIdx) return;\n\n      this.isThrottling = true;\n      this.barIdx = newBarIdx;\n      this.page = 1;\n\n      // 检查是否有缓存数据\n      const cacheData = this.tabDataCache[newBarIdx];\n      const now = Date.now();\n      const hasFreshCache = cacheData && cacheData.timestamp &&\n                           (now - cacheData.timestamp < this.cacheTimeout) &&\n                           cacheData.data.length > 0;\n\n      if (hasFreshCache) {\n        // 使用缓存数据，不显示loading\n        this.list = [...cacheData.data];\n        this.totalCount = cacheData.totalCount;\n        this.isEmpty = this.list.length === 0;\n        this.loadStatus = this.list.length >= this.totalCount ? 'noMore' : 'more';\n\n        setTimeout(() => {\n          this.isThrottling = false;\n        }, 100); // 缩短节流时间\n      } else {\n        // 没有缓存或缓存过期，显示loading并请求数据\n        this.loadStatus = 'loading';\n        this.userDynamic().finally(() => {\n          setTimeout(() => {\n            this.isThrottling = false;\n          }, 300);\n        });\n      }\n    },\n    \n    delClick(e) {\n      uni.showModal({\n        content: '确定删除该笔记吗？',\n        confirmColor: '#FA5150',\n        success: (res) => {\n          if (res.confirm) {\n            deleteDynamic(this.list[e.idx].id).then(res => {\n              if (res.status == 200) {\n                this.list.splice(e.idx, 1);\n                if (this.list.length <= 0) {\n                  this.isEmpty = true;\n                }\n                uni.showToast({title: '删除成功', icon: 'success'});\n              } else {\n                uni.showToast({title: res.msg || '删除失败', icon: 'none'});\n              }\n            }).catch(() => {\n              uni.showToast({title: '删除失败，请重试', icon: 'none'});\n            });\n          }\n        }\n      });\n    },\n    \n    likePopupClick(open) {\n      if (open) {\n        this.$refs.likePopup.open();\n      } else {\n        this.$refs.likePopup.close();\n      }\n    },\n    \n    navigateToFun(e) {\n      // 统一登录状态检查\n      if (!this.checkLoginStatus(true)) {\n        return;\n      }\n      \n      if (this.showSidebar) {\n        this.showSidebar = false;\n      }\n      \n      const url = e.currentTarget.dataset.url;\n      if (url === 'center/visitor') {\n        this.getVisitorList();\n      }\n      \n      uni.navigateTo({url: '/pages/' + url});\n    },\n    \n    toFollowList(type) {\n      // 统一登录状态检查\n      if (!this.checkLoginStatus(true)) {\n        return;\n      }\n      \n      uni.navigateTo({\n        url: `/pages/center/follow?type=${type}&id=${this.userInfo.uid}&name=${this.userInfo.nickname}`\n      });\n    },\n    \n    userClick() {\n      this.blockList[0].img = this.userInfo.circle_img || '';\n      this.blockList[0].count = this.userInfo.circle_count || 0;\n      this.blockList[1].img = this.userInfo.cart_img || '';\n      this.blockList[1].count = this.userInfo.cart_count || 0;\n      this.blockList[2].img = this.userInfo.order_img || '';\n      this.blockList[2].count = this.userInfo.order_count || 0;\n    },\n    \n    navigationBarColor(status) {\n      uni.setNavigationBarColor({\n        frontColor: status ? '#000000' : '#ffffff',\n        backgroundColor: 'transparent',\n        animation: {duration: 300, timingFunc: 'easeIn'}\n      });\n    },\n    \n    getSidebarMenu() {\n      // 检查缓存是否有效\n      const cachedMenu = uni.getStorageSync('SIDEBAR_MENU');\n      const now = Date.now();\n\n      if (cachedMenu && cachedMenu.timestamp && (now - cachedMenu.timestamp < this.cacheTimeout)) {\n        // 使用缓存数据\n        this.sidebarMenu = cachedMenu.data || [];\n        return Promise.resolve(this.sidebarMenu);\n      }\n      \n      return new Promise((resolve, reject) => {\n      getMenuList().then(res => {\n          \n        if (res.data && res.data.routine_my_menus) {\n          let storeMenu = [];\n          let sidebarMenus = [];\n          \n          res.data.routine_my_menus.forEach((el) => {\n            const menuItem = {\n                name: el.name,\n                icon: el.pic,\n                url: el.url.replace('/pages/', ''),\n                badge: \"\"\n            };\n            \n            if (el.url == '/pages/admin/order/index' || \n                el.url == '/pages/admin/order_cancellation/index' || \n                el.name == '客服接待') {\n              storeMenu.push(menuItem);\n            } else {\n              sidebarMenus.push(menuItem);\n            }\n          });\n          \n          this.sidebarMenu = [...sidebarMenus, ...storeMenu];\n            \n            // 缓存菜单数据\n            uni.setStorageSync('SIDEBAR_MENU', {\n              data: this.sidebarMenu,\n              timestamp: now\n            });\n            \n            resolve(this.sidebarMenu);\n          } else {\n            reject(new Error('获取菜单数据失败'));\n        }\n        }).catch(err => {\n          reject(err);\n        });\n      });\n    },\n\n    toggleSidebar() {\n      this.showSidebar = !this.showSidebar;\n      this.$store.commit('SET_PREVENT_SCROLL', this.showSidebar);\n      \n      // #ifdef H5\n      document.body.style.overflow = this.showSidebar ? 'hidden' : '';\n      // #endif\n    },\n\n    getVisitorList() {\n      this.userInfo.visitor_badge = 0;\n      this.$store.commit(\"UPDATE_USERINFO\", this.userInfo);\n\n      getVisitorDetails({page: 1, limit: 20, type: 0}).then((res) => {\n        const emptyData = {visitors: [], total: 0, has_more: false};\n\n        if (res.status === 200 || res.code === 200) {\n          const resData = res.data || {};\n          this.userInfo.visitor_count = resData.total || 0;\n          this.userInfo.visitor_badge = 0;\n          this.$store.commit(\"UPDATE_USERINFO\", this.userInfo);\n\n          // 计算是否还有更多数据\n          const hasMore = resData.page * resData.limit < resData.total;\n\n          uni.$emit('updateVisitorList', {\n            visitors: resData.list || [],\n            total: resData.total || 0,\n            has_more: hasMore,\n            page: resData.page || 1,\n            limit: resData.limit || 20\n          });\n        } else {\n          this.userInfo.visitor_count = 0;\n          this.userInfo.visitor_badge = 0;\n          this.$store.commit(\"UPDATE_USERINFO\", this.userInfo);\n          uni.$emit('updateVisitorList', emptyData);\n        }\n      }).catch((error) => {\n        console.error('获取访客列表失败:', error);\n        this.userInfo.visitor_count = 0;\n        this.userInfo.visitor_badge = 0;\n        this.$store.commit(\"UPDATE_USERINFO\", this.userInfo);\n        uni.$emit('updateVisitorList', {visitors: [], total: 0, has_more: false});\n      });\n    },\n\n    setVisit() {\n      setVisit({url: '/pages/tabbar/center'}).catch(() => {});\n    },\n\n    formatDate(timestamp) {\n      if (!timestamp) return '';\n      const date = new Date(timestamp * 1000);\n      return `${date.getFullYear()}-${('0' + (date.getMonth() + 1)).slice(-2)}-${('0' + date.getDate()).slice(-2)}`;\n    },\n    \n    goToVipPage() {\n      // 统一登录状态检查\n      if (!this.checkLoginStatus(true)) {\n        return;\n      }\n      uni.navigateTo({url: '/pages/annex/vip_paid/index'});\n    },\n\n    handleUserInfoUpdate() {\n      // 使用统一的数据源同步方法\n      this.syncUserInfoFromAllSources();\n    },\n\n    handleBottomNav(type) {\n      // 统一登录状态检查\n      if (!this.checkLoginStatus(true)) {\n        return;\n      }\n\n      this.showSidebar = false;\n\n      switch(type) {\n        case 'scan':\n          // #ifdef APP-PLUS || MP-WEIXIN\n          uni.scanCode({\n            success: (res) => {\n              if(res.result) {\n                uni.showToast({title: '扫码成功', icon: 'success'});\n              }\n            },\n            fail: () => {\n              uni.showToast({title: '扫码失败', icon: 'none'});\n            }\n          });\n          // #endif\n          \n          // #ifdef H5\n          uni.showToast({title: 'H5环境不支持扫码功能', icon: 'none'});\n          // #endif\n          break;\n          \n        case 'help':\n          uni.navigateTo({url: '/pages/setting/service'});\n          break;\n          \n        case 'setting':\n          uni.navigateTo({url: '/pages/setting/index'});\n          break;\n      }\n    },\n\n    // 模板辅助方法\n    getEmptyTitle() {\n      const titles = ['暂无笔记内容', '暂无喜欢的内容'];\n      return titles[this.barIdx] || '暂无内容';\n    },\n\n    getEmptySubtitle() {\n      if (this.barIdx === 1) {\n        return '快在推荐中寻找更多笔记吧';\n      }\n      return '发笔记，记录灵感日常';\n    },\n\n    handleLogin() {\n      toLogin();\n    }\n  },\n  onReachBottom() {\n    // 防止重复加载或已经加载完所有数据\n    if (this.loadingState.dynamicList || this.loadingState.loadMore || this.loadStatus === 'noMore') {\n      return;\n    }\n\n    // 检查是否还有更多数据\n    if (this.list.length && this.list.length < this.totalCount) {\n      this.loadingState.loadMore = true;\n      this.loadStatus = 'loading';\n      const currentPage = this.page;\n      this.page++;\n\n      this.userDynamic(false, false).catch(err => {\n        console.log('加载更多数据失败:', err);\n        this.page = currentPage;\n        this.loadStatus = 'more';\n\n        if (err.message !== '正在加载中') {\n          this.showErrorToast('加载更多失败，请稍后再试', 2000);\n        }\n      }).finally(() => {\n        this.loadingState.loadMore = false;\n      });\n    } else if (this.list.length >= this.totalCount && this.list.length > 0) {\n      this.loadStatus = 'noMore';\n    }\n  },\n  onPageScroll(e) {\n    if (this.showSidebar) {\n      return;\n    }\n    \n    this.scrollTop = e.scrollTop;\n    \n    const threshold = this.statusBarHeight + this.titleBarHeight + 80;\n    if (this.scrollTop <= threshold) {\n      this.navbarTrans = Math.min(this.scrollTop / threshold, 1);\n    } else {\n      this.navbarTrans = 1;\n    }\n    \n    if (this.scrollTop > threshold) {\n      this.navigationBarColor(1);\n    } else {\n      this.navigationBarColor(0);\n    }\n  }\n}\n</script>\n\n<style>\n.nav-box{\n  position:fixed;\n  z-index:99;\n  top:0;\n  left:0;\n  width:100%;\n  box-sizing:border-box;\n  transition:all .3s ease-in-out\n}\n.nav-box .nav-item{\n  position: relative;\n  width: 100%;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n}\n.nav-box .nav-item .ohto{\n  max-width: 420rpx;\n  font-size: 26rpx;\n  font-weight: 700;\n  transition: all 0.3s ease-in-out;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n.nav-user-avatar {\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 50%;\n  margin-right: 12rpx;\n}\n.user-box{\n  width:calc(100% - 60rpx);\n  padding:60rpx 30rpx;\n  color:#fff;\n  position:relative;\n  overflow:hidden\n}\n.user-box .user-img,\n.user-box .user-bg{\n  position:absolute;\n  top:0;\n  left:0;\n  width:100%;\n  height:100%\n}\n.user-box .user-bg{\n  z-index:-1;\n  /* 优化性能：条件编译backdrop-filter */\n  /* #ifndef APP-PLUS */\n  -webkit-backdrop-filter:saturate(150%) blur(25px);\n  backdrop-filter:saturate(150%) blur(25px);\n  /* #endif */\n  background:rgba(0,0,0,.6)\n}\n.user-box .user-top{\n  width:100%;\n  justify-content:space-between\n}\n.user-top .avatar{\n  width:140rpx;\n  height:140rpx;\n  border-radius:50%;\n  background:#fff;\n  border:2px solid #f5f5f5;\n  overflow:hidden\n}\n.user-box .user-name{\n  margin:20rpx 0 10rpx;\n  width:100%;\n  font-size:34rpx;\n  font-weight:700\n}\n.user-box .user-intro{\n  width:100%;\n  word-break:break-word;\n  white-space:pre-line\n}\n.user-box .user-intro text{\n  color:#ccc;\n  font-size:24rpx;\n  font-weight:400\n}\n.user-box .user-tag{\n  margin:20rpx 0;\n  width:100%\n}\n.user-tag .tag-item{\n  margin-right:16rpx;\n  height:44rpx;\n  padding:0 14rpx;\n  border-radius:8rpx;\n  background:rgba(255,255,255,.15);\n  font-weight:500;\n  font-size:20rpx;\n  justify-content:center\n}\n.user-tag .tag-item image{\n  width:24rpx;\n  height:24rpx\n}\n.user-num-wrap {\n  width: 100%;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 20rpx;\n}\n.user-num {\n  flex: 1;\n}\n.user-num .num-item {\n  margin-right: 30rpx;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n.user-num .num-item .t1 {\n  color: #fff;\n  font-size: 32rpx;\n  font-weight: 700;\n  margin-bottom: 8rpx;\n}\n.user-num .num-item .t2 {\n  font-size: 20rpx;\n  font-weight: 300;\n  color: #ccc;\n}\n.visitor-item {\n  position: relative;\n}\n.visitor-item .badge {\n  position: absolute;\n  top: -12rpx;\n  right: -28rpx;\n  min-width: 32rpx;\n  height: 32rpx;\n  padding: 0 6rpx;\n  background: #ff3a3a;\n  color: #fff;\n  border-radius: 16rpx;\n  font-size: 20rpx;\n  text-align: center;\n  line-height: 32rpx;\n  z-index: 2;\n  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);\n}\n.user-actions {\n  align-items: center;\n}\n.btn-item {\n  padding: 0 30rpx;\n  height: 64rpx;\n  line-height: 64rpx;\n  border-radius: 8rpx;\n  font-size: 20rpx;\n  font-weight: 700;\n}\n.bg1 {\n  color: #fff;\n  background: rgba(255,255,255,.15);\n}\n.btn-icon {\n  width: 64rpx;\n  height: 64rpx;\n  border-radius: 8rpx;\n  background: #fff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-left: 20rpx;\n}\n.btn-icon image {\n  width: 32rpx;\n  height: 32rpx;\n}\n.user-block{\n  margin-top:-30rpx;\n  width:100%;\n  white-space:nowrap;\n  background:#fff;\n  border-radius:30rpx 30rpx 0 0\n}\n.user-block .block-box{\n  width:100%;\n  padding:30rpx 15rpx;\n  display:flex\n}\n.block-box .block-item{\n  flex-shrink:0;\n  margin:0 15rpx;\n  padding:24rpx;\n  background:#f8f8f8;\n  border-radius:16rpx;\n  justify-content:space-between;\n  position:relative\n}\n.block-item .block-title .t1{\n  font-size:26rpx;\n  font-weight:700\n}\n.block-item .block-title .t2{\n  margin-top:4rpx;\n  color:#999;\n  font-size:16rpx;\n  font-weight:300\n}\n.block-item .cu-group{\n  position:relative;\n  right:38rpx\n}\n.cu-group .cu-item{\n  z-index:3;\n  width:68rpx;\n  height:68rpx;\n  border-radius:8rpx;\n  overflow:hidden\n}\n.cu-group .cu-item .icon{\n  margin:18rpx;\n  width:32rpx;\n  height:32rpx\n}\n.cu-group .cu-item .img{\n  width:100%;\n  height:100%\n}\n.cu-group .cu-lump2{\n  position:absolute;\n  z-index:2;\n  left:18rpx;\n  width:58rpx;\n  height:58rpx;\n  border-radius:8rpx;\n  background:#dbdbdb\n}\n.cu-group .cu-lump1{\n  position:absolute;\n  z-index:1;\n  left:38rpx;\n  width:48rpx;\n  height:48rpx;\n  border-radius:8rpx;\n  background:#eaeaea\n}\n.block-item .block-icon{\n  position:absolute;\n  right:12rpx;\n  width:20rpx;\n  height:20rpx;\n  transform:rotate(-90deg)\n}\n.bar-box{\n  position:sticky;\n  left:0;\n  z-index:99;\n  margin-top:-1px;\n  width:100%;\n  height:80rpx;\n  background:#fff\n}\n.bar-box .bar-item{\n  padding:0 30rpx;\n  height:100%;\n  flex-direction:column;\n  justify-content:center;\n  position:relative\n}\n.bar-box .bar-item text{\n  font-weight:700;\n  transition:all .3s ease-in-out\n}\n.bar-item .bar-line{\n  position:absolute;\n  bottom:12rpx;\n  width:18rpx;\n  height:6rpx;\n  border-radius:6rpx;\n  background:#000;\n  transition:opacity .3s ease-in-out\n}\n/* 优化的CSS类名 */\n.content-container{\n  padding-bottom:180rpx\n}\n.like-popup{\n  background:#fff;\n  width:400rpx;\n  padding:30rpx;\n  border-radius:30rpx;\n  overflow:hidden\n}\n.like-popup .like-img{\n  margin:0 40rpx;\n  width:320rpx;\n  height:200rpx\n}\n.like-popup .like-content{\n  margin:20rpx 0 40rpx;\n  width:100%;\n  color:#333;\n  font-size:26rpx;\n  text-align:center\n}\n.like-popup .like-btn{\n  width:100%;\n  height:80rpx;\n  line-height:80rpx;\n  text-align:center;\n  font-size:24rpx;\n  font-weight:700;\n  color:#fff;\n  background:#000;\n  border-radius:16rpx\n}\n.df {\n  display: flex;\n  align-items: center;\n}\n.ohto {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.empty-state {\n  flex-direction: column;\n  padding: 120rpx 0;\n}\n.empty-state image {\n  width: 280rpx;\n  height: 280rpx;\n}\n.empty-state .empty-title {\n  margin-top: 40rpx;\n  font-size: 32rpx;\n  font-weight: bold;\n}\n.empty-state .empty-subtitle {\n  margin-top: 20rpx;\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 重试按钮样式已删除 */\n\n.loading-state {\n  flex-direction: column;\n  padding: 120rpx 0;\n}\n\n.loading-text {\n  margin-top: 20rpx;\n  font-size: 24rpx;\n  color: #999;\n}\n.loading-indicator {\n  justify-content: center;\n}\n.nav-menu-btn {\n  position: absolute;\n  left: 30rpx;\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 50%;\n  background: rgba(0,0,0,0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 100;\n  -webkit-tap-highlight-color: transparent;\n}\n.nav-menu-btn image {\n  width: 24rpx;\n  height: 24rpx;\n}\n.nav-menu-btn:active {\n  background: rgba(0,0,0,0.5);\n}\n.sidebar-menu {\n  position: fixed;\n  top: 0;\n  left: -75%;\n  width: 75%;\n  height: 100%;\n  max-height: 100vh;\n  background: #fff;\n  z-index: 999;\n  box-shadow: 2rpx 0 10rpx rgba(0,0,0,0.1);\n  transform: translateX(0);\n  transition: transform 0.3s ease-in-out;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.sidebar-menu.active {\n  transform: translateX(100%);\n}\n\n.sidebar-header {\n  flex-shrink: 0;\n  padding: 30rpx;\n  border-bottom: 1px solid #f5f5f5;\n  position: relative; /* 为关闭按钮提供定位基准 */\n}\n\n.sidebar-user-info {\n  display: flex;\n  align-items: center;\n}\n\n.sidebar-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  margin-right: 20rpx;\n}\n\n.sidebar-user-details {\n  flex: 1;\n  overflow: hidden;\n}\n\n.sidebar-user-name {\n  font-size: 30rpx;\n  font-weight: 500;\n  margin-bottom: 8rpx;\n}\n\n.user-status {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n}\n\n.status-item {\n  display: flex;\n  align-items: center;\n  padding: 4rpx 10rpx;\n  border-radius: 20rpx;\n  margin-right: 12rpx;\n  font-size: 20rpx;\n  margin-top: 4rpx;\n}\n\n.status-icon {\n  width: 80rpx;\n  height: 40rpx;\n  margin-right: 8rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.status-icon image {\n  width: 80rpx;\n  height: 36rpx;\n  display: block; /* 确保图片正确显示 */\n}\n\n\n.member-card {\n  flex-shrink: 0;\n  margin: 0 30rpx 20rpx;\n  padding: 30rpx 20rpx;\n  background: #2c2c2c;\n  border-radius: 16rpx;\n  color: #fff;\n}\n\n.member-status {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.member-label {\n  font-size: 30rpx;\n  font-weight: bold;\n}\n\n.member-price {\n  padding: 6rpx 20rpx;\n  background: #fff;\n  color: #333;\n  border-radius: 30rpx;\n  font-size: 22rpx;\n}\n\n.member-benefits {\n  margin-bottom: 10rpx;\n}\n\n.member-rights {\n  font-size: 22rpx;\n  color: rgba(255,255,255,0.8);\n}\n\n.member-desc {\n  margin-top: 16rpx;\n  font-size: 22rpx;\n  color: rgba(255,255,255,0.7);\n}\n\n.sidebar-scroll {\n  flex: 1;\n  overflow-y: auto;\n  -webkit-overflow-scrolling: touch;\n  overscroll-behavior: contain;\n}\n\n.sidebar-content {\n  padding: 16rpx 0 200rpx;\n  background-color: #f7f7f7;\n}\n\n/* 菜单部分样式 */\n.menu-section {\n  margin: 20rpx;\n  background-color: #fff;\n  border-radius: 16rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);\n}\n\n.section-title {\n  padding: 20rpx 30rpx 10rpx;\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n  border-bottom: 1px solid #f5f5f5;\n}\n\n/* 菜单宫格样式 */\n.menu-grid {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 10rpx;\n  background-color: #fff;\n}\n\n.grid-item {\n  width: 33.33%;\n  text-align: center;\n  padding: 20rpx 0;\n  position: relative;\n}\n\n.grid-item:active {\n  background-color: #f8f8f8;\n}\n\n.grid-icon-wrapper {\n  position: relative;\n  margin: 0 auto 10rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.grid-icon {\n  width: 50rpx;\n  height: 50rpx;\n}\n\n.grid-badge {\n  position: absolute;\n  top: -6rpx;\n  right: -6rpx;\n  min-width: 32rpx;\n  height: 32rpx;\n  padding: 0 6rpx;\n  background: #ff3a3a;\n  color: #fff;\n  border-radius: 16rpx;\n  font-size: 20rpx;\n  text-align: center;\n  line-height: 32rpx;\n  z-index: 2;\n  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);\n}\n\n.grid-text {\n  font-size: 24rpx;\n  color: #333;\n  display: block;\n  padding: 0 10rpx;\n}\n\n.sidebar-footer {\n  flex-shrink: 0;\n  background: #fff;\n}\n\n.bottom-nav {\n  width: 100%;\n  height: 120rpx;\n  justify-content: space-around;\n  padding: 0;\n  background-color: #f7f7f7;\n}\n\n.bottom-nav-item {\n  flex: 1;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n}\n\n.nav-icon-box {\n  width: 50rpx;\n  height: 50rpx;\n  justify-content: center;\n  align-items: center;\n}\n\n.nav-icon {\n  width: 44rpx;\n  height: 44rpx;\n}\n\n.nav-text {\n  font-size: 22rpx;\n  color: #666;\n  margin-top: 8rpx;\n}\n\n.copyright-text {\n  text-align: center;\n  color: #999;\n  font-size: 20rpx;\n  padding: 10rpx 0 30rpx;\n}\n\n.sidebar-mask {\n  position: fixed;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  background: rgba(0,0,0,0.5);\n  z-index: 998;\n  touch-action: none;\n}\n\n.no-scroll {\n  overflow: hidden !important;\n}\n\n.container.no-scroll,\n.container[style*=\"position: fixed\"] {\n  position: fixed !important;\n  left: 0 !important;\n  width: 100% !important;\n  overflow: hidden !important;\n  touch-action: none !important;\n  height: 100vh !important;\n}\n\n.container[style*=\"position: fixed\"] .user-box,\n.container[style*=\"position: fixed\"] .nav-box {\n  transform: none !important;\n}\n\n@media screen and (max-height: 667px) {\n  .sidebar-scroll {\n    height: calc(100vh - 350rpx - 170rpx);\n  }\n}\n\n.sidebar-item:active {\n  background-color: #f8f8f8;\n}\n\n.menu-group {\n  margin-bottom: 0;\n  margin: 0 20rpx;\n  border-radius: 16rpx;\n  background-color: #fff;\n  overflow: hidden;\n}\n\n.menu-divider {\n  height: 16rpx;\n  background-color: #f7f7f7;\n  margin: 0;\n  width: 100%;\n}\n\n.close-btn {\n  position: absolute;\n  right: 30rpx;\n  top: 50%;\n  transform: translateY(-50%) rotate(45deg);\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 50%;\n  background: #f8f8f8;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10;\n}\n\nbutton, \n.btn, \n.nav-menu-btn, \nview[role=\"button\"] {\n  -webkit-touch-callout: none;\n  -webkit-user-select: none;\n  -khtml-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  -webkit-tap-highlight-color: transparent;\n}\n\n.user-top {\n  width: 100%;\n  align-items: flex-start;\n  margin-bottom: 20rpx;\n}\n\n.avatar-wrapper {\n  position: relative;\n  margin-right: 20rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.avatar {\n  width: 140rpx;\n  height: 140rpx;\n  border-radius: 50%;\n  background: #fff;\n  border: 2px solid #f5f5f5;\n  overflow: hidden;\n  position: relative;\n}\n\n.profile-percent {\n  position: absolute;\n  left: 50%;\n  top: 0;\n  transform: translateX(-50%);\n  background: #ff6600;\n  border-radius: 12rpx;\n  padding: 4rpx 10rpx;\n  display: flex;\n  align-items: center;\n  font-size: 20rpx;\n  color: #fff;\n  z-index: 2;\n  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);\n}\n\n.edit-icon {\n  width: 24rpx;\n  height: 24rpx;\n  margin-right: 4rpx;\n}\n\n.user-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  padding-top: 20rpx;\n}\n\n.user-name-row {\n  align-items: center;\n  margin-bottom: 10rpx;\n}\n\n.user-name-text {\n  font-size: 34rpx;\n  font-weight: 700;\n  color: #fff;\n  margin-right: 12rpx;\n}\n\n.status-icon {\n  width: 80rpx;\n  height: 40rpx;\n  margin-right: 8rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.status-icon image {\n  width: 80rpx;\n  height: 36rpx;\n  display: block; /* 确保图片正确显示 */\n}\n\n.user-id {\n  font-size: 22rpx;\n  color: rgba(255,255,255,0.6);\n}\n\n.user-id-row {\n  align-items: center;\n  margin-top: 8rpx;\n}\n\n.sex-icon {\n  width: 32rpx;\n  height: 32rpx;\n  border-radius: 6rpx;\n  background: rgba(255,255,255,0.15);\n  align-items: center;\n  justify-content: center;\n  margin-right: 12rpx;\n}\n\n.sex-icon image {\n  width: 20rpx;\n  height: 20rpx;\n}\n\n.vip-icon {\n  border-radius: 6rpx;\n  padding: 2rpx;\n}\n\n.verified-icon {\n  border-radius: 6rpx;\n  padding: 2rpx;\n}\n\n.tag-wrapper {\n  position: relative;\n  width: 100%;\n  box-sizing: border-box;\n  display: flex;\n  align-items: center;\n}\n\n.tag-scroll-view {\n  width: calc(100% - 70rpx);\n  white-space: nowrap;\n}\n\n.tag-add-btn {\n  position: absolute;\n  right: 0rpx;\n  width: 46rpx;\n  height: 46rpx;\n  border-radius: 46rpx;\n  background: #f0f0f0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.tag-add-btn text {\n  font-size: 40rpx;\n  color: #666;\n  font-weight: 300;\n}\n\n.tag-add-empty {\n  width: 100%;\n  height: 60rpx;\n  background: #f8f8f8;\n  border-radius: 30rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.tag-add-empty text {\n  color: #999;\n  font-size: 26rpx;\n}\n\n.tag-empty {\n  flex: 1;\n  height: 60rpx;\n  background: rgba(255,255,255,.15);\n  border-radius: 30rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 20rpx;\n}\n\n.tag-empty text {\n  color: rgba(255,255,255,0.6);\n  font-size: 24rpx;\n}\n\n.user-tag {\n  flex-wrap: nowrap;\n  padding: 0;\n  margin-top: 0;\n}\n\n.tag-wrapper .user-tag {\n  display: inline-flex;\n  padding-right: 20rpx;\n}\n\n.tag-wrapper .tag-item {\n  margin-right: 20rpx;\n  flex-shrink: 0;\n}\n\n.menu-grid {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 20rpx 10rpx;\n  background-color: #fff;\n}\n\n.grid-item {\n  width: 33.33%;\n  text-align: center;\n  padding: 20rpx 0;\n  position: relative;\n}\n\n.grid-icon {\n  width: 50rpx;\n  height: 50rpx;\n}\n\n.grid-badge {\n  position: absolute;\n  top: -6rpx;\n  right: -6rpx;\n  min-width: 32rpx;\n  height: 32rpx;\n  padding: 0 6rpx;\n  background: #ff3a3a;\n  color: #fff;\n  border-radius: 16rpx;\n  font-size: 20rpx;\n  text-align: center;\n  line-height: 32rpx;\n  z-index: 2;\n  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);\n}\n\n.grid-text {\n  font-size: 24rpx;\n  color: #333;\n  display: block;\n  padding: 0 10rpx;\n}\n\n.profile-progress {\n  position: absolute;\n  top: -8rpx;\n  left: -8rpx;\n  right: -8rpx;\n  bottom: -8rpx;\n  z-index: 10;\n}\n\n.progress-circle {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.progress-inner {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: rgba(0,0,0,0.6);\n  border-radius: 20rpx;\n  padding: 4rpx 8rpx;\n  backdrop-filter: blur(10rpx);\n}\n\n.progress-text {\n  color: #fff;\n  font-size: 20rpx;\n  font-weight: 700;\n  line-height: 1;\n}\n\n.avatar {\n  position: relative;\n}\n\n.user-box .user-intro{\n  width:100%;\n  position: relative;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n}\n.user-box .user-intro .intro-text{\n  color:#ccc;\n  font-size:24rpx;\n  font-weight:400;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n  line-clamp: 2;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: normal;\n  word-break: break-all;\n  flex: 1;\n}\n.user-box .user-intro .more-btn{\n  position: absolute;\n  right: 0;\n  top: 0;\n  width: 50rpx;\n  height: 50rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.user-box .user-intro .more-btn image{\n  width: 32rpx;\n  height: 32rpx;\n}\n\n.user-box .user-top{\n  width:100%;\n  justify-content:space-between;\n  position: relative;\n  padding-right: 40rpx;\n}\n.user-box .user-top .right-arrow {\n  position: absolute;\n  right: 0;\n  top: 50%;\n  display: flex;\n  -webkit-transform: translateY(-50%) rotate(270deg);\n  transform: translateY(-50%) rotate(270deg)\n}\n.user-box .user-top .right-arrow image {\n  width: 140rpx;\n  height: 40rpx;\n}\n\n.stat-box {\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  width: calc(100% - 60rpx);\n  padding: 20rpx 0;\n  background-color: rgba(0, 0, 0, 0.6);\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);\n  margin: 20rpx auto;\n  position: relative;\n}\n\n.stat-divider {\n  width: 1px;\n  height: 60rpx;\n  background-color: rgba(255, 255, 255, 0.2);\n}\n\n.stat-item {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  flex: 1;\n  text-align: center;\n  padding: 10rpx 0;\n}\n\n.stat-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-bottom: 16rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.stat-icon .iconfont {\n  font-size: 36rpx;\n}\n\n.stat-value {\n  font-size: 36rpx;\n  font-weight: 700;\n  color: #ffffff;\n  margin-bottom: 8rpx;\n  line-height: 1;\n}\n\n.stat-label {\n  font-size: 22rpx;\n  color: rgba(255, 255, 255, 0.7);\n  line-height: 1;\n}\n\n.user-num-wrap {\n  width: 100%;\n}\n\n.stat-badge {\n  position: absolute;\n  top: -8rpx;\n  right: -10rpx;\n  min-width: 30rpx;\n  height: 30rpx;\n  line-height: 30rpx;\n  padding: 0 6rpx;\n  background-color: #ff3a3a;\n  color: #ffffff;\n  font-size: 16rpx;\n  border-radius: 15rpx;\n  text-align: center;\n}\n\n.stat-icon .icon-like {\n  color: #ff7ca8;\n  font-size: 42rpx;\n}\n\n.stat-icon .icon-eye {\n  color: #e3d6ff;\n  font-size: 42rpx;\n}\n\n.stat-icon .icon-heart {\n  color: #e3d6ff;\n  font-size: 42rpx;\n}\n\n.stat-like-text {\n  color: #ff7ca8;\n  font-size: 42rpx;\n}\n\n.stat-eye-text {\n  color: #e3d6ff;\n  font-size: 42rpx;\n}\n\n.stat-heart-text {\n  color: #e3d6ff;\n  font-size: 42rpx;\n}\n</style>", "import MiniProgramPage from 'Z:/WWW/shejiao/vue3/pages/index/center.vue'\nwx.createPage(MiniProgramPage)"], "names": ["mapGetters", "uni", "userManager", "<PERSON><PERSON><PERSON><PERSON>", "getUserSocialInfo", "getLikeDynamicList", "deleteDynamic", "res"], "mappings": ";;;;;;;;;;AAoRA,MAAA,YAAA,MAAA;AACA,MAAA,cAAA,MAAA;AACA,MAAA,SAAA,MAAA;AACA,MAAA,SAAA,MAAA;AACA,MAAA,SAAA,MAAA;AACA,MAAA,WAAA,MAAA;AAYA,MAAA,YAAA;AAAA;IAEI;AAAA;IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;EAEF,UAAA;AAAA,IACE,GAAAA,yBAAA;AAAA,MACE,SAAA;AAAA;;EAGJ,OAAA;AACE,WAAA;AAAA;AAAA;;;;;;MASE,cAAA,oBAAA,KAAA,GAAA,YAAA;AAAA;;MAGA,UAAA;AAAA,QACE,QAAA;AAAA,QACA,UAAA;AAAA,QACA,cAAA;AAAA;;QAGA,gBAAA;AAAA,QACA,eAAA;AAAA,QACA,eAAA;AAAA;QAEA,gBAAA;AAAA;;QAGA,gBAAA;AAAA;QAEA,eAAA,CAAA;AAAA;QAEA,cAAA;AAAA;QAEA,gBAAA;AAAA,QACA,cAAA;AAAA;;QAGA,EAAA,MAAA,MAAA,KAAA,IAAA,MAAA,sBAAA,KAAA,wBAAA,OAAA,EAAA;AAAA,QACA,EAAA,MAAA,OAAA,KAAA,IAAA,MAAA,uBAAA,KAAA,cAAA,OAAA,EAAA;AAAA,QACA,EAAA,MAAA,MAAA,KAAA,IAAA,MAAA,sBAAA,KAAA,eAAA,OAAA,EAAA;AAAA;MAEF,SAAA,CAAA,MAAA,IAAA;AAAA,MACA,QAAA;AAAA,MACA,MAAA,CAAA;AAAA,MACA,MAAA;AAAA;;MAGA,YAAA;AAAA,MACA,aAAA;AAAA,MACA,aAAA,CAAA;AAAA,MACA,WAAA;AAAA,MACA,cAAA;AAAA;;MAIA,iBAAA;AAAA,MACA,iBAAA;AAAA;AAAA;AAAA;;;;;;;QAOE,UAAA;AAAA;AAAA;;MAIF,cAAA;AAAA;AAAA,MAGA,cAAA;AAAA;AAAA;;;;QAKE,GAAA,EAAA,MAAA,CAAA,GAAA,WAAA,GAAA,YAAA,EAAA;AAAA;AAAA,QACA,GAAA,EAAA,MAAA,CAAA,GAAA,WAAA,GAAA,YAAA,EAAA;AAAA;AAAA;;;;QAMA,YAAA;AAAA,QACA,gBAAA;AAAA;;QAGA;AAAA,MACF;AAAA,IACF;AAAA;EAEF,oBAAA;;;AAII;AAAA,IACF;;;EAGF,SAAA;;;;;;;AAiBE,QAAA,CAAA,KAAA,iBAAA,IAAA,GAAA;AACE;AAAA,IACF;AAGA,SAAA,UAAA,MAAA;AACE,WAAA,aAAA;AAAA,IACF,CAAA;AAAA;;;;AAOA,SAAA,eAAA;AAAA;EAEF,SAAA;;;AAOE,QAAA,CAAA,KAAA,iBAAA,IAAA,GAAA;AACE;AAAA,IACF;AAGA,QAAA,KAAA,SAAA;;AAKE,WAAA,UAAA,MAAA;AACE,aAAA,SAAA;AAAA,MACF,CAAA;AAAA,IACF;AAAA;EAEF,SAAA;AAAA;AAAA,IAEE,kBAAA;AAEE,WAAA,UAAA,gBAAA;AACA,WAAA,SAAA,gBAAA;AAGA,WAAA,2BAAA;AAEAC,oBAAAA,MAAA,MAAA,OAAA,iCAAA,sBAAA;AAAA,QACE,QAAA,KAAA,UAAA;AAAA,QACA,MAAA,KAAA,UAAA;AAAA;;;;;;AAQF,UAAA;;;AAQE,YAAA,WAAA,SAAA,UAAA;AACE,eAAA,WAAA,EAAA,GAAA,KAAA,UAAA,GAAA,SAAA;;YAEE,KAAA,SAAA;AAAA;;QAGJ;AAAA;AAEAA,sBAAAA,MAAA,MAAA,SAAA,iCAAA,aAAA,KAAA;AAAA,MACF;AAAA;;IAIF,2BAAA,aAAA;AACE,UAAA;;;AAGI;AAAA,QACF;;;AAOE,gBAAA,EAAA,SAAA,IAAAC,kBAAA,YAAA,YAAA;AACA,eAAA,WAAA,EAAA,GAAA,KAAA,UAAA,GAAA;QACF;AAAA;AAGAD,sBAAA,MAAA,MAAA,SAAA,iCAAA,eAAA,KAAA;AAAA,MACF;AAAA;;;;;AAOE,cAAA,UAAA,KAAA,WAAA,IAAA,UAAA;AAGA,aAAA,YAAA,gBAAA,WAAA,SAAA,KAAA,MAAA,KAAA,UAAA;AAEAA,sBAAAA,MAAA,MAAA,OAAA,iCAAA,kBAAA;AAAA;UAEE,KAAA,KAAA,KAAA;AAAA,UACA,IAAA,KAAA;AAAA;MAEJ;AAAA;;;;AAMA,YAAA,UAAA,KAAA,WAAA,IAAA,UAAA;;;;AAOE,aAAA,aAAA,MAAA;AACA,aAAA,UAAA,KAAA,KAAA,WAAA;AACA,aAAA,aAAA,KAAA,KAAA,UAAA,KAAA,aAAA,WAAA;AAEAA,sBAAAA,MAAA,MAAA,OAAA,iCAAA,iBAAA;AAAA;UAEE,KAAA,KAAA,KAAA;AAAA,UACA,MAAA,IAAA,KAAA,MAAA,SAAA,EAAA,eAAA;AAAA;AAGF,eAAA;AAAA,MACF;;;;IAMF,iBAAA,kBAAA,OAAA;;AAUE,aAAA;AAAA;;IAIF,iBAAA;AACE,UAAA,KAAA,cAAA;AACE,qBAAA,KAAA,YAAA;;MAEF;AAAA;;IAIF,kBAAA;AACE,UAAA,KAAA,cAAA;AACE,qBAAA,KAAA,YAAA;AAAA,MACF;AAEA,WAAA,eAAA,WAAA,MAAA;AACE,aAAA,YAAA;AAAA,MACF,GAAA,GAAA;AAAA;;IAIF,sBAAA;;AAEI;AAAA,MACF;AAEA,YAAA,MAAA,KAAA;;AAIE,aAAA,YAAA;AAAA,MACF;AAAA;IAGF,MAAA,eAAA;AACE,UAAA;AAEE,aAAA,eAAA,EAAA,MAAA,SAAA;AACEA,wBAAA,MAAA,MAAA,OAAA,iCAAA,cAAA,GAAA;AAAA,QACF,CAAA;AAEA,YAAA,CAAA,KAAA,SAAA;AACE,eAAA,UAAA;;AAEA;AAAA,QACF;;;;QAMA,CAAA;AAAA;AAGAA,sBAAA,MAAA,MAAA,SAAA,iCAAA,YAAA,KAAA;AACA,aAAA,eAAA,cAAA;AAAA,MACF,UAAA;AAEE,aAAA,UAAA,MAAA;AACE,eAAA,SAAA;AAAA,QACF,CAAA;AAAA,MACF;AAAA;;;AAKAA,oBAAA,MAAA,MAAA,SAAA,iCAAA,aAAA,KAAA;AAEA,UAAA,CAAA,KAAA,SAAA,YAAA,KAAA,SAAA,aAAA,WAAA;;AAEE,aAAA,SAAA,SAAA;AAAA,MACF;AAAA;;AAIAA,oBAAA,MAAA,MAAA,SAAA,iCAAA,aAAA,KAAA;;AAGE,aAAA,UAAA;;MAEF;AAAA;IAGF,eAAA,SAAA,WAAA,KAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA;;QAGE;AAAA,MACF,CAAA;AAAA;IAGF,oBAAA;AACE,UAAA;AAEE,aAAA,2BAAA;;;;;AAOI,iBAAA,UAAA;AAAA,UACF,WAAA,OAAA,mBAAA,UAAA;AACE,gBAAA;AACE,oBAAA,aAAA,KAAA,MAAA,cAAA;;AAEA,mBAAA,UAAA;AAAA,YACF,SAAA,GAAA;AACEA,4BAAA,MAAA,MAAA,SAAA,iCAAA,eAAA,CAAA;AAAA,YACF;AAAA,UACF;AAAA;AAEA,eAAA,UAAA;AAAA,QACF;AAAA,MACF,SAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,SAAA,iCAAA,eAAA,CAAA;AAAA,MACF;AAAA;;;AAKA,YAAA,MAAA,KAAA;AACA,aAAA,KAAA,KAAA,YAAA,EAAA,QAAA,SAAA;AACE,cAAA,QAAA,KAAA,aAAA,GAAA;;;QAGA;AAAA,MACF,CAAA;AAAA;IAGF,MAAA,YAAA,aAAA,OAAA;AAEE,UAAA,KAAA,aAAA,aAAA;AACE;AAAA,MACF;AAGA,WAAA,aAAA,cAAA;AAEA,UAAA;;;AAMIA,wBAAA,MAAA,MAAA,SAAA,iCAAA,aAAA,GAAA;AAAA,QACF,CAAA;AAEA,cAAA,KAAA,YAAA,MAAA,KAAA,EAAA,MAAA,SAAA;AACEA,wBAAA,MAAA,MAAA,SAAA,iCAAA,aAAA,GAAA;AAAA,QACF,CAAA;AAGA,aAAA,kBAAA,KAAA;MAEF,UAAA;AACE,aAAA,aAAA,cAAA;AACA,YAAA,YAAA;;QAEA;AAAA,MACF;AAAA;IAGF,wBAAA,YAAA;AACE,UAAA,YAAA;AAEE,aAAA,kBAAA,IAAA,EAAA,MAAA,SAAA;AACEA,wBAAA,MAAA,MAAA,OAAA,iCAAA,oBAAA,GAAA;AAAA,QACF,CAAA;AACA,aAAA,YAAA,OAAA,IAAA,EAAA,MAAA,SAAA;;AAEIA,0BAAA,MAAA,MAAA,OAAA,iCAAA,oBAAA,GAAA;AAAA,UACF;AAAA,QACF,CAAA;AAAA;AAGA,aAAA,cAAA;AAAA,MACF;AAAA;IAGF,gBAAA;AAEEC,wBAAA,YAAA,OAAA;AAGA,WAAA,WAAA;AAAA;QAEE,QAAA;AAAA,QACA,UAAA;AAAA;QAEA,cAAA;AAAA;;QAGA,gBAAA;AAAA,QACA,eAAA;AAAA,QACA,eAAA;AAAA;;AAKF,WAAA,UAAA;;;AAKA,aAAA,KAAA,KAAA,YAAA,EAAA,QAAA,SAAA;;MAEA,CAAA;;;;IAQF,eAAA,KAAA;AACE,UAAA,QAAA,IAAA,eAAA,OAAA,IAAA,SAAA,OAAA,IAAA,WAAA,MAAA;AACED,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA;AAAA;;QAGF,CAAA;AACA,aAAA,cAAA;AACA,mBAAA,MAAAE,WAAAA,WAAA,IAAA;;MAEF;;;;;AAMA,UAAA,KAAA,aAAA,YAAA,CAAA,KAAA,SAAA;;AAEE,YAAA,CAAA,UAAA,KAAA,aAAA,UAAA;AACE,eAAA,eAAA,aAAA,IAAA;AAAA,QACF;AACA,eAAA,QAAA,OAAA,IAAA,MAAA,QAAA,CAAA;AAAA,MACF;AAGA,WAAA,aAAA,WAAA;;AAIEF,sBAAAA,MAAA,YAAA;AAAA,UACE,OAAA;AAAA,UACA,MAAA;AAAA,QACF,CAAA;AAAA,MACF;AAEA,UAAA;AACE,cAAA,MAAA,MAAAG,WAAAA;AAGA,aAAA,kBAAA,KAAA;AAEA,YAAA,IAAA,WAAA,OAAA,IAAA,SAAA,KAAA;;AAIE,cAAA,WAAA,eAAA,QAAA;uEAEI,WAAA,aAAA,KAAA,QAAA,CAAA,IAAA,MACA,WAAA,WAAA;UACJ;;;UAIA;AAGA,eAAA,2BAAA,UAAA;AACA,eAAA,UAAA;AAEA,cAAA,WAAA,gBAAA,QAAA;AACE,iBAAA,aAAA,WAAA;AAAA,UACF;AAGA,eAAA,YAAA,eAAA,WAAA;AAEA,iBAAA;AAAA;AAEA,gBAAA,IAAA,MAAA,IAAA,OAAA,UAAA;AAAA,QACF;AAAA;;;QAMA;;;;;;;QAWA;;AAIE,eAAA,WAAA,EAAA,GAAA,KAAA,UAAA,GAAA,KAAA,OAAA,MAAA,IAAA,SAAA;AACA,eAAA,UAAA;AAAA,QACF;AAEA,cAAA;AAAA,MAEF,UAAA;AACE,aAAA,aAAA,WAAA;;AAEEH,wBAAA,MAAA,YAAA;AAAA,QACF;AAAA,MACF;AAAA;;AAIA,aAAA,KAAA,kBAAA,MAAA;AAAA;;;AAME,aAAA,UAAA;;;AAGA,eAAA,QAAA,OAAA,IAAA,MAAA,OAAA,CAAA;AAAA,MACF;AAGA,UAAA,KAAA,aAAA,aAAA;AACE,cAAA,WAAA;;AAEE,eAAA,eAAA,aAAA,IAAA;AAAA,QACF;AACA,eAAA,QAAA,OAAA,IAAA,MAAA,QAAA,CAAA;AAAA,MACF;AAGA,WAAA,aAAA,cAAA;;;MAKA;;;AAKA,UAAA;AACE,cAAA,cAAA,KAAA,OAAA,MAAA,IAAA,OAAA;;AAIA,YAAA,KAAA,UAAA,GAAA;;YAEI,MAAA,KAAA;AAAA,YACA,OAAA;AAAA,UACF,CAAA;AAAA;AAEA,oBAAAI,WAAA,mBAAA,aAAA;AAAA,YACE,MAAA,KAAA;AAAA,YACA,OAAA;AAAA,UACF,CAAA;AAAA;AAEAJ,wBAAA,MAAA,MAAA,QAAA,iCAAA,cAAA,KAAA,MAAA;AACA;AAAA,QACF;AAEA,cAAA,MAAA,MAAA;;;QAKA;;AAGE,cAAA,IAAA,KAAA,QAAA,IAAA,KAAA,KAAA,SAAA,GAAA;AAEE,gBAAA,KAAA,QAAA,GAAA;;;AAIE,mBAAA,KAAA,KAAA,GAAA,IAAA,KAAA,IAAA;AAAA,YACF;AAEA,gBAAA,IAAA,KAAA,UAAA,QAAA;AACE,mBAAA,aAAA,IAAA,KAAA;AAAA,YACF;AACA,iBAAA,UAAA;AAAA,UACF,WAAA,KAAA,QAAA,GAAA;AACE,iBAAA,UAAA;;UAEF;;;AAME,iBAAA,aAAA,KAAA,MAAA,IAAA;AAAA,cACE,MAAA,CAAA,GAAA,KAAA,IAAA;AAAA,cACA,WAAA,KAAA,IAAA;AAAA;;UAGJ;AAGA,eAAA,YAAA,eAAA,cAAA;AAEA,iBAAA,IAAA;AAAA;AAEA,gBAAA,IAAA,MAAA,IAAA,OAAA,QAAA;AAAA,QACF;AAAA;;;QAMA;AAGA,YAAA,QAAA,IAAA,eAAA,OAAA,IAAA,SAAA,OAAA,IAAA,WAAA,MAAA;AACE,eAAA,eAAA,EAAA,QAAA,IAAA,CAAA;AACA,gBAAA;AAAA,QACF;;;;;;;QAWA;;AAIE,eAAA,UAAA;;QAEF;;AAGA,cAAA;AAAA,MAEF,UAAA;AACE,aAAA,aAAA,cAAA;AAAA,MACF;AAAA;IAGF,mBAAA;AAEE,UAAA;;AAEI,eAAA,OAAA,OAAA,mBAAA,IAAA;AAAA,QACF;AAAA;AAIAA,sBAAA,MAAA,MAAA,QAAA,kCAAA,kBAAA,MAAA,OAAA;AAAA,MACF;AAAA;;;;;AAQA,UAAA,cAAA,KAAA;AAAA;;AAGA,WAAA,SAAA;;AAIA,YAAA,YAAA,KAAA,aAAA,SAAA;AACA,YAAA,MAAA,KAAA;gEAEqB,MAAA,UAAA,YAAA,KAAA;AAGrB,UAAA,eAAA;;AAGE,aAAA,aAAA,UAAA;AACA,aAAA,UAAA,KAAA,KAAA,WAAA;AACA,aAAA,aAAA,KAAA,KAAA,UAAA,KAAA,aAAA,WAAA;AAEA,mBAAA,MAAA;;QAEA,GAAA,GAAA;AAAA;;AAIA,aAAA,cAAA,QAAA,MAAA;AACE,qBAAA,MAAA;;UAEA,GAAA,GAAA;AAAA,QACF,CAAA;AAAA,MACF;AAAA;;AAIAA,oBAAAA,MAAA,UAAA;AAAA,QACE,SAAA;AAAA;QAEA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AACEK,qCAAA,KAAA,KAAA,EAAA,GAAA,EAAA,EAAA,EAAA,KAAA,CAAAC,SAAA;AACE,kBAAAA,KAAA,UAAA,KAAA;;;AAGI,uBAAA,UAAA;AAAA,gBACF;AACAN,8BAAA,MAAA,UAAA,EAAA,OAAA,QAAA,MAAA,UAAA,CAAA;AAAA;;cAGF;AAAA;AAEAA,4BAAA,MAAA,UAAA,EAAA,OAAA,YAAA,MAAA,OAAA,CAAA;AAAA,YACF,CAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;IAGF,eAAA,MAAA;AACE,UAAA,MAAA;;;;MAIA;AAAA;IAGF,cAAA,GAAA;AAEE,UAAA,CAAA,KAAA,iBAAA,IAAA,GAAA;AACE;AAAA,MACF;AAEA,UAAA,KAAA,aAAA;;MAEA;AAEA,YAAA,MAAA,EAAA,cAAA,QAAA;;AAEE,aAAA,eAAA;AAAA,MACF;AAEAA,oBAAA,MAAA,WAAA,EAAA,KAAA,YAAA,IAAA,CAAA;AAAA;IAGF,aAAA,MAAA;AAEE,UAAA,CAAA,KAAA,iBAAA,IAAA,GAAA;AACE;AAAA,MACF;AAEAA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA,6BAAA,IAAA,OAAA,KAAA,SAAA,GAAA,SAAA,KAAA,SAAA,QAAA;AAAA,MACF,CAAA;AAAA;;;;;;;;;;;QAcE,YAAA,SAAA,YAAA;AAAA;QAEA,WAAA,EAAA,UAAA,KAAA,YAAA,SAAA;AAAA,MACF,CAAA;AAAA;IAGF,iBAAA;;AAGE,YAAA,MAAA,KAAA;AAEA,UAAA,cAAA,WAAA,aAAA,MAAA,WAAA,YAAA,KAAA,cAAA;AAEE,aAAA,cAAA,WAAA,QAAA,CAAA;AACA,eAAA,QAAA,QAAA,KAAA,WAAA;AAAA,MACF;AAEA,aAAA,IAAA,QAAA,CAAA,SAAA,WAAA;;AAGE,cAAA,IAAA,QAAA,IAAA,KAAA,kBAAA;AACE,gBAAA,YAAA,CAAA;AACA,gBAAA,eAAA,CAAA;AAEA,gBAAA,KAAA,iBAAA,QAAA,CAAA,OAAA;AACE,oBAAA,WAAA;AAAA;;gBAGI,KAAA,GAAA,IAAA,QAAA,WAAA,EAAA;AAAA,gBACA,OAAA;AAAA;AAGJ,kBAAA,GAAA,OAAA,mFAEI,GAAA,QAAA,QAAA;;;;cAIJ;AAAA,YACF,CAAA;;AAKEA,0BAAA,MAAA,eAAA,gBAAA;AAAA,cACE,MAAA,KAAA;AAAA;YAEF,CAAA;;;;UAKJ;AAAA,QACA,CAAA,EAAA,MAAA,SAAA;;QAEA,CAAA;AAAA,MACF,CAAA;AAAA;IAGF,gBAAA;AACE,WAAA,cAAA,CAAA,KAAA;;;IAQF,iBAAA;;;;;AAOI,YAAA,IAAA,WAAA,OAAA,IAAA,SAAA,KAAA;;AAEE,eAAA,SAAA,gBAAA,QAAA,SAAA;;;;AAOAA,wBAAA,MAAA,MAAA,qBAAA;AAAA;;YAGE,UAAA;AAAA,YACA,MAAA,QAAA,QAAA;AAAA;UAEF,CAAA;AAAA;;;;AAKAA,wBAAAA,MAAA,MAAA,qBAAA,SAAA;AAAA,QACF;AAAA,MACF,CAAA,EAAA,MAAA,CAAA,UAAA;AACEA,sBAAA,MAAA,MAAA,SAAA,kCAAA,aAAA,KAAA;;;;AAIAA,sBAAAA,MAAA,MAAA,qBAAA,EAAA,UAAA,CAAA,GAAA,OAAA,GAAA,UAAA,MAAA,CAAA;AAAA,MACF,CAAA;AAAA;;;;;IAOF,WAAA,WAAA;;;AAEE,YAAA,OAAA,IAAA,KAAA,YAAA,GAAA;;;;AAMA,UAAA,CAAA,KAAA,iBAAA,IAAA,GAAA;AACE;AAAA,MACF;;;;AAMA,WAAA,2BAAA;AAAA;IAGF,gBAAA,MAAA;AAEE,UAAA,CAAA,KAAA,iBAAA,IAAA,GAAA;AACE;AAAA,MACF;;;;;YAQM,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,QAAA;AACEA,8BAAA,MAAA,UAAA,EAAA,OAAA,QAAA,MAAA,UAAA,CAAA;AAAA,cACF;AAAA;YAEF,MAAA,MAAA;AACEA,4BAAA,MAAA,UAAA,EAAA,OAAA,QAAA,MAAA,OAAA,CAAA;AAAA,YACF;AAAA,UACF,CAAA;;;;;;AAaAA,wBAAAA,MAAA,WAAA,EAAA,KAAA,uBAAA,CAAA;;MAEJ;AAAA;;IAIF,gBAAA;AACE,YAAA,SAAA,CAAA,UAAA,SAAA;AACA,aAAA,OAAA,KAAA,MAAA,KAAA;AAAA;IAGF,mBAAA;AACE,UAAA,KAAA,WAAA,GAAA;AACE,eAAA;AAAA,MACF;AACA,aAAA;AAAA;;AAIAE,iBAAAA;IACF;AAAA;EAEF,gBAAA;AAEE,QAAA,KAAA,aAAA,eAAA,KAAA,aAAA,YAAA,KAAA,eAAA,UAAA;AACE;AAAA,IACF;;AAIE,WAAA,aAAA,WAAA;;;;AAKA,WAAA,YAAA,OAAA,KAAA,EAAA,MAAA,SAAA;AACEF,sBAAA,MAAA,MAAA,OAAA,kCAAA,aAAA,GAAA;AACA,aAAA,OAAA;;;AAIE,eAAA,eAAA,gBAAA,GAAA;AAAA,QACF;AAAA,MACF,CAAA,EAAA,QAAA,MAAA;AACE,aAAA,aAAA,WAAA;AAAA,MACF,CAAA;AAAA,IACF,WAAA,KAAA,KAAA,UAAA,KAAA,cAAA,KAAA,KAAA,SAAA,GAAA;;IAEA;AAAA;EAEF,aAAA,GAAA;AACE,QAAA,KAAA,aAAA;AACE;AAAA,IACF;;;;;;AAQE,WAAA,cAAA;AAAA,IACF;;;;;IAMA;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC12CA,GAAG,WAAW,eAAe;"}