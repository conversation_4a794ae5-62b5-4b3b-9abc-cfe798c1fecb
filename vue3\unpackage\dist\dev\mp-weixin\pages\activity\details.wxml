<view class="container"><view class="nav-box df" style="{{'padding-top:' + g + ';' + ('background:' + h)}}"><view class="nav-back df" style="{{'height:' + c}}" bindtap="{{d}}"><image class="{{a}}" src="{{b}}"></image></view><view wx:if="{{e}}" class="nav-title ohto">{{f}}</view></view><swiper class="swiper-box" circular bindchange="{{j}}"><swiper-item wx:for="{{i}}" wx:for-item="item" wx:key="c" class="swiper-item" bindtap="{{item.d}}" data-i="{{item.e}}"><lazy-image wx:if="{{item.b}}" u-i="{{item.a}}" bind:__l="__l" u-p="{{item.b}}"></lazy-image></swiper-item></swiper><view class="indicator df"><block wx:if="{{k}}"><view wx:for="{{l}}" wx:for-item="item" wx:key="a" class="{{['indicator-item', item.b]}}"></view></block></view><view class="content-box"><view class="content-bar df" style="{{'top:' + v}}"><view class="df" style="height:100%;margin-left:15rpx"><view wx:for="{{m}}" wx:for-item="item" wx:key="e" class="bar-nav df" bindtap="{{item.f}}" data-idx="{{item.g}}"><text style="{{'color:' + item.b + ';' + ('font-size:' + item.c)}}">{{item.a}}</text><view style="{{'opacity:' + item.d}}" class="line"></view></view></view><view class="df" style="margin-right:30rpx"><view class="{{['bar-btn', 'df', o, p]}}"><text style="margin-left:0">{{n}}</text></view><view class="bar-btn df" bindtap="{{r}}" style="margin:0 15rpx"><image class="avatar" src="{{q}}" mode="aspectFill"></image><text>＋笔记</text></view><view class="bar-btn df" bindtap="{{t}}"><image class="icon" src="{{s}}"></image><text>分享</text></view></view></view><view wx:if="{{w}}" class="content-item"><view wx:if="{{x}}" class="joins-box df" bindtap="{{A}}" data-url="activity/index?type=1"><view class="txt df"><image class="avatar" src="{{y}}" mode="aspectFill"></image><text>您已参加该活动，前往查看门票</text></view><view class="arr effect df"><image src="{{z}}"></image></view></view><view class="info-map"><image class="bg" src="{{B}}" mode="aspectFill" style="z-index:-2"></image><view class="mk"></view><view class="info-item" bindtap="{{J}}"><view class="info-item-tit"> 地点：<text user-select="true">{{C}}</text></view><view class="info-item-tit"> 时间：<text user-select="true">{{D}}</text></view><view class="df" style="justify-content:space-between"><view wx:if="{{E}}" class="cu-img-group"><view wx:for="{{F}}" wx:for-item="img" wx:key="b" class="img-group"><image src="{{img.a}}" mode="aspectFill"></image></view><view class="img-tit">{{G}}人已参加</view></view><view wx:else class="info-item-tit"><text>{{H}}人想参加</text></view><view class="adds-box df"><image src="{{I}}"></image></view></view></view></view><view class="info-title">{{K}}</view><view class="info-intro"><rich-text nodes="{{L}}" bindtap="{{M}}"></rich-text></view></view><view wx:else class="{{T}}"><view wx:if="{{N}}" class="empty-box df"><image src="{{O}}"/><view class="e1">暂无相关笔记</view><view class="e2">空空如也，等待探索</view></view><waterfall wx:if="{{P}}" u-i="930f9a38-1" bind:__l="__l" u-p="{{Q}}"></waterfall><block wx:else><block wx:for="{{R}}" wx:for-item="item" wx:key="d"><card-gg wx:if="{{item.c}}" bindlikeback="{{item.a}}" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"></card-gg></block></block><uni-load-more wx:if="{{S}}" u-i="930f9a38-3" bind:__l="__l" u-p="{{S}}"></uni-load-more></view></view><view class="footer-box bfw bUp"><view class="btn-box df"><view wx:if="{{U}}" class="btn-item df" bindtap="{{W}}" data-url="center/means" style="width:calc(100% - 120rpx);justify-content:center"><text>完善账号资料后即可参加活动，去完善</text><view class="arr effect df" style="margin-left:20rpx"><image src="{{V}}"></image></view></view><block wx:else><view class="btn-price"><view class="df" style="height:48rpx"><view class="nian">优惠价</view><money wx:if="{{X}}" u-i="930f9a38-4" bind:__l="__l" u-p="{{X}}"></money></view><view class="through">原价：¥{{Y}}</view></view><view class="btn-item df" bindtap="{{aa}}"><image class="icon" src="{{Z}}"></image><text>立即参加</text></view></block></view></view><uni-popup wx:if="{{ar}}" class="r" u-s="{{['d']}}" u-r="registerPopup" u-i="930f9a38-5" bind:__l="__l" u-p="{{ar}}"><view class="popup-box"><view class="popup-top df"><view class="popup-title"><view class="t1">参加活动</view><view class="t2">购买<text>"</text>{{ab}}<text>"</text>的门票</view></view><view class="popup-close df" bindtap="{{ad}}"><image src="{{ac}}" style="width:20rpx;height:20rpx"/></view></view><scroll-view scroll-x="true" class="scroll-box"><view class="product-box"><view wx:for="{{ae}}" wx:for-item="item" wx:key="f" class="{{['product-item', item.g]}}" bindtap="{{item.h}}"><view class="tag">剩余{{item.a}}张</view><view class="name">{{item.b}}</view><view class="time">报名截止时间：{{af}}</view><view style="display:flex;align-items:flex-end"><money wx:if="{{item.d}}" u-i="{{item.c}}" bind:__l="__l" u-p="{{item.d}}"></money><view class="td-lt">¥{{item.e}}</view></view></view></view></scroll-view><view class="quantity-box df"><view class="quantity-tit">门票数（人）</view><view class="quantity-item df"><view class="quantity-btn" style="{{'color:' + ag}}" bindtap="{{ah}}">－ </view><input bindblur="{{ai}}" type="number" maxlength="2" value="{{aj}}" bindinput="{{ak}}"/><view class="quantity-btn" style="{{'color:' + al}}" bindtap="{{am}}">＋ </view></view></view><view class="quantity-box df"><view class="quantity-tit">支付金额</view><money wx:if="{{an}}" u-i="930f9a38-7,930f9a38-5" bind:__l="__l" u-p="{{an}}"></money></view><button class="popup-btn df" bindtap="{{ap}}"><image src="{{ao}}"></image><text>确认支付</text></button></view></uni-popup><uni-popup u-s="{{['d']}}" u-r="sharePopup" class="r r" u-i="930f9a38-8" bind:__l="__l"><view class="share-popup"><image class="share-img" src="{{as}}"></image><view class="share-tips"><text>点击页面右上角</text><image src="{{at}}"></image><text style="margin-left:78rpx">即可转发给朋友、分享到朋友圈、复制链接分享等操作。</text></view><view class="share-btn" bindtap="{{av}}">确认</view></view></uni-popup><uni-popup wx:if="{{aE}}" class="r" u-s="{{['d']}}" u-r="notePopup" u-i="930f9a38-9" bind:__l="__l" u-p="{{aE}}"><view class="note-box"><view class="note-add df" bindtap="{{ay}}"><image src="{{ax}}"></image><text>图文笔记</text></view><view class="note-add df" bindtap="{{aA}}"><image src="{{az}}"></image><text>视频笔记</text></view><view class="note-add df" bindtap="{{aC}}"><image src="{{aB}}"></image><text>音文笔记</text></view></view></uni-popup><uni-popup wx:if="{{aH}}" class="r" u-s="{{['d']}}" u-r="tipsPopup" u-i="930f9a38-10" bind:__l="__l" u-p="{{aH}}"><view class="tips-box df"><view class="tips-item">{{aF}}</view></view></uni-popup></view>