
.uni-popup[data-v-4dd3c44b] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-4dd3c44b],
.uni-popup.left[data-v-4dd3c44b],
.uni-popup.right[data-v-4dd3c44b] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-4dd3c44b] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-4dd3c44b],
.uni-popup .uni-popup__wrapper.right[data-v-4dd3c44b] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-4dd3c44b] {

  z-index: 999;
}
.fixforpc-top[data-v-4dd3c44b] {
  top: 0;
}


/* 基础样式 - 参考 details.vue 的 empty-box 样式 */
.empty-page[data-v-5cea664a] {
  width: 100%;
  padding: 3.125rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.empty-content[data-v-5cea664a] {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
}

/* 图片样式 - 参考 details.vue */
.empty-image[data-v-5cea664a] {
  width: 9.375rem;
  height: 9.375rem;
  margin-bottom: 0.9375rem;
  opacity: 0.8;
}

/* 标题样式 - 参考 details.vue 的 .e1 */
.empty-title[data-v-5cea664a] {
  font-size: 0.9375rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.3125rem;
  line-height: 1.4;
}

/* 描述样式 - 参考 details.vue 的 .e2 */
.empty-description[data-v-5cea664a] {
  margin-top: 0.3125rem;
  color: #999;
  font-size: 0.8125rem;
  line-height: 1.5;
  margin-bottom: 1.25rem;
  max-width: 15.625rem;
  text-align: center;
}

/* 按钮样式 - 参考 details.vue 的 retry-btn */
.empty-button[data-v-5cea664a] {
  margin-top: 1.25rem;
  width: 6.25rem;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 700;
  color: #fff;
  background: #007aff;
  border-radius: 1.25rem;
  transition: all 0.3s ease;
}
.empty-button[data-v-5cea664a]:active {
  background: #0056cc;
  transform: scale(0.95);
}

/* 工具类 */
.df[data-v-5cea664a] {
  display: flex;
  align-items: center;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
.empty-title[data-v-5cea664a] {
    color: #fff;
}
.empty-description[data-v-5cea664a] {
    color: #ccc;
}
.empty-page[data-v-5cea664a] {
    background-color: #1a1a1a;
}
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
.empty-image[data-v-5cea664a] {
    width: 7.8125rem;
    height: 7.8125rem;
}
.empty-title[data-v-5cea664a] {
    font-size: 0.875rem;
}
.empty-description[data-v-5cea664a] {
    font-size: 0.75rem;
    padding: 0 1.25rem;
}
}

/* 小程序兼容性 */






/* H5 兼容性 */








.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
}
.bar-box .bar-back {
  padding: 0 0.9375rem;
  width: 1.0625rem;
  height: 100%;
}
.bar-box .bar-title {
  max-width: 60%;
  font-size: 1rem;
  font-weight: 700;
  flex: 1;
  text-align: center;
}
.nav-box {
  width: 100%;
  height: 2.5rem;
}
.nav-box .nav-item {
  padding: 0 0.9375rem;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  position: relative;
}
.nav-box .nav-item uni-text {
  font-weight: 700;
  transition: all .3s ease-in-out;
}
.nav-box .nav-line {
  position: absolute;
  bottom: 0.375rem;
  width: 0.5625rem;
  height: 0.1875rem;
  border-radius: 0.1875rem;
  background: #000;
  transition: opacity .3s ease-in-out;
}
.content-box {
  width: calc(100% - 1.875rem);
  padding: 0.9375rem;
}
.notification-btn {
  width: 1.875rem;
  height: 1.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.notification-icon {
  font-size: 1rem;
}

/* 加载中状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 1.875rem;
  margin-bottom: 0.625rem;
}
.loading-indicator {
  width: 0.9375rem;
  height: 0.9375rem;
  border: 0.09375rem solid #f3f3f3;
  border-top: 0.09375rem solid #000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.paper-list {
  width: 100%;
}

/* 纸条项 */
.paper-item {
  background: #fff;
  border-radius: 0.75rem;
  padding: 1rem;
  margin-bottom: 0.75rem;
  box-shadow: 0 0.0625rem 0.375rem rgba(0, 0, 0, 0.05);
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}
.avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  overflow: hidden;
  background: #f0f0f0;
}
.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.user-details {
  flex: 1;
}
.username {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.25rem;
}
.user-meta {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}
.gender-icon {
  color: #666;
  font-size: 0.75rem;
}
.age {
  color: #666;
  font-size: 0.75rem;
}
.type-tag {
  padding: 0.375rem 0.625rem;
  border-radius: 0.625rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}
.type-1 { background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
}
.type-2 { background: linear-gradient(135deg, #EC4899 0%, #BE185D 100%);
}
.type-3 { background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
}
.type-4 { background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
}
.type-icon,
.type-text {
  color: #fff;
  font-size: 0.75rem;
  font-weight: 500;
}

/* 纸条内容 */
.paper-content {
  margin-bottom: 0.75rem;
}
.content-text {
  color: #333;
  font-size: 0.875rem;
  line-height: 1.6;
}

/* 状态信息 */
.paper-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}
.status-text {
  color: #999;
  font-size: 0.75rem;
}
.time-text {
  color: #999;
  font-size: 0.75rem;
}

/* 回应信息 */
.response-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.625rem 0;
  border-top: 0.03125rem solid #f0f0f0;
  margin-bottom: 0.75rem;
}
.response-text {
  color: #333;
  font-size: 0.875rem;
}
.arrow-icon {
  color: #999;
  font-size: 1rem;
}

/* 回应按钮 */
.reply-btn {
  width: 100%;
  height: 2.25rem;
  background: #f8f9fa;
  border-radius: 1.125rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.reply-btn:active {
  background: #e9ecef;
  transform: scale(0.98);
}
.reply-text {
  color: #666;
  font-size: 0.875rem;
}

/* 加载状态 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3.75rem 1.25rem;
  color: #666;
  font-size: 0.875rem;
}

/* 空状态 */
.empty-box {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.125rem 0;
}
.empty-box uni-image {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.9375rem;
}
.empty-box .e1 {
  font-size: 0.875rem;
  font-weight: bold;
  margin-bottom: 0.3125rem;
}
.empty-box .e2 {
  font-size: 0.75rem;
  color: #999;
  margin-bottom: 0.9375rem;
}
.empty-btn {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  border-radius: 1.5rem;
  box-shadow: 0 0.125rem 0.5rem rgba(139, 92, 246, 0.3);
}
.empty-btn-text {
  color: #fff;
  font-size: 0.875rem;
  font-weight: 500;
}
.df {
  display: flex;
  align-items: center;
}
.bfw {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255,255,255,.8);
}
.bfh {
  background: #000;
  color: #fff;
  padding: 0.625rem 1.25rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 700;
}
.tips-box {
  justify-content: center;
  width: 100%;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

