<view class="container"><navbar u-i="ff186348-0" bind:__l="__l"></navbar><view class="content-box" style="{{'margin-top:' + g}}"><empty-page wx:if="{{a}}" bindbuttonClick="{{b}}" u-i="ff186348-1" bind:__l="__l" u-p="{{c}}"/><block wx:else><view wx:for="{{d}}" wx:for-item="item" wx:key="l" class="list" data-id="{{item.m}}" bindtap="{{item.n}}"><view class="list-item"><view class="name">{{item.a}}</view><view class="mobile">{{item.b}}</view><view class="adds">{{item.c}} {{item.d}} {{item.e}}</view><view class="adds">{{item.f}}</view><view class="df" style="margin-top:30rpx" data-idx="{{item.h}}" catchtap="{{item.i}}"><image src="{{item.g}}" style="width:28rpx;height:28rpx"></image><text style="margin-left:12rpx;color:#000;font-size:24rpx">设为默认</text></view></view><image class="list-editor" src="{{e}}" data-idx="{{item.j}}" catchtap="{{item.k}}"></image></view></block><uni-load-more wx:if="{{f}}" u-i="ff186348-2" bind:__l="__l" u-p="{{f}}"></uni-load-more></view><view class="btn-box"><view bindtap="{{h}}" class="bg1">新建地址</view><view bindtap="{{i}}" class="bg2">使用微信地址</view></view><uni-popup wx:if="{{H}}" u-s="{{['d']}}" u-r="addsPopup" class="r r" u-i="ff186348-3" bind:__l="__l" u-p="{{H}}"><view class="popup-box"><view class="popup-top df"><view class="popup-title"><view class="t1">{{j}}</view><view class="t2">请仔细核对地址信息后保存</view></view><view class="popup-close df" bindtap="{{l}}"><image src="{{k}}" style="width:20rpx;height:20rpx"></image></view></view><view class="popup-adds"><view class="adds-tit">收货人</view><input class="adds-item" cursor-spacing="20" type="text" placeholder="点击输入名字" placeholder-class="apc" value="{{m}}" bindinput="{{n}}"/></view><view class="popup-adds"><view class="adds-tit">手机号码</view><input class="adds-item" cursor-spacing="20" maxlength="11" type="number" placeholder="点击输入手机号" placeholder-class="apc" value="{{o}}" bindinput="{{p}}"/></view><view class="popup-adds"><view class="adds-tit">地区</view><picker bindchange="{{v}}" value="{{w}}" mode="multiSelector" bindcolumnchange="{{x}}" range="{{y}}"><view class="adds-item ohto"><text wx:if="{{q}}">{{r}} {{s}} {{t}}</text><text wx:else>点击选择</text></view></picker></view><view class="popup-adds"><view class="adds-tit">详细地址</view><input class="adds-item" cursor-spacing="20" type="text" placeholder="点击输入详细地址" placeholder-class="apc" value="{{z}}" bindinput="{{A}}"/></view><view class="popup-default" bindtap="{{C}}"><checkbox-group><checkbox checked="{{B}}"/><text>设置为默认地址</text></checkbox-group></view><view class="popup-btn bg1" bindtap="{{D}}">确认保存</view><view wx:if="{{E}}" bindtap="{{F}}" class="popup-btn" style="color:#FA5150;height:48rpx;line-height:48rpx">删除</view></view></uni-popup><uni-popup wx:if="{{K}}" class="r" u-s="{{['d']}}" u-r="tipsPopup" u-i="ff186348-4" bind:__l="__l" u-p="{{K}}"><view class="tips-box df"><view class="tips-item">{{I}}</view></view></uni-popup></view>