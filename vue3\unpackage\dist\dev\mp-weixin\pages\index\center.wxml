<view class="{{['container', be && 'no-scroll']}}"><view class="nav-box" style="{{'padding-top:' + h + ';' + ('background:' + i)}}"><view class="nav-item df" style="{{'height:' + g}}"><view class="nav-menu-btn" bindtap="{{b}}"><image src="{{a}}"></image></view><view class="ohto df" style="{{'transform:' + e + ';' + ('opacity:' + f)}}"><image src="{{c}}" mode="aspectFill" class="nav-user-avatar"></image><text>{{d}}</text></view></view></view><view class="user-box" style="{{'padding-top:' + N}}"><view class="user-bg"></view><view class="user-img" style="z-index:-2"><lazy-image wx:if="{{j}}" u-i="fd24fe58-0" bind:__l="__l" u-p="{{j}}"></lazy-image></view><view class="user-top df" data-url="center/means" bindtap="{{v}}" style="position:relative;padding-right:40rpx;padding-top:40rpx"><view class="avatar-wrapper"><view class="avatar"><lazy-image wx:if="{{k}}" u-i="fd24fe58-1" bind:__l="__l" u-p="{{k}}"></lazy-image></view></view><view class="user-info"><view class="user-name-row df"><text class="user-name-text">{{l}}</text><view wx:if="{{m}}" class="status-icon vip-icon"><image src="{{n}}"></image></view><view wx:if="{{o}}" class="status-icon verified-icon"><image src="{{p}}"></image></view></view><view class="user-id-row df"><view wx:if="{{q}}" class="sex-icon df"><image src="{{r}}"></image></view><text class="user-id">ID: {{s}}</text></view></view><view class="right-arrow"><image src="{{t}}" mode="aspectFit"></image></view></view><view class="user-num-wrap df"><view class="user-num df"><view class="num-item df" bindtap="{{x}}"><text class="t1">{{w}}</text><text class="t2">关注</text></view><view class="num-item df" bindtap="{{z}}"><text class="t1">{{y}}</text><text class="t2">粉丝</text></view><view class="num-item df" bindtap="{{B}}"><text class="t1">{{A}}</text><text class="t2">获赞</text></view><view class="num-item df visitor-item" data-url="center/visitor" bindtap="{{F}}"><text class="t1">{{C}}</text><text class="t2">访客</text><view wx:if="{{D}}" class="badge">+{{E}}</view></view></view></view><view class="tag-wrapper"><view wx:if="{{G}}" class="user-tag df"><view wx:for="{{H}}" wx:for-item="tag" wx:key="b" class="tag-item df"><text>{{tag.a}}</text></view></view><view wx:else class="tag-empty"><text>添加兴趣标签，让大家更了解你</text></view><view class="user-actions df"><view class="btn-icon" data-url="setting/index" bindtap="{{J}}"><image src="{{I}}"></image></view></view></view><view class="user-intro" data-url="center/means" bindtap="{{M}}"><text class="intro-text" user-select="true">{{K}}</text><view class="more-btn"><image src="{{L}}" mode="aspectFit"></image></view></view></view><scroll-view scroll-x="true" class="user-block"><view class="block-box"><view wx:if="{{O}}" class="block-item df" data-url="activity/index?type=1" bindtap="{{V}}"><view class="block-title" style="margin-right:68rpx"><view class="t1">活动</view><view class="t2">{{P}}</view></view><view class="cu-group df"><view class="cu-item" style="{{'background:' + T}}"><image wx:if="{{Q}}" class="icon" src="{{R}}"></image><image wx:else class="img" src="{{S}}" mode="aspectFill"></image></view><view class="cu-lump1"></view><view class="cu-lump2"></view></view><image class="block-icon" src="{{U}}"></image></view><view wx:for="{{W}}" wx:for-item="item" wx:key="j" class="block-item df" data-url="{{item.k}}" bindtap="{{item.l}}"><view class="block-title" style="margin-right:68rpx"><view class="t1">{{item.a}}</view><view wx:if="{{item.b}}" class="t2"> 共{{item.c}}{{item.d}}</view><view wx:else class="t2">{{item.e}}</view></view><view class="cu-group df"><view class="cu-item" style="{{'background:' + item.i}}"><image wx:if="{{item.f}}" class="icon" src="{{item.g}}"></image><image wx:else class="img" src="{{item.h}}" mode="aspectFill"></image></view><view class="cu-lump1"></view><view class="cu-lump2"></view></view><image class="block-icon" src="{{X}}"></image></view><view wx:if="{{Y}}" class="block-item df" data-url="center/card?type=1" bindtap="{{ac}}"><view class="block-title" style="margin-right:68rpx"><view class="t1">卡券</view><view class="t2">{{Z}}</view></view><view class="cu-group df"><view class="cu-item" style="background:#000"><image class="icon" src="{{aa}}"></image></view><view class="cu-lump1"></view><view class="cu-lump2"></view></view><image class="block-icon" src="{{ab}}"></image></view><view style="flex-shrink:0;width:15rpx;height:15rpx"></view></view></scroll-view><view class="bar-box df" style="{{'top:' + ae}}"><view wx:for="{{ad}}" wx:for-item="item" wx:key="e" class="bar-item df" bindtap="{{item.f}}" data-idx="{{item.g}}"><text style="{{'color:' + item.b + ';' + ('font-size:' + item.c)}}">{{item.a}}</text><view style="{{'opacity:' + item.d}}" class="bar-line"></view></view></view><view class="loading-indicator df" style="{{'height:' + ah}}"><uni-load-more wx:if="{{af}}" u-i="fd24fe58-2" bind:__l="__l" u-p="{{ag}}"></uni-load-more></view><view class="content-container"><view wx:if="{{ai}}" class="empty-state df"><image src="{{aj}}"/><view class="empty-title">{{ak}}</view><view class="empty-subtitle">{{al}}</view></view><view wx:elif="{{am}}" class="loading-state df"><uni-load-more wx:if="{{an}}" u-i="fd24fe58-3" bind:__l="__l" u-p="{{an}}"></uni-load-more><view class="loading-text">正在加载内容...</view></view><block wx:else><block wx:for="{{ao}}" wx:for-item="item" wx:key="f"><card-gg wx:if="{{ap}}" u-i="{{item.a}}" bind:__l="__l" u-p="{{item.b}}"></card-gg><card-wd wx:else binddelback="{{item.c}}" u-i="{{item.d}}" bind:__l="__l" u-p="{{item.e||''}}"></card-wd></block></block><uni-load-more wx:if="{{aq}}" u-i="fd24fe58-6" bind:__l="__l" u-p="{{ar}}"></uni-load-more></view><uni-popup u-s="{{['d']}}" u-r="likePopup" class="r r" u-i="fd24fe58-7" bind:__l="__l"><view class="like-popup"><image class="like-img" src="{{as}}" mode="aspectFill"></image><view class="like-content"><text>"</text>{{at}}<text>"</text>共获得 {{av}} 个赞 </view><view class="like-btn" bindtap="{{aw}}">确认</view></view></uni-popup><tabbar wx:if="{{ay}}" u-i="fd24fe58-8" bind:__l="__l" u-p="{{ay}}"></tabbar><view class="{{['sidebar-menu', aY && 'active']}}"><view class="sidebar-header" style="{{'padding-top:' + aH}}"><view class="sidebar-user-info"><image src="{{az}}" mode="aspectFill" class="sidebar-avatar"></image><view class="sidebar-user-details"><view class="sidebar-user-name">{{aA}}</view><view class="user-status"><view wx:if="{{aB}}" class="status-item"><image src="{{aC}}" class="status-icon"></image></view><view wx:if="{{aD}}" class="status-item verified-tag"><image src="{{aE}}" class="status-icon"></image><text>已认证</text></view></view></view></view><view class="close-btn df" bindtap="{{aG}}"><image src="{{aF}}" style="width:20rpx;height:20rpx"></image></view></view><view class="member-card"><view class="member-status"><view wx:if="{{aI}}" class="member-label">永久会员</view><view wx:elif="{{aJ}}" class="member-label"> 会员到期：{{aK}}</view><view wx:elif="{{aL}}" class="member-label">会员已过期</view><view wx:elif="{{aM}}" class="member-label">未开通会员</view><view wx:if="{{aN}}" class="member-price" bindtap="{{aO}}">¥3.8续费</view></view><view class="member-benefits"><text class="member-rights" bindtap="{{aP}}">会员权益 | 领取我的等级特权</text></view><view class="member-desc">专属优惠，VIP低至¥88，畅听1年！</view></view><scroll-view scroll-y class="sidebar-scroll"><view class="sidebar-content"><view class="menu-section"><view class="section-title">我的服务</view><view class="menu-grid"><view wx:for="{{aQ}}" wx:for-item="item" wx:key="e" class="grid-item" data-url="{{item.f}}" bindtap="{{item.g}}"><view class="grid-icon-wrapper"><image src="{{item.a}}" class="grid-icon"></image><view wx:if="{{item.b}}" class="grid-badge">{{item.c}}</view></view><text class="grid-text">{{item.d}}</text></view></view></view></view></scroll-view><view class="sidebar-footer"><view class="bottom-nav df"><view class="bottom-nav-item df" bindtap="{{aS}}"><view class="nav-icon-box df"><image src="{{aR}}" class="nav-icon"></image></view><text class="nav-text">扫一扫</text></view><view class="bottom-nav-item df" bindtap="{{aU}}"><view class="nav-icon-box df"><image src="{{aT}}" class="nav-icon"></image></view><text class="nav-text">帮助与客服</text></view><view class="bottom-nav-item df" bindtap="{{aW}}"><view class="nav-icon-box df"><image src="{{aV}}" class="nav-icon"></image></view><text class="nav-text">设置</text></view></view><view class="copyright-text">© {{aX}} 个人中心</view></view></view><view wx:if="{{aZ}}" class="sidebar-mask" bindtap="{{ba}}" catchtouchmove="{{bb}}" catchtouchstart="{{bc}}" catchtouchend="{{bd}}"></view></view>