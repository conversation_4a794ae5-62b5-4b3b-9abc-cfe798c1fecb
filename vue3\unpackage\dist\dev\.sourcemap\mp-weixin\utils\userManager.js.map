{"version": 3, "file": "userManager.js", "sources": ["utils/userManager.js"], "sourcesContent": ["// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\n/**\n * 统一用户信息管理工具\n * 解决Vuex、Pinia、本地缓存之间的数据不一致问题\n */\n\nimport { useUserStore } from '@/stores/user.js'\nimport Cache from '@/utils/cache.js'\nimport cacheConfig from '@/config/cache.js'\n\nconst { UID, LOGIN_STATUS, USER_INFO } = cacheConfig\n\nclass UserManager {\n  constructor() {\n    this.userStore = null\n    this.vuexStore = null\n  }\n\n  // 初始化，传入store实例\n  init(vuexStore) {\n    this.vuexStore = vuexStore\n    this.userStore = useUserStore()\n  }\n\n  /**\n   * 统一获取用户信息\n   * 优先级：缓存 > Pinia > Vuex\n   */\n  getUserInfo() {\n    try {\n      // 从各个数据源获取用户信息\n      const vuexUserInfo = this.vuexStore?.state?.app?.userInfo || {}\n      const vuexUid = this.vuexStore?.state?.app?.uid || 0\n      const vuexToken = this.vuexStore?.state?.app?.token || ''\n      \n      const piniaUserInfo = this.userStore?.userInfo || {}\n      const piniaUid = this.userStore?.uid || 0\n      const piniaToken = this.userStore?.token || ''\n      \n      const cachedUserInfo = uni.getStorageSync('USER_INFO') || {}\n      const cachedUid = uni.getStorageSync('UID') || 0\n      const cachedToken = uni.getStorageSync('token') || uni.getStorageSync('LOGIN_STATUS_TOKEN') || ''\n\n      // 确定最权威的数据源\n      const finalToken = cachedToken || piniaToken || vuexToken\n      const finalUid = cachedUid || piniaUid || vuexUid || cachedUserInfo.uid || piniaUserInfo.uid || vuexUserInfo.uid || 0\n      \n      // 合并用户信息，确保uid一致性\n      const finalUserInfo = {\n        ...vuexUserInfo,\n        ...piniaUserInfo,\n        ...cachedUserInfo,\n        uid: finalUid\n      }\n\n      return {\n        userInfo: finalUserInfo,\n        uid: finalUid,\n        token: finalToken,\n        isLogin: !!(finalToken && finalUid && finalUserInfo.nickname)\n      }\n    } catch (error) {\n      console.error('获取用户信息失败:', error)\n      return {\n        userInfo: {},\n        uid: 0,\n        token: '',\n        isLogin: false\n      }\n    }\n  }\n\n  /**\n   * 统一更新用户信息到所有数据源\n   */\n  updateUserInfo(newUserInfo, options = {}) {\n    try {\n      if (!newUserInfo || typeof newUserInfo !== 'object') {\n        console.warn('updateUserInfo: 无效的用户信息', newUserInfo)\n        return false\n      }\n\n      const { syncToVuex = true, syncToPinia = true, syncToCache = true } = options\n\n      // 获取当前用户信息\n      const current = this.getUserInfo()\n      const currentUid = current.uid || newUserInfo.uid || 0\n      \n      // 合并用户信息\n      const finalUserInfo = {\n        ...current.userInfo,\n        ...newUserInfo,\n        uid: currentUid\n      }\n\n      // 更新Vuex\n      if (syncToVuex && this.vuexStore) {\n        this.vuexStore.commit('UPDATE_USERINFO', finalUserInfo)\n        if (currentUid) {\n          this.vuexStore.commit('SETUID', currentUid)\n        }\n      }\n\n      // 更新Pinia\n      if (syncToPinia && this.userStore) {\n        this.userStore.updateUserInfo(finalUserInfo)\n        if (currentUid) {\n          this.userStore.setUid(currentUid)\n        }\n      }\n\n      // 更新缓存\n      if (syncToCache) {\n        uni.setStorageSync('USER_INFO', finalUserInfo)\n        if (currentUid) {\n          uni.setStorageSync('UID', currentUid)\n        }\n      }\n\n      console.log('用户信息已统一更新:', {\n        uid: currentUid,\n        nickname: finalUserInfo.nickname,\n        updateFields: Object.keys(newUserInfo),\n        syncOptions: { syncToVuex, syncToPinia, syncToCache }\n      })\n\n      return true\n    } catch (error) {\n      console.error('统一更新用户信息失败:', error)\n      return false\n    }\n  }\n\n  /**\n   * 设置登录状态和token\n   */\n  setLoginStatus(token, userInfo = null) {\n    try {\n      if (!token) {\n        console.warn('setLoginStatus: token为空')\n        return false\n      }\n\n      // 更新Vuex\n      if (this.vuexStore) {\n        this.vuexStore.commit('UPDATE_LOGIN', token)\n      }\n\n      // 更新Pinia\n      if (this.userStore) {\n        this.userStore.setToken(token)\n        this.userStore.setLoginStatus(true)\n      }\n\n      // 更新缓存\n      uni.setStorageSync('token', token)\n      Cache.set(LOGIN_STATUS, token)\n\n      // 如果提供了用户信息，一并更新\n      if (userInfo) {\n        this.updateUserInfo(userInfo)\n      }\n\n      console.log('登录状态已设置:', { hasToken: !!token, hasUserInfo: !!userInfo })\n      return true\n    } catch (error) {\n      console.error('设置登录状态失败:', error)\n      return false\n    }\n  }\n\n  /**\n   * 统一登出，清理所有数据源\n   */\n  logout() {\n    try {\n      // 重置Vuex\n      if (this.vuexStore) {\n        this.vuexStore.commit('LOGOUT')\n      }\n\n      // 重置Pinia\n      if (this.userStore) {\n        this.userStore.logout()\n      }\n\n      // 清除所有相关缓存\n      const keysToRemove = [\n        'USER_INFO', 'UID', 'token', 'LOGIN_STATUS_TOKEN',\n        'pinia_user', 'pinia_social', 'pinia_app'\n      ]\n\n      keysToRemove.forEach(key => {\n        try {\n          uni.removeStorageSync(key)\n        } catch (e) {\n          console.warn(`清除${key}失败:`, e)\n        }\n      })\n\n      // 清除旧系统缓存\n      Cache.clear(LOGIN_STATUS)\n      Cache.clear(USER_INFO)\n      Cache.clear(UID)\n\n      console.log('用户已统一登出，所有数据源已清理')\n      return true\n    } catch (error) {\n      console.error('统一登出失败:', error)\n      return false\n    }\n  }\n\n  /**\n   * 检查登录状态\n   */\n  checkLoginStatus() {\n    const { isLogin, token, uid, userInfo } = this.getUserInfo()\n    return {\n      isLogin,\n      hasToken: !!token,\n      hasUid: !!uid,\n      hasUserInfo: !!(userInfo && userInfo.nickname),\n      valid: isLogin && !!token && !!uid && !!(userInfo && userInfo.nickname)\n    }\n  }\n\n  /**\n   * 同步所有数据源\n   */\n  syncAllSources() {\n    const current = this.getUserInfo()\n    if (current.isLogin && current.userInfo.nickname) {\n      this.updateUserInfo(current.userInfo)\n      if (current.token) {\n        this.setLoginStatus(current.token)\n      }\n    }\n  }\n}\n\n// 创建单例实例\nconst userManager = new UserManager()\n\nexport default userManager\n"], "names": ["cacheConfig", "useUserStore", "uni", "<PERSON><PERSON>"], "mappings": ";;;;;AAmBA,MAAM,EAAE,KAAK,cAAc,UAAS,IAAKA,aAAW;AAEpD,MAAM,YAAY;AAAA,EAChB,cAAc;AACZ,SAAK,YAAY;AACjB,SAAK,YAAY;AAAA,EAClB;AAAA;AAAA,EAGD,KAAK,WAAW;AACd,SAAK,YAAY;AACjB,SAAK,YAAYC,yBAAc;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,cAAc;;AACZ,QAAI;AAEF,YAAM,iBAAe,sBAAK,cAAL,mBAAgB,UAAhB,mBAAuB,QAAvB,mBAA4B,aAAY,CAAE;AAC/D,YAAM,YAAU,sBAAK,cAAL,mBAAgB,UAAhB,mBAAuB,QAAvB,mBAA4B,QAAO;AACnD,YAAM,cAAY,sBAAK,cAAL,mBAAgB,UAAhB,mBAAuB,QAAvB,mBAA4B,UAAS;AAEvD,YAAM,kBAAgB,UAAK,cAAL,mBAAgB,aAAY,CAAE;AACpD,YAAM,aAAW,UAAK,cAAL,mBAAgB,QAAO;AACxC,YAAM,eAAa,UAAK,cAAL,mBAAgB,UAAS;AAE5C,YAAM,iBAAiBC,cAAG,MAAC,eAAe,WAAW,KAAK,CAAE;AAC5D,YAAM,YAAYA,cAAG,MAAC,eAAe,KAAK,KAAK;AAC/C,YAAM,cAAcA,cAAG,MAAC,eAAe,OAAO,KAAKA,oBAAI,eAAe,oBAAoB,KAAK;AAG/F,YAAM,aAAa,eAAe,cAAc;AAChD,YAAM,WAAW,aAAa,YAAY,WAAW,eAAe,OAAO,cAAc,OAAO,aAAa,OAAO;AAGpH,YAAM,gBAAgB;AAAA,QACpB,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,KAAK;AAAA,MACN;AAED,aAAO;AAAA,QACL,UAAU;AAAA,QACV,KAAK;AAAA,QACL,OAAO;AAAA,QACP,SAAS,CAAC,EAAE,cAAc,YAAY,cAAc;AAAA,MACrD;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAAA,mDAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,UAAU,CAAE;AAAA,QACZ,KAAK;AAAA,QACL,OAAO;AAAA,QACP,SAAS;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe,aAAa,UAAU,IAAI;AACxC,QAAI;AACF,UAAI,CAAC,eAAe,OAAO,gBAAgB,UAAU;AACnDA,sBAAAA,kDAAa,2BAA2B,WAAW;AACnD,eAAO;AAAA,MACR;AAED,YAAM,EAAE,aAAa,MAAM,cAAc,MAAM,cAAc,KAAI,IAAK;AAGtE,YAAM,UAAU,KAAK,YAAa;AAClC,YAAM,aAAa,QAAQ,OAAO,YAAY,OAAO;AAGrD,YAAM,gBAAgB;AAAA,QACpB,GAAG,QAAQ;AAAA,QACX,GAAG;AAAA,QACH,KAAK;AAAA,MACN;AAGD,UAAI,cAAc,KAAK,WAAW;AAChC,aAAK,UAAU,OAAO,mBAAmB,aAAa;AACtD,YAAI,YAAY;AACd,eAAK,UAAU,OAAO,UAAU,UAAU;AAAA,QAC3C;AAAA,MACF;AAGD,UAAI,eAAe,KAAK,WAAW;AACjC,aAAK,UAAU,eAAe,aAAa;AAC3C,YAAI,YAAY;AACd,eAAK,UAAU,OAAO,UAAU;AAAA,QACjC;AAAA,MACF;AAGD,UAAI,aAAa;AACfA,4BAAI,eAAe,aAAa,aAAa;AAC7C,YAAI,YAAY;AACdA,8BAAI,eAAe,OAAO,UAAU;AAAA,QACrC;AAAA,MACF;AAEDA,oBAAAA,MAAY,MAAA,OAAA,+BAAA,cAAc;AAAA,QACxB,KAAK;AAAA,QACL,UAAU,cAAc;AAAA,QACxB,cAAc,OAAO,KAAK,WAAW;AAAA,QACrC,aAAa,EAAE,YAAY,aAAa,YAAa;AAAA,MAC7D,CAAO;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,MAAc,MAAA,SAAA,+BAAA,eAAe,KAAK;AAClC,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe,OAAO,WAAW,MAAM;AACrC,QAAI;AACF,UAAI,CAAC,OAAO;AACVA,sBAAAA,MAAa,MAAA,QAAA,+BAAA,yBAAyB;AACtC,eAAO;AAAA,MACR;AAGD,UAAI,KAAK,WAAW;AAClB,aAAK,UAAU,OAAO,gBAAgB,KAAK;AAAA,MAC5C;AAGD,UAAI,KAAK,WAAW;AAClB,aAAK,UAAU,SAAS,KAAK;AAC7B,aAAK,UAAU,eAAe,IAAI;AAAA,MACnC;AAGDA,0BAAI,eAAe,SAAS,KAAK;AACjCC,wBAAM,IAAI,cAAc,KAAK;AAG7B,UAAI,UAAU;AACZ,aAAK,eAAe,QAAQ;AAAA,MAC7B;AAEDD,oBAAAA,MAAA,MAAA,OAAA,+BAAY,YAAY,EAAE,UAAU,CAAC,CAAC,OAAO,aAAa,CAAC,CAAC,SAAQ,CAAE;AACtE,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,oDAAc,aAAa,KAAK;AAChC,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,SAAS;AACP,QAAI;AAEF,UAAI,KAAK,WAAW;AAClB,aAAK,UAAU,OAAO,QAAQ;AAAA,MAC/B;AAGD,UAAI,KAAK,WAAW;AAClB,aAAK,UAAU,OAAQ;AAAA,MACxB;AAGD,YAAM,eAAe;AAAA,QACnB;AAAA,QAAa;AAAA,QAAO;AAAA,QAAS;AAAA,QAC7B;AAAA,QAAc;AAAA,QAAgB;AAAA,MAC/B;AAED,mBAAa,QAAQ,SAAO;AAC1B,YAAI;AACFA,wBAAG,MAAC,kBAAkB,GAAG;AAAA,QAC1B,SAAQ,GAAG;AACVA,8BAAa,MAAA,QAAA,+BAAA,KAAK,GAAG,OAAO,CAAC;AAAA,QAC9B;AAAA,MACT,CAAO;AAGDC,kBAAK,MAAC,MAAM,YAAY;AACxBA,kBAAK,MAAC,MAAM,SAAS;AACrBA,kBAAK,MAAC,MAAM,GAAG;AAEfD,oBAAAA,kDAAY,kBAAkB;AAC9B,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,MAAc,MAAA,SAAA,+BAAA,WAAW,KAAK;AAC9B,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,mBAAmB;AACjB,UAAM,EAAE,SAAS,OAAO,KAAK,SAAU,IAAG,KAAK,YAAa;AAC5D,WAAO;AAAA,MACL;AAAA,MACA,UAAU,CAAC,CAAC;AAAA,MACZ,QAAQ,CAAC,CAAC;AAAA,MACV,aAAa,CAAC,EAAE,YAAY,SAAS;AAAA,MACrC,OAAO,WAAW,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,EAAE,YAAY,SAAS;AAAA,IAC/D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,iBAAiB;AACf,UAAM,UAAU,KAAK,YAAa;AAClC,QAAI,QAAQ,WAAW,QAAQ,SAAS,UAAU;AAChD,WAAK,eAAe,QAAQ,QAAQ;AACpC,UAAI,QAAQ,OAAO;AACjB,aAAK,eAAe,QAAQ,KAAK;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AACH;AAGK,MAAC,cAAc,IAAI,YAAW;;"}