
.container {
  min-height: 100vh;
  background: #f8f8f8;
}

/* 顶部导航栏 */
.nav-box {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
}
.nav-box .nav-title {
  padding: 0 30rpx;
  font-size: 40rpx;
  font-weight: 700;
}
.nav-title .nav-del {
  margin-left: 15rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #f8f8f8;
  border: 1px solid #f5f5f5;
  justify-content: center;
}
.nav-title .nav-del image {
  width: 28rpx;
  height: 28rpx;
}
.df {
  display: flex;
  align-items: center;
}
.bfw {
  background: #fff;
}

/* 功能图标区域 */
.icon-section {
  padding: 20rpx 30rpx;
  background: #fff;
}
.icon-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.icon-wrapper {
  position: relative;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}
.like-icon {
  background: linear-gradient(135deg, #ff6b9d, #ff8a9b);
}
.comment-icon {
  background: linear-gradient(135deg, #ffd93d, #ffb347);
}
.favorite-icon {
  background: linear-gradient(135deg, #6bcf7f, #4ecdc4);
}
.contacts-icon {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
}
.icon-image {
  width: 48rpx;
  height: 48rpx;
  filter: brightness(0) invert(1);
}
.icon-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 34rpx;
  height: 34rpx;
  line-height: 34rpx;
  text-align: center;
  font-size: 20rpx;
  font-weight: 700;
  color: #fff;
  background: #ff4757;
  border-radius: 34rpx;
  border: 2rpx solid #fff;
}
.icon-label {
  font-size: 24rpx;
  color: #999;
  font-weight: 400;
}

/* 消息列表 */
.message-list {
  background: #fff;
  margin-top: 20rpx;
  padding-bottom: 180rpx;
}
.message-item {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1px solid #f5f5f5;
}
.message-item:last-child {
  border-bottom: none;
}
.message-left {
  margin-right: 24rpx;
}
.message-icon-wrapper {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.app-icon {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
}
.system-icon {
  background: linear-gradient(135deg, #ffd93d, #ffb347);
}
.contact-icon {
  background: linear-gradient(135deg, #6bcf7f, #4ecdc4);
}
.ai-icon {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
}
.user-icon {
  background: #f5f5f5;
  border-radius: 50%;
  overflow: hidden;
}
.message-icon {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}
.user-icon .message-icon {
  width: 80rpx;
  height: 80rpx;
  filter: none;
}
.message-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.message-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}
.message-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #000;
}
.message-right {
  display: flex;
  align-items: center;
}
.message-time {
  font-size: 24rpx;
  color: #999;
  margin-right: 12rpx;
}
.message-badge {
  min-width: 34rpx;
  height: 34rpx;
  line-height: 34rpx;
  text-align: center;
  font-size: 20rpx;
  font-weight: 700;
  color: #fff;
  background: #ff4757;
  border-radius: 34rpx;
}
.message-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

/* 空状态 */
.empty-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 0;
  background: #fff;
}
.empty-image {
  width: 280rpx;
  height: 280rpx;
  opacity: 0.6;
}
.empty-title {
  margin-top: 40rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.empty-desc {
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #999;
}

/* 弹窗样式 */
.more-popup {
  width: 100%;
  background: #fff;
  border-radius: 30rpx 30rpx 0 0;
  overflow: hidden;
}
.more-popup .more-item {
  width: calc(100% - 60rpx);
  padding: 30rpx;
  font-size: 26rpx;
  font-weight: 700;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.more-popup .more-item:first-child {
  padding-top: 40rpx;
}
.more-popup .more-item:last-child {
  padding-bottom: 80rpx;
}
.more-popup .more-item image {
  width: 36rpx;
  height: 36rpx;
}
.tips-box {
  width: 100%;
  display: flex;
  justify-content: center;
}
.tips-item {
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}
