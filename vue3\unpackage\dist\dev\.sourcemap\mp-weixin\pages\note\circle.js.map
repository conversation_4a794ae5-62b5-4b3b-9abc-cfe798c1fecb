{"version": 3, "file": "circle.js", "sources": ["pages/note/circle.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbm90ZS9jaXJjbGUudnVl"], "sourcesContent": ["<template>\n    <view class=\"container\">\n      <!-- 导航栏 -->\n      <view class=\"nav-box df\" :style=\"{'padding-top': statusBarHeight + 'px', 'background': 'rgba(255, 255, 255,' + navbarTrans + ')'}\">\n        <view class=\"nav-back df\" :style=\"{'height': titleBarHeight + 'px'}\" @tap=\"navBack\">\n          <image src=\"/static/img/back.png\" style=\"width:34rpx;height:34rpx\"></image>\n        </view>\n        <view v-if=\"navbarTrans == 1\" class=\"nav-title ohto\">{{ circleInfo.circle_name || circleInfo.name }}</view>\n      </view>\n      \n          <!-- 圈子头部区域 - 沉浸式全屏设计 -->\n      <view class=\"circle-header\">\n        <!-- 主背景容器 - 从屏幕顶部开始 -->\n        <view class=\"images-box df\">\n          <!-- 圈子背景图片容器 -->\n          <view class=\"circle-image-container\">\n            <lazy-image\n              v-if=\"circleInfo.circle_background || circleInfo.circle_avatar\"\n              :src=\"circleInfo.circle_background || circleInfo.circle_avatar\"\n              class=\"circle-bg-image\"\n              mode=\"aspectFill\"\n              @tap=\"previewCircleImage\">\n            </lazy-image>\n\n            <!-- 默认背景 -->\n            <view v-else class=\"default-bg\"></view>\n\n            <!-- 背景遮罩层 -->\n            <view class=\"bg-overlay\"></view>\n\n          </view>\n        </view>\n        \n        <!-- 圈子信息叠加层 -->\n        <view class=\"circle-info-overlay\" :style=\"{'padding-top': statusBarHeight + titleBarHeight + 40 + 'rpx'}\">\n          <view class=\"circle-main-info df\">\n            <!-- 圈子头像 -->\n            <view class=\"circle-avatar\">\n              <image \n                v-if=\"!showLoading\"\n                :src=\"circleInfo.circle_avatar || '/static/img/avatar.png'\" \n                mode=\"aspectFill\"\n                class=\"avatar-img\">\n              </image>\n              <view v-else class=\"skeleton-avatar\"></view>\n          </view>\n            \n            <!-- 圈子名称和统计信息 -->\n            <view class=\"circle-details\">\n              <view class=\"circle-name\">\n                <text v-if=\"!showLoading\">{{ circleInfo.circle_name || circleInfo.name || '圈子加载中...' }}</text>\n                <view v-else class=\"skeleton-text\"></view>\n              </view>\n              <view class=\"circle-stats df\">\n                <text v-if=\"!showLoading\" class=\"stat-item\">{{ circleInfo.dynamic_count || 0 }}篇笔记</text>\n                <text v-if=\"!showLoading\" class=\"stat-divider\">·</text>\n                <text v-if=\"!showLoading\" class=\"stat-item\">{{ circleInfo.member_count || 0 }}人加入</text>\n                <text v-if=\"!showLoading && circleInfo.view_count\" class=\"stat-divider\">·</text>\n                <text v-if=\"!showLoading && circleInfo.view_count\" class=\"stat-item\">访问 {{ circleInfo.view_count }}</text>\n                <!-- 骨架屏 -->\n                <view v-if=\"showLoading\" class=\"skeleton-stats\">\n                  <view class=\"skeleton-item\"></view>\n                  <view class=\"skeleton-item\"></view>\n                  <view class=\"skeleton-item\"></view>\n                </view>\n              </view>\n            </view>\n          </view>\n          \n          <!-- 圈子描述 -->\n          <view class=\"circle-description\" v-if=\"!showLoading && (circleInfo.circle_description || circleInfo.intro)\">\n            <text>{{ circleInfo.circle_description || circleInfo.intro }}</text>\n          </view>\n          \n          <!-- 圈子公告 -->\n          <view class=\"circle-notice\" v-if=\"!showLoading && circleInfo.circle_notice\">\n            <text>【公告】{{ circleInfo.circle_notice }}</text>\n          </view>\n          \n          <!-- 操作按钮 -->\n          <view class=\"btn-box df\">\n            <!-- 加载中状态 -->\n            <view v-if=\"showLoading\" class=\"btn df bg1 loading\">\n              <view class=\"loading-dots\">\n                <view class=\"dot\"></view>\n                <view class=\"dot\"></view>\n                <view class=\"dot\"></view>\n              </view>\n            </view>\n\n            <!-- 正常状态 -->\n            <template v-else>\n              <!-- 加入/已加入按钮 -->\n              <view\n                @tap=\"handleJoinCircle\"\n                :class=\"['btn', 'df', 'bg1', circleInfo.is_joined ? 'joined-state' : '']\">\n                <!-- 未加入时显示成员头像 -->\n                <view v-if=\"!circleInfo.is_joined && circleInfo.recent_members && circleInfo.recent_members.length > 0\" class=\"cu-img-group\">\n                  <view\n                    v-for=\"(member, index) in circleInfo.recent_members.slice(0, 3)\"\n                    :key=\"index\"\n                    class=\"cu-img\">\n                    <image :src=\"member.avatar || '/static/img/avatar.png'\" mode=\"aspectFill\"></image>\n                  </view>\n                </view>\n                <text>{{ circleInfo.is_joined ? \"已加入\" : \"加入圈子\" }}</text>\n              </view>\n\n              <!-- 分享按钮 -->\n              <view\n                @tap=\"shareClick(true)\"\n                :class=\"['btn', 'df', 'bg2', circleInfo.is_joined ? 'share-expanded' : '']\">\n                <image src=\"/static/img/fx1.png\" class=\"icon\"></image>\n                <text v-show=\"circleInfo.is_joined\">分享</text>\n              </view>\n            </template>\n          </view>\n          </view>\n        </view>\n        \n        <!-- 分类标签 -->\n      <view class=\"bar-box df\">\n          <view \n            v-for=\"(item, index) in barList\" \n            :key=\"index\" \n            class=\"bar-item df\" \n            @tap=\"barClick\" \n            :data-idx=\"index\">\n            <text :style=\"{\n              'color': index == barIdx ? '#000' : '#999',\n              'font-size': index == barIdx ? '28rpx' : '26rpx'\n            }\">{{ item }}</text>\n            <view :style=\"{'opacity': index == barIdx ? 1 : 0}\" class=\"bar-line\"></view>\n          </view>\n      </view>\n      <!-- 规则tab内容 -->\n      <view v-if=\"barIdx === barList.length - 1\" class=\"rule-box\">\n        <view class=\"rule-title\">小圈规则</view>\n        <view class=\"rule-list\">\n          <view class=\"rule-item\">\n            <view class=\"rule-tag tag-orange\">适合人群</view>\n            <text class=\"rule-text\">性格属性有K8倾向，以及喜欢和K8做朋友的圈友。性格属性有K8倾向，以及喜欢和K8做朋友的圈友。</text>\n          </view>\n          <view class=\"rule-item\">\n            <view class=\"rule-tag tag-orange\">提倡内容</view>\n            <text class=\"rule-text\">分享记录猫咪生活，如猫咪的想法、装扮、吐槽等。</text>\n          </view>\n          <view class=\"rule-item\">\n            <view class=\"rule-tag tag-orange\">禁止内容</view>\n            <text class=\"rule-text\">广告/引流贴，或与圈子主题毫不相关的内容，看到直接删除。</text>\n          </view>\n        </view>\n        <view class=\"rule-team-title df\">\n          <text>管理团队</text>\n          <view class=\"rule-team-all\" @tap=\"goToAllMembers\">全部成员</view>\n        </view>\n        <view class=\"rule-team-list\">\n          <view class=\"member-item df\" v-for=\"(member, idx) in adminMembers\" :key=\"'admin-'+idx\" @tap=\"goToUserProfile(member.uid)\">\n            <image class=\"avatar\" :src=\"member.user_avatar || '/static/img/avatar.png'\"></image>\n            <view class=\"info\">\n              <view class=\"name df\">\n                <text>{{member.user_nickname}}</text>\n                <view v-if=\"member.role_type === 3\" class=\"role-tag owner\">圈主</view>\n                <view v-else-if=\"member.role_type === 2\" class=\"role-tag admin\">管理员</view>\n              </view>\n              <view class=\"meta df\">\n                <text :class=\"['gender', member.gender==='男' ? 'male' : 'female']\">{{member.gender==='男'?'♂':'♀'}}</text>\n                <text class=\"contrib\">加入时间：{{formatJoinTime(member.join_time)}}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      <!-- 推荐、最新内容区域（barIdx>0时显示） -->\n      <view v-else class=\"content-area\">\n        <!-- 加载中提示 -->\n        <view v-if=\"showLoading\" class=\"loading-container\">\n          <view class=\"loading-indicator\"></view>\n        </view>\n        \n        <!-- 空状态 -->\n        <view v-if=\"isEmpty\" class=\"empty-box df\">\n          <image src=\"/static/img/empty.png\"/>\n          <view class=\"e1\">{{ (circleInfo.circle_name || circleInfo.name || '该圈子') }} 暂无笔记</view>\n          <view class=\"e2\">{{ circleInfo.is_joined ? '快来发布第一篇圈内笔记吧' : '加入圈子即可发布圈内笔记' }}</view>\n        </view>\n        \n        <!-- 笔记列表 -->\n        <view v-else :class=\"[isWaterfall ? 'dynamic-box' : '']\">\n          <!-- 瀑布流布局 -->\n          <waterfall v-if=\"isWaterfall\" :note=\"list\" :page=\"page\"></waterfall>\n          \n          <!-- 普通列表布局 -->\n          <block v-else>\n            <card-gg \n              v-for=\"(item, index) in list\" \n              :key=\"index\" \n              @likeback=\"likeClick\" \n              :item=\"item\" \n              :idx=\"index\"\n              @update=\"onCardUpdate\"\n            ></card-gg>\n          </block>\n        </view>\n        \n        <!-- 加载更多 -->\n        <view v-if=\"list.length > 0 && loadStatus === 'noMore'\" style=\"text-align: center; padding: 20rpx 0; color: #999; font-size: 24rpx;\">\n          没有更多数据了\n      </view>\n          </view>\n      \n      <!-- 分享组件 -->\n      <share-component \n        :show=\"showShare\"\n        :noteInfo=\"circleShareInfo\"\n        :userId=\"userId\"\n        @close=\"closeShare\"\n        @share=\"handleShare\"\n        @dislike=\"handleDislike\"\n        @report=\"handleReport\"\n        @edit=\"handleEdit\"\n        @delete=\"handleDelete\">\n      </share-component>\n      \n      <!-- 提示弹窗 -->\n      <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\n        <view class=\"tips-box df\">\n          <view class=\"tips-item\">{{ tipsTitle }}</view>\n        </view>\n      </uni-popup>\n    </view>\n  </template>\n  \n  <script>\n\n  import lazyImage from '@/components/lazyImage/lazyImage.vue'\n  import uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue'\n  import waterfall from '@/components/waterfall/waterfall.vue'\n  import cardGg from '@/components/card-gg/card-gg.vue'\n  import shareComponent from '@/components/share/index.vue'\n  import { getCircleDetail, joinCircle, exitCircle, getCircleDynamicList, getCircleMemberList } from '@/api/social'\n\n  const app = getApp();\n  \n  export default {\n    components: {\n      lazyImage,\n      uniLoadMore,\n      waterfall,\n      cardGg,\n      shareComponent\n    },\n    data() {\n      return {\n        statusBarHeight: this.$store.state.statusBarHeight || 20,\n        titleBarHeight: this.$store.state.titleBarHeight || 44,\n  \n        navbarTrans: 0,\n        barList: [\"推荐\", \"最新\", \"规则\"],\n        barIdx: 0,\n        circleId: 0,\n        circleInfo: {\n          id: 0,\n          circle_name: '',\n          circle_avatar: '',\n          circle_background: '',\n          circle_description: '',\n          circle_notice: '',\n          member_count: 0,\n          dynamic_count: 0,\n          view_count: 0,\n          is_joined: false,\n          user_role: 0,\n          recent_members: []\n        },\n        list: [],\n        page: 1,\n        limit: 10,\n        totalCount: 0,\n        isThrottling: true,\n        isEmpty: false,\n        loadStatus: \"more\",\n        tipsTitle: \"\",\n        isWaterfall: false,\n        showLoading: true, // 初始状态设为加载中\n        loadingTimer: null,\n        debounceTimer: null,\n        showShare: false,\n        userId: 0,\n        adminMembers: [], // 管理团队成员列表\n        // 缓存相关\n        cacheKey: '',\n        lastUpdateTime: 0\n      }\n    },\n    computed: {\n      // 圈子分享信息，转换为分享组件需要的格式\n      circleShareInfo() {\n        return {\n          id: this.circleInfo.id || this.circleId,\n          uid: this.circleInfo.creator_id || this.circleInfo.uid, // 圈主ID\n          title: this.circleInfo.circle_name || this.circleInfo.name,\n          content: this.circleInfo.circle_description || this.circleInfo.intro,\n          image: this.circleInfo.circle_avatar,\n          type: 'circle', // 标识这是圈子类型\n          share_url: `/pages/note/circle?id=${this.circleId}`\n        };\n      }\n    },\n    onLoad(options) {\n      // 开启分享功能 - 仅在小程序环境中有效\n      // #ifdef MP\n      uni.showShareMenu();\n      // #endif\n      \n      // 获取用户信息 - 优先从Vuex获取，其次从storage获取\n      const userInfo = uni.getStorageSync('userInfo');\n      const vuexUid = this.$store.state.app.uid;\n      this.userId = vuexUid || (userInfo && userInfo.uid) || 0;\n      \n\n      \n      if (options.id) {\n        this.circleId = parseInt(options.id);\n        this.circleInfo.id = this.circleId;\n        this.cacheKey = `circle_${this.circleId}`;\n\n        // 检查缓存\n        this.checkCache();\n\n        this.getCircleDetail();\n      } else {\n        this.opTipsPopup(\"圈子ID不存在\", true);\n      }\n    },\n    methods: {\n      // 检查缓存\n      checkCache() {\n        try {\n          const cached = uni.getStorageSync(this.cacheKey);\n          const now = Date.now();\n\n          // 缓存有效期5分钟\n          if (cached && cached.timestamp && (now - cached.timestamp < 5 * 60 * 1000)) {\n            this.circleInfo = { ...this.circleInfo, ...cached.data };\n            this.lastUpdateTime = cached.timestamp;\n          }\n        } catch (e) {\n          console.warn('读取缓存失败:', e);\n        }\n      },\n\n      // 更新缓存\n      updateCache(data) {\n        try {\n          const cacheData = {\n            data: data,\n            timestamp: Date.now()\n          };\n          uni.setStorageSync(this.cacheKey, cacheData);\n          this.lastUpdateTime = cacheData.timestamp;\n        } catch (e) {\n          console.warn('更新缓存失败:', e);\n        }\n      },\n\n      // 预览圈子背景图片\n      previewCircleImage() {\n        if (this.circleInfo.circle_background || this.circleInfo.circle_avatar) {\n          const imageUrl = this.circleInfo.circle_background || this.circleInfo.circle_avatar;\n          uni.previewImage({\n            current: imageUrl,\n            urls: [imageUrl]\n          });\n        }\n      },\n      \n      // 获取圈子详情\n      async getCircleDetail() {\n        try {\n          this.showLoading = true;\n\n          const res = await getCircleDetail(this.circleId);\n\n          if (res.status === 200 && res.data) {\n            // 处理圈子信息\n            const newCircleInfo = {\n              ...this.circleInfo,\n              ...res.data,\n              id: res.data.id || this.circleId\n            };\n            this.circleInfo = newCircleInfo;\n\n            // 更新缓存\n            this.updateCache(newCircleInfo);\n\n            // 并行获取圈子动态和管理团队成员\n            await Promise.all([\n              this.getCircleDynamic(),\n              this.getAdminMembers()\n            ]);\n          } else {\n            this.opTipsPopup(res.msg || \"获取圈子详情失败\", true);\n          }\n        } catch (err) {\n          console.error('获取圈子详情失败:', err);\n          this.handleError(err, \"网络错误，请稍后重试\");\n          // 延迟返回上一页\n          setTimeout(() => {\n            uni.navigateBack();\n          }, 2000);\n        } finally {\n          this.showLoading = false;\n        }\n      },\n      \n      // 获取圈子动态\n      async getCircleDynamic() {\n        this.isThrottling = false;\n        this.loadStatus = 'loading';\n\n        // 延迟显示加载指示器，避免短时间内的闪烁\n        if (this.loadingTimer) {\n          clearTimeout(this.loadingTimer);\n        }\n        this.loadingTimer = setTimeout(() => {\n          if (this.loadStatus === 'loading') {\n            this.showLoading = true;\n          }\n        }, 300);\n\n        // 构建请求参数\n        const params = {\n          page: this.page,\n          limit: this.limit,\n          circle_id: this.circleId,\n          type: this.barIdx === 0 ? 'recommend' : 'latest', // 0=推荐, 1=最新\n          uid: this.userId // 传递用户ID用于获取点赞和关注状态\n        };\n        \n\n        \n        try {\n          // 调用圈子动态列表API\n          const res = await getCircleDynamicList(params);\n\n          if (res.status === 200 && res.data) {\n            const responseData = res.data;\n\n            if (responseData.list && responseData.list.length > 0) {\n              // 处理动态数据，确保包含圈子信息\n              const formattedList = responseData.list.map(item => ({\n                ...item,\n                circle_id: this.circleId,\n                circle_name: this.circleInfo.circle_name,\n                circle_avatar: this.circleInfo.circle_avatar\n              }));\n\n              if (this.page == 1) {\n                this.list = formattedList;\n              } else {\n                this.list = this.list.concat(formattedList);\n              }\n\n              // 更新总记录数\n              if (responseData.count !== undefined) {\n                this.totalCount = responseData.count;\n              }\n\n              this.isEmpty = false;\n            } else if (this.page == 1) {\n              this.isEmpty = true;\n              this.list = [];\n            }\n\n            // 判断是否还有更多数据\n            if (this.list.length >= this.totalCount && this.list.length > 0) {\n              this.loadStatus = \"noMore\";\n            }\n          } else {\n            if (this.page == 1) {\n              this.isEmpty = true;\n              this.list = [];\n            }\n            // 不显示错误提示，保持静默\n          }\n        } catch (err) {\n          if (this.page == 1) {\n            this.isEmpty = true;\n            this.list = [];\n          }\n\n          console.error('获取圈子动态失败:', err);\n\n          // 只在首次加载失败时显示错误提示\n          if (this.page === 1) {\n            this.handleError(err, '获取圈子动态失败，请稍后重试');\n          }\n        } finally {\n          this.isThrottling = true;\n          this.loadStatus = \"more\";\n\n          // 清除加载定时器并隐藏加载指示器\n          if (this.loadingTimer) {\n            clearTimeout(this.loadingTimer);\n            this.loadingTimer = null;\n          }\n          this.showLoading = false;\n        }\n      },\n      // 获取管理团队成员\n      async getAdminMembers() {\n        try {\n\n          \n          const res = await getCircleMemberList({ \n            circle_id: this.circleId, \n            page: 1, \n            limit: 50,\n          });\n          \n\n          \n          if (res.status === 200 && res.data && res.data.list) {\n            // 按角色排序：圈主在前，管理员在后\n            const adminList = res.data.list.filter(member => member.role_type === 2 || member.role_type === 3);\n            this.adminMembers = adminList.sort((a, b) => {\n              // 圈主(3)排在管理员(2)前面\n              if (a.role_type === 3 && b.role_type === 2) return -1;\n              if (a.role_type === 2 && b.role_type === 3) return 1;\n              return 0;\n            });\n          }\n        } catch (e) {\n          console.error('获取管理团队成员失败:', e);\n          // 管理团队获取失败不影响主要功能，只记录错误\n          this.handleError(e, '获取管理团队信息失败');\n        }\n      },\n      // 加入/退出圈子\n      async handleJoinCircle() {\n        // 检查登录状态 - 优先从Vuex获取\n        const userInfo = uni.getStorageSync('userInfo');\n        const vuexUid = this.$store.state.app.uid;\n        const currentUserId = vuexUid || (userInfo && userInfo.uid) || 0;\n\n        if (!currentUserId) {\n          this.opTipsPopup(\"请先登录\");\n          return;\n        }\n\n        try {\n          uni.showLoading({\n            title: this.circleInfo.is_joined ? '退出中...' : '加入中...',\n            mask: true\n          });\n\n          const apiMethod = this.circleInfo.is_joined ? exitCircle : joinCircle;\n          const params = {\n            circle_id: this.circleId\n          };\n\n          const res = await apiMethod(params);\n\n          if (res.status === 200) {\n            // 更新加入状态\n            this.circleInfo.is_joined = !this.circleInfo.is_joined;\n\n            // 更新成员数量\n            if (this.circleInfo.is_joined) {\n              this.circleInfo.member_count += 1;\n              this.opTipsPopup(\"已成功加入圈子\");\n            } else {\n              this.circleInfo.member_count = Math.max(0, this.circleInfo.member_count - 1);\n              this.opTipsPopup(\"已退出圈子\");\n            }\n\n            // 标记需要刷新圈子列表\n            app.globalData.isCenterPage = true;\n          } else {\n            this.opTipsPopup(res.msg || \"操作失败，请重试\");\n          }\n        } catch (err) {\n          console.error('圈子操作失败:', err);\n          this.handleError(err, \"网络错误，请稍后重试\");\n        } finally {\n          uni.hideLoading();\n        }\n      },\n      \n      // 切换分类标签\n      barClick(e) {\n        const clickIdx = parseInt(e.currentTarget.dataset.idx);\n        if (clickIdx === this.barIdx) return;\n\n        // 防抖处理\n        if (this.debounceTimer) {\n          clearTimeout(this.debounceTimer);\n        }\n\n        this.barIdx = clickIdx;\n\n        if (clickIdx < this.barList.length - 1) {\n          // 推荐/最新tab才加载内容\n          this.debounceTimer = setTimeout(() => {\n            this.isThrottling = false;\n            this.page = 1;\n            this.loadStatus = \"loading\";\n            this.list = [];\n            this.getCircleDynamic();\n          }, 100);\n        }\n      },\n      goToAllMembers() {\n        // 跳转到全部成员页面\n        uni.navigateTo({ url: '/pages/note/circlemember?id=' + this.circleId });\n      },\n      // 跳转到用户主页\n      goToUserProfile(uid) {\n        uni.navigateTo({\n          url: `/pages/user/details?id=${uid}`\n        });\n      },\n      // 格式化加入时间\n      formatJoinTime(timeStr) {\n        if (!timeStr) return '';\n        const date = new Date(timeStr);\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        return `${year}-${month}-${day}`;\n      },\n      \n      // 分享控制\n      shareClick(isOpen) {\n        this.showShare = isOpen;\n      },\n      \n      // 关闭分享弹窗\n      closeShare() {\n        this.showShare = false;\n      },\n      \n      // 处理分享事件\n      handleShare(type) {\n        console.log('分享圈子:', type, this.circleInfo);\n        uni.showToast({\n          title: '分享成功',\n          icon: 'success'\n        });\n        this.showShare = false;\n      },\n      \n      // 处理不感兴趣\n      handleDislike(circleId) {\n        console.log('标记圈子不感兴趣:', circleId);\n        this.showShare = false;\n      },\n      \n      // 处理举报\n      handleReport(reportData) {\n        console.log('举报圈子:', reportData);\n        uni.showToast({\n          title: '举报成功',\n          icon: 'success'\n        });\n        this.showShare = false;\n      },\n      \n      // 处理编辑（如果是圈主）\n      handleEdit(circleId) {\n        console.log('编辑圈子:', circleId);\n        // 跳转到圈子编辑页面\n        uni.navigateTo({\n          url: `/pages/circle/edit?id=${circleId}`\n        });\n        this.showShare = false;\n      },\n      \n      // 处理删除（如果是圈主）\n      handleDelete(circleId) {\n        console.log('删除圈子:', circleId);\n        uni.showModal({\n          title: '确认删除',\n          content: '确定要删除这个圈子吗？删除后不可恢复。',\n          confirmColor: '#FA5150',\n          success: (res) => {\n            if (res.confirm) {\n              // 调用删除圈子的API\n              this.deleteCircle(circleId);\n            }\n          }\n        });\n        this.showShare = false;\n      },\n      \n      // 删除圈子\n      deleteCircle() {\n        uni.showLoading({\n          title: '删除中...',\n          mask: true\n        });\n        \n        // 这里调用删除圈子的API\n        // deleteCircle(this.circleInfo.id).then(res => {\n        //   uni.hideLoading();\n        //   if (res.status === 200) {\n        //     this.opTipsPopup('删除成功', true);\n        //   } else {\n        //     this.handleError(res, '删除失败');\n        //   }\n        // }).catch(err => {\n        //   uni.hideLoading();\n        //   console.error('删除圈子失败:', err);\n        //   this.handleError(err, '网络错误，请稍后重试');\n        // });\n        \n        // 临时模拟成功\n        setTimeout(() => {\n          uni.hideLoading();\n          uni.showToast({\n            title: '删除成功',\n            icon: 'success'\n          });\n          setTimeout(() => {\n            this.navBack();\n          }, 1500);\n        }, 1000);\n      },\n      \n      // 点赞回调\n      likeClick(e) {\n        if (!e || !this.list || !this.list[e.idx]) return;\n        \n        this.list[e.idx].is_like = e.is_like;\n        this.list[e.idx].like_count = e.like_count;\n        \n        // 更新like_count_str\n        this.list[e.idx].like_count_str = String(e.like_count);\n      },\n      \n      // 返回上一页\n      navBack() {\n        if (getCurrentPages().length > 1) {\n          uni.navigateBack();\n        } else {\n          uni.switchTab({\n            url: \"/pages/index/index\"\n          });\n        }\n      },\n      \n      // 统一错误处理方法\n      handleError(error, defaultMessage = '操作失败') {\n        console.error('错误处理:', error);\n\n        let message = defaultMessage;\n\n        // 如果是字符串，直接使用\n        if (typeof error === 'string') {\n          message = error;\n        }\n        // 如果是对象，尝试获取错误信息\n        else if (error && typeof error === 'object') {\n          // 网络错误处理\n          if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network')) {\n            message = '网络连接异常，请检查网络设置';\n          }\n          // 超时错误处理\n          else if (error.code === 'TIMEOUT' || error.message?.includes('timeout')) {\n            message = '请求超时，请稍后重试';\n          }\n          // 其他错误\n          else {\n            message = error.msg || error.message || error.data?.msg || defaultMessage;\n          }\n        }\n\n        this.opTipsPopup(message);\n        return message;\n      },\n\n      // 提示弹窗\n      opTipsPopup(msg, isBack = false) {\n        let that = this;\n        that.tipsTitle = msg;\n        that.$refs.tipsPopup.open();\n\n        setTimeout(function() {\n          that.$refs.tipsPopup.close();\n\n          if (isBack) {\n            that.navBack();\n          }\n        }, 2000);\n      },\n\n      // 刷新列表数据\n      fetchList() {\n        this.page = 1;\n        this.getCircleDynamic();\n      },\n      onCardUpdate({ vote_info, idx }) {\n        if (this.list[idx]) {\n          this.$set(this.list[idx], 'vote_info', vote_info);\n        }\n      }\n    },\n    \n    onReachBottom() {\n      // 如果当前状态是加载中，不触发加载更多\n      if (this.loadStatus === \"loading\") {\n        return;\n      }\n      \n      // 如果未到达最大数量，加载更多\n      if (this.isThrottling && this.list.length && this.list.length < this.totalCount) {\n        this.page = this.page + 1;\n        this.loadStatus = \"loading\";\n        this.getCircleDynamic();\n      } else if (this.list.length >= this.totalCount && this.list.length > 0) {\n        this.loadStatus = \"noMore\";\n      }\n    },\n    \n    onPageScroll(e) {\n      const scrollTop = e.scrollTop > 150 ? 150 : e.scrollTop;\n      this.navbarTrans = scrollTop / 150;\n    },\n    \n    onUnload() {\n      // 组件卸载时清除定时器\n      if (this.loadingTimer) {\n        clearTimeout(this.loadingTimer);\n        this.loadingTimer = null;\n      }\n      if (this.debounceTimer) {\n        clearTimeout(this.debounceTimer);\n        this.debounceTimer = null;\n      }\n    },\n    \n    // 分享到微信好友\n    onShareAppMessage() {\n      return {\n        title: this.circleInfo.circle_name || '圈子分享',\n        path: \"/pages/note/circle?id=\" + this.circleId,\n        imageUrl: this.circleInfo.circle_avatar || '/static/img/avatar.png'\n      };\n    },\n    \n    // 分享到朋友圈\n    onShareTimeline() {\n      return {\n        title: this.circleInfo.circle_name || '圈子分享',\n        query: \"id=\" + this.circleId,\n        imageUrl: this.circleInfo.circle_avatar || '/static/img/avatar.png'\n      };\n    }\n  }\n  </script>\n  \n  <style>\n  /* 基础样式 */\n  .nav-box {\n    position: fixed;\n    top: 0;\n    left: 0;\n    width: 100%;\n    z-index: 99;\n    box-sizing: border-box;\n  }\n  \n  .nav-box .nav-back {\n    padding: 0 30rpx;\n    width: 34rpx;\n    height: 100%;\n  }\n  \n  .nav-box .nav-title {\n    max-width: 60%;\n    font-size: 32rpx;\n    font-weight: 700;\n  }\n  \n  /* 圈子头部区域 - 参考商品轮播图设计 */\n  .circle-header {\n    position: relative;\n    overflow: hidden;\n  }\n  \n  /* 主背景容器 - 参考商品轮播图样式 */\n  .images-box {\n    width: 100%;\n    flex-direction: column;\n    position: relative;\n    background: #f8f8f8;\n    border-radius: 0 0 24rpx 24rpx;\n    overflow: hidden;\n  }\n  \n  /* 圈子背景图片容器 - 沉浸式适中高度 */\n  .circle-image-container {\n    position: relative;\n    width: 100%;\n    height: 600rpx; /* 500rpx + 状态栏和导航栏高度 */\n    background: #f8f8f8;\n    overflow: hidden;\n  }\n  \n  /* 圈子背景图片 */\n  .circle-bg-image {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    transition: transform 0.3s ease;\n    will-change: transform; /* 优化动画性能 */\n  }\n\n  /* 默认背景 */\n  .default-bg {\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  }\n  \n  /* 图片点击效果 */\n  .circle-image-container:active .circle-bg-image {\n    transform: scale(0.98);\n  }\n  \n  /* 装饰指示器 - 参考商品轮播图指示器 */\n  .circle-indicator {\n    position: absolute;\n    bottom: 30rpx;\n    right: 30rpx;\n    display: flex;\n    align-items: center;\n    padding: 8rpx 16rpx;\n    background: rgba(255, 255, 255, 0.2);\n    border-radius: 20rpx;\n    backdrop-filter: blur(10rpx);\n    -webkit-backdrop-filter: blur(10rpx);\n  }\n  \n  .indicator-dot {\n    width: 12rpx;\n    height: 12rpx;\n    border-radius: 50%;\n    background: rgba(255, 255, 255, 0.5);\n    transition: all 0.3s ease;\n  }\n  \n  .indicator-dot.active {\n    background: #fff;\n    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\n  }\n  \n  /* 沉浸式背景遮罩层 */\n  .bg-overlay {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background: \n      /* 主要渐变 - 从透明到深色 */\n      linear-gradient(to bottom, \n        transparent 0%, \n        rgba(0,0,0,0.1) 30%, \n        rgba(0,0,0,0.4) 70%, \n        rgba(0,0,0,0.8) 100%\n      ),\n      /* 径向渐变 - 增强中心聚焦效果 */\n      radial-gradient(circle at center bottom, \n        rgba(0,0,0,0.2) 0%, \n        rgba(0,0,0,0.6) 70%, \n        rgba(0,0,0,0.9) 100%\n      ),\n      /* 顶部微妙渐变 - 增强层次感 */\n      linear-gradient(to bottom,\n        rgba(0,0,0,0.1) 0%,\n        transparent 25%,\n        transparent 75%,\n        rgba(0,0,0,0.2) 100%\n      );\n    \n    /* 现代模糊效果 - 增强沉浸感 */\n    backdrop-filter: blur(1px) saturate(180%);\n    -webkit-backdrop-filter: blur(1px) saturate(180%);\n    \n    /* 微妙的边缘阴影 */\n    box-shadow: \n      inset 0 -80rpx 120rpx -40rpx rgba(0,0,0,0.4),\n      inset 0 80rpx 120rpx -40rpx rgba(0,0,0,0.1);\n    \n    /* 平滑过渡效果 */\n    transition: all 0.3s ease-out;\n  }\n  \n  /* 悬停时增强效果（如果需要交互） */\n  .circle-header:hover .bg-overlay {\n    backdrop-filter: blur(2px) saturate(200%);\n    -webkit-backdrop-filter: blur(2px) saturate(200%);\n  }\n  \n  /* 圈子信息叠加层 - 居中显示内容 */\n  .circle-info-overlay {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    padding: 40rpx 30rpx 30rpx;\n    box-sizing: border-box;\n    display: flex;\n    flex-direction: column;\n    justify-content: center; /* 垂直居中 */\n  }\n  \n  .circle-main-info {\n    align-items: flex-end;\n    margin-bottom: 20rpx;\n  }\n  \n  /* 圈子头像 */\n  .circle-avatar {\n    margin-right: 20rpx;\n  }\n  \n  .avatar-img {\n    width: 120rpx;\n    height: 120rpx;\n    border-radius: 16rpx;\n    border: 4rpx solid rgba(255,255,255,0.3);\n  }\n  \n  /* 圈子详情 */\n  .circle-details {\n    flex: 1;\n  }\n  \n  .circle-name {\n    color: #fff;\n    font-size: 36rpx;\n    font-weight: bold;\n    margin-bottom: 10rpx;\n    text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);\n  }\n  \n  .circle-stats {\n    color: rgba(255,255,255,0.9);\n    font-size: 22rpx;\n    line-height: 1.4;\n  }\n  \n  .stat-item {\n    font-size: 22rpx;\n    font-weight: 400;\n    text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);\n  }\n  \n  .stat-divider {\n    margin: 0 8rpx;\n    font-size: 22rpx;\n  }\n  \n  /* 圈子描述和公告 */\n  .circle-description,\n  .circle-notice {\n    color: rgba(255,255,255,0.9);\n    font-size: 24rpx;\n    line-height: 1.4;\n    margin-bottom: 15rpx;\n    text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);\n  }\n  \n  .circle-notice {\n    color: #ffeb3b;\n  }\n  \n  /* 操作按钮 - 参考 pages/note/1.vue 设计 */\n  .btn-box {\n    width: 100%;\n    height: 80rpx;\n    justify-content: space-between;\n    margin-top: 30rpx;\n  }\n\n  .btn-box .btn {\n    height: 80rpx;\n    font-size: 24rpx;\n    font-weight: 700;\n    justify-content: center;\n    border-radius: 80rpx;\n    transition: all 0.3s ease;\n    transform: translateZ(0); /* 启用硬件加速 */\n    will-change: transform, background-color;\n  }\n\n  .btn-box .btn:active {\n    transform: scale(0.98);\n  }\n\n  .btn-box .btn .icon {\n    width: 32rpx;\n    height: 32rpx;\n  }\n\n  .btn-box .btn text {\n    margin: 0 12rpx;\n    white-space: nowrap;\n  }\n\n  .btn-box .bg1 {\n    width: calc(100% - 100rpx);\n    color: #000;\n    background: #fff;\n  }\n\n  .btn-box .bg2 {\n    width: 80rpx;\n    background: #f8f8f8;\n  }\n\n  .btn-box .joined-state {\n    color: #000;\n    background: #f8f8f8;\n    width: calc(50% - 10rpx);\n  }\n\n  .btn-box .share-expanded {\n    width: calc(50% - 10rpx);\n  }\n  \n  /* 加载状态样式 */\n  .btn-box .loading {\n    background: #ccc;\n    color: transparent;\n    pointer-events: none;\n  }\n  \n  .loading-dots {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8rpx;\n  }\n  \n  .loading-dots .dot {\n    width: 8rpx;\n    height: 8rpx;\n    border-radius: 50%;\n    background: #999;\n    animation: loadingDot 1.4s infinite ease-in-out;\n  }\n  \n  .loading-dots .dot:nth-child(1) {\n    animation-delay: -0.32s;\n  }\n  \n  .loading-dots .dot:nth-child(2) {\n    animation-delay: -0.16s;\n  }\n  \n  @keyframes loadingDot {\n    0%, 80%, 100% {\n      transform: scale(0.8);\n      opacity: 0.5;\n    }\n    40% {\n      transform: scale(1);\n      opacity: 1;\n    }\n  }\n  \n  /* 骨架屏样式 */\n  .skeleton-text {\n    width: 200rpx;\n    height: 36rpx;\n    background: linear-gradient(90deg, rgba(255,255,255,0.3) 25%, rgba(255,255,255,0.6) 50%, rgba(255,255,255,0.3) 75%);\n    background-size: 200% 100%;\n    border-radius: 6rpx;\n    animation: skeletonLoading 1.5s infinite;\n  }\n  \n  .skeleton-stats {\n    display: flex;\n    gap: 16rpx;\n  }\n  \n  .skeleton-item {\n    width: 80rpx;\n    height: 22rpx;\n    background: linear-gradient(90deg, rgba(255,255,255,0.3) 25%, rgba(255,255,255,0.6) 50%, rgba(255,255,255,0.3) 75%);\n    background-size: 200% 100%;\n    border-radius: 4rpx;\n    animation: skeletonLoading 1.5s infinite;\n  }\n  \n  @keyframes skeletonLoading {\n    0% {\n      background-position: -200% 0;\n    }\n    100% {\n      background-position: 200% 0;\n    }\n  }\n  \n  /* 头像骨架屏 */\n  .skeleton-avatar {\n    width: 120rpx;\n    height: 120rpx;\n    border-radius: 16rpx;\n    background: linear-gradient(90deg, rgba(255,255,255,0.3) 25%, rgba(255,255,255,0.6) 50%, rgba(255,255,255,0.3) 75%);\n    background-size: 200% 100%;\n    animation: skeletonLoading 1.5s infinite;\n  }\n  \n  /* 成员头像组样式 */\n  .cu-img-group {\n    direction: ltr;\n    unicode-bidi: bidi-override;\n    display: inline-block;\n  }\n\n  .cu-img-group .cu-img {\n    width: 32rpx;\n    height: 32rpx;\n    display: inline-flex;\n    position: relative;\n    margin-left: -16rpx;\n    border: 4rpx solid #000;\n    background: #111;\n    vertical-align: middle;\n    border-radius: 50%;\n  }\n\n  .cu-img-group .cu-img image {\n    width: 100%;\n    height: 100%;\n    border-radius: 50%;\n  }\n\n  \n  /* 分类标签 - 适应沉浸式设计 */\n  .bar-box {\n    position: sticky;\n    top: 0;\n    z-index: 98;\n    width: 100%;\n    height: 80rpx;\n    background: #fff;\n    border-bottom: 1rpx solid #f0f0f0;\n    margin-top: -24rpx; /* 抵消圆角造成的间隙 */\n    border-radius: 24rpx 24rpx 0 0; /* 与图片底部圆角呼应 */\n    backdrop-filter: blur(10rpx); /* 毛玻璃效果 */\n    -webkit-backdrop-filter: blur(10rpx);\n  }\n  \n  .bar-box .bar-item {\n    padding: 0 30rpx;\n    height: 100%;\n    flex-direction: column;\n    justify-content: center;\n    position: relative;\n  }\n  \n  .bar-box .bar-item text {\n    font-weight: 700;\n    transition: all 0.3s ease-in-out;\n  }\n  \n  .bar-item .bar-line {\n    position: absolute;\n    bottom: 12rpx;\n    width: 18rpx;\n    height: 6rpx;\n    border-radius: 6rpx;\n    background: #000;\n    transition: opacity 0.3s ease-in-out;\n  }\n  \n  /* 内容区域 */\n  .content-area {\n    min-height: 600rpx;\n    background: #fff;\n  }\n  \n  .dynamic-box {\n    width: calc(100% - 16rpx);\n    padding: 22rpx 8rpx 0;\n  }\n  \n  /* 加载中状态样式 */\n  .loading-container {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    height: 60rpx;\n    margin: 20rpx 0;\n  }\n  \n  .loading-indicator {\n    width: 30rpx;\n    height: 30rpx;\n    border: 3rpx solid #f3f3f3;\n    border-top: 3rpx solid #000;\n    border-radius: 50%;\n    animation: spin 1s linear infinite;\n  }\n  \n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n  \n  /* 空状态 */\n  .empty-box {\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    padding: 100rpx 0;\n  }\n  \n  .empty-box image {\n    width: 200rpx;\n    height: 200rpx;\n    margin-bottom: 30rpx;\n  }\n  \n  .empty-box .e1 {\n    font-size: 28rpx;\n    font-weight: bold;\n    margin-bottom: 10rpx;\n  }\n  \n  .empty-box .e2 {\n    font-size: 24rpx;\n    color: #999;\n  }\n  \n  /* 提示弹窗 */\n  .tips-box {\n    padding: 20rpx 30rpx;\n    border-radius: 12rpx;\n    justify-content: center;\n  }\n  \n  .tips-box .tips-item {\n    color: #fff;\n    font-size: 28rpx;\n    font-weight: 700;\n  }\n  \n  /* 工具类 */\n  .df {\n    display: flex;\n    align-items: center;\n  }\n  \n  .ohto {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n  .rule-box {\n    background: #fff;\n    padding: 30rpx 30rpx 0 30rpx;\n  }\n  .rule-title {\n    font-size: 28rpx;\n    font-weight: bold;\n    margin-bottom: 20rpx;\n  }\n  .rule-list {\n    margin-bottom: 30rpx;\n  }\n  .rule-item {\n    display: flex;\n    align-items: flex-start;\n    margin-bottom: 16rpx;\n  }\n  .rule-tag {\n    font-size: 20rpx;\n    color: #fff;\n    border-radius: 8rpx;\n    padding: 4rpx 16rpx;\n    margin-right: 12rpx;\n    margin-top: 2rpx;\n    height: 32rpx;\n    line-height: 32rpx;\n    flex-shrink: 0; /* 防止标签被压缩 */\n    white-space: nowrap; /* 防止标签文字换行 */\n  }\n  .tag-orange {\n    background: #ff9500;\n  }\n  .rule-text {\n    font-size: 22rpx;\n    color: #333;\n    line-height: 32rpx;\n    flex: 1; /* 占据剩余空间 */\n    word-break: break-all; /* 允许长单词换行 */\n    word-wrap: break-word; /* 兼容性更好的换行 */\n  }\n  .rule-team-title {\n    margin: 30rpx 0 10rpx 0;\n    font-size: 28rpx;\n    font-weight: bold;\n    justify-content: space-between;\n  }\n  .rule-team-all {\n  margin-left:12rpx;\n  padding:0 12rpx;\n  height:40rpx;\n  line-height:40rpx;\n  font-size:20rpx;\n  font-weight:700;\n  color:#999;\n  background:#f8f8f8;\n  border-radius:8rpx\n  }\n  .rule-team-list {\n    margin-bottom: 30rpx;\n  }\n  .member-item {\n    padding: 0 30rpx 0 30rpx;\n    margin-bottom: 24rpx;\n    display: flex;\n    align-items: center;\n    position: relative;\n  }\n  .avatar {\n    width: 56rpx;\n    height: 56rpx;\n    border-radius: 50%;\n    margin-right: 16rpx;\n    background: #f5f5f5;\n  }\n  .info {\n    flex: 1;\n    min-width: 0;\n  }\n  .name {\n    font-size: 26rpx;\n    font-weight: 700;\n    color: #222;\n    display: flex;\n    align-items: center;\n  }\n  .role-tag {\n    font-size: 20rpx;\n    border-radius: 8rpx;\n    padding: 2rpx 14rpx;\n    margin-left: 12rpx;\n    color: #fff;\n    font-weight: 400;\n  }\n  .role-tag.owner {\n    background: #ff9500;\n  }\n  .role-tag.admin {\n    background: #3da0ff;\n  }\n  .meta {\n    margin-top: 6rpx;\n    font-size: 22rpx;\n    color: #999;\n    display: flex;\n    align-items: center;\n  }\n  .gender {\n    margin-right: 6rpx;\n  }\n  .male {\n    color: #4e6ef2;\n  }\n  .female {\n    color: #fa5a8a;\n  }\n  .contrib {\n    color: #999;\n  }\n  </style>", "import MiniProgramPage from 'Z:/WWW/shejiao/vue3/pages/note/circle.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "getCircleDetail", "getCircleDynamicList", "getCircleMemberList", "exitCircle", "joinCircle"], "mappings": ";;;;;AA4OE,MAAA,cAAA,MAAA;;;;AAMA,MAAA,MAAA;;;IAII;AAAA;IAEA;AAAA,IACA;AAAA;;EAGF,OAAA;AACE,WAAA;AAAA;;;;MAME,QAAA;AAAA;;;QAIE,aAAA;AAAA,QACA,eAAA;AAAA,QACA,mBAAA;AAAA,QACA,oBAAA;AAAA,QACA,eAAA;AAAA,QACA,cAAA;AAAA,QACA,eAAA;AAAA;QAEA,WAAA;AAAA;QAEA,gBAAA,CAAA;AAAA;MAEF,MAAA,CAAA;AAAA,MACA,MAAA;AAAA,MACA,OAAA;AAAA;MAEA,cAAA;AAAA;MAEA,YAAA;AAAA;MAEA,aAAA;AAAA;;MAEA,cAAA;AAAA,MACA,eAAA;AAAA,MACA,WAAA;AAAA,MACA,QAAA;AAAA;;;;MAIA,gBAAA;AAAA,IACF;AAAA;EAEF,UAAA;AAAA;AAAA,IAEE,kBAAA;AACE,aAAA;AAAA,QACE,IAAA,KAAA,WAAA,MAAA,KAAA;AAAA;;;QAGA,SAAA,KAAA,WAAA,sBAAA,KAAA,WAAA;AAAA,QACA,OAAA,KAAA,WAAA;AAAA;;;;IAIJ;AAAA;EAEF,OAAA,SAAA;AAGEA,kBAAA,MAAA,cAAA;AAIA,UAAA,WAAAA,cAAAA,MAAA,eAAA,UAAA;AACA,UAAA,UAAA,KAAA,OAAA,MAAA,IAAA;;AAKA,QAAA,QAAA,IAAA;AACE,WAAA,WAAA,SAAA,QAAA,EAAA;AACA,WAAA,WAAA,KAAA,KAAA;AACA,WAAA,WAAA,UAAA,KAAA,QAAA;AAGA,WAAA,WAAA;;;AAIA,WAAA,YAAA,WAAA,IAAA;AAAA,IACF;AAAA;EAEF,SAAA;AAAA;AAAA;AAGI,UAAA;AACE,cAAA,SAAAA,cAAA,MAAA,eAAA,KAAA,QAAA;AACA,cAAA,MAAA,KAAA;AAGA,YAAA,UAAA,OAAA,aAAA,MAAA,OAAA,YAAA,IAAA,KAAA,KAAA;;AAEE,eAAA,iBAAA,OAAA;AAAA,QACF;AAAA,MACF,SAAA,GAAA;;MAEA;AAAA;;IAIF,YAAA,MAAA;AACE,UAAA;AACE,cAAA,YAAA;AAAA,UACE;AAAA,UACA,WAAA,KAAA,IAAA;AAAA;AAEFA,sBAAAA,MAAA,eAAA,KAAA,UAAA,SAAA;AACA,aAAA,iBAAA,UAAA;AAAA,MACF,SAAA,GAAA;;MAEA;AAAA;;IAIF,qBAAA;AACE,UAAA,KAAA,WAAA,qBAAA,KAAA,WAAA,eAAA;AACE,cAAA,WAAA,KAAA,WAAA,qBAAA,KAAA,WAAA;AACAA,sBAAAA,MAAA,aAAA;AAAA,UACE,SAAA;AAAA,UACA,MAAA,CAAA,QAAA;AAAA,QACF,CAAA;AAAA,MACF;AAAA;;;AAKA,UAAA;AACE,aAAA,cAAA;AAEA,cAAA,MAAA,MAAAC,WAAAA,gBAAA,KAAA,QAAA;AAEA,YAAA,IAAA,WAAA,OAAA,IAAA,MAAA;AAEE,gBAAA,gBAAA;AAAA,YACE,GAAA,KAAA;AAAA;;;;AAOF,eAAA,YAAA,aAAA;AAGA,gBAAA,QAAA,IAAA;AAAA;YAEE,KAAA,gBAAA;AAAA,UACF,CAAA;AAAA;AAEA,eAAA,YAAA,IAAA,OAAA,YAAA,IAAA;AAAA,QACF;AAAA;AAEAD,sBAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,GAAA;AACA,aAAA,YAAA,KAAA,YAAA;AAEA,mBAAA,MAAA;AACEA,wBAAA,MAAA,aAAA;AAAA,QACF,GAAA,GAAA;AAAA,MACF,UAAA;;MAEA;AAAA;;;;;AASA,UAAA,KAAA,cAAA;AACE,qBAAA,KAAA,YAAA;AAAA,MACF;AACA,WAAA,eAAA,WAAA,MAAA;AACE,YAAA,KAAA,eAAA,WAAA;AACE,eAAA,cAAA;AAAA,QACF;AAAA,MACF,GAAA,GAAA;;QAIE,MAAA,KAAA;AAAA,QACA,OAAA,KAAA;AAAA;;;QAGA,KAAA,KAAA;AAAA;AAAA;AAKF,UAAA;AAEE,cAAA,MAAA,MAAAE,gCAAA,MAAA;AAEA,YAAA,IAAA,WAAA,OAAA,IAAA,MAAA;;;;cAMM,GAAA;AAAA;cAEA,aAAA,KAAA,WAAA;AAAA,cACA,eAAA,KAAA,WAAA;AAAA,YACF,EAAA;AAEA,gBAAA,KAAA,QAAA,GAAA;;;AAGE,mBAAA,OAAA,KAAA,KAAA,OAAA,aAAA;AAAA,YACF;AAGA,gBAAA,aAAA,UAAA,QAAA;AACE,mBAAA,aAAA,aAAA;AAAA,YACF;AAEA,iBAAA,UAAA;AAAA,UACF,WAAA,KAAA,QAAA,GAAA;AACE,iBAAA,UAAA;;UAEF;;;UAKA;AAAA;AAEA,cAAA,KAAA,QAAA,GAAA;AACE,iBAAA,UAAA;;UAEF;AAAA,QAEF;AAAA;AAEA,YAAA,KAAA,QAAA,GAAA;AACE,eAAA,UAAA;;QAEF;AAEAF,sBAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,GAAA;AAGA,YAAA,KAAA,SAAA,GAAA;AACE,eAAA,YAAA,KAAA,gBAAA;AAAA,QACF;AAAA,MACF,UAAA;;;AAKE,YAAA,KAAA,cAAA;AACE,uBAAA,KAAA,YAAA;;QAEF;;MAEF;AAAA;;;AAIA,UAAA;AAGE,cAAA,MAAA,MAAAG,+BAAA;AAAA;UAEE,MAAA;AAAA,UACA,OAAA;AAAA,QACF,CAAA;AAIA,YAAA,IAAA,WAAA,OAAA,IAAA,QAAA,IAAA,KAAA,MAAA;AAEE,gBAAA,YAAA,IAAA,KAAA,KAAA,OAAA,YAAA,OAAA,cAAA,KAAA,OAAA,cAAA,CAAA;AACA,eAAA,eAAA,UAAA,KAAA,CAAA,GAAA,MAAA;AAEE,gBAAA,EAAA,cAAA,KAAA,EAAA,cAAA;AAAA,qBAAA;AACA,gBAAA,EAAA,cAAA,KAAA,EAAA,cAAA;AAAA,qBAAA;AACA,mBAAA;AAAA,UACF,CAAA;AAAA,QACF;AAAA,MACF,SAAA,GAAA;AACEH,sBAAA,MAAA,MAAA,SAAA,gCAAA,eAAA,CAAA;AAEA,aAAA,YAAA,GAAA,YAAA;AAAA,MACF;AAAA;;;AAKA,YAAA,WAAAA,cAAAA,MAAA,eAAA,UAAA;AACA,YAAA,UAAA,KAAA,OAAA,MAAA,IAAA;;AAGA,UAAA,CAAA,eAAA;;AAEE;AAAA,MACF;AAEA,UAAA;AACEA,sBAAAA,MAAA,YAAA;AAAA;UAEE,MAAA;AAAA,QACF,CAAA;AAEA,cAAA,YAAA,KAAA,WAAA,YAAAI,WAAAA,aAAAC,WAAAA;;;;AAKA,cAAA,MAAA,MAAA,UAAA,MAAA;AAEA,YAAA,IAAA,WAAA,KAAA;;;AAMI,iBAAA,WAAA,gBAAA;;;AAGA,iBAAA,WAAA,eAAA,KAAA,IAAA,GAAA,KAAA,WAAA,eAAA,CAAA;;UAEF;AAGA,cAAA,WAAA,eAAA;AAAA;AAEA,eAAA,YAAA,IAAA,OAAA,UAAA;AAAA,QACF;AAAA;AAEAL,sBAAA,MAAA,MAAA,SAAA,gCAAA,WAAA,GAAA;AACA,aAAA,YAAA,KAAA,YAAA;AAAA,MACF,UAAA;AACEA,sBAAA,MAAA,YAAA;AAAA,MACF;AAAA;;;;AAMA,UAAA,aAAA,KAAA;AAAA;;AAIE,qBAAA,KAAA,aAAA;AAAA,MACF;AAEA,WAAA,SAAA;AAEA,UAAA,WAAA,KAAA,QAAA,SAAA,GAAA;AAEE,aAAA,gBAAA,WAAA,MAAA;;;;;;QAMA,GAAA,GAAA;AAAA,MACF;AAAA;IAEF,iBAAA;AAEEA,oBAAA,MAAA,WAAA,EAAA,KAAA,iCAAA,KAAA,SAAA,CAAA;AAAA;;IAGF,gBAAA,KAAA;AACEA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA,0BAAA,GAAA;AAAA,MACF,CAAA;AAAA;;;AAIA,UAAA,CAAA;AAAA,eAAA;;;;;AAKA,aAAA,GAAA,IAAA,IAAA,KAAA,IAAA,GAAA;AAAA;;IAIF,WAAA,QAAA;AACE,WAAA,YAAA;AAAA;;;AAKA,WAAA,YAAA;AAAA;;IAIF,YAAA,MAAA;AACEA,0BAAA,MAAA,OAAA,gCAAA,SAAA,MAAA,KAAA,UAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA;;MAGA,CAAA;AACA,WAAA,YAAA;AAAA;;;AAKAA,oBAAA,MAAA,MAAA,OAAA,gCAAA,aAAA,QAAA;AACA,WAAA,YAAA;AAAA;;;AAKAA,oBAAA,MAAA,MAAA,OAAA,gCAAA,SAAA,UAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA;;MAGA,CAAA;AACA,WAAA,YAAA;AAAA;;IAIF,WAAA,UAAA;AACEA,oBAAA,MAAA,MAAA,OAAA,gCAAA,SAAA,QAAA;AAEAA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA,yBAAA,QAAA;AAAA,MACF,CAAA;AACA,WAAA,YAAA;AAAA;;;AAKAA,oBAAA,MAAA,MAAA,OAAA,gCAAA,SAAA,QAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA;;;QAIE,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;;UAGA;AAAA,QACF;AAAA,MACF,CAAA;AACA,WAAA,YAAA;AAAA;;IAIF,eAAA;AACEA,oBAAAA,MAAA,YAAA;AAAA,QACE,OAAA;AAAA,QACA,MAAA;AAAA,MACF,CAAA;AAiBA,iBAAA,MAAA;AACEA,sBAAA,MAAA,YAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA;;QAGA,CAAA;AACA,mBAAA,MAAA;AACE,eAAA,QAAA;AAAA,QACF,GAAA,IAAA;AAAA,MACF,GAAA,GAAA;AAAA;;;AAKA,UAAA,CAAA,KAAA,CAAA,KAAA,QAAA,CAAA,KAAA,KAAA,EAAA,GAAA;AAAA;AAEA,WAAA,KAAA,EAAA,GAAA,EAAA,UAAA,EAAA;AACA,WAAA,KAAA,EAAA,GAAA,EAAA,aAAA,EAAA;;;;IAOF,UAAA;AACE,UAAA,gBAAA,EAAA,SAAA,GAAA;AACEA,sBAAA,MAAA,aAAA;AAAA;AAEAA,sBAAAA,MAAA,UAAA;AAAA;QAEA,CAAA;AAAA,MACF;AAAA;;IAIF,YAAA,OAAA,iBAAA,QAAA;;AACEA,oBAAA,MAAA,MAAA,SAAA,gCAAA,SAAA,KAAA;;;;MAOA,WAEA,SAAA,OAAA,UAAA,UAAA;AAEE,YAAA,MAAA,SAAA,qBAAA,WAAA,YAAA,mBAAA,SAAA,aAAA;;QAEA,WAEA,MAAA,SAAA,eAAA,WAAA,YAAA,mBAAA,SAAA,aAAA;AACE,oBAAA;AAAA,QACF;AAGE,oBAAA,MAAA,OAAA,MAAA,aAAA,WAAA,SAAA,mBAAA,QAAA;AAAA,QACF;AAAA,MACF;;;;;;;AASA,WAAA,YAAA;;AAGA,iBAAA,WAAA;;;AAII,eAAA,QAAA;AAAA,QACF;AAAA,MACF,GAAA,GAAA;AAAA;;;;;;;AASA,UAAA,KAAA,KAAA,GAAA,GAAA;;MAEA;AAAA,IACF;AAAA;EAGF,gBAAA;AAEE,QAAA,KAAA,eAAA,WAAA;AACE;AAAA,IACF;AAGA,QAAA,KAAA,gBAAA,KAAA,KAAA,UAAA,KAAA,KAAA,SAAA,KAAA,YAAA;AACE,WAAA,OAAA,KAAA,OAAA;;;IAGF,WAAA,KAAA,KAAA,UAAA,KAAA,cAAA,KAAA,KAAA,SAAA,GAAA;;IAEA;AAAA;EAGF,aAAA,GAAA;;AAEE,SAAA,cAAA,YAAA;AAAA;;AAKA,QAAA,KAAA,cAAA;AACE,mBAAA,KAAA,YAAA;;IAEF;;AAEE,mBAAA,KAAA,aAAA;;IAEF;AAAA;;EAIF,oBAAA;AACE,WAAA;AAAA,MACE,OAAA,KAAA,WAAA,eAAA;AAAA,MACA,MAAA,2BAAA,KAAA;AAAA,MACA,UAAA,KAAA,WAAA,iBAAA;AAAA;;;EAKJ,kBAAA;AACE,WAAA;AAAA,MACE,OAAA,KAAA,WAAA,eAAA;AAAA;MAEA,UAAA,KAAA,WAAA,iBAAA;AAAA;EAEJ;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC11BF,GAAG,WAAW,eAAe;"}