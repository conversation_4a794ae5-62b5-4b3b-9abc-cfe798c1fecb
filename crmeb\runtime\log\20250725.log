2025-07-25 00:24:02|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 00:24:02|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 00:24:02|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 00:24:02|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:24:02|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 00:24:02|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 00:24:02|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 00:24:02|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 00:24:02|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 00:24:02|info|我的动态列表返回数据条数: 10
2025-07-25 00:24:04|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 00:24:04|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 00:24:04|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 00:24:04|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:24:04|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 00:24:04|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 00:24:04|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 00:24:04|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 00:24:04|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 00:24:04|info|我的动态列表返回数据条数: 10
2025-07-25 00:24:04|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 00:24:04|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 00:24:04|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 00:24:04|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:24:04|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 00:24:04|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 00:24:04|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 00:24:04|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 00:24:04|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 00:24:04|info|我的动态列表返回数据条数: 10
2025-07-25 00:24:05|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 00:24:05|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 00:24:05|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 00:24:05|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:24:05|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 00:24:05|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 00:24:05|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 00:24:05|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 00:24:05|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 00:24:05|info|我的动态列表返回数据条数: 10
2025-07-25 00:26:15|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 00:26:15|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 00:26:15|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 00:26:15|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:26:15|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 00:26:15|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 00:26:15|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 00:26:15|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 00:26:15|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 00:26:15|info|我的动态列表返回数据条数: 10
2025-07-25 00:26:30|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 00:26:30|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:26:30|info|where条件元素: key=status, type=integer, value=1
2025-07-25 00:26:30|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 00:26:30|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 00:26:30|info|handleDynamicList处理 - 用户ID:0, 动态数量:10
2025-07-25 00:26:30|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 00:26:30|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 00:26:30|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 00:26:30|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 00:26:30|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:26:30|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 00:26:30|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:26:30|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:26:30|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:26:30|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:26:30|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:26:30|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 00:26:30|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 00:26:30|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:26:54|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 00:26:54|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 00:26:54|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 00:26:54|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:26:54|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 00:26:54|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 00:26:54|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 00:26:54|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 00:26:54|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 00:26:54|info|我的动态列表返回数据条数: 10
2025-07-25 00:26:57|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 00:26:57|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 00:26:57|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 00:26:57|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:26:57|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 00:26:57|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 00:26:57|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 00:26:57|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 00:26:57|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 00:26:57|info|我的动态列表返回数据条数: 10
2025-07-25 00:30:58|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 00:30:58|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 00:30:58|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 00:30:58|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:30:58|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 00:30:58|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 00:30:58|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 00:30:58|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 00:30:58|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 00:30:58|info|我的动态列表返回数据条数: 10
2025-07-25 00:31:10|info|获取点赞动态ID列表 - 用户ID:3, 页码:1, 每页数量:10, 点赞动态ID数量:8, 动态IDs:11,10,13,41,23,26,22,14
2025-07-25 00:31:10|info|直接查询验证结果: [{"id":10,"uid":3,"content":"************"},{"id":11,"uid":3,"content":"42342423432"},{"id":13,"uid":3,"content":"那年春天来得早，阳光四溢。\n连影子都是半透明的。那年春天来得早，阳光四溢。\n连影子都是半透明的。"},{"id":14,"uid":3,"content":"当你的灵魂变得宁静、和平、喜悦时，你的眼睛就有一种深度、清澈、纯洁、天真。它们变得如此透明，以至于你能够看到一个人的灵魂。"},{"id":23,"uid":3,"content":"1231545363643"},{"id":41,"uid":3,"content":"哈哈哈哈，我美不美"}]
2025-07-25 00:31:10|info|查询动态列表条件: {"id":["in",[11,10,13,41,23,26,22,14]]}, 排序: id desc
2025-07-25 00:31:10|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=8, order=id desc, where={"id":["in",[11,10,13,41,23,26,22,14]]}, extraFields=[]
2025-07-25 00:31:10|info|where条件元素: key=id, type=array, value=["in",[11,10,13,41,23,26,22,14]]
2025-07-25 00:31:10|info|DynamicContentDao.getDynamicList - 查询条件: {"id":["in",[11,10,13,41,23,26,22,14]]}
2025-07-25 00:31:10|info|DynamicContentDao.getDynamicList - 查询结果数量: 6
2025-07-25 00:31:10|info|handleDynamicList处理 - 用户ID:3, 动态数量:6
2025-07-25 00:31:10|info|用户点赞状态 - 用户ID:3, 已点赞动态数:6, 已点赞ID:10,11,13,14,23,41
2025-07-25 00:31:10|info|DynamicContentServices.getDynamicList - 处理后数据条数:6
2025-07-25 00:31:10|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 00:31:10|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:31:10|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:31:10|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:31:10|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:31:10|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:31:10|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:34:48|info|[ VIEW ] /volume1/迅雷/WWW/shejiao/crmeb/public/mobile.html [ array (
  0 => 'siteName',
  1 => 'siteUrl',
) ]
2025-07-25 00:34:48|info|[ VIEW ] /volume1/迅雷/WWW/shejiao/crmeb/public/index.html [ array (
) ]
2025-07-25 00:35:18|info|[ VIEW ] /volume1/迅雷/WWW/shejiao/crmeb/public/mobile.html [ array (
  0 => 'siteName',
  1 => 'siteUrl',
) ]
2025-07-25 00:35:18|info|[ VIEW ] /volume1/迅雷/WWW/shejiao/crmeb/public/index.html [ array (
) ]
2025-07-25 00:36:21|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 00:36:21|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:36:21|info|where条件元素: key=status, type=integer, value=1
2025-07-25 00:36:21|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 00:36:21|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 00:36:21|info|handleDynamicList处理 - 用户ID:0, 动态数量:10
2025-07-25 00:36:21|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 00:36:21|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 00:36:21|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 00:36:21|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 00:36:21|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:36:21|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 00:36:21|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:36:21|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:36:21|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:36:21|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:36:21|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:36:21|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 00:36:21|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 00:36:21|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:41:48|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 00:41:48|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:41:48|info|where条件元素: key=status, type=integer, value=1
2025-07-25 00:41:48|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 00:41:48|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 00:41:48|info|handleDynamicList处理 - 用户ID:0, 动态数量:10
2025-07-25 00:41:48|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 00:41:48|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 00:41:48|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 00:41:48|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 00:41:48|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:41:48|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 00:41:48|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:41:48|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:41:48|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:41:48|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:41:48|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:41:48|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 00:41:48|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 00:41:48|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:41:52|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=id desc, where={"uid":6,"is_show":1,"status":1}, extraFields=[]
2025-07-25 00:41:52|info|where条件元素: key=uid, type=integer, value=6
2025-07-25 00:41:52|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:41:52|info|where条件元素: key=status, type=integer, value=1
2025-07-25 00:41:52|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":6,"is_show":1,"status":1}
2025-07-25 00:41:52|info|DynamicContentDao.getDynamicList - 查询结果数量: 2
2025-07-25 00:41:52|info|handleDynamicList处理 - 用户ID:0, 动态数量:2
2025-07-25 00:41:52|info|DynamicContentServices.getDynamicList - 处理后数据条数:2
2025-07-25 00:41:52|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 00:41:52|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 00:41:52|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 00:41:52|info|=== getUserHomepage接口被调用 ===
2025-07-25 00:41:52|info|请求参数: {"user_id":"6"}
2025-07-25 00:41:52|info|请求头信息: {"user-agent":"Mozilla\/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/16.6 Mobile\/15E148 Safari\/604.1","authorization":"\u672a\u8bbe\u7f6e","token":"\u672a\u8bbe\u7f6e"}
2025-07-25 00:41:52|info|目标用户ID: 6
2025-07-25 00:41:52|info|成功获取当前登录用户ID: 0
2025-07-25 00:41:52|info|=== 访问记录检查开始 ===
2025-07-25 00:41:52|info|当前登录用户ID: 0
2025-07-25 00:41:52|info|目标用户ID: 6
2025-07-25 00:41:52|info|是否登录: 否
2025-07-25 00:41:52|info|是否查看自己: 否
2025-07-25 00:41:52|info|不记录访问 - 原因: 用户未登录
2025-07-25 00:41:52|info|=== 访问记录检查结束 ===
2025-07-25 00:41:53|info|获取点赞动态ID列表 - 用户ID:6, 页码:1, 每页数量:10, 点赞动态ID数量:1, 动态IDs:11
2025-07-25 00:41:53|info|直接查询验证结果: [{"id":11,"uid":3,"content":"42342423432"}]
2025-07-25 00:41:53|info|查询动态列表条件: {"id":["in",[11]],"is_show":1,"status":1}, 排序: id desc
2025-07-25 00:41:53|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=1, order=id desc, where={"id":["in",[11]],"is_show":1,"status":1}, extraFields=[]
2025-07-25 00:41:53|info|where条件元素: key=id, type=array, value=["in",[11]]
2025-07-25 00:41:53|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:41:53|info|where条件元素: key=status, type=integer, value=1
2025-07-25 00:41:53|info|DynamicContentDao.getDynamicList - 查询条件: {"id":["in",[11]],"is_show":1,"status":1}
2025-07-25 00:41:53|info|DynamicContentDao.getDynamicList - 查询结果数量: 1
2025-07-25 00:41:53|info|handleDynamicList处理 - 用户ID:0, 动态数量:1
2025-07-25 00:41:53|info|DynamicContentServices.getDynamicList - 处理后数据条数:1
2025-07-25 00:41:53|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 00:41:53|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:41:54|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=id desc, where={"uid":6,"is_show":1,"status":1}, extraFields=[]
2025-07-25 00:41:54|info|where条件元素: key=uid, type=integer, value=6
2025-07-25 00:41:54|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:41:54|info|where条件元素: key=status, type=integer, value=1
2025-07-25 00:41:54|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":6,"is_show":1,"status":1}
2025-07-25 00:41:54|info|DynamicContentDao.getDynamicList - 查询结果数量: 2
2025-07-25 00:41:54|info|handleDynamicList处理 - 用户ID:0, 动态数量:2
2025-07-25 00:41:54|info|DynamicContentServices.getDynamicList - 处理后数据条数:2
2025-07-25 00:41:54|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 00:41:54|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 00:41:54|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 00:42:00|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=id desc, where={"uid":1,"is_show":1,"status":1}, extraFields=[]
2025-07-25 00:42:00|info|where条件元素: key=uid, type=integer, value=1
2025-07-25 00:42:00|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:42:00|info|where条件元素: key=status, type=integer, value=1
2025-07-25 00:42:00|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":1,"is_show":1,"status":1}
2025-07-25 00:42:00|info|DynamicContentDao.getDynamicList - 查询结果数量: 0
2025-07-25 00:42:00|info|DynamicContentServices.getDynamicList - 处理后数据条数:0
2025-07-25 00:42:00|info|=== getUserHomepage接口被调用 ===
2025-07-25 00:42:00|info|请求参数: {"user_id":"1"}
2025-07-25 00:42:00|info|请求头信息: {"user-agent":"Mozilla\/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/16.6 Mobile\/15E148 Safari\/604.1","authorization":"\u672a\u8bbe\u7f6e","token":"\u672a\u8bbe\u7f6e"}
2025-07-25 00:42:00|info|目标用户ID: 1
2025-07-25 00:42:00|info|成功获取当前登录用户ID: 0
2025-07-25 00:42:00|info|=== 访问记录检查开始 ===
2025-07-25 00:42:00|info|当前登录用户ID: 0
2025-07-25 00:42:00|info|目标用户ID: 1
2025-07-25 00:42:00|info|是否登录: 否
2025-07-25 00:42:00|info|是否查看自己: 否
2025-07-25 00:42:00|info|不记录访问 - 原因: 用户未登录
2025-07-25 00:42:00|info|=== 访问记录检查结束 ===
2025-07-25 00:42:01|info|获取点赞动态ID列表 - 用户ID:1, 页码:1, 每页数量:10, 点赞动态ID数量:0, 动态IDs:
2025-07-25 00:42:02|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=id desc, where={"uid":1,"is_show":1,"status":1}, extraFields=[]
2025-07-25 00:42:02|info|where条件元素: key=uid, type=integer, value=1
2025-07-25 00:42:02|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:42:02|info|where条件元素: key=status, type=integer, value=1
2025-07-25 00:42:02|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":1,"is_show":1,"status":1}
2025-07-25 00:42:02|info|DynamicContentDao.getDynamicList - 查询结果数量: 0
2025-07-25 00:42:02|info|DynamicContentServices.getDynamicList - 处理后数据条数:0
2025-07-25 00:42:03|info|获取点赞动态ID列表 - 用户ID:1, 页码:1, 每页数量:10, 点赞动态ID数量:0, 动态IDs:
2025-07-25 00:42:05|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=id desc, where={"uid":1,"is_show":1,"status":1}, extraFields=[]
2025-07-25 00:42:05|info|where条件元素: key=uid, type=integer, value=1
2025-07-25 00:42:05|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:42:05|info|where条件元素: key=status, type=integer, value=1
2025-07-25 00:42:05|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":1,"is_show":1,"status":1}
2025-07-25 00:42:05|info|DynamicContentDao.getDynamicList - 查询结果数量: 0
2025-07-25 00:42:05|info|DynamicContentServices.getDynamicList - 处理后数据条数:0
2025-07-25 00:42:06|info|获取点赞动态ID列表 - 用户ID:1, 页码:1, 每页数量:10, 点赞动态ID数量:0, 动态IDs:
2025-07-25 00:42:07|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=id desc, where={"uid":1,"is_show":1,"status":1}, extraFields=[]
2025-07-25 00:42:07|info|where条件元素: key=uid, type=integer, value=1
2025-07-25 00:42:07|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:42:07|info|where条件元素: key=status, type=integer, value=1
2025-07-25 00:42:07|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":1,"is_show":1,"status":1}
2025-07-25 00:42:07|info|DynamicContentDao.getDynamicList - 查询结果数量: 0
2025-07-25 00:42:07|info|DynamicContentServices.getDynamicList - 处理后数据条数:0
2025-07-25 00:42:43|info|获取点赞动态ID列表 - 用户ID:1, 页码:1, 每页数量:10, 点赞动态ID数量:0, 动态IDs:
2025-07-25 00:42:43|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=id desc, where={"uid":1,"is_show":1,"status":1}, extraFields=[]
2025-07-25 00:42:43|info|where条件元素: key=uid, type=integer, value=1
2025-07-25 00:42:43|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:42:43|info|where条件元素: key=status, type=integer, value=1
2025-07-25 00:42:43|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":1,"is_show":1,"status":1}
2025-07-25 00:42:43|info|DynamicContentDao.getDynamicList - 查询结果数量: 0
2025-07-25 00:42:43|info|DynamicContentServices.getDynamicList - 处理后数据条数:0
2025-07-25 00:42:44|info|获取点赞动态ID列表 - 用户ID:1, 页码:1, 每页数量:10, 点赞动态ID数量:0, 动态IDs:
2025-07-25 00:42:45|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=id desc, where={"uid":1,"is_show":1,"status":1}, extraFields=[]
2025-07-25 00:42:45|info|where条件元素: key=uid, type=integer, value=1
2025-07-25 00:42:45|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:42:45|info|where条件元素: key=status, type=integer, value=1
2025-07-25 00:42:45|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":1,"is_show":1,"status":1}
2025-07-25 00:42:45|info|DynamicContentDao.getDynamicList - 查询结果数量: 0
2025-07-25 00:42:45|info|DynamicContentServices.getDynamicList - 处理后数据条数:0
2025-07-25 00:42:46|info|获取点赞动态ID列表 - 用户ID:1, 页码:1, 每页数量:10, 点赞动态ID数量:0, 动态IDs:
2025-07-25 00:42:46|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=id desc, where={"uid":1,"is_show":1,"status":1}, extraFields=[]
2025-07-25 00:42:46|info|where条件元素: key=uid, type=integer, value=1
2025-07-25 00:42:46|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:42:46|info|where条件元素: key=status, type=integer, value=1
2025-07-25 00:42:46|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":1,"is_show":1,"status":1}
2025-07-25 00:42:46|info|DynamicContentDao.getDynamicList - 查询结果数量: 0
2025-07-25 00:42:46|info|DynamicContentServices.getDynamicList - 处理后数据条数:0
2025-07-25 00:53:45|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=id desc, where={"uid":1,"is_show":1,"status":1}, extraFields=[]
2025-07-25 00:53:45|info|where条件元素: key=uid, type=integer, value=1
2025-07-25 00:53:45|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:53:45|info|where条件元素: key=status, type=integer, value=1
2025-07-25 00:53:45|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":1,"is_show":1,"status":1}
2025-07-25 00:53:45|info|DynamicContentDao.getDynamicList - 查询结果数量: 0
2025-07-25 00:53:45|info|DynamicContentServices.getDynamicList - 处理后数据条数:0
2025-07-25 00:53:45|info|=== getUserHomepage接口被调用 ===
2025-07-25 00:53:45|info|请求参数: {"user_id":"1"}
2025-07-25 00:53:45|info|请求头信息: {"user-agent":"Mozilla\/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/16.6 Mobile\/15E148 Safari\/604.1","authorization":"\u672a\u8bbe\u7f6e","token":"\u672a\u8bbe\u7f6e"}
2025-07-25 00:53:45|info|目标用户ID: 1
2025-07-25 00:53:45|info|成功获取当前登录用户ID: 0
2025-07-25 00:53:45|info|=== 访问记录检查开始 ===
2025-07-25 00:53:45|info|当前登录用户ID: 0
2025-07-25 00:53:45|info|目标用户ID: 1
2025-07-25 00:53:45|info|是否登录: 否
2025-07-25 00:53:45|info|是否查看自己: 否
2025-07-25 00:53:45|info|不记录访问 - 原因: 用户未登录
2025-07-25 00:53:45|info|=== 访问记录检查结束 ===
2025-07-25 00:53:58|info|=== getUserHomepage接口被调用 ===
2025-07-25 00:53:58|info|请求参数: {"user_id":"6"}
2025-07-25 00:53:58|info|请求头信息: {"user-agent":"Mozilla\/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/16.6 Mobile\/15E148 Safari\/604.1","authorization":"\u672a\u8bbe\u7f6e","token":"\u672a\u8bbe\u7f6e"}
2025-07-25 00:53:58|info|目标用户ID: 6
2025-07-25 00:53:58|info|成功获取当前登录用户ID: 0
2025-07-25 00:53:58|info|=== 访问记录检查开始 ===
2025-07-25 00:53:58|info|当前登录用户ID: 0
2025-07-25 00:53:58|info|目标用户ID: 6
2025-07-25 00:53:58|info|是否登录: 否
2025-07-25 00:53:58|info|是否查看自己: 否
2025-07-25 00:53:58|info|不记录访问 - 原因: 用户未登录
2025-07-25 00:53:58|info|=== 访问记录检查结束 ===
2025-07-25 00:53:58|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=id desc, where={"uid":6,"is_show":1,"status":1}, extraFields=[]
2025-07-25 00:53:58|info|where条件元素: key=uid, type=integer, value=6
2025-07-25 00:53:58|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:53:58|info|where条件元素: key=status, type=integer, value=1
2025-07-25 00:53:58|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":6,"is_show":1,"status":1}
2025-07-25 00:53:58|info|DynamicContentDao.getDynamicList - 查询结果数量: 2
2025-07-25 00:53:58|info|handleDynamicList处理 - 用户ID:0, 动态数量:2
2025-07-25 00:53:58|info|DynamicContentServices.getDynamicList - 处理后数据条数:2
2025-07-25 00:53:58|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 00:53:58|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 00:53:58|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 00:57:42|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 00:57:42|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 00:57:42|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 00:57:42|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:57:42|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 00:57:42|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 00:57:42|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 00:57:42|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 00:57:42|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 00:57:42|info|我的动态列表返回数据条数: 10
2025-07-25 00:57:45|info|获取点赞动态ID列表 - 用户ID:3, 页码:1, 每页数量:10, 点赞动态ID数量:8, 动态IDs:11,10,13,41,23,26,22,14
2025-07-25 00:57:45|info|直接查询验证结果: [{"id":10,"uid":3,"content":"************"},{"id":11,"uid":3,"content":"42342423432"},{"id":13,"uid":3,"content":"那年春天来得早，阳光四溢。\n连影子都是半透明的。那年春天来得早，阳光四溢。\n连影子都是半透明的。"},{"id":14,"uid":3,"content":"当你的灵魂变得宁静、和平、喜悦时，你的眼睛就有一种深度、清澈、纯洁、天真。它们变得如此透明，以至于你能够看到一个人的灵魂。"},{"id":23,"uid":3,"content":"1231545363643"},{"id":41,"uid":3,"content":"哈哈哈哈，我美不美"}]
2025-07-25 00:57:45|info|查询动态列表条件: {"id":["in",[11,10,13,41,23,26,22,14]]}, 排序: id desc
2025-07-25 00:57:45|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=8, order=id desc, where={"id":["in",[11,10,13,41,23,26,22,14]]}, extraFields=[]
2025-07-25 00:57:45|info|where条件元素: key=id, type=array, value=["in",[11,10,13,41,23,26,22,14]]
2025-07-25 00:57:45|info|DynamicContentDao.getDynamicList - 查询条件: {"id":["in",[11,10,13,41,23,26,22,14]]}
2025-07-25 00:57:45|info|DynamicContentDao.getDynamicList - 查询结果数量: 6
2025-07-25 00:57:45|info|handleDynamicList处理 - 用户ID:3, 动态数量:6
2025-07-25 00:57:45|info|用户点赞状态 - 用户ID:3, 已点赞动态数:6, 已点赞ID:10,11,13,14,23,41
2025-07-25 00:57:45|info|DynamicContentServices.getDynamicList - 处理后数据条数:6
2025-07-25 00:57:45|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 00:57:45|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:57:45|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:57:45|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:57:45|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:57:45|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:57:45|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:57:51|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 00:57:51|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:57:51|info|where条件元素: key=status, type=integer, value=1
2025-07-25 00:57:51|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 00:57:51|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 00:57:51|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 00:57:51|info|用户点赞状态 - 用户ID:3, 已点赞动态数:6, 已点赞ID:14,23,41,13,10,11
2025-07-25 00:57:51|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 00:57:51|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 00:57:51|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 00:57:51|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 00:57:51|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:57:51|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 00:57:51|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:57:51|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:57:51|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:57:51|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:57:51|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:57:51|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 00:57:51|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 00:57:51|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 00:57:53|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":6,"is_show":1,"status":1}, extraFields=[]
2025-07-25 00:57:53|info|where条件元素: key=uid, type=integer, value=6
2025-07-25 00:57:53|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:57:53|info|where条件元素: key=status, type=integer, value=1
2025-07-25 00:57:53|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":6,"is_show":1,"status":1}
2025-07-25 00:57:53|info|DynamicContentDao.getDynamicList - 查询结果数量: 2
2025-07-25 00:57:53|info|handleDynamicList处理 - 用户ID:3, 动态数量:2
2025-07-25 00:57:53|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 00:57:53|info|DynamicContentServices.getDynamicList - 处理后数据条数:2
2025-07-25 00:57:53|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 00:57:53|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 00:57:53|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 00:57:53|info|=== getUserHomepage接口被调用 ===
2025-07-25 00:57:53|info|请求参数: {"user_id":"6"}
2025-07-25 00:57:53|info|请求头信息: {"user-agent":"Mozilla\/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/16.6 Mobile\/15E148 Safari\/604.1","authorization":"\u672a\u8bbe\u7f6e","token":"\u672a\u8bbe\u7f6e"}
2025-07-25 00:57:53|info|目标用户ID: 6
2025-07-25 00:57:53|info|成功获取当前登录用户ID: 3
2025-07-25 00:57:53|info|=== 访问记录检查开始 ===
2025-07-25 00:57:53|info|当前登录用户ID: 3
2025-07-25 00:57:53|info|目标用户ID: 6
2025-07-25 00:57:53|info|是否登录: 是
2025-07-25 00:57:53|info|是否查看自己: 否
2025-07-25 00:57:53|info|满足访问记录条件，开始记录用户访问
2025-07-25 00:57:53|info|=== recordUserVisit方法开始 ===
2025-07-25 00:57:53|info|目标用户ID: 6
2025-07-25 00:57:53|info|访问者ID: 3
2025-07-25 00:57:53|info|获取到请求信息 - IP: ************, User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1
2025-07-25 00:57:53|info|判断访问渠道: app
2025-07-25 00:57:53|info|准备记录新的访问记录（不去重）
2025-07-25 00:57:53|info|准备插入访问记录: {"visitor_uid":3,"visited_uid":6,"ip":"************","channel_type":"app","add_time":"2025-07-25 00:57:53"}
2025-07-25 00:57:53|info|访问记录插入结果: 成功
2025-07-25 00:57:53|info|插入后验证查询结果: {"id":32,"visitor_uid":3,"visited_uid":6,"ip":"************","channel_type":"app","add_time":"2025-07-25 00:57:53"}
2025-07-25 00:57:53|info|=== recordUserVisit方法结束 ===
2025-07-25 00:57:53|info|访问记录方法调用完成
2025-07-25 00:57:53|info|=== 访问记录检查结束 ===
2025-07-25 00:57:56|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=id desc, where={"uid":1,"is_show":1,"status":1}, extraFields=[]
2025-07-25 00:57:56|info|where条件元素: key=uid, type=integer, value=1
2025-07-25 00:57:56|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:57:56|info|where条件元素: key=status, type=integer, value=1
2025-07-25 00:57:56|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":1,"is_show":1,"status":1}
2025-07-25 00:57:56|info|DynamicContentDao.getDynamicList - 查询结果数量: 0
2025-07-25 00:57:56|info|DynamicContentServices.getDynamicList - 处理后数据条数:0
2025-07-25 00:57:56|info|=== getUserHomepage接口被调用 ===
2025-07-25 00:57:56|info|请求参数: {"user_id":"1"}
2025-07-25 00:57:56|info|请求头信息: {"user-agent":"Mozilla\/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/16.6 Mobile\/15E148 Safari\/604.1","authorization":"\u672a\u8bbe\u7f6e","token":"\u672a\u8bbe\u7f6e"}
2025-07-25 00:57:56|info|目标用户ID: 1
2025-07-25 00:57:56|info|成功获取当前登录用户ID: 0
2025-07-25 00:57:56|info|=== 访问记录检查开始 ===
2025-07-25 00:57:56|info|当前登录用户ID: 0
2025-07-25 00:57:56|info|目标用户ID: 1
2025-07-25 00:57:56|info|是否登录: 否
2025-07-25 00:57:56|info|是否查看自己: 否
2025-07-25 00:57:56|info|不记录访问 - 原因: 用户未登录
2025-07-25 00:57:56|info|=== 访问记录检查结束 ===
2025-07-25 00:57:57|info|获取点赞动态ID列表 - 用户ID:1, 页码:1, 每页数量:10, 点赞动态ID数量:0, 动态IDs:
2025-07-25 00:57:58|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=id desc, where={"uid":1,"is_show":1,"status":1}, extraFields=[]
2025-07-25 00:57:58|info|where条件元素: key=uid, type=integer, value=1
2025-07-25 00:57:58|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:57:58|info|where条件元素: key=status, type=integer, value=1
2025-07-25 00:57:58|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":1,"is_show":1,"status":1}
2025-07-25 00:57:58|info|DynamicContentDao.getDynamicList - 查询结果数量: 0
2025-07-25 00:57:58|info|DynamicContentServices.getDynamicList - 处理后数据条数:0
2025-07-25 00:57:59|info|获取点赞动态ID列表 - 用户ID:1, 页码:1, 每页数量:10, 点赞动态ID数量:0, 动态IDs:
2025-07-25 00:58:00|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=id desc, where={"uid":1,"is_show":1,"status":1}, extraFields=[]
2025-07-25 00:58:00|info|where条件元素: key=uid, type=integer, value=1
2025-07-25 00:58:00|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 00:58:00|info|where条件元素: key=status, type=integer, value=1
2025-07-25 00:58:00|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":1,"is_show":1,"status":1}
2025-07-25 00:58:00|info|DynamicContentDao.getDynamicList - 查询结果数量: 0
2025-07-25 00:58:00|info|DynamicContentServices.getDynamicList - 处理后数据条数:0
2025-07-25 01:02:04|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 01:02:04|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:02:04|info|where条件元素: key=status, type=integer, value=1
2025-07-25 01:02:04|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 01:02:04|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:02:04|info|handleDynamicList处理 - 用户ID:0, 动态数量:10
2025-07-25 01:02:04|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:02:04|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 01:02:04|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 01:02:04|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 01:02:04|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:02:04|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 01:02:04|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:02:04|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:02:04|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:02:04|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:02:04|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:02:04|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 01:02:04|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 01:02:04|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:03:42|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 01:03:42|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:03:42|info|where条件元素: key=status, type=integer, value=1
2025-07-25 01:03:42|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 01:03:42|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:03:42|info|handleDynamicList处理 - 用户ID:0, 动态数量:10
2025-07-25 01:03:42|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:03:42|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 01:03:42|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 01:03:42|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 01:03:42|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:03:42|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 01:03:42|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:03:42|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:03:42|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:03:42|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:03:42|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:03:42|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 01:03:42|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 01:03:42|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:06:34|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=id desc, where={"uid":1,"is_show":1,"status":1}, extraFields=[]
2025-07-25 01:06:34|info|where条件元素: key=uid, type=integer, value=1
2025-07-25 01:06:34|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:06:34|info|where条件元素: key=status, type=integer, value=1
2025-07-25 01:06:34|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":1,"is_show":1,"status":1}
2025-07-25 01:06:34|info|DynamicContentDao.getDynamicList - 查询结果数量: 0
2025-07-25 01:06:34|info|DynamicContentServices.getDynamicList - 处理后数据条数:0
2025-07-25 01:06:34|info|=== getUserHomepage接口被调用 ===
2025-07-25 01:06:34|info|请求参数: {"user_id":"1"}
2025-07-25 01:06:34|info|请求头信息: {"user-agent":"Mozilla\/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/16.6 Mobile\/15E148 Safari\/604.1","authorization":"\u672a\u8bbe\u7f6e","token":"\u672a\u8bbe\u7f6e"}
2025-07-25 01:06:34|info|目标用户ID: 1
2025-07-25 01:06:34|info|成功获取当前登录用户ID: 0
2025-07-25 01:06:34|info|=== 访问记录检查开始 ===
2025-07-25 01:06:34|info|当前登录用户ID: 0
2025-07-25 01:06:34|info|目标用户ID: 1
2025-07-25 01:06:34|info|是否登录: 否
2025-07-25 01:06:34|info|是否查看自己: 否
2025-07-25 01:06:34|info|不记录访问 - 原因: 用户未登录
2025-07-25 01:06:34|info|=== 访问记录检查结束 ===
2025-07-25 01:11:18|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 01:11:18|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 01:11:18|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 01:11:18|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:11:18|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 01:11:18|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:11:18|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:11:18|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 01:11:18|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:11:18|info|我的动态列表返回数据条数: 10
2025-07-25 01:11:26|info|获取点赞动态ID列表 - 用户ID:3, 页码:1, 每页数量:10, 点赞动态ID数量:8, 动态IDs:11,10,13,41,23,26,22,14
2025-07-25 01:11:26|info|直接查询验证结果: [{"id":10,"uid":3,"content":"************"},{"id":11,"uid":3,"content":"42342423432"},{"id":13,"uid":3,"content":"那年春天来得早，阳光四溢。\n连影子都是半透明的。那年春天来得早，阳光四溢。\n连影子都是半透明的。"},{"id":14,"uid":3,"content":"当你的灵魂变得宁静、和平、喜悦时，你的眼睛就有一种深度、清澈、纯洁、天真。它们变得如此透明，以至于你能够看到一个人的灵魂。"},{"id":23,"uid":3,"content":"1231545363643"},{"id":41,"uid":3,"content":"哈哈哈哈，我美不美"}]
2025-07-25 01:11:26|info|查询动态列表条件: {"id":["in",[11,10,13,41,23,26,22,14]]}, 排序: id desc
2025-07-25 01:11:26|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=8, order=id desc, where={"id":["in",[11,10,13,41,23,26,22,14]]}, extraFields=[]
2025-07-25 01:11:26|info|where条件元素: key=id, type=array, value=["in",[11,10,13,41,23,26,22,14]]
2025-07-25 01:11:26|info|DynamicContentDao.getDynamicList - 查询条件: {"id":["in",[11,10,13,41,23,26,22,14]]}
2025-07-25 01:11:26|info|DynamicContentDao.getDynamicList - 查询结果数量: 6
2025-07-25 01:11:26|info|handleDynamicList处理 - 用户ID:3, 动态数量:6
2025-07-25 01:11:26|info|用户点赞状态 - 用户ID:3, 已点赞动态数:6, 已点赞ID:10,11,13,14,23,41
2025-07-25 01:11:26|info|DynamicContentServices.getDynamicList - 处理后数据条数:6
2025-07-25 01:11:26|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 01:11:26|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:11:26|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:11:26|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:11:26|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:11:26|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:11:26|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:11:27|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 01:11:27|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 01:11:27|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 01:11:27|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:11:27|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 01:11:27|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:11:27|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:11:27|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 01:11:27|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:11:27|info|我的动态列表返回数据条数: 10
2025-07-25 01:11:28|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 01:11:28|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 01:11:28|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 01:11:28|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:11:28|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 01:11:28|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:11:28|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:11:28|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 01:11:28|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:11:28|info|我的动态列表返回数据条数: 10
2025-07-25 01:14:59|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 01:14:59|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 01:14:59|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 01:14:59|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:14:59|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 01:14:59|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:14:59|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:14:59|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 01:14:59|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:14:59|info|我的动态列表返回数据条数: 10
2025-07-25 01:14:59|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 01:14:59|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 01:14:59|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 01:14:59|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:14:59|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 01:14:59|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:14:59|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:14:59|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 01:14:59|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:14:59|info|我的动态列表返回数据条数: 10
2025-07-25 01:15:49|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 01:15:49|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 01:15:49|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 01:15:49|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:15:49|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 01:15:49|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:15:49|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:15:49|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 01:15:49|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:15:49|info|我的动态列表返回数据条数: 10
2025-07-25 01:15:49|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 01:15:49|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 01:15:49|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 01:15:49|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:15:49|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 01:15:49|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:15:49|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:15:49|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 01:15:49|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:15:49|info|我的动态列表返回数据条数: 10
2025-07-25 01:15:52|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 01:15:52|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 01:15:52|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 01:15:52|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:15:52|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 01:15:52|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:15:52|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:15:52|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 01:15:52|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:15:52|info|我的动态列表返回数据条数: 10
2025-07-25 01:15:52|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 01:15:52|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 01:15:52|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 01:15:52|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:15:52|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 01:15:52|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:15:52|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:15:52|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 01:15:52|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:15:52|info|我的动态列表返回数据条数: 10
2025-07-25 01:15:54|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 01:15:54|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 01:15:54|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 01:15:54|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:15:54|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 01:15:54|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:15:54|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:15:54|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 01:15:54|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:15:54|info|我的动态列表返回数据条数: 10
2025-07-25 01:15:55|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 01:15:55|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:15:55|info|where条件元素: key=status, type=integer, value=1
2025-07-25 01:15:55|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 01:15:55|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:15:55|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:15:55|info|用户点赞状态 - 用户ID:3, 已点赞动态数:6, 已点赞ID:14,23,41,13,10,11
2025-07-25 01:15:55|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:15:55|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 01:15:55|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 01:15:55|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 01:15:55|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:15:55|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 01:15:55|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:15:55|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:15:55|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:15:55|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:15:55|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:15:55|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 01:15:55|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 01:15:55|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:15:59|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 01:15:59|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:15:59|info|where条件元素: key=status, type=integer, value=1
2025-07-25 01:15:59|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 01:15:59|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:15:59|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:15:59|info|用户点赞状态 - 用户ID:3, 已点赞动态数:6, 已点赞ID:14,23,41,13,10,11
2025-07-25 01:15:59|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:15:59|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 01:15:59|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 01:15:59|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 01:15:59|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:15:59|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 01:15:59|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:15:59|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:15:59|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:15:59|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:15:59|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:15:59|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 01:15:59|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 01:15:59|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:16:13|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 01:16:13|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 01:16:13|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 01:16:13|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:16:13|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 01:16:13|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:16:13|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:16:13|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 01:16:13|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:16:13|info|我的动态列表返回数据条数: 10
2025-07-25 01:16:48|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 01:16:48|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:16:48|info|where条件元素: key=status, type=integer, value=1
2025-07-25 01:16:48|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 01:16:48|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:16:48|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:16:48|info|用户点赞状态 - 用户ID:3, 已点赞动态数:6, 已点赞ID:14,23,41,13,10,11
2025-07-25 01:16:48|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:16:48|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 01:16:48|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 01:16:48|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 01:16:48|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:16:48|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 01:16:48|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:16:48|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:16:48|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:16:48|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:16:48|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:16:48|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 01:16:48|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 01:16:48|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:16:50|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 01:16:50|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 01:16:50|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 01:16:50|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:16:50|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 01:16:50|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:16:50|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:16:50|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 01:16:50|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:16:50|info|我的动态列表返回数据条数: 10
2025-07-25 01:16:50|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 01:16:50|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 01:16:50|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 01:16:50|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:16:50|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 01:16:50|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:16:50|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:16:50|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 01:16:50|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:16:50|info|我的动态列表返回数据条数: 10
2025-07-25 01:16:52|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 01:16:52|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:16:52|info|where条件元素: key=status, type=integer, value=1
2025-07-25 01:16:52|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 01:16:52|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:16:52|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:16:52|info|用户点赞状态 - 用户ID:3, 已点赞动态数:6, 已点赞ID:14,23,41,13,10,11
2025-07-25 01:16:52|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:16:52|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 01:16:52|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 01:16:52|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 01:16:52|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:16:52|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 01:16:52|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:16:52|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:16:52|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:16:52|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:16:52|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:16:52|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 01:16:52|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 01:16:52|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:16:53|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 01:16:53|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 01:16:53|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 01:16:53|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:16:53|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 01:16:53|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:16:53|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:16:53|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 01:16:53|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:16:53|info|我的动态列表返回数据条数: 10
2025-07-25 01:16:58|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 01:16:58|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 01:16:58|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 01:16:58|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:16:58|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 01:16:58|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:16:58|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:16:58|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 01:16:58|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:16:58|info|我的动态列表返回数据条数: 10
2025-07-25 01:20:41|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=id desc, where={"uid":1,"is_show":1,"status":1}, extraFields=[]
2025-07-25 01:20:41|info|where条件元素: key=uid, type=integer, value=1
2025-07-25 01:20:41|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:20:41|info|where条件元素: key=status, type=integer, value=1
2025-07-25 01:20:41|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":1,"is_show":1,"status":1}
2025-07-25 01:20:41|info|DynamicContentDao.getDynamicList - 查询结果数量: 0
2025-07-25 01:20:41|info|DynamicContentServices.getDynamicList - 处理后数据条数:0
2025-07-25 01:20:41|info|=== getUserHomepage接口被调用 ===
2025-07-25 01:20:41|info|请求参数: {"user_id":"1"}
2025-07-25 01:20:41|info|请求头信息: {"user-agent":"Mozilla\/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/16.6 Mobile\/15E148 Safari\/604.1","authorization":"\u672a\u8bbe\u7f6e","token":"\u672a\u8bbe\u7f6e"}
2025-07-25 01:20:41|info|目标用户ID: 1
2025-07-25 01:20:41|info|成功获取当前登录用户ID: 0
2025-07-25 01:20:41|info|=== 访问记录检查开始 ===
2025-07-25 01:20:41|info|当前登录用户ID: 0
2025-07-25 01:20:41|info|目标用户ID: 1
2025-07-25 01:20:41|info|是否登录: 否
2025-07-25 01:20:41|info|是否查看自己: 否
2025-07-25 01:20:41|info|不记录访问 - 原因: 用户未登录
2025-07-25 01:20:41|info|=== 访问记录检查结束 ===
2025-07-25 01:20:44|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=id desc, where={"uid":1,"is_show":1,"status":1}, extraFields=[]
2025-07-25 01:20:44|info|where条件元素: key=uid, type=integer, value=1
2025-07-25 01:20:44|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:20:44|info|where条件元素: key=status, type=integer, value=1
2025-07-25 01:20:44|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":1,"is_show":1,"status":1}
2025-07-25 01:20:44|info|DynamicContentDao.getDynamicList - 查询结果数量: 0
2025-07-25 01:20:44|info|DynamicContentServices.getDynamicList - 处理后数据条数:0
2025-07-25 01:20:44|info|=== getUserHomepage接口被调用 ===
2025-07-25 01:20:44|info|请求参数: {"user_id":"1"}
2025-07-25 01:20:44|info|请求头信息: {"user-agent":"Mozilla\/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/16.6 Mobile\/15E148 Safari\/604.1","authorization":"\u672a\u8bbe\u7f6e","token":"\u672a\u8bbe\u7f6e"}
2025-07-25 01:20:44|info|目标用户ID: 1
2025-07-25 01:20:44|info|成功获取当前登录用户ID: 0
2025-07-25 01:20:44|info|=== 访问记录检查开始 ===
2025-07-25 01:20:44|info|当前登录用户ID: 0
2025-07-25 01:20:44|info|目标用户ID: 1
2025-07-25 01:20:44|info|是否登录: 否
2025-07-25 01:20:44|info|是否查看自己: 否
2025-07-25 01:20:44|info|不记录访问 - 原因: 用户未登录
2025-07-25 01:20:44|info|=== 访问记录检查结束 ===
2025-07-25 01:20:45|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=id desc, where={"uid":1,"is_show":1,"status":1}, extraFields=[]
2025-07-25 01:20:45|info|where条件元素: key=uid, type=integer, value=1
2025-07-25 01:20:45|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:20:45|info|where条件元素: key=status, type=integer, value=1
2025-07-25 01:20:45|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":1,"is_show":1,"status":1}
2025-07-25 01:20:45|info|DynamicContentDao.getDynamicList - 查询结果数量: 0
2025-07-25 01:20:45|info|DynamicContentServices.getDynamicList - 处理后数据条数:0
2025-07-25 01:20:45|info|=== getUserHomepage接口被调用 ===
2025-07-25 01:20:45|info|请求参数: {"user_id":"1"}
2025-07-25 01:20:45|info|请求头信息: {"user-agent":"Mozilla\/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/16.6 Mobile\/15E148 Safari\/604.1","authorization":"\u672a\u8bbe\u7f6e","token":"\u672a\u8bbe\u7f6e"}
2025-07-25 01:20:45|info|目标用户ID: 1
2025-07-25 01:20:45|info|成功获取当前登录用户ID: 0
2025-07-25 01:20:45|info|=== 访问记录检查开始 ===
2025-07-25 01:20:45|info|当前登录用户ID: 0
2025-07-25 01:20:45|info|目标用户ID: 1
2025-07-25 01:20:45|info|是否登录: 否
2025-07-25 01:20:45|info|是否查看自己: 否
2025-07-25 01:20:45|info|不记录访问 - 原因: 用户未登录
2025-07-25 01:20:45|info|=== 访问记录检查结束 ===
2025-07-25 01:26:52|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 01:26:52|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 01:26:52|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 01:26:52|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:26:52|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 01:26:52|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:26:52|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:26:52|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 01:26:52|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:26:52|info|我的动态列表返回数据条数: 10
2025-07-25 01:26:52|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 01:26:52|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 01:26:52|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 01:26:52|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:26:52|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 01:26:52|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:26:52|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:26:52|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 01:26:52|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:26:52|info|我的动态列表返回数据条数: 10
2025-07-25 01:26:54|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 01:26:54|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:26:54|info|where条件元素: key=status, type=integer, value=1
2025-07-25 01:26:54|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 01:26:54|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:26:54|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:26:54|info|用户点赞状态 - 用户ID:3, 已点赞动态数:6, 已点赞ID:14,23,41,13,10,11
2025-07-25 01:26:54|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:26:54|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 01:26:54|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 01:26:54|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 01:26:54|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:26:54|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 01:26:54|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:26:54|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:26:54|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:26:54|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:26:54|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:26:54|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 01:26:54|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 01:26:54|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:52:07|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 01:52:07|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:52:07|info|where条件元素: key=status, type=integer, value=1
2025-07-25 01:52:07|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 01:52:07|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:52:07|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:52:07|info|用户点赞状态 - 用户ID:3, 已点赞动态数:6, 已点赞ID:14,23,41,13,10,11
2025-07-25 01:52:07|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:52:07|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 01:52:07|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 01:52:07|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 01:52:07|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:52:07|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 01:52:07|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:52:07|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:52:07|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:52:07|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:52:07|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:52:07|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 01:52:07|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 01:52:07|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:52:09|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 01:52:09|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 01:52:09|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 01:52:09|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:52:09|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 01:52:09|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:52:09|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:52:09|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 01:52:09|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:52:09|info|我的动态列表返回数据条数: 10
2025-07-25 01:52:09|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 01:52:09|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 01:52:09|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 01:52:09|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:52:09|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 01:52:09|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:52:09|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 01:52:09|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 01:52:09|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:52:09|info|我的动态列表返回数据条数: 10
2025-07-25 01:52:45|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 01:52:45|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 01:52:45|info|where条件元素: key=status, type=integer, value=1
2025-07-25 01:52:45|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 01:52:45|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 01:52:45|info|handleDynamicList处理 - 用户ID:0, 动态数量:10
2025-07-25 01:52:45|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 01:52:45|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 01:52:45|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 01:52:45|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 01:52:45|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:52:45|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 01:52:45|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:52:45|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:52:45|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:52:45|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:52:45|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 01:52:45|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 01:52:45|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 01:52:45|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:09:53|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 02:09:53|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 02:09:53|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 02:09:53|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:09:53|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 02:09:53|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:09:53|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 02:09:53|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 02:09:53|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:09:53|info|我的动态列表返回数据条数: 10
2025-07-25 02:09:53|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 02:09:53|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 02:09:53|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 02:09:53|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:09:53|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 02:09:53|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:09:53|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 02:09:53|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 02:09:53|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:09:53|info|我的动态列表返回数据条数: 10
2025-07-25 02:09:57|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 02:09:57|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:09:57|info|where条件元素: key=status, type=integer, value=1
2025-07-25 02:09:57|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 02:09:57|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:09:57|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 02:09:57|info|用户点赞状态 - 用户ID:3, 已点赞动态数:6, 已点赞ID:14,23,41,13,10,11
2025-07-25 02:09:57|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:09:57|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 02:09:57|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 02:09:57|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 02:09:57|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:09:57|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:09:57|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:09:57|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:09:57|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:09:57|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:09:57|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:09:57|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 02:09:57|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:09:57|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:09:58|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 02:09:58|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 02:09:58|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 02:09:58|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:09:58|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 02:09:58|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:09:58|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 02:09:58|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 02:09:58|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:09:58|info|我的动态列表返回数据条数: 10
2025-07-25 02:09:58|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 02:09:58|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 02:09:58|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 02:09:58|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:09:58|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 02:09:58|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:09:58|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 02:09:58|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 02:09:58|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:09:58|info|我的动态列表返回数据条数: 10
2025-07-25 02:10:00|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 02:10:00|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 02:10:00|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 02:10:00|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:10:00|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 02:10:00|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:10:00|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 02:10:00|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 02:10:00|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:10:00|info|我的动态列表返回数据条数: 10
2025-07-25 02:10:00|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 02:10:00|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 02:10:00|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 02:10:00|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:10:00|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 02:10:00|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:10:00|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 02:10:00|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 02:10:00|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:10:00|info|我的动态列表返回数据条数: 10
2025-07-25 02:10:02|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 02:10:02|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:10:02|info|where条件元素: key=status, type=integer, value=1
2025-07-25 02:10:02|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 02:10:02|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:10:02|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 02:10:02|info|用户点赞状态 - 用户ID:3, 已点赞动态数:6, 已点赞ID:14,23,41,13,10,11
2025-07-25 02:10:02|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:10:02|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 02:10:02|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 02:10:02|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 02:10:02|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:10:02|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:10:02|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:10:02|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:10:02|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:10:02|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:10:02|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:10:02|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 02:10:02|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:10:02|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:10:03|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 02:10:03|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 02:10:03|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 02:10:03|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:10:03|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 02:10:03|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:10:03|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 02:10:03|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 02:10:03|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:10:03|info|我的动态列表返回数据条数: 10
2025-07-25 02:10:04|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 02:10:04|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 02:10:04|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 02:10:04|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:10:04|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 02:10:04|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:10:04|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 02:10:04|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 02:10:04|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:10:04|info|我的动态列表返回数据条数: 10
2025-07-25 02:10:04|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 02:10:04|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:10:04|info|where条件元素: key=status, type=integer, value=1
2025-07-25 02:10:04|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 02:10:04|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:10:04|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 02:10:04|info|用户点赞状态 - 用户ID:3, 已点赞动态数:6, 已点赞ID:14,23,41,13,10,11
2025-07-25 02:10:04|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:10:04|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 02:10:04|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 02:10:04|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 02:10:04|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:10:04|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:10:04|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:10:04|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:10:04|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:10:04|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:10:04|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:10:04|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 02:10:04|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:10:04|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:13:32|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 02:13:32|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:13:32|info|where条件元素: key=status, type=integer, value=1
2025-07-25 02:13:32|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 02:13:32|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:13:32|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 02:13:32|info|用户点赞状态 - 用户ID:3, 已点赞动态数:6, 已点赞ID:14,23,41,13,10,11
2025-07-25 02:13:32|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:13:32|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 02:13:32|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 02:13:32|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 02:13:32|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:13:32|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:13:32|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:13:32|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:13:32|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:13:32|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:13:32|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:13:32|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 02:13:32|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:13:32|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:19:18|info|获取我的动态列表 - 登录用户ID:6
2025-07-25 02:19:18|info|DynamicContentServices.getDynamicList - 传入参数: uid=6, page=1, limit=10, order=id desc, where={"uid":6,"is_show":1}, extraFields=[]
2025-07-25 02:19:18|info|where条件元素: key=uid, type=integer, value=6
2025-07-25 02:19:18|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:19:18|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":6,"is_show":1}
2025-07-25 02:19:18|info|DynamicContentDao.getDynamicList - 查询结果数量: 2
2025-07-25 02:19:18|info|handleDynamicList处理 - 用户ID:6, 动态数量:2
2025-07-25 02:19:18|info|用户点赞状态 - 用户ID:6, 已点赞动态数:0, 已点赞ID:
2025-07-25 02:19:18|info|DynamicContentServices.getDynamicList - 处理后数据条数:2
2025-07-25 02:19:18|info|我的动态列表返回数据条数: 2
2025-07-25 02:20:40|info|获取我的动态列表 - 登录用户ID:6
2025-07-25 02:20:40|info|DynamicContentServices.getDynamicList - 传入参数: uid=6, page=1, limit=10, order=id desc, where={"uid":6,"is_show":1}, extraFields=[]
2025-07-25 02:20:40|info|where条件元素: key=uid, type=integer, value=6
2025-07-25 02:20:40|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:20:40|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":6,"is_show":1}
2025-07-25 02:20:40|info|DynamicContentDao.getDynamicList - 查询结果数量: 2
2025-07-25 02:20:40|info|handleDynamicList处理 - 用户ID:6, 动态数量:2
2025-07-25 02:20:40|info|用户点赞状态 - 用户ID:6, 已点赞动态数:0, 已点赞ID:
2025-07-25 02:20:40|info|DynamicContentServices.getDynamicList - 处理后数据条数:2
2025-07-25 02:20:40|info|我的动态列表返回数据条数: 2
2025-07-25 02:20:56|info|获取点赞动态ID列表 - 用户ID:6, 页码:1, 每页数量:10, 点赞动态ID数量:1, 动态IDs:11
2025-07-25 02:20:56|info|直接查询验证结果: [{"id":11,"uid":3,"content":"42342423432"}]
2025-07-25 02:20:56|info|查询动态列表条件: {"id":["in",[11]]}, 排序: id desc
2025-07-25 02:20:56|info|DynamicContentServices.getDynamicList - 传入参数: uid=6, page=1, limit=1, order=id desc, where={"id":["in",[11]]}, extraFields=[]
2025-07-25 02:20:56|info|where条件元素: key=id, type=array, value=["in",[11]]
2025-07-25 02:20:56|info|DynamicContentDao.getDynamicList - 查询条件: {"id":["in",[11]]}
2025-07-25 02:20:56|info|DynamicContentDao.getDynamicList - 查询结果数量: 1
2025-07-25 02:20:56|info|handleDynamicList处理 - 用户ID:6, 动态数量:1
2025-07-25 02:20:56|info|用户点赞状态 - 用户ID:6, 已点赞动态数:1, 已点赞ID:11
2025-07-25 02:20:56|info|DynamicContentServices.getDynamicList - 处理后数据条数:1
2025-07-25 02:20:56|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 02:20:56|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:21:18|info|获取我的动态列表 - 登录用户ID:6
2025-07-25 02:21:18|info|DynamicContentServices.getDynamicList - 传入参数: uid=6, page=1, limit=10, order=id desc, where={"uid":6,"is_show":1}, extraFields=[]
2025-07-25 02:21:18|info|where条件元素: key=uid, type=integer, value=6
2025-07-25 02:21:18|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:21:18|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":6,"is_show":1}
2025-07-25 02:21:18|info|DynamicContentDao.getDynamicList - 查询结果数量: 2
2025-07-25 02:21:18|info|handleDynamicList处理 - 用户ID:6, 动态数量:2
2025-07-25 02:21:18|info|用户点赞状态 - 用户ID:6, 已点赞动态数:0, 已点赞ID:
2025-07-25 02:21:18|info|DynamicContentServices.getDynamicList - 处理后数据条数:2
2025-07-25 02:21:18|info|我的动态列表返回数据条数: 2
2025-07-25 02:21:26|info|获取我的动态列表 - 登录用户ID:6
2025-07-25 02:21:26|info|DynamicContentServices.getDynamicList - 传入参数: uid=6, page=1, limit=10, order=id desc, where={"uid":6,"is_show":1}, extraFields=[]
2025-07-25 02:21:26|info|where条件元素: key=uid, type=integer, value=6
2025-07-25 02:21:26|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:21:26|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":6,"is_show":1}
2025-07-25 02:21:26|info|DynamicContentDao.getDynamicList - 查询结果数量: 2
2025-07-25 02:21:26|info|handleDynamicList处理 - 用户ID:6, 动态数量:2
2025-07-25 02:21:26|info|用户点赞状态 - 用户ID:6, 已点赞动态数:0, 已点赞ID:
2025-07-25 02:21:26|info|DynamicContentServices.getDynamicList - 处理后数据条数:2
2025-07-25 02:21:26|info|我的动态列表返回数据条数: 2
2025-07-25 02:21:31|info|DynamicContentServices.getDynamicList - 传入参数: uid=6, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 02:21:31|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:21:31|info|where条件元素: key=status, type=integer, value=1
2025-07-25 02:21:31|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 02:21:31|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:21:31|info|handleDynamicList处理 - 用户ID:6, 动态数量:10
2025-07-25 02:21:31|info|用户点赞状态 - 用户ID:6, 已点赞动态数:1, 已点赞ID:11
2025-07-25 02:21:31|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:21:31|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 02:21:31|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 02:21:31|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 02:21:31|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:21:31|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:21:31|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:21:31|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:21:31|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:21:31|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:21:31|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:21:31|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 02:21:31|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:21:31|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:21:40|info|获取我的动态列表 - 登录用户ID:6
2025-07-25 02:21:40|info|DynamicContentServices.getDynamicList - 传入参数: uid=6, page=1, limit=10, order=id desc, where={"uid":6,"is_show":1}, extraFields=[]
2025-07-25 02:21:40|info|where条件元素: key=uid, type=integer, value=6
2025-07-25 02:21:40|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:21:40|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":6,"is_show":1}
2025-07-25 02:21:40|info|DynamicContentDao.getDynamicList - 查询结果数量: 2
2025-07-25 02:21:40|info|handleDynamicList处理 - 用户ID:6, 动态数量:2
2025-07-25 02:21:40|info|用户点赞状态 - 用户ID:6, 已点赞动态数:0, 已点赞ID:
2025-07-25 02:21:40|info|DynamicContentServices.getDynamicList - 处理后数据条数:2
2025-07-25 02:21:40|info|我的动态列表返回数据条数: 2
2025-07-25 02:21:53|info|获取我的动态列表 - 登录用户ID:6
2025-07-25 02:21:53|info|DynamicContentServices.getDynamicList - 传入参数: uid=6, page=1, limit=10, order=id desc, where={"uid":6,"is_show":1}, extraFields=[]
2025-07-25 02:21:53|info|where条件元素: key=uid, type=integer, value=6
2025-07-25 02:21:53|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:21:53|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":6,"is_show":1}
2025-07-25 02:21:53|info|DynamicContentDao.getDynamicList - 查询结果数量: 2
2025-07-25 02:21:53|info|handleDynamicList处理 - 用户ID:6, 动态数量:2
2025-07-25 02:21:53|info|用户点赞状态 - 用户ID:6, 已点赞动态数:0, 已点赞ID:
2025-07-25 02:21:53|info|DynamicContentServices.getDynamicList - 处理后数据条数:2
2025-07-25 02:21:53|info|我的动态列表返回数据条数: 2
2025-07-25 02:22:05|info|获取我的动态列表 - 登录用户ID:6
2025-07-25 02:22:05|info|DynamicContentServices.getDynamicList - 传入参数: uid=6, page=1, limit=10, order=id desc, where={"uid":6,"is_show":1}, extraFields=[]
2025-07-25 02:22:05|info|where条件元素: key=uid, type=integer, value=6
2025-07-25 02:22:05|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:22:05|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":6,"is_show":1}
2025-07-25 02:22:05|info|DynamicContentDao.getDynamicList - 查询结果数量: 2
2025-07-25 02:22:05|info|handleDynamicList处理 - 用户ID:6, 动态数量:2
2025-07-25 02:22:05|info|用户点赞状态 - 用户ID:6, 已点赞动态数:0, 已点赞ID:
2025-07-25 02:22:05|info|DynamicContentServices.getDynamicList - 处理后数据条数:2
2025-07-25 02:22:05|info|我的动态列表返回数据条数: 2
2025-07-25 02:23:35|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 02:23:35|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 02:23:35|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 02:23:35|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:23:35|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 02:23:35|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:23:35|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 02:23:35|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 02:23:35|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:23:35|info|我的动态列表返回数据条数: 10
2025-07-25 02:23:35|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 02:23:35|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 02:23:35|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 02:23:35|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:23:35|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 02:23:35|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:23:35|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 02:23:35|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 02:23:35|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:23:35|info|我的动态列表返回数据条数: 10
2025-07-25 02:23:38|info|获取点赞动态ID列表 - 用户ID:3, 页码:1, 每页数量:10, 点赞动态ID数量:8, 动态IDs:11,10,13,41,23,26,22,14
2025-07-25 02:23:38|info|直接查询验证结果: [{"id":10,"uid":3,"content":"************"},{"id":11,"uid":3,"content":"42342423432"},{"id":13,"uid":3,"content":"那年春天来得早，阳光四溢。\n连影子都是半透明的。那年春天来得早，阳光四溢。\n连影子都是半透明的。"},{"id":14,"uid":3,"content":"当你的灵魂变得宁静、和平、喜悦时，你的眼睛就有一种深度、清澈、纯洁、天真。它们变得如此透明，以至于你能够看到一个人的灵魂。"},{"id":23,"uid":3,"content":"1231545363643"},{"id":41,"uid":3,"content":"哈哈哈哈，我美不美"}]
2025-07-25 02:23:38|info|查询动态列表条件: {"id":["in",[11,10,13,41,23,26,22,14]]}, 排序: id desc
2025-07-25 02:23:38|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=8, order=id desc, where={"id":["in",[11,10,13,41,23,26,22,14]]}, extraFields=[]
2025-07-25 02:23:38|info|where条件元素: key=id, type=array, value=["in",[11,10,13,41,23,26,22,14]]
2025-07-25 02:23:38|info|DynamicContentDao.getDynamicList - 查询条件: {"id":["in",[11,10,13,41,23,26,22,14]]}
2025-07-25 02:23:38|info|DynamicContentDao.getDynamicList - 查询结果数量: 6
2025-07-25 02:23:38|info|handleDynamicList处理 - 用户ID:3, 动态数量:6
2025-07-25 02:23:38|info|用户点赞状态 - 用户ID:3, 已点赞动态数:6, 已点赞ID:10,11,13,14,23,41
2025-07-25 02:23:38|info|DynamicContentServices.getDynamicList - 处理后数据条数:6
2025-07-25 02:23:38|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 02:23:38|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:23:38|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:23:38|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:23:38|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:23:38|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:23:38|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:24:24|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 02:24:24|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 02:24:24|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 02:24:24|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:24:24|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 02:24:24|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:24:24|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 02:24:24|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 02:24:24|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:24:24|info|我的动态列表返回数据条数: 10
2025-07-25 02:24:27|info|获取我的动态列表 - 登录用户ID:3
2025-07-25 02:24:27|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=id desc, where={"uid":3,"is_show":1}, extraFields=[]
2025-07-25 02:24:27|info|where条件元素: key=uid, type=integer, value=3
2025-07-25 02:24:27|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:24:27|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":3,"is_show":1}
2025-07-25 02:24:27|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:24:27|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 02:24:27|info|用户点赞状态 - 用户ID:3, 已点赞动态数:0, 已点赞ID:
2025-07-25 02:24:27|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:24:27|info|我的动态列表返回数据条数: 10
2025-07-25 02:24:28|info|DynamicContentServices.getDynamicList - 传入参数: uid=3, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 02:24:28|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:24:28|info|where条件元素: key=status, type=integer, value=1
2025-07-25 02:24:28|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 02:24:28|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:24:28|info|handleDynamicList处理 - 用户ID:3, 动态数量:10
2025-07-25 02:24:28|info|用户点赞状态 - 用户ID:3, 已点赞动态数:6, 已点赞ID:14,23,41,13,10,11
2025-07-25 02:24:28|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:24:28|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 02:24:28|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 02:24:28|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 02:24:28|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:24:28|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:24:28|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:24:28|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:24:28|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:24:28|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:24:28|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:24:28|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 02:24:28|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:24:28|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:27|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 02:26:27|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:26:27|info|where条件元素: key=status, type=integer, value=1
2025-07-25 02:26:27|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 02:26:27|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:26:27|info|handleDynamicList处理 - 用户ID:0, 动态数量:10
2025-07-25 02:26:27|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:26:27|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 02:26:27|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 02:26:27|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 02:26:27|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:27|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:26:27|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:27|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:27|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:27|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:27|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:27|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 02:26:27|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:26:27|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:31|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 02:26:31|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:26:31|info|where条件元素: key=status, type=integer, value=1
2025-07-25 02:26:31|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 02:26:31|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:26:31|info|handleDynamicList处理 - 用户ID:0, 动态数量:10
2025-07-25 02:26:31|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:26:31|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 02:26:31|info|动态列表VIP信息设置 - 动态ID:61, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:31|info|动态列表VIP信息设置 - 动态ID:60, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:31|info|动态列表VIP信息设置 - 动态ID:59, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:31|info|动态列表VIP信息设置 - 动态ID:58, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:31|info|动态列表VIP信息设置 - 动态ID:57, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:31|info|动态列表VIP信息设置 - 动态ID:56, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:31|info|动态列表VIP信息设置 - 动态ID:50, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:31|info|动态列表VIP信息设置 - 动态ID:49, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:31|info|动态列表VIP信息设置 - 动态ID:48, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:31|info|动态列表VIP信息设置 - 动态ID:47, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:42|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 02:26:42|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:26:42|info|where条件元素: key=status, type=integer, value=1
2025-07-25 02:26:42|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 02:26:42|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:26:42|info|handleDynamicList处理 - 用户ID:0, 动态数量:10
2025-07-25 02:26:42|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:26:42|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 02:26:42|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 02:26:42|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 02:26:42|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:42|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:26:42|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:42|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:42|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:42|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:42|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:26:42|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 02:26:42|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:26:42|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:27:03|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 02:27:03|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:27:03|info|where条件元素: key=status, type=integer, value=1
2025-07-25 02:27:03|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 02:27:03|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:27:03|info|handleDynamicList处理 - 用户ID:0, 动态数量:10
2025-07-25 02:27:03|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:27:03|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 02:27:03|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 02:27:03|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 02:27:03|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:27:03|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:27:03|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:27:03|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:27:03|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:27:03|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:27:03|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:27:03|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 02:27:03|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:27:03|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:28:01|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 02:28:01|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:28:01|info|where条件元素: key=status, type=integer, value=1
2025-07-25 02:28:01|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 02:28:01|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:28:01|info|handleDynamicList处理 - 用户ID:0, 动态数量:10
2025-07-25 02:28:01|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:28:01|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 02:28:01|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 02:28:01|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 02:28:01|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:28:01|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:28:01|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:28:01|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:28:01|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:28:01|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:28:01|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:28:01|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 02:28:01|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:28:01|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:30:32|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"status":1}, extraFields=[]
2025-07-25 02:30:32|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:30:32|info|where条件元素: key=status, type=integer, value=1
2025-07-25 02:30:32|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"status":1}
2025-07-25 02:30:32|info|DynamicContentDao.getDynamicList - 查询结果数量: 10
2025-07-25 02:30:32|info|handleDynamicList处理 - 用户ID:0, 动态数量:10
2025-07-25 02:30:32|info|DynamicContentServices.getDynamicList - 处理后数据条数:10
2025-07-25 02:30:32|info|用户VIP信息处理 - 用户ID:2, VIP状态:{"vip":true,"vip_id":6,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":1}
2025-07-25 02:30:32|info|用户VIP信息处理 - 用户ID:3, VIP状态:{"vip":true,"vip_id":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4","vip_status":3,"overdue_time":1754924766,"auth_status":2,"residence_name":"重庆市渝北区","is_money_level":1,"is_ever_level":0,"svip_open":1}
2025-07-25 02:30:32|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 02:30:32|info|动态列表VIP信息设置 - 动态ID:11, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:30:32|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:30:32|info|动态列表VIP信息设置 - 动态ID:13, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:30:32|info|动态列表VIP信息设置 - 动态ID:14, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:30:32|info|动态列表VIP信息设置 - 动态ID:10, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:30:32|info|动态列表VIP信息设置 - 动态ID:41, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:30:32|info|动态列表VIP信息设置 - 动态ID:23, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:30:32|info|动态列表VIP信息设置 - 动态ID:9, 用户ID:2, VIP信息:{"vip":true,"vip_status":2,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_2_icon.jpeg","vip_name":"V2"}
2025-07-25 02:30:32|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:30:32|info|动态列表VIP信息设置 - 动态ID:44, 用户ID:3, VIP信息:{"vip":true,"vip_status":3,"vip_icon":"http:\/\/nas4.weiyun6.com:8033\/statics\/system_images\/user_level_4_icon.jpeg","vip_name":"V4"}
2025-07-25 02:30:35|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=id desc, where={"uid":6,"is_show":1,"status":1}, extraFields=[]
2025-07-25 02:30:35|info|where条件元素: key=uid, type=integer, value=6
2025-07-25 02:30:35|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:30:35|info|where条件元素: key=status, type=integer, value=1
2025-07-25 02:30:35|info|DynamicContentDao.getDynamicList - 查询条件: {"uid":6,"is_show":1,"status":1}
2025-07-25 02:30:35|info|DynamicContentDao.getDynamicList - 查询结果数量: 2
2025-07-25 02:30:35|info|handleDynamicList处理 - 用户ID:0, 动态数量:2
2025-07-25 02:30:35|info|DynamicContentServices.getDynamicList - 处理后数据条数:2
2025-07-25 02:30:35|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 02:30:35|info|动态列表VIP信息设置 - 动态ID:16, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:30:35|info|动态列表VIP信息设置 - 动态ID:12, 用户ID:6, VIP信息:{"vip":false,"vip_status":2,"vip_icon":"","vip_name":""}
2025-07-25 02:30:35|info|=== getUserHomepage接口被调用 ===
2025-07-25 02:30:35|info|请求参数: {"user_id":"6"}
2025-07-25 02:30:35|info|请求头信息: {"user-agent":"Mozilla\/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Mobile\/15E148 Html5Plus\/1.0 (Immersed\/59) uni-app","authorization":"\u672a\u8bbe\u7f6e","token":"\u672a\u8bbe\u7f6e"}
2025-07-25 02:30:35|info|目标用户ID: 6
2025-07-25 02:30:35|info|成功获取当前登录用户ID: 0
2025-07-25 02:30:35|info|=== 访问记录检查开始 ===
2025-07-25 02:30:35|info|当前登录用户ID: 0
2025-07-25 02:30:35|info|目标用户ID: 6
2025-07-25 02:30:35|info|是否登录: 否
2025-07-25 02:30:35|info|是否查看自己: 否
2025-07-25 02:30:35|info|不记录访问 - 原因: 用户未登录
2025-07-25 02:30:35|info|=== 访问记录检查结束 ===
2025-07-25 02:30:53|info|动态详情请求 - 登录用户ID:0, URL参数传递ID:0
2025-07-25 02:30:53|info|用户VIP信息处理 - 用户ID:6, VIP状态:{"vip":false,"vip_id":0,"vip_icon":"","vip_name":"","vip_status":2,"overdue_time":0,"auth_status":0,"residence_name":"","is_money_level":0,"is_ever_level":0,"svip_open":0}
2025-07-25 02:30:57|info|圈子详情查询 - 圈子ID:1, 用户ID:0
2025-07-25 02:30:57|info|用户ID为空，跳过成员查询
2025-07-25 02:30:57|info|圈子动态列表请求 - 登录用户ID:0, URL参数传递ID:0
2025-07-25 02:30:57|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"circle_id":"1","status":1}, extraFields=[]
2025-07-25 02:30:57|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:30:57|info|where条件元素: key=circle_id, type=string, value=1
2025-07-25 02:30:57|info|where条件元素: key=status, type=integer, value=1
2025-07-25 02:30:57|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"circle_id":"1","status":1}
2025-07-25 02:30:57|info|DynamicContentDao.getDynamicList - 查询结果数量: 6
2025-07-25 02:30:57|info|handleDynamicList处理 - 用户ID:0, 动态数量:6
2025-07-25 02:30:57|info|DynamicContentServices.getDynamicList - 处理后数据条数:6
2025-07-25 02:30:57|info|圈子动态列表返回数据条数: 6
2025-07-25 02:31:03|info|圈子详情查询 - 圈子ID:1, 用户ID:0
2025-07-25 02:31:03|info|用户ID为空，跳过成员查询
2025-07-25 02:31:03|info|圈子动态列表请求 - 登录用户ID:0, URL参数传递ID:0
2025-07-25 02:31:03|info|DynamicContentServices.getDynamicList - 传入参数: uid=0, page=1, limit=10, order=likes desc, comments desc, views desc, id desc, where={"is_show":1,"circle_id":"1","status":1}, extraFields=[]
2025-07-25 02:31:03|info|where条件元素: key=is_show, type=integer, value=1
2025-07-25 02:31:03|info|where条件元素: key=circle_id, type=string, value=1
2025-07-25 02:31:03|info|where条件元素: key=status, type=integer, value=1
2025-07-25 02:31:03|info|DynamicContentDao.getDynamicList - 查询条件: {"is_show":1,"circle_id":"1","status":1}
2025-07-25 02:31:03|info|DynamicContentDao.getDynamicList - 查询结果数量: 6
2025-07-25 02:31:03|info|handleDynamicList处理 - 用户ID:0, 动态数量:6
2025-07-25 02:31:03|info|DynamicContentServices.getDynamicList - 处理后数据条数:6
2025-07-25 02:31:03|info|圈子动态列表返回数据条数: 6
