
.container {
  width: 100%;
  padding-bottom: 320rpx;
}
.list {
  border-top: 1px solid #f8f8f8;
  width: calc(100% - 60rpx);
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
}
.list .list-editor {
  width: 34rpx;
  height: 34rpx;
}
.list .list-item {
  font-size: 24rpx;
}
.list .list-item .name {
  color: #000;
  font-weight: 700;
}
.list .list-item .mobile {
  margin-top: 20rpx;
  color: #000;
}
.list .list-item .adds {
  color: #999;
}
.btn-box {
  position: fixed;
  z-index: 99;
  left: 30rpx;
  right: 30rpx;
  bottom: 60rpx;
  width: calc(100% - 60rpx);
}
.btn-box view {
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 700;
  border-radius: 100rpx;
  border: 1px solid #000;
}
.bg1 {
  color: #fff;
  background: #000;
}
.bg2 {
  margin-top: 30rpx;
  color: #000;
  background: #fff;
}
.popup-box {
  width: calc(100% - 40rpx);
  padding: 20rpx;
  background: #fff;
  border-radius: 30rpx 30rpx 0 0;
  position: relative;
  overflow: hidden;
}
.popup-box .popup-top {
  width: calc(100% - 20rpx);
  padding: 10rpx;
  justify-content: space-between;
}
.popup-top .popup-title .t1 {
  font-size: 38rpx;
  font-weight: 700;
}
.popup-top .popup-title .t2 {
  color: #999;
  font-size: 20rpx;
  font-weight: 300;
}
.popup-top .popup-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f8f8f8;
  justify-content: center;
  transform: rotate(45deg);
}
.popup-box .popup-btn {
  margin: 40rpx 10rpx;
  width: calc(100% - 20rpx);
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 700;
  border-radius: 100rpx;
}
.popup-adds .adds-tit {
  padding: 30rpx 10rpx 0;
  color: #999;
  font-size: 20rpx;
  font-weight: 500;
}
.popup-adds .adds-item {
  width: calc(100% - 20rpx);
  margin: 0 10rpx;
  height: 70rpx;
  line-height: 70rpx;
  color: #000;
  font-size: 28rpx;
  font-weight: 700;
}
.popup-default {
  padding: 30rpx 10rpx;
  display: flex;
  align-items: center;
}
.popup-default checkbox-group {
  display: flex;
  align-items: center;
}
.popup-default checkbox {
  margin-right: 15rpx;
}
.popup-default text {
  font-size: 28rpx;
  color: #000;
}
.apc {
  color: #000;
}
.empty-box {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-box image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-box .e1 {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.empty-box .e2 {
  font-size: 24rpx;
  color: #999;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tips-box {
  padding: 20rpx 30rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12rpx;
  justify-content: center;
}
.tips-box .tips-item {
  color: #fff;
  font-size: 28rpx;
  font-weight: 700;
}
.df {
  display: flex;
  align-items: center;
}
