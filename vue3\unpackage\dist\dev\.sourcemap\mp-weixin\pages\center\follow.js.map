{"version": 3, "file": "follow.js", "sources": ["pages/center/follow.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY2VudGVyL2ZvbGxvdy52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <view class=\"nav-bar bfw\" :style=\"{'padding-top': statusBarHeight + 'px'}\">\r\n      <view class=\"bar-box df\" :style=\"{'height': titleBarHeight + 'px', 'width': '100%'}\">\r\n        <view class=\"bar-back df\" @tap=\"navBack\">\r\n          <image src=\"/static/img/back.png\" style=\"width:34rpx;height:34rpx\"></image>\r\n        </view>\r\n        <view class=\"bar-title ohto\">{{userName}}</view>\r\n      </view>\r\n      <view class=\"nav-box df\">\r\n        <view \r\n          v-for=\"(item, index) in barList\" \r\n          :key=\"index\" \r\n          class=\"nav-item df\" \r\n          @tap=\"barClick\" \r\n          :data-idx=\"index\"\r\n        >\r\n          <text :style=\"{\r\n            'color': index == barIdx ? '#000' : '#999',\r\n            'font-size': index == barIdx ? '28rpx' : '26rpx'\r\n          }\">{{item}}</text>\r\n          <view :style=\"{'opacity': index == barIdx ? 1 : 0}\" class=\"nav-line\"></view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <view class=\"content-box\" :style=\"{'margin-top': 'calc(' + (statusBarHeight + titleBarHeight) + 'px + 80rpx)'}\">\r\n      <!-- 自定义加载指示器 -->\r\n      <view v-if=\"uiState.showLoading\" class=\"loading-container\">\r\n        <view class=\"loading-indicator\"></view>\r\n      </view>\r\n      \r\n      <emptyPage\r\n        v-if=\"uiState.isEmpty\"\r\n        :title=\"userId ? (barIdx == 0 ? 'TA还没有关注' : 'TA还没有粉丝') : (barIdx == 0 ? '你还没有关注任何人' : '还没有人关注你')\"\r\n        :description=\"userId ? '' : (barIdx == 0 ? '赶快去关注感兴趣的小伙伴吧' : '发笔记，让大家可以关注你')\"\r\n        image=\"/static/img/empty.png\"\r\n      />\r\n      \r\n      <block v-else>\r\n        <view \r\n          v-for=\"(item, index) in formattedList\" \r\n          :key=\"index\" \r\n          class=\"list-box df\" \r\n          @tap=\"toUser\" \r\n          :data-id=\"barIdx === 0 ? item.follow_uid : item.uid\"\r\n        >\r\n          <view class=\"list-item df\">\r\n            <view class=\"list-avatar\">\r\n              <lazy-image :src=\"barIdx === 0 ? item.follow_avatar : item.avatar\"></lazy-image>\r\n            </view>\r\n            <view class=\"item-content\">\r\n              <view class=\"name ohto\">{{barIdx === 0 ? item.follow_nickname : item.nickname}}</view>\r\n              <view class=\"intro ohto\">{{barIdx === 0 ? item.follow_about_me || '' : item.about_me || ''}}</view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 操作按钮 -->\r\n          <view\r\n            v-if=\"userId == 0 || userId == myUserId\"\r\n            :data-idx=\"index\"\r\n            @tap.stop=\"followUser\"\r\n            :class=\"getButtonClass(item)\"\r\n          >\r\n            {{getButtonText(item)}}\r\n          </view>\r\n        </view>\r\n      </block>\r\n      \r\n      <!-- 底部加载状态显示 -->\r\n      <view v-if=\"formattedList.length > 0 && dataState.loadStatus === 'noMore'\" style=\"text-align: center; padding: 20rpx 0; color: #999; font-size: 24rpx;\">\r\n        没有更多数据了\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed } from 'vue'\r\nimport { onLoad, onPullDownRefresh, onReachBottom, onUnload } from '@dcloudio/uni-app'\r\nimport { useStore } from 'vuex'\r\nimport lazyImage from '@/components/lazyImage/lazyImage.vue'\r\nimport emptyPage from '@/components/emptyPage/emptyPage.vue'\r\nimport { getSocialFollowList, getSocialFansList, followUser as followUserApi } from '@/api/social'\r\n\r\n// 使用store\r\nconst store = useStore()\r\n\r\n// 响应式数据\r\nconst statusBarHeight = ref(store.state.statusBarHeight || 20)\r\nconst titleBarHeight = ref(store.state.titleBarHeight || 44)\r\nconst userId = ref(0) // 查看的用户ID，0表示查看自己的\r\nconst myUserId = ref(0) // 当前登录用户的ID\r\nconst userName = ref(\"关注列表\")\r\nconst barList = ref([\"关注\", \"粉丝\"])\r\nconst barIdx = ref(0)\r\n\r\n// 优化的状态管理\r\nconst uiState = reactive({\r\n  isEmpty: false,\r\n  showLoading: false\r\n})\r\n\r\n// 数据状态管理\r\nconst dataState = reactive({\r\n  list: [],\r\n  page: 1,\r\n  limit: 10,\r\n  totalCount: 0,\r\n  loadStatus: \"more\"\r\n})\r\n\r\n// 定时器管理\r\nconst timers = reactive({\r\n  loading: null,\r\n  debounce: null\r\n})\r\n\r\n// 计算属性\r\nconst formattedList = computed(() => {\r\n  return dataState.list\r\n})\r\n\r\nconst isLoading = computed(() => {\r\n  return dataState.loadStatus === 'loading'\r\n})\r\n\r\nconst hasMoreData = computed(() => {\r\n  return dataState.list.length < dataState.totalCount && dataState.list.length > 0\r\n})\r\n\r\n// 获取按钮样式类\r\nconst getButtonClass = (item) => {\r\n  const baseClass = ['list-btn', 'bg1']\r\n  \r\n  // 如果是关注列表或者已经关注的粉丝，显示为已关注状态\r\n  if (barIdx.value === 0 || (barIdx.value === 1 && item.is_follow === 1)) {\r\n    baseClass.push('bg2')\r\n  }\r\n  \r\n  return baseClass.join(' ')\r\n}\r\n\r\n// 获取按钮文本\r\nconst getButtonText = (item) => {\r\n  // 互相关注\r\n  if (item.is_mutual === 1) {\r\n    return '互相关注'\r\n  }\r\n  \r\n  // 关注列表中的用户，显示\"已关注\"\r\n  if (barIdx.value === 0) {\r\n    return '已关注'\r\n  }\r\n  \r\n  // 粉丝列表中的用户\r\n  if (barIdx.value === 1) {\r\n    return item.is_follow === 1 ? '已关注' : '关注'\r\n  }\r\n  \r\n  return '关注'\r\n}\r\n\r\n// 防抖处理方法\r\nconst debounce = (func, delay = 300) => {\r\n  if (timers.debounce) {\r\n    clearTimeout(timers.debounce)\r\n  }\r\n  \r\n  timers.debounce = setTimeout(() => {\r\n    func()\r\n    timers.debounce = null\r\n  }, delay)\r\n}\r\n\r\n// 统一的错误处理方法\r\nconst handleError = (error, context = '操作', showToast = true) => {\r\n  console.error(`${context}失败:`, error)\r\n  \r\n  if (showToast) {\r\n    let message = `${context}失败，请稍后重试`\r\n    if (error.message) {\r\n      message = error.message\r\n    } else if (error.msg) {\r\n      message = error.msg\r\n    }\r\n    \r\n    uni.showToast({\r\n      title: message,\r\n      icon: 'none',\r\n      duration: 2000\r\n    })\r\n  }\r\n}\r\n\r\n// 清理定时器\r\nconst clearTimers = () => {\r\n  Object.keys(timers).forEach(key => {\r\n    if (timers[key]) {\r\n      clearTimeout(timers[key])\r\n      timers[key] = null\r\n    }\r\n  })\r\n}\r\n\r\n// 获取用户关注/粉丝列表\r\nconst getUserFollowList = () => {\r\n  // 防止重复请求 - 如果已经在加载中，直接返回\r\n  if (dataState.loadStatus === 'loading') {\r\n    return\r\n  }\r\n  \r\n  dataState.loadStatus = 'loading'\r\n  \r\n  // 延迟显示加载指示器，避免短时间内的闪烁\r\n  clearTimers()\r\n  timers.loading = setTimeout(() => {\r\n    if (dataState.loadStatus === 'loading') {\r\n      uiState.showLoading = true\r\n    }\r\n  }, 300)\r\n  \r\n  // 请求参数\r\n  const params = {\r\n    page: dataState.page,\r\n    limit: dataState.limit\r\n  }\r\n  \r\n  // 如果是查看他人的关注/粉丝列表，添加用户ID参数\r\n  if (userId.value > 0) {\r\n    params.user_id = userId.value\r\n  }\r\n  \r\n  // 根据标签选择API\r\n  const apiMethod = barIdx.value === 0 ? getSocialFollowList : getSocialFansList\r\n  \r\n  // 调用API\r\n  apiMethod(params).then(res => {\r\n    dataState.loadStatus = \"more\"\r\n    \r\n    // 清除加载定时器并隐藏加载指示器\r\n    clearTimers()\r\n    uiState.showLoading = false\r\n    \r\n    // 停止下拉刷新\r\n    uni.stopPullDownRefresh()\r\n    \r\n    if (res.status === 200 && res.data) {\r\n      // 处理返回的数据\r\n      const responseData = res.data\r\n      \r\n      if (responseData.list && responseData.list.length > 0) {\r\n        if (dataState.page == 1) {\r\n          dataState.list = responseData.list\r\n        } else {\r\n          dataState.list = dataState.list.concat(responseData.list)\r\n        }\r\n        \r\n        // 更新总记录数\r\n        if (responseData.count !== undefined) {\r\n          dataState.totalCount = responseData.count\r\n        }\r\n        \r\n        uiState.isEmpty = false\r\n      } else if (dataState.page == 1) {\r\n        uiState.isEmpty = true\r\n        dataState.list = []\r\n      }\r\n      \r\n      // 判断是否还有更多数据\r\n      if (dataState.list.length >= dataState.totalCount && dataState.list.length > 0) {\r\n        dataState.loadStatus = \"noMore\"\r\n      }\r\n    } else {\r\n      if (dataState.page == 1) {\r\n        uiState.isEmpty = true\r\n        dataState.list = []\r\n      }\r\n      handleError({ msg: res.msg || \"获取数据失败\" }, '获取数据')\r\n    }\r\n  }).catch(error => {\r\n    dataState.loadStatus = \"more\"\r\n    \r\n    // 清除加载定时器并隐藏加载指示器\r\n    clearTimers()\r\n    uiState.showLoading = false\r\n    \r\n    // 停止下拉刷新\r\n    uni.stopPullDownRefresh()\r\n    \r\n    if (dataState.page == 1) {\r\n      uiState.isEmpty = true\r\n      dataState.list = []\r\n    }\r\n    \r\n    handleError(error, '网络请求')\r\n  })\r\n}\r\n\r\n// 关注/取消关注用户\r\nconst followUser = (e) => {\r\n  // 安全获取索引\r\n  if (!e || !e.currentTarget || !e.currentTarget.dataset) {\r\n    uni.showToast({\r\n      title: \"操作失败，参数错误\",\r\n      icon: \"none\"\r\n    })\r\n    return\r\n  }\r\n\r\n  const index = e.currentTarget.dataset.idx\r\n\r\n  // 验证数据有效性\r\n  if (index === undefined || !formattedList.value[index]) {\r\n    uni.showToast({\r\n      title: \"数据错误，请刷新后重试\",\r\n      icon: \"none\"\r\n    })\r\n    return\r\n  }\r\n\r\n  const item = formattedList.value[index]\r\n  // 根据当前标签页确定是否已关注和目标用户ID\r\n  const isFollow = barIdx.value === 0 ? true : (item.is_follow === 1)\r\n  // 获取目标用户ID，确保是整数类型\r\n  const targetUserId = parseInt(barIdx.value === 0 ? item.follow_uid : item.uid)\r\n\r\n  // 验证用户ID\r\n  if (!targetUserId || targetUserId <= 0) {\r\n    uni.showToast({\r\n      title: \"获取用户ID失败\",\r\n      icon: \"none\"\r\n    })\r\n    console.error('关注操作失败: 无效的用户ID', targetUserId)\r\n    return\r\n  }\r\n\r\n  // 请求参数\r\n  const params = {\r\n    follow_uid: targetUserId,\r\n    type: isFollow ? 0 : 1 // 0取消关注，1关注\r\n  }\r\n\r\n  uni.showLoading({ title: '处理中...', mask: true })\r\n\r\n  // 调用关注/取消关注API\r\n  followUserApi(params).then(res => {\r\n    uni.hideLoading()\r\n\r\n    if (res.status === 200) {\r\n      // 关注列表(barIdx=0)中取消关注，直接移除\r\n      if (barIdx.value === 0 && isFollow) {\r\n        dataState.list.splice(index, 1)\r\n        if (dataState.list.length === 0) {\r\n          uiState.isEmpty = true\r\n          dataState.loadStatus = \"more\"\r\n        }\r\n      }\r\n      // 粉丝列表(barIdx=1)中关注/取消关注，更新状态\r\n      else if (barIdx.value === 1 && dataState.list[index]) {\r\n        // 更新原始列表中的关注状态\r\n        dataState.list[index].is_follow = isFollow ? 0 : 1\r\n        // 如果取消关注了互关粉丝，也要更新互关状态\r\n        if (isFollow && dataState.list[index].is_mutual === 1) {\r\n          dataState.list[index].is_mutual = 0\r\n        }\r\n      }\r\n\r\n      // 显示操作结果提示\r\n      uni.showToast({\r\n        title: isFollow ? \"已取消关注\" : \"关注成功\",\r\n        icon: \"success\"\r\n      })\r\n    } else {\r\n      uni.showToast({\r\n        title: res.msg || \"操作失败，请重试\",\r\n        icon: \"none\"\r\n      })\r\n    }\r\n  }).catch(error => {\r\n    uni.hideLoading()\r\n    handleError(error, '关注操作')\r\n  })\r\n}\r\n\r\n// 标签切换\r\nconst barClick = (e) => {\r\n  // 如果正在请求数据，不处理点击\r\n  if (dataState.loadStatus === 'loading') {\r\n    return\r\n  }\r\n\r\n  // 获取点击的标签索引\r\n  const clickIdx = parseInt(e.currentTarget.dataset.idx)\r\n\r\n  // 如果点击的是当前选中的标签，不重复加载\r\n  if (clickIdx === barIdx.value) {\r\n    return\r\n  }\r\n\r\n  // 使用统一的防抖处理\r\n  debounce(() => {\r\n    // 更新状态\r\n    barIdx.value = clickIdx\r\n    dataState.page = 1\r\n\r\n    // 更新标题\r\n    if (userId.value > 0) {\r\n      userName.value = userName.value.replace(/(.*?)的(关注|粉丝)/, `$1的${clickIdx == 0 ? '关注' : '粉丝'}`)\r\n    } else {\r\n      userName.value = userName.value.replace(/(.*?)(关注|粉丝)/, `$1${clickIdx == 0 ? '关注' : '粉丝'}`)\r\n    }\r\n\r\n    // 重置列表数据和UI状态\r\n    dataState.list = []\r\n    uiState.isEmpty = false\r\n\r\n    // 获取数据\r\n    getUserFollowList()\r\n  }, 100)\r\n}\r\n\r\n// 查看用户详情\r\nconst toUser = (e) => {\r\n  // 安全检查参数\r\n  if (!e || !e.currentTarget || !e.currentTarget.dataset || !e.currentTarget.dataset.id) {\r\n    uni.showToast({\r\n      title: '用户信息无效',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  const userId = parseInt(e.currentTarget.dataset.id)\r\n  // 检查userId是否有效\r\n  if (!userId || userId <= 0) {\r\n    uni.showToast({\r\n      title: '用户ID无效',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  uni.navigateTo({\r\n    url: \"/pages/user/details?id=\" + userId\r\n  })\r\n}\r\n\r\n// 返回\r\nconst navBack = () => {\r\n  const pages = getCurrentPages()\r\n  if (pages.length > 1) {\r\n    uni.navigateBack()\r\n  } else {\r\n    uni.switchTab({\r\n      url: \"/pages/user/index\"\r\n    })\r\n  }\r\n}\r\n\r\n// 页面加载\r\nonLoad((options) => {\r\n  // 设置选项卡索引\r\n  if (options.type) {\r\n    barIdx.value = parseInt(options.type)\r\n  }\r\n\r\n  // 获取用户信息 - 优先从store获取\r\n  myUserId.value = parseInt(store.state.app?.uid || 0)\r\n\r\n  // 设置查看的用户\r\n  if (options.id) {\r\n    userId.value = parseInt(options.id)\r\n    userName.value = options.name ? options.name + '的' + (barIdx.value == 0 ? '关注' : '粉丝') : (barIdx.value == 0 ? '关注' : '粉丝')\r\n  } else {\r\n    userId.value = 0 // 查看自己的\r\n    const USER_INFO = uni.getStorageSync('USER_INFO')\r\n    const nickname = USER_INFO?.nickname || '我'\r\n    userName.value = nickname + '的' + (barIdx.value == 0 ? '关注' : '粉丝')\r\n  }\r\n\r\n  // 获取用户关注/粉丝列表\r\n  getUserFollowList()\r\n})\r\n\r\n// 下拉刷新\r\nonPullDownRefresh(() => {\r\n  if (dataState.loadStatus !== 'loading') {\r\n    dataState.page = 1\r\n    getUserFollowList()\r\n  } else {\r\n    // 如果正在加载中，直接停止下拉刷新\r\n    uni.stopPullDownRefresh()\r\n  }\r\n})\r\n\r\n// 上拉加载更多\r\nonReachBottom(() => {\r\n  // 如果当前状态是加载中，不触发加载更多\r\n  if (dataState.loadStatus === \"loading\") {\r\n    return\r\n  }\r\n\r\n  // 如果未到达最大数量，加载更多\r\n  if (hasMoreData.value) {\r\n    dataState.page = dataState.page + 1\r\n    dataState.loadStatus = \"loading\"\r\n    getUserFollowList()\r\n  } else if (dataState.list.length >= dataState.totalCount && dataState.list.length > 0) {\r\n    dataState.loadStatus = \"noMore\"\r\n  }\r\n})\r\n\r\n// 页面卸载\r\nonUnload(() => {\r\n  // 使用统一的定时器清理方法\r\n  clearTimers()\r\n})\r\n</script>\r\n\r\n<style>\r\n/* 优化性能：添加will-change和transform3d */\r\n.nav-bar{\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  z-index: 99;\r\n  box-sizing: border-box;\r\n  /* APP端兼容性：条件编译backdrop-filter */\r\n  /* #ifndef APP-PLUS */\r\n  backdrop-filter: blur(10px);\r\n  -webkit-backdrop-filter: blur(10px);\r\n  /* #endif */\r\n}\r\n.bar-box .bar-back{\r\n  padding: 0 30rpx;\r\n  width: 34rpx;\r\n  height: 100%;\r\n}\r\n.bar-box .bar-title{\r\n  max-width: 60%;\r\n  font-size: 32rpx;\r\n  font-weight: 700;\r\n}\r\n.nav-box{\r\n  width: 100%;\r\n  height: 80rpx;\r\n}\r\n.nav-box .nav-item{\r\n  padding: 0 30rpx;\r\n  height: 100%;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n.nav-box .nav-item text{\r\n  font-weight: 700;\r\n  transition: all .3s ease-in-out;\r\n}\r\n.nav-box .nav-line{\r\n  position: absolute;\r\n  bottom: 12rpx;\r\n  width: 18rpx;\r\n  height: 6rpx;\r\n  border-radius: 6rpx;\r\n  background: #000;\r\n  transition: opacity .3s ease-in-out;\r\n}\r\n.content-box{\r\n  width: calc(100% - 80rpx);\r\n  padding: 20rpx 40rpx;\r\n}\r\n/* 加载中状态样式 */\r\n.loading-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 60rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n.loading-indicator {\r\n  width: 30rpx;\r\n  height: 30rpx;\r\n  border: 3rpx solid #f3f3f3;\r\n  border-top: 3rpx solid #000;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n.list-box{\r\n  width: 100%;\r\n  padding: 20rpx 0;\r\n  justify-content: space-between;\r\n}\r\n.list-box .list-item{\r\n  width: calc(100% - 120rpx - 2px);\r\n}\r\n.list-item .list-avatar{\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 50%;\r\n  border: 1px solid #f8f8f8;\r\n  overflow: hidden;\r\n}\r\n.list-item .item-content{\r\n  width: calc(100% - 100rpx);\r\n  margin-left: 20rpx;\r\n}\r\n.item-content .name{\r\n  font-size: 28rpx;\r\n  font-weight: 700;\r\n}\r\n.item-content .intro{\r\n  color: #999;\r\n  font-size: 20rpx;\r\n}\r\n.list-box .list-btn{\r\n  width: 100rpx;\r\n  height: 50rpx;\r\n  line-height: 50rpx;\r\n  text-align: center;\r\n  font-size: 20rpx;\r\n  font-weight: 700;\r\n  border-radius: 8rpx;\r\n}\r\n.bg1{\r\n  color: #999;\r\n  background: #f8f8f8;\r\n}\r\n.bg2{\r\n  color: #fff;\r\n  background: #000;\r\n}\r\n.heio{\r\n  transition: height 0.3s;\r\n}\r\n.empty-box{\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n}\r\n.empty-box image{\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n.empty-box .e1{\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 10rpx;\r\n}\r\n.empty-box .e2{\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n.df{\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.bfw{\r\n  backdrop-filter: blur(10px);\r\n  -webkit-backdrop-filter: blur(10px);\r\n  background: rgba(255,255,255,.8);\r\n}\r\n.ohto{\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'Z:/WWW/shejiao/vue3/pages/center/follow.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useStore", "ref", "reactive", "computed", "uni", "getSocialFollowList", "getSocialFansList", "followUserApi", "userId", "onLoad", "onPullDownRefresh", "onReachBottom", "onUnload", "MiniProgramPage"], "mappings": ";;;;;;;AAiFA,MAAM,YAAY,MAAW;AAC7B,MAAM,YAAY,MAAW;;;;AAI7B,UAAM,QAAQA,cAAAA,SAAU;AAGxB,UAAM,kBAAkBC,cAAAA,IAAI,MAAM,MAAM,mBAAmB,EAAE;AAC7D,UAAM,iBAAiBA,cAAAA,IAAI,MAAM,MAAM,kBAAkB,EAAE;AAC3D,UAAM,SAASA,cAAG,IAAC,CAAC;AACpB,UAAM,WAAWA,cAAG,IAAC,CAAC;AACtB,UAAM,WAAWA,cAAG,IAAC,MAAM;AAC3B,UAAM,UAAUA,cAAG,IAAC,CAAC,MAAM,IAAI,CAAC;AAChC,UAAM,SAASA,cAAG,IAAC,CAAC;AAGpB,UAAM,UAAUC,cAAAA,SAAS;AAAA,MACvB,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC;AAGD,UAAM,YAAYA,cAAAA,SAAS;AAAA,MACzB,MAAM,CAAE;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,YAAY;AAAA,IACd,CAAC;AAGD,UAAM,SAASA,cAAAA,SAAS;AAAA,MACtB,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AAGD,UAAM,gBAAgBC,cAAQ,SAAC,MAAM;AACnC,aAAO,UAAU;AAAA,IACnB,CAAC;AAEiBA,kBAAAA,SAAS,MAAM;AAC/B,aAAO,UAAU,eAAe;AAAA,IAClC,CAAC;AAED,UAAM,cAAcA,cAAQ,SAAC,MAAM;AACjC,aAAO,UAAU,KAAK,SAAS,UAAU,cAAc,UAAU,KAAK,SAAS;AAAA,IACjF,CAAC;AAGD,UAAM,iBAAiB,CAAC,SAAS;AAC/B,YAAM,YAAY,CAAC,YAAY,KAAK;AAGpC,UAAI,OAAO,UAAU,KAAM,OAAO,UAAU,KAAK,KAAK,cAAc,GAAI;AACtE,kBAAU,KAAK,KAAK;AAAA,MACrB;AAED,aAAO,UAAU,KAAK,GAAG;AAAA,IAC3B;AAGA,UAAM,gBAAgB,CAAC,SAAS;AAE9B,UAAI,KAAK,cAAc,GAAG;AACxB,eAAO;AAAA,MACR;AAGD,UAAI,OAAO,UAAU,GAAG;AACtB,eAAO;AAAA,MACR;AAGD,UAAI,OAAO,UAAU,GAAG;AACtB,eAAO,KAAK,cAAc,IAAI,QAAQ;AAAA,MACvC;AAED,aAAO;AAAA,IACT;AAGA,UAAM,WAAW,CAAC,MAAM,QAAQ,QAAQ;AACtC,UAAI,OAAO,UAAU;AACnB,qBAAa,OAAO,QAAQ;AAAA,MAC7B;AAED,aAAO,WAAW,WAAW,MAAM;AACjC,aAAM;AACN,eAAO,WAAW;AAAA,MACnB,GAAE,KAAK;AAAA,IACV;AAGA,UAAM,cAAc,CAAC,OAAO,UAAU,MAAM,YAAY,SAAS;AAC/DC,0BAAc,MAAA,SAAA,kCAAA,GAAG,OAAO,OAAO,KAAK;AAEpC,UAAI,WAAW;AACb,YAAI,UAAU,GAAG,OAAO;AACxB,YAAI,MAAM,SAAS;AACjB,oBAAU,MAAM;AAAA,QACtB,WAAe,MAAM,KAAK;AACpB,oBAAU,MAAM;AAAA,QACjB;AAEDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,cAAc,MAAM;AACxB,aAAO,KAAK,MAAM,EAAE,QAAQ,SAAO;AACjC,YAAI,OAAO,GAAG,GAAG;AACf,uBAAa,OAAO,GAAG,CAAC;AACxB,iBAAO,GAAG,IAAI;AAAA,QACf;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,MAAM;AAE9B,UAAI,UAAU,eAAe,WAAW;AACtC;AAAA,MACD;AAED,gBAAU,aAAa;AAGvB,kBAAa;AACb,aAAO,UAAU,WAAW,MAAM;AAChC,YAAI,UAAU,eAAe,WAAW;AACtC,kBAAQ,cAAc;AAAA,QACvB;AAAA,MACF,GAAE,GAAG;AAGN,YAAM,SAAS;AAAA,QACb,MAAM,UAAU;AAAA,QAChB,OAAO,UAAU;AAAA,MAClB;AAGD,UAAI,OAAO,QAAQ,GAAG;AACpB,eAAO,UAAU,OAAO;AAAA,MACzB;AAGD,YAAM,YAAY,OAAO,UAAU,IAAIC,WAAmB,sBAAGC,WAAiB;AAG9E,gBAAU,MAAM,EAAE,KAAK,SAAO;AAC5B,kBAAU,aAAa;AAGvB,oBAAa;AACb,gBAAQ,cAAc;AAGtBF,sBAAAA,MAAI,oBAAqB;AAEzB,YAAI,IAAI,WAAW,OAAO,IAAI,MAAM;AAElC,gBAAM,eAAe,IAAI;AAEzB,cAAI,aAAa,QAAQ,aAAa,KAAK,SAAS,GAAG;AACrD,gBAAI,UAAU,QAAQ,GAAG;AACvB,wBAAU,OAAO,aAAa;AAAA,YACxC,OAAe;AACL,wBAAU,OAAO,UAAU,KAAK,OAAO,aAAa,IAAI;AAAA,YACzD;AAGD,gBAAI,aAAa,UAAU,QAAW;AACpC,wBAAU,aAAa,aAAa;AAAA,YACrC;AAED,oBAAQ,UAAU;AAAA,UAC1B,WAAiB,UAAU,QAAQ,GAAG;AAC9B,oBAAQ,UAAU;AAClB,sBAAU,OAAO,CAAE;AAAA,UACpB;AAGD,cAAI,UAAU,KAAK,UAAU,UAAU,cAAc,UAAU,KAAK,SAAS,GAAG;AAC9E,sBAAU,aAAa;AAAA,UACxB;AAAA,QACP,OAAW;AACL,cAAI,UAAU,QAAQ,GAAG;AACvB,oBAAQ,UAAU;AAClB,sBAAU,OAAO,CAAE;AAAA,UACpB;AACD,sBAAY,EAAE,KAAK,IAAI,OAAO,SAAU,GAAE,MAAM;AAAA,QACjD;AAAA,MACL,CAAG,EAAE,MAAM,WAAS;AAChB,kBAAU,aAAa;AAGvB,oBAAa;AACb,gBAAQ,cAAc;AAGtBA,sBAAAA,MAAI,oBAAqB;AAEzB,YAAI,UAAU,QAAQ,GAAG;AACvB,kBAAQ,UAAU;AAClB,oBAAU,OAAO,CAAE;AAAA,QACpB;AAED,oBAAY,OAAO,MAAM;AAAA,MAC7B,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,MAAM;AAExB,UAAI,CAAC,KAAK,CAAC,EAAE,iBAAiB,CAAC,EAAE,cAAc,SAAS;AACtDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,YAAM,QAAQ,EAAE,cAAc,QAAQ;AAGtC,UAAI,UAAU,UAAa,CAAC,cAAc,MAAM,KAAK,GAAG;AACtDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,YAAM,OAAO,cAAc,MAAM,KAAK;AAEtC,YAAM,WAAW,OAAO,UAAU,IAAI,OAAQ,KAAK,cAAc;AAEjE,YAAM,eAAe,SAAS,OAAO,UAAU,IAAI,KAAK,aAAa,KAAK,GAAG;AAG7E,UAAI,CAAC,gBAAgB,gBAAgB,GAAG;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACDA,sBAAAA,MAAc,MAAA,SAAA,kCAAA,mBAAmB,YAAY;AAC7C;AAAA,MACD;AAGD,YAAM,SAAS;AAAA,QACb,YAAY;AAAA,QACZ,MAAM,WAAW,IAAI;AAAA;AAAA,MACtB;AAEDA,oBAAG,MAAC,YAAY,EAAE,OAAO,UAAU,MAAM,MAAM;AAG/CG,iBAAAA,WAAc,MAAM,EAAE,KAAK,SAAO;AAChCH,sBAAAA,MAAI,YAAa;AAEjB,YAAI,IAAI,WAAW,KAAK;AAEtB,cAAI,OAAO,UAAU,KAAK,UAAU;AAClC,sBAAU,KAAK,OAAO,OAAO,CAAC;AAC9B,gBAAI,UAAU,KAAK,WAAW,GAAG;AAC/B,sBAAQ,UAAU;AAClB,wBAAU,aAAa;AAAA,YACxB;AAAA,UACF,WAEQ,OAAO,UAAU,KAAK,UAAU,KAAK,KAAK,GAAG;AAEpD,sBAAU,KAAK,KAAK,EAAE,YAAY,WAAW,IAAI;AAEjD,gBAAI,YAAY,UAAU,KAAK,KAAK,EAAE,cAAc,GAAG;AACrD,wBAAU,KAAK,KAAK,EAAE,YAAY;AAAA,YACnC;AAAA,UACF;AAGDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,WAAW,UAAU;AAAA,YAC5B,MAAM;AAAA,UACd,CAAO;AAAA,QACP,OAAW;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG,EAAE,MAAM,WAAS;AAChBA,sBAAAA,MAAI,YAAa;AACjB,oBAAY,OAAO,MAAM;AAAA,MAC7B,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,CAAC,MAAM;AAEtB,UAAI,UAAU,eAAe,WAAW;AACtC;AAAA,MACD;AAGD,YAAM,WAAW,SAAS,EAAE,cAAc,QAAQ,GAAG;AAGrD,UAAI,aAAa,OAAO,OAAO;AAC7B;AAAA,MACD;AAGD,eAAS,MAAM;AAEb,eAAO,QAAQ;AACf,kBAAU,OAAO;AAGjB,YAAI,OAAO,QAAQ,GAAG;AACpB,mBAAS,QAAQ,SAAS,MAAM,QAAQ,iBAAiB,MAAM,YAAY,IAAI,OAAO,IAAI,EAAE;AAAA,QAClG,OAAW;AACL,mBAAS,QAAQ,SAAS,MAAM,QAAQ,gBAAgB,KAAK,YAAY,IAAI,OAAO,IAAI,EAAE;AAAA,QAC3F;AAGD,kBAAU,OAAO,CAAE;AACnB,gBAAQ,UAAU;AAGlB,0BAAmB;AAAA,MACpB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,SAAS,CAAC,MAAM;AAEpB,UAAI,CAAC,KAAK,CAAC,EAAE,iBAAiB,CAAC,EAAE,cAAc,WAAW,CAAC,EAAE,cAAc,QAAQ,IAAI;AACrFA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,YAAMI,UAAS,SAAS,EAAE,cAAc,QAAQ,EAAE;AAElD,UAAI,CAACA,WAAUA,WAAU,GAAG;AAC1BJ,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAEDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4BI;AAAA,MACrC,CAAG;AAAA,IACH;AAGA,UAAM,UAAU,MAAM;AACpB,YAAM,QAAQ,gBAAiB;AAC/B,UAAI,MAAM,SAAS,GAAG;AACpBJ,sBAAAA,MAAI,aAAc;AAAA,MACtB,OAAS;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,KAAK;AAAA,QACX,CAAK;AAAA,MACF;AAAA,IACH;AAGAK,kBAAM,OAAC,CAAC,YAAY;;AAElB,UAAI,QAAQ,MAAM;AAChB,eAAO,QAAQ,SAAS,QAAQ,IAAI;AAAA,MACrC;AAGD,eAAS,QAAQ,WAAS,WAAM,MAAM,QAAZ,mBAAiB,QAAO,CAAC;AAGnD,UAAI,QAAQ,IAAI;AACd,eAAO,QAAQ,SAAS,QAAQ,EAAE;AAClC,iBAAS,QAAQ,QAAQ,OAAO,QAAQ,OAAO,OAAO,OAAO,SAAS,IAAI,OAAO,QAAS,OAAO,SAAS,IAAI,OAAO;AAAA,MACzH,OAAS;AACL,eAAO,QAAQ;AACf,cAAM,YAAYL,cAAAA,MAAI,eAAe,WAAW;AAChD,cAAM,YAAW,uCAAW,aAAY;AACxC,iBAAS,QAAQ,WAAW,OAAO,OAAO,SAAS,IAAI,OAAO;AAAA,MAC/D;AAGD,wBAAmB;AAAA,IACrB,CAAC;AAGDM,kBAAAA,kBAAkB,MAAM;AACtB,UAAI,UAAU,eAAe,WAAW;AACtC,kBAAU,OAAO;AACjB,0BAAmB;AAAA,MACvB,OAAS;AAELN,sBAAAA,MAAI,oBAAqB;AAAA,MAC1B;AAAA,IACH,CAAC;AAGDO,kBAAAA,cAAc,MAAM;AAElB,UAAI,UAAU,eAAe,WAAW;AACtC;AAAA,MACD;AAGD,UAAI,YAAY,OAAO;AACrB,kBAAU,OAAO,UAAU,OAAO;AAClC,kBAAU,aAAa;AACvB,0BAAmB;AAAA,MACvB,WAAa,UAAU,KAAK,UAAU,UAAU,cAAc,UAAU,KAAK,SAAS,GAAG;AACrF,kBAAU,aAAa;AAAA,MACxB;AAAA,IACH,CAAC;AAGDC,kBAAAA,SAAS,MAAM;AAEb,kBAAa;AAAA,IACf,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpgBD,GAAG,WAAWC,SAAe;"}