"use strict";
const common_vendor = require("../../common/vendor.js");
const api_user = require("../../api/user.js");
const api_social = require("../../api/social.js");
const libs_login = require("../../libs/login.js");
const stores_user = require("../../stores/user.js");
const stores_app = require("../../stores/app.js");
const stores_social = require("../../stores/social.js");
const common_assets = require("../../common/assets.js");
const lazyImage = () => "../../components/lazyImage/lazyImage.js";
const uniLoadMore = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const cardWd = () => "../../components/card-wd/card-wd.js";
const cardGg = () => "../../components/card-gg/card-gg.js";
const tabbar = () => "../../components/tabbar/tabbar.js";
const uniPopup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
const _sfc_main = {
  components: {
    lazyImage,
    uniLoadMore,
    cardWd,
    cardGg,
    tabbar,
    uniPopup
  },
  computed: {
    ...common_vendor.mapGetters({
      isLogin: "isLogin"
    })
  },
  data() {
    return {
      // Pinia stores实例
      userStore: stores_user.useUserStore(),
      appStore: stores_app.useAppStore(),
      socialStore: stores_social.useSocialStore(),
      statusBarHeight: this.$store.state.statusBarHeight || 20,
      titleBarHeight: this.$store.state.titleBarHeight || 44,
      currentMsg: 0,
      currentYear: (/* @__PURE__ */ new Date()).getFullYear(),
      scrollTop: 0,
      navbarTrans: 0,
      userInfo: {
        avatar: "",
        nickname: "您还未登录哦~",
        follow_count: 0,
        fans_count: 0,
        like_count: 0,
        like_count_str: "0",
        visitor_count: 0,
        visitor_badge: 0,
        is_verified: 0,
        is_money_level: 0,
        svip_open: 0,
        sex: 0,
        user_id_number: "",
        about_me: "",
        interest_tags: [],
        vip_status: 0,
        overdue_time: "",
        card_count: 0,
        activity_count: 0,
        activity_img: ""
      },
      blockList: [
        { name: "圈子", img: "", icon: "/static/img/qz.png", url: "center/circle?type=1", count: 0 },
        { name: "购物车", img: "", icon: "/static/img/gwc.png", url: "goods/cart", count: 0 },
        { name: "订单", img: "", icon: "/static/img/dd.png", url: "order/index", count: 0 }
      ],
      barList: ["笔记", "赞过"],
      barIdx: 0,
      list: [],
      page: 1,
      totalCount: 0,
      isEmpty: false,
      loadStatus: "more",
      showSidebar: false,
      sidebarMenu: [],
      isLoading: false,
      isThrottling: false,
      appCard: true,
      // 优化后的数据管理变量
      lastRefreshTime: 0,
      refreshInterval: 3e5,
      // 5分钟刷新间隔
      // 优化的加载状态管理 - 避免状态冲突，使用语义化命名
      loadingState: {
        userInfo: false,
        // 用户信息加载状态
        dynamicList: false,
        // 动态列表加载状态
        pullRefresh: false,
        // 下拉刷新状态
        loadMore: false
        // 加载更多状态
      },
      // 简化的定时器管理
      refreshTimer: null,
      // 简化的缓存策略
      cacheTimeout: 3e5,
      // 5分钟统一缓存时间
      lastRefreshTime: 0,
      // 最后刷新时间
      // tab数据缓存
      tabDataCache: {
        0: { data: [], timestamp: 0, totalCount: 0 },
        // 笔记数据缓存
        1: { data: [], timestamp: 0, totalCount: 0 }
        // 点赞数据缓存
      },
      // 错误重试机制
      retryConfig: {
        maxRetries: 3,
        retryDelay: 1e3,
        currentRetries: {
          userInfo: 0,
          dynamicList: 0
        }
      }
    };
  },
  onPullDownRefresh() {
    if (!this.checkLoginStatus()) {
      common_vendor.index.stopPullDownRefresh();
      return;
    }
    this.refreshData(true);
  },
  onLoad() {
    this.navigationBarColor(0);
    this.initPiniaStores();
    common_vendor.index.$on("loginStateChanged", this.handleLoginStateChanged);
    common_vendor.index.$on("userInfoUpdated", this.handleUserInfoUpdate);
    this.loadUserFromCache();
    if (!this.checkLoginStatus(true)) {
      return;
    }
    this.$nextTick(() => {
      this.initPageData();
    });
  },
  onUnload() {
    common_vendor.index.$off("userInfoUpdated", this.handleUserInfoUpdate);
    common_vendor.index.$off("loginStateChanged", this.handleLoginStateChanged);
    this.clearAllTimers();
  },
  onShow() {
    this.navigationBarColor(0);
    this.cleanExpiredTabCache();
    if (!this.checkLoginStatus(true)) {
      return;
    }
    if (this.isLogin) {
      this.debounceRefresh();
      this.$nextTick(() => {
        this.setVisit();
      });
    }
  },
  methods: {
    // Pinia使用示例 - 初始化stores
    initPiniaStores() {
      this.userStore.initFromStorage();
      this.appStore.initFromStorage();
      if (this.isLogin && this.USER_INFO.UID) {
        this.userStore.updateUserInfo(this.USER_INFO);
        this.userStore.setLoginStatus(true);
      }
      common_vendor.index.__f__("log", "at pages/index/center.vue:468", "Pinia stores初始化完成:", {
        用户登录状态: this.userStore.isLoggedIn,
        用户昵称: this.userStore.displayName,
        是否VIP: this.userStore.isVip,
        主题模式: this.appStore.theme.mode
      });
    },
    // Pinia使用示例 - 缓存动态数据
    cacheDynamicDataToPinia() {
      if (this.list.length > 0) {
        const cacheType = "personal";
        const subType = this.barIdx === 0 ? "notes" : "likes";
        this.socialStore.setDynamicCache(cacheType, subType, this.list, this.totalCount);
        common_vendor.index.__f__("log", "at pages/index/center.vue:485", "动态数据已缓存到Pinia:", {
          类型: `${cacheType}.${subType}`,
          数据量: this.list.length,
          总数: this.totalCount
        });
      }
    },
    // Pinia使用示例 - 从缓存加载数据
    loadDataFromPiniaCache() {
      const cacheType = "personal";
      const subType = this.barIdx === 0 ? "notes" : "likes";
      if (this.socialStore.isCacheValid(cacheType, subType)) {
        const cache = this.socialStore.getDynamicCache(cacheType, subType);
        this.list = [...cache.data];
        this.totalCount = cache.totalCount;
        this.isEmpty = this.list.length === 0;
        this.loadStatus = this.list.length >= this.totalCount ? "noMore" : "more";
        common_vendor.index.__f__("log", "at pages/index/center.vue:507", "从Pinia缓存加载数据:", {
          类型: `${cacheType}.${subType}`,
          数据量: this.list.length,
          缓存时间: new Date(cache.timestamp).toLocaleString()
        });
        return true;
      }
      return false;
    },
    // 统一登录状态检查
    checkLoginStatus(redirectToLogin = false) {
      const isLoggedIn = this.isLogin && this.$store.state.app.token;
      return isLoggedIn;
    },
    // 清除定时器
    clearAllTimers() {
      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer);
        this.refreshTimer = null;
      }
    },
    // 简化的刷新方法
    debounceRefresh() {
      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer);
      }
      this.refreshTimer = setTimeout(() => {
        this.refreshData();
      }, 300);
    },
    // 简化的数据刷新检查
    checkAndRefreshData() {
      if (!this.checkLoginStatus()) {
        return;
      }
      const now = Date.now();
      if (now - this.lastRefreshTime > this.cacheTimeout) {
        this.refreshData();
      }
    },
    async initPageData() {
      try {
        this.getSidebarMenu().catch((err) => {
          common_vendor.index.__f__("log", "at pages/index/center.vue:570", "获取侧边栏菜单失败:", err);
        });
        if (!this.isLogin) {
          this.isEmpty = true;
          this.list = [];
          return;
        }
        await Promise.allSettled([
          this.getUserSocialInfo(false),
          this.userDynamic(false, false)
        ]);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/center.vue:586", "页面初始化失败:", error);
        this.showErrorToast("页面加载失败，请稍后重试");
      } finally {
        this.$nextTick(() => {
          this.setVisit();
        });
      }
    },
    // 错误处理方法
    handleUserInfoError(error) {
      common_vendor.index.__f__("error", "at pages/index/center.vue:598", "用户信息加载失败:", error);
      if (!this.userInfo.nickname || this.userInfo.nickname === "您还未登录哦~") {
        this.userInfo.nickname = "加载失败";
        this.userInfo.avatar = "/static/img/avatar.png";
      }
    },
    handleDynamicListError(error) {
      common_vendor.index.__f__("error", "at pages/index/center.vue:607", "动态列表加载失败:", error);
      if (this.list.length === 0) {
        this.isEmpty = true;
        this.loadStatus = "more";
      }
    },
    showErrorToast(message, duration = 2e3) {
      common_vendor.index.showToast({
        title: message,
        icon: "none",
        duration
      });
    },
    loadUserFromCache() {
      try {
        const cachedUserInfo = common_vendor.index.getStorageSync("USER_INFO");
        if (cachedUserInfo && typeof cachedUserInfo === "object") {
          this.userInfo = { ...this.userInfo, ...cachedUserInfo };
          this.$store.commit("UPDATE_USERINFO", cachedUserInfo);
          this.userClick();
        } else if (typeof cachedUserInfo === "string") {
          try {
            const parsedInfo = JSON.parse(cachedUserInfo);
            this.userInfo = { ...this.userInfo, ...parsedInfo };
            this.$store.commit("UPDATE_USERINFO", parsedInfo);
            this.userClick();
          } catch (e) {
            common_vendor.index.__f__("error", "at pages/index/center.vue:637", "解析缓存用户信息失败:", e);
          }
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/index/center.vue:641", "读取缓存用户信息失败:", e);
      }
    },
    // 清理过期的tab数据缓存
    cleanExpiredTabCache() {
      const now = Date.now();
      Object.keys(this.tabDataCache).forEach((key) => {
        const cache = this.tabDataCache[key];
        if (cache.timestamp && now - cache.timestamp > this.cacheTimeout) {
          this.tabDataCache[key] = { data: [], timestamp: 0, totalCount: 0 };
        }
      });
    },
    async refreshData(isPullDown = false) {
      if (this.loadingState.pullRefresh) {
        return;
      }
      this.loadingState.pullRefresh = true;
      try {
        this.page = 1;
        await this.getUserSocialInfo(false).catch((err) => {
          common_vendor.index.__f__("error", "at pages/index/center.vue:671", "刷新用户信息失败:", err);
        });
        await this.userDynamic(true, false).catch((err) => {
          common_vendor.index.__f__("error", "at pages/index/center.vue:675", "刷新动态列表失败:", err);
        });
        this.lastRefreshTime = Date.now();
      } finally {
        this.loadingState.pullRefresh = false;
        if (isPullDown) {
          common_vendor.index.stopPullDownRefresh();
        }
      }
    },
    handleLoginStateChanged(isLoggedIn) {
      if (isLoggedIn) {
        this.getUserSocialInfo(true).catch((err) => {
          common_vendor.index.__f__("log", "at pages/index/center.vue:693", "登录状态变更后刷新用户信息失败:", err);
        });
        this.userDynamic(false, true).catch((err) => {
          if (err.message !== "正在加载中") {
            common_vendor.index.__f__("log", "at pages/index/center.vue:697", "登录状态变更后刷新动态列表失败:", err);
          }
        });
      } else {
        this.resetUserInfo();
      }
    },
    resetUserInfo() {
      this.userInfo = {
        avatar: "/static/img/avatar.png",
        nickname: "未登录",
        about_me: "",
        follow_count: 0,
        fans_count: 0,
        like_count: 0,
        like_count_str: "0",
        visitor_count: 0,
        visitor_badge: 0
      };
      this.$store.commit("LOGOUT");
      this.list = [];
      this.isEmpty = true;
      this.loadStatus = "more";
      this.page = 1;
      Object.keys(this.loadingState).forEach((key) => {
        this.loadingState[key] = false;
      });
      this.lastRefreshTime = 0;
    },
    handleApiError(err) {
      if (err && (err.statusCode === 401 || err.code === 401 || err.status === 401)) {
        common_vendor.index.showToast({
          title: "登录信息已过期，请重新登录",
          icon: "none",
          duration: 2e3
        });
        this.resetUserInfo();
        setTimeout(() => libs_login.toLogin(), 1500);
        return true;
      }
      return false;
    },
    async getUserSocialInfo(silent = false, retryCount = 0) {
      var _a;
      if (this.loadingState.userInfo || !this.isLogin) {
        const errorMsg = "正在加载中或未登录";
        if (!silent && this.loadingState.userInfo) {
          this.showErrorToast("数据加载中，请稍候", 1500);
        }
        return Promise.reject(new Error(errorMsg));
      }
      this.loadingState.userInfo = true;
      if (!silent) {
        common_vendor.index.showLoading({
          title: "加载中...",
          mask: false
        });
      }
      try {
        const res = await api_social.getUserSocialInfo();
        this.lastRefreshTime = Date.now();
        if (res.status === 200 || res.code === 200) {
          const socialData = res.data;
          if (socialData.like_count !== void 0) {
            socialData.like_count_str = socialData.like_count > 999 ? (socialData.like_count / 1e3).toFixed(1) + "k" : socialData.like_count.toString();
          }
          if (!socialData.uid && ((_a = this.$store.state.app.userInfo) == null ? void 0 : _a.uid)) {
            socialData.uid = this.$store.state.app.userInfo.uid;
          }
          this.userInfo = { ...this.userInfo, ...socialData };
          this.$store.commit("UPDATE_USERINFO", socialData);
          this.userClick();
          if (socialData.service_num !== void 0) {
            this.currentMsg = socialData.service_num;
          }
          this.retryConfig.currentRetries.userInfo = 0;
          return socialData;
        } else {
          throw new Error(res.msg || "获取用户信息失败");
        }
      } catch (err) {
        if (this.handleApiError(err)) {
          return Promise.reject(err);
        }
        if (retryCount < this.retryConfig.maxRetries && !silent) {
          common_vendor.index.__f__("log", "at pages/index/center.vue:815", `用户信息获取失败，正在重试 ${retryCount + 1}/${this.retryConfig.maxRetries}`);
          await new Promise(
            (resolve) => setTimeout(resolve, this.retryConfig.retryDelay * (retryCount + 1))
          );
          return this.getUserSocialInfo(silent, retryCount + 1);
        }
        if (this.$store.state.app.userInfo && !this.userInfo.nickname) {
          this.userInfo = { ...this.userInfo, ...this.$store.state.app.userInfo };
          this.userClick();
        }
        throw err;
      } finally {
        this.loadingState.userInfo = false;
        if (!silent) {
          common_vendor.index.hideLoading();
        }
      }
    },
    getUserInfo(silent = false) {
      return this.getUserSocialInfo(silent);
    },
    async userDynamic(isRefresh = false, silent = false, retryCount = 0) {
      if (!this.checkLoginStatus()) {
        this.isEmpty = true;
        this.list = [];
        this.loadStatus = "more";
        return Promise.reject(new Error("未登录状态"));
      }
      if (this.loadingState.dynamicList) {
        const errorMsg = "正在加载中";
        if (!silent) {
          this.showErrorToast("数据加载中，请稍候", 1500);
        }
        return Promise.reject(new Error(errorMsg));
      }
      this.loadingState.dynamicList = true;
      if (!silent) {
        this.loadStatus = "loading";
      }
      if (isRefresh)
        this.page = 1;
      try {
        const loginUserId = this.$store.state.app.uid || 0;
        let apiCall;
        if (this.barIdx == 0) {
          apiCall = api_social.getMyDynamicList({
            page: this.page,
            limit: 10
          });
        } else if (this.barIdx == 1) {
          apiCall = api_social.getLikeDynamicList(loginUserId, {
            page: this.page,
            limit: 10
          });
        } else {
          common_vendor.index.__f__("warn", "at pages/index/center.vue:889", "未知的barIdx:", this.barIdx);
          return;
        }
        const res = await apiCall;
        if (!silent) {
          this.loadStatus = "more";
        }
        if (res.status == 200 && res.data) {
          if (res.data.list && res.data.list.length > 0) {
            if (this.page == 1) {
              this.list = res.data.list;
            } else {
              this.list.push(...res.data.list);
            }
            if (res.data.count !== void 0) {
              this.totalCount = res.data.count;
            }
            this.isEmpty = false;
          } else if (this.page == 1) {
            this.isEmpty = true;
            this.list = [];
          }
          this.updateStoreState();
          if (this.page === 1) {
            this.tabDataCache[this.barIdx] = {
              data: [...this.list],
              timestamp: Date.now(),
              totalCount: this.totalCount
            };
          }
          this.retryConfig.currentRetries.dynamicList = 0;
          return res.data;
        } else {
          throw new Error(res.msg || "获取数据失败");
        }
      } catch (err) {
        if (!silent) {
          this.loadStatus = "more";
        }
        if (err && (err.statusCode === 401 || err.code === 401 || err.status === 401)) {
          this.handleApiError({ status: 401 });
          throw err;
        }
        if (retryCount < this.retryConfig.maxRetries && !silent) {
          common_vendor.index.__f__("log", "at pages/index/center.vue:952", `动态列表获取失败，正在重试 ${retryCount + 1}/${this.retryConfig.maxRetries}`);
          await new Promise(
            (resolve) => setTimeout(resolve, this.retryConfig.retryDelay * (retryCount + 1))
          );
          return this.userDynamic(isRefresh, silent, retryCount + 1);
        }
        if (this.page == 1 && !silent) {
          this.isEmpty = true;
          this.list = [];
        }
        this.updateStoreState();
        throw err;
      } finally {
        this.loadingState.dynamicList = false;
      }
    },
    updateStoreState() {
      try {
        if (typeof this.$store.state.app.isCurrentMsg !== "undefined") {
          this.$store.commit("SET_CURRENT_MSG", true);
        }
      } catch (error) {
        common_vendor.index.__f__("warn", "at pages/index/center.vue:984", "更新 store 状态失败:", error.message);
      }
    },
    barClick(e) {
      if (this.isThrottling || this.loadingState.dynamicList)
        return;
      const newBarIdx = parseInt(e.currentTarget.dataset.idx);
      if (newBarIdx === this.barIdx)
        return;
      this.isThrottling = true;
      this.barIdx = newBarIdx;
      this.page = 1;
      const cacheData = this.tabDataCache[newBarIdx];
      const now = Date.now();
      const hasFreshCache = cacheData && cacheData.timestamp && now - cacheData.timestamp < this.cacheTimeout && cacheData.data.length > 0;
      if (hasFreshCache) {
        this.list = [...cacheData.data];
        this.totalCount = cacheData.totalCount;
        this.isEmpty = this.list.length === 0;
        this.loadStatus = this.list.length >= this.totalCount ? "noMore" : "more";
        setTimeout(() => {
          this.isThrottling = false;
        }, 100);
      } else {
        this.loadStatus = "loading";
        this.userDynamic().finally(() => {
          setTimeout(() => {
            this.isThrottling = false;
          }, 300);
        });
      }
    },
    delClick(e) {
      common_vendor.index.showModal({
        content: "确定删除该笔记吗？",
        confirmColor: "#FA5150",
        success: (res) => {
          if (res.confirm) {
            api_social.deleteDynamic(this.list[e.idx].id).then((res2) => {
              if (res2.status == 200) {
                this.list.splice(e.idx, 1);
                if (this.list.length <= 0) {
                  this.isEmpty = true;
                }
                common_vendor.index.showToast({ title: "删除成功", icon: "success" });
              } else {
                common_vendor.index.showToast({ title: res2.msg || "删除失败", icon: "none" });
              }
            }).catch(() => {
              common_vendor.index.showToast({ title: "删除失败，请重试", icon: "none" });
            });
          }
        }
      });
    },
    likePopupClick(open) {
      if (open) {
        this.$refs.likePopup.open();
      } else {
        this.$refs.likePopup.close();
      }
    },
    navigateToFun(e) {
      if (!this.checkLoginStatus(true)) {
        return;
      }
      if (this.showSidebar) {
        this.showSidebar = false;
      }
      const url = e.currentTarget.dataset.url;
      if (url === "center/visitor") {
        this.getVisitorList();
      }
      common_vendor.index.navigateTo({ url: "/pages/" + url });
    },
    toFollowList(type) {
      if (!this.checkLoginStatus(true)) {
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/center/follow?type=${type}&id=${this.userInfo.uid}&name=${this.userInfo.nickname}`
      });
    },
    userClick() {
      this.blockList[0].img = this.userInfo.circle_img || "";
      this.blockList[0].count = this.userInfo.circle_count || 0;
      this.blockList[1].img = this.userInfo.cart_img || "";
      this.blockList[1].count = this.userInfo.cart_count || 0;
      this.blockList[2].img = this.userInfo.order_img || "";
      this.blockList[2].count = this.userInfo.order_count || 0;
    },
    navigationBarColor(status) {
      common_vendor.index.setNavigationBarColor({
        frontColor: status ? "#000000" : "#ffffff",
        backgroundColor: "transparent",
        animation: { duration: 300, timingFunc: "easeIn" }
      });
    },
    getSidebarMenu() {
      const cachedMenu = common_vendor.index.getStorageSync("SIDEBAR_MENU");
      const now = Date.now();
      if (cachedMenu && cachedMenu.timestamp && now - cachedMenu.timestamp < this.cacheTimeout) {
        this.sidebarMenu = cachedMenu.data || [];
        return Promise.resolve(this.sidebarMenu);
      }
      return new Promise((resolve, reject) => {
        api_user.getMenuList().then((res) => {
          if (res.data && res.data.routine_my_menus) {
            let storeMenu = [];
            let sidebarMenus = [];
            res.data.routine_my_menus.forEach((el) => {
              const menuItem = {
                name: el.name,
                icon: el.pic,
                url: el.url.replace("/pages/", ""),
                badge: ""
              };
              if (el.url == "/pages/admin/order/index" || el.url == "/pages/admin/order_cancellation/index" || el.name == "客服接待") {
                storeMenu.push(menuItem);
              } else {
                sidebarMenus.push(menuItem);
              }
            });
            this.sidebarMenu = [...sidebarMenus, ...storeMenu];
            common_vendor.index.setStorageSync("SIDEBAR_MENU", {
              data: this.sidebarMenu,
              timestamp: now
            });
            resolve(this.sidebarMenu);
          } else {
            reject(new Error("获取菜单数据失败"));
          }
        }).catch((err) => {
          reject(err);
        });
      });
    },
    toggleSidebar() {
      this.showSidebar = !this.showSidebar;
      this.$store.commit("SET_PREVENT_SCROLL", this.showSidebar);
    },
    getVisitorList() {
      this.userInfo.visitor_badge = 0;
      this.$store.commit("UPDATE_USERINFO", this.userInfo);
      api_social.getVisitorDetails({ page: 1, limit: 20, type: 0 }).then((res) => {
        const emptyData = { visitors: [], total: 0, has_more: false };
        if (res.status === 200 || res.code === 200) {
          const resData = res.data || {};
          this.userInfo.visitor_count = resData.total || 0;
          this.userInfo.visitor_badge = 0;
          this.$store.commit("UPDATE_USERINFO", this.userInfo);
          const hasMore = resData.page * resData.limit < resData.total;
          common_vendor.index.$emit("updateVisitorList", {
            visitors: resData.list || [],
            total: resData.total || 0,
            has_more: hasMore,
            page: resData.page || 1,
            limit: resData.limit || 20
          });
        } else {
          this.userInfo.visitor_count = 0;
          this.userInfo.visitor_badge = 0;
          this.$store.commit("UPDATE_USERINFO", this.userInfo);
          common_vendor.index.$emit("updateVisitorList", emptyData);
        }
      }).catch((error) => {
        common_vendor.index.__f__("error", "at pages/index/center.vue:1197", "获取访客列表失败:", error);
        this.userInfo.visitor_count = 0;
        this.userInfo.visitor_badge = 0;
        this.$store.commit("UPDATE_USERINFO", this.userInfo);
        common_vendor.index.$emit("updateVisitorList", { visitors: [], total: 0, has_more: false });
      });
    },
    setVisit() {
      api_user.setVisit({ url: "/pages/tabbar/center" }).catch(() => {
      });
    },
    formatDate(timestamp) {
      if (!timestamp)
        return "";
      const date = new Date(timestamp * 1e3);
      return `${date.getFullYear()}-${("0" + (date.getMonth() + 1)).slice(-2)}-${("0" + date.getDate()).slice(-2)}`;
    },
    goToVipPage() {
      if (!this.checkLoginStatus(true)) {
        return;
      }
      common_vendor.index.navigateTo({ url: "/pages/annex/vip_paid/index" });
    },
    handleUserInfoUpdate() {
      this.loadUserFromCache();
    },
    handleBottomNav(type) {
      if (!this.checkLoginStatus(true)) {
        return;
      }
      this.showSidebar = false;
      switch (type) {
        case "scan":
          common_vendor.index.scanCode({
            success: (res) => {
              if (res.result) {
                common_vendor.index.showToast({ title: "扫码成功", icon: "success" });
              }
            },
            fail: () => {
              common_vendor.index.showToast({ title: "扫码失败", icon: "none" });
            }
          });
          break;
        case "help":
          common_vendor.index.navigateTo({ url: "/pages/setting/service" });
          break;
        case "setting":
          common_vendor.index.navigateTo({ url: "/pages/setting/index" });
          break;
      }
    },
    // 模板辅助方法
    getEmptyTitle() {
      const titles = ["暂无笔记内容", "暂无喜欢的内容"];
      return titles[this.barIdx] || "暂无内容";
    },
    getEmptySubtitle() {
      if (this.barIdx === 1) {
        return "快在推荐中寻找更多笔记吧";
      }
      return "发笔记，记录灵感日常";
    },
    handleLogin() {
      libs_login.toLogin();
    }
  },
  onReachBottom() {
    if (this.loadingState.dynamicList || this.loadingState.loadMore || this.loadStatus === "noMore") {
      return;
    }
    if (this.list.length && this.list.length < this.totalCount) {
      this.loadingState.loadMore = true;
      this.loadStatus = "loading";
      const currentPage = this.page;
      this.page++;
      this.userDynamic(false, false).catch((err) => {
        common_vendor.index.__f__("log", "at pages/index/center.vue:1296", "加载更多数据失败:", err);
        this.page = currentPage;
        this.loadStatus = "more";
        if (err.message !== "正在加载中") {
          this.showErrorToast("加载更多失败，请稍后再试", 2e3);
        }
      }).finally(() => {
        this.loadingState.loadMore = false;
      });
    } else if (this.list.length >= this.totalCount && this.list.length > 0) {
      this.loadStatus = "noMore";
    }
  },
  onPageScroll(e) {
    if (this.showSidebar) {
      return;
    }
    this.scrollTop = e.scrollTop;
    const threshold = this.statusBarHeight + this.titleBarHeight + 80;
    if (this.scrollTop <= threshold) {
      this.navbarTrans = Math.min(this.scrollTop / threshold, 1);
    } else {
      this.navbarTrans = 1;
    }
    if (this.scrollTop > threshold) {
      this.navigationBarColor(1);
    } else {
      this.navigationBarColor(0);
    }
  }
};
if (!Array) {
  const _component_lazy_image = common_vendor.resolveComponent("lazy-image");
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  const _component_card_gg = common_vendor.resolveComponent("card-gg");
  const _component_card_wd = common_vendor.resolveComponent("card-wd");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  const _component_tabbar = common_vendor.resolveComponent("tabbar");
  (_component_lazy_image + _easycom_uni_load_more2 + _component_card_gg + _component_card_wd + _easycom_uni_popup2 + _component_tabbar)();
}
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_load_more + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$6,
    b: common_vendor.o((...args) => $options.toggleSidebar && $options.toggleSidebar(...args)),
    c: $data.userInfo.avatar,
    d: common_vendor.t($data.userInfo.nickname),
    e: "translateY(" + (1 - $data.navbarTrans) * 30 + "px)",
    f: $data.navbarTrans,
    g: $data.titleBarHeight + "px",
    h: $data.statusBarHeight + "px",
    i: "rgba(255,255,255," + $data.navbarTrans + ")",
    j: common_vendor.p({
      src: $data.userInfo.avatar || "/static/img/avatar.png",
      mode: "aspectFill"
    }),
    k: common_vendor.p({
      src: $data.userInfo.avatar
    }),
    l: common_vendor.t($data.userInfo.nickname),
    m: $data.userInfo.is_money_level > 0 && $data.userInfo.svip_open
  }, $data.userInfo.is_money_level > 0 && $data.userInfo.svip_open ? {
    n: common_assets._imports_1$3
  } : {}, {
    o: $data.userInfo.is_verified
  }, $data.userInfo.is_verified ? {
    p: common_assets._imports_0$5
  } : {}, {
    q: $data.userInfo.sex != 2
  }, $data.userInfo.sex != 2 ? {
    r: $data.userInfo.sex == 1 ? "/static/img/nan.png" : "/static/img/nv.png"
  } : {}, {
    s: common_vendor.t($data.userInfo.user_id_number),
    t: common_assets._imports_1$4,
    v: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    w: common_vendor.t($data.userInfo.follow_count),
    x: common_vendor.o(($event) => $options.toFollowList(0)),
    y: common_vendor.t($data.userInfo.fans_count),
    z: common_vendor.o(($event) => $options.toFollowList(1)),
    A: common_vendor.t($data.userInfo.like_count_str),
    B: common_vendor.o(($event) => $options.likePopupClick(true)),
    C: common_vendor.t($data.userInfo.visitor_count),
    D: $data.userInfo.visitor_badge
  }, $data.userInfo.visitor_badge ? {
    E: common_vendor.t($data.userInfo.visitor_badge > 99 ? "99+" : $data.userInfo.visitor_badge)
  } : {}, {
    F: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    G: $data.userInfo.interest_tags && $data.userInfo.interest_tags.length
  }, $data.userInfo.interest_tags && $data.userInfo.interest_tags.length ? {
    H: common_vendor.f($data.userInfo.interest_tags, (tag, index, i0) => {
      return {
        a: common_vendor.t(tag),
        b: index
      };
    })
  } : {}, {
    I: common_assets._imports_7,
    J: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    K: common_vendor.t($data.userInfo.about_me ? $data.userInfo.about_me : "添加个人简介，让大家认识你..."),
    L: common_assets._imports_5,
    M: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    N: $data.statusBarHeight + $data.titleBarHeight + "px",
    O: $data.userInfo.activity_count
  }, $data.userInfo.activity_count ? common_vendor.e({
    P: common_vendor.t($data.userInfo.activity_count ? "共" + $data.userInfo.activity_count + "个活动" : "没有参加活动"),
    Q: !$data.userInfo.activity_img
  }, !$data.userInfo.activity_img ? {
    R: common_assets._imports_3$5
  } : {
    S: $data.userInfo.activity_img
  }, {
    T: $data.userInfo.activity_img ? "#CECECE" : "#000",
    U: common_assets._imports_1$4,
    V: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args))
  }) : {}, {
    W: common_vendor.f($data.blockList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.name),
        b: item.count
      }, item.count ? {
        c: common_vendor.t(item.count),
        d: common_vendor.t(index == 0 ? "个圈子" : index == 1 ? "件商品" : "笔订单")
      } : {
        e: common_vendor.t(index == 0 ? "没有加入圈子" : index == 1 ? "购物车空空的" : "订单空空的")
      }, {
        f: !item.img
      }, !item.img ? {
        g: item.icon
      } : {
        h: item.img
      }, {
        i: item.img ? "#CECECE" : "#000",
        j: index,
        k: item.url,
        l: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), index)
      });
    }),
    X: common_assets._imports_1$4,
    Y: $data.appCard || $data.userInfo.card_count
  }, $data.appCard || $data.userInfo.card_count ? {
    Z: common_vendor.t($data.userInfo.card_count ? "共" + $data.userInfo.card_count + "张卡券" : "暂无可用卡券"),
    aa: common_assets._imports_3$4,
    ab: common_assets._imports_1$4,
    ac: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args))
  } : {}, {
    ad: common_vendor.f($data.barList, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index == $data.barIdx ? "#000" : "#999",
        c: index == $data.barIdx ? "28rpx" : "26rpx",
        d: index == $data.barIdx ? 1 : 0,
        e: index,
        f: common_vendor.o((...args) => $options.barClick && $options.barClick(...args), index),
        g: index
      };
    }),
    ae: $data.statusBarHeight + $data.titleBarHeight - 1 + "px",
    af: !$data.isThrottling && ($data.loadStatus == "loading" || $data.loadingState.pullRefresh) && $data.list.length === 0
  }, !$data.isThrottling && ($data.loadStatus == "loading" || $data.loadingState.pullRefresh) && $data.list.length === 0 ? {
    ag: common_vendor.p({
      status: "loading"
    })
  } : {}, {
    ah: !$data.isThrottling && ($data.loadStatus == "loading" || $data.loadingState.pullRefresh) && $data.list.length === 0 ? "60rpx" : "0px",
    ai: $data.isEmpty && !$data.loadingState.dynamicList
  }, $data.isEmpty && !$data.loadingState.dynamicList ? {
    aj: common_assets._imports_3$1,
    ak: common_vendor.t($options.getEmptyTitle()),
    al: common_vendor.t($options.getEmptySubtitle())
  } : $data.loadingState.dynamicList && $data.list.length === 0 ? {
    an: common_vendor.p({
      status: "loading"
    })
  } : {
    ao: common_vendor.f($data.list, (item, index, i0) => {
      return common_vendor.e($data.barIdx == 1 ? {
        a: "fd24fe58-4-" + i0,
        b: common_vendor.p({
          item,
          idx: index
        })
      } : {
        c: common_vendor.o($options.delClick, item.id || index),
        d: "fd24fe58-5-" + i0,
        e: common_vendor.p({
          item,
          idx: index,
          bar: $data.barIdx
        })
      }, {
        f: item.id || index
      });
    }),
    ap: $data.barIdx == 1
  }, {
    am: $data.loadingState.dynamicList && $data.list.length === 0,
    aq: $data.list.length > 0
  }, $data.list.length > 0 ? {
    ar: common_vendor.p({
      status: $data.loadStatus
    })
  } : {}, {
    as: common_assets._imports_9,
    at: common_vendor.t($data.userInfo.nickname),
    av: common_vendor.t($data.userInfo.like_count || 0),
    aw: common_vendor.o(($event) => $options.likePopupClick(false)),
    ax: common_vendor.sr("likePopup", "fd24fe58-7"),
    ay: common_vendor.p({
      currentPage: 4,
      currentMsg: $data.currentMsg,
      userAvatar: $data.userInfo.avatar
    }),
    az: $data.userInfo.avatar || "/static/img/avatar.png",
    aA: common_vendor.t($data.userInfo.nickname),
    aB: $data.userInfo.is_money_level > 0 && $data.userInfo.svip_open
  }, $data.userInfo.is_money_level > 0 && $data.userInfo.svip_open ? {
    aC: common_assets._imports_1$3
  } : {}, {
    aD: $data.userInfo.is_verified
  }, $data.userInfo.is_verified ? {
    aE: common_assets._imports_0$5
  } : {}, {
    aF: common_assets._imports_0$4,
    aG: common_vendor.o((...args) => $options.toggleSidebar && $options.toggleSidebar(...args)),
    aH: $data.statusBarHeight + 10 + "px",
    aI: $data.userInfo.vip_status == 1
  }, $data.userInfo.vip_status == 1 ? {} : $data.userInfo.vip_status == 3 ? {
    aK: common_vendor.t($options.formatDate($data.userInfo.overdue_time))
  } : $data.userInfo.vip_status == -1 ? {} : $data.userInfo.vip_status == 2 ? {} : {}, {
    aJ: $data.userInfo.vip_status == 3,
    aL: $data.userInfo.vip_status == -1,
    aM: $data.userInfo.vip_status == 2,
    aN: $data.userInfo.vip_status != 1
  }, $data.userInfo.vip_status != 1 ? {
    aO: common_vendor.o((...args) => $options.goToVipPage && $options.goToVipPage(...args))
  } : {}, {
    aP: common_vendor.o((...args) => $options.goToVipPage && $options.goToVipPage(...args)),
    aQ: common_vendor.f($data.sidebarMenu, (item, index, i0) => {
      return common_vendor.e({
        a: item.icon,
        b: item.badge
      }, item.badge ? {
        c: common_vendor.t(item.badge)
      } : {}, {
        d: common_vendor.t(item.name),
        e: "menu-" + index,
        f: item.url,
        g: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), "menu-" + index)
      });
    }),
    aR: common_assets._imports_11,
    aS: common_vendor.o(($event) => $options.handleBottomNav("scan")),
    aT: common_assets._imports_2$3,
    aU: common_vendor.o(($event) => $options.handleBottomNav("help")),
    aV: common_assets._imports_7,
    aW: common_vendor.o(($event) => $options.handleBottomNav("setting")),
    aX: common_vendor.t($data.currentYear),
    aY: $data.showSidebar ? 1 : "",
    aZ: $data.showSidebar
  }, $data.showSidebar ? {
    ba: common_vendor.o((...args) => $options.toggleSidebar && $options.toggleSidebar(...args)),
    bb: common_vendor.o(() => {
    }),
    bc: common_vendor.o(() => {
    }),
    bd: common_vendor.o(() => {
    })
  } : {}, {
    be: $data.showSidebar ? 1 : ""
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 1;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/center.js.map
