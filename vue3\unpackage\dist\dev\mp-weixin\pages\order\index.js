"use strict";
const common_vendor = require("../../common/vendor.js");
const config_mock = require("../../config/mock.js");
const common_assets = require("../../common/assets.js");
const navbar = () => "../../components/navbar/navbar.js";
const lazyImage = () => "../../components/lazyImage/lazyImage.js";
const money = () => "../../components/money/money.js";
const uniLoadMore = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const emptyPage = () => "../../components/emptyPage/emptyPage.js";
const _sfc_main = {
  components: {
    navbar,
    lazyImage,
    money,
    uniLoadMore,
    emptyPage
  },
  data() {
    return {
      statusBarHeight: 20,
      titleBarHeight: 44,
      barList: ["全部", "待付款", "待发货", "待收货", "评价", "售后"],
      barIdx: 0,
      isThrottling: true,
      list: [],
      listIdx: 0,
      page: 1,
      isEmpty: false,
      loadStatus: "more",
      tipsTitle: "",
      useMockData: true
      // 开发环境下使用mock数据
    };
  },
  onLoad(options) {
    if (options.idx) {
      this.barIdx = parseInt(options.idx);
    }
    this.orderList();
  },
  onReachBottom() {
    if (this.list.length) {
      this.loadStatus = "loading";
      this.page = this.page + 1;
      this.orderList();
    }
  },
  methods: {
    // 获取订单列表
    orderList() {
      let that = this;
      if (this.useMockData) {
        setTimeout(() => {
          that.isThrottling = true;
          that.loadStatus = "more";
          let filteredOrders = [...config_mock.orderList];
          if (that.barIdx > 0 && that.barIdx <= 5) {
            filteredOrders = filteredOrders.filter(
              (order) => order.status == that.barIdx
            );
          }
          if (filteredOrders.length > 0) {
            const pageSize = 3;
            const startIndex = (that.page - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageData = filteredOrders.slice(startIndex, endIndex);
            if (pageData.length > 0) {
              if (that.page === 1) {
                that.list = pageData;
              } else {
                that.list = that.list.concat(pageData);
              }
              that.isEmpty = false;
              that.loadStatus = endIndex >= filteredOrders.length ? "noMore" : "more";
            } else {
              that.loadStatus = "noMore";
            }
          } else {
            if (that.page === 1) {
              that.isEmpty = true;
              that.list = [];
            } else {
              that.loadStatus = "noMore";
            }
          }
        }, 500);
        return;
      }
      request("order/list", {
        type: that.barIdx,
        page: that.page
      }).then(function(res) {
        that.isThrottling = true;
        that.loadStatus = "more";
        if (res.code == 200 && res.data && res.data.data) {
          if (res.data.data.length > 0) {
            if (that.page === 1) {
              that.list = res.data.data;
            } else {
              that.list = that.list.concat(res.data.data);
            }
            that.page = res.data.current_page || that.page;
            that.isEmpty = false;
            if (res.data.current_page >= res.data.last_page) {
              that.loadStatus = "noMore";
            }
          } else if (that.page === 1) {
            that.isEmpty = true;
            that.list = [];
          } else {
            that.loadStatus = "noMore";
          }
        } else {
          that.loadStatus = "more";
          showToast(res.msg || "获取订单列表失败");
          if (that.page === 1) {
            that.list = config_mock.orderList;
            that.isEmpty = that.list.length === 0;
          }
        }
      }).catch(() => {
        that.isThrottling = true;
        if (that.page === 1) {
          that.list = [...config_mock.orderList];
          that.isEmpty = that.list.length === 0;
        }
        that.loadStatus = "noMore";
      });
    },
    // 支付
    paymentClick(e) {
      let that = this;
      common_vendor.index.showLoading({
        title: "唤起支付中..",
        mask: true
      });
      if (this.useMockData) {
        setTimeout(() => {
          common_vendor.index.hideLoading();
          that.opTipsPopup("支付成功，我们会尽快为您发货 🎉");
          that.barIdx = 2;
          that.isThrottling = false;
          that.page = 1;
          that.orderList();
        }, 1e3);
        return;
      }
      request("order/payment", {
        id: e.currentTarget.dataset.id
      }, "POST").then(function(res) {
        common_vendor.index.hideLoading();
        if (res.code == 200) {
          let payData = res.data;
          common_vendor.index.requestPayment({
            provider: "weixin",
            timeStamp: payData.timestamp,
            nonceStr: payData.nonceStr,
            package: payData.package,
            signType: payData.signType,
            paySign: payData.paySign,
            success: function() {
              that.opTipsPopup("支付成功，我们会尽快为您发货 🎉");
              that.barIdx = 2;
              that.isThrottling = false;
              that.page = 1;
              that.orderList();
            },
            fail: function() {
              showToast("支付失败或已取消");
            }
          });
        } else {
          showToast(res.msg || "支付异常，请稍后重试");
        }
      }).catch(() => {
        common_vendor.index.hideLoading();
        showToast("网络异常，请稍后重试");
      });
    },
    // 编辑订单(取消订单/确认收货/删除订单)
    editClick(e) {
      let that = this;
      let idx = e.currentTarget.dataset.idx;
      let type = e.currentTarget.dataset.type;
      let refund = 0;
      if (type == 1 && that.list[idx].pay_status == 1 && that.list[idx].status == 2) {
        refund = 1;
      }
      let tipText = "确定要";
      if (type == 0)
        tipText += "删除";
      else if (type == 1)
        tipText += "取消";
      else if (type == 2)
        tipText += "确认收货";
      tipText += "吗？";
      if (type == 0)
        tipText += "删除后将无法恢复。";
      common_vendor.index.showModal({
        title: "提示",
        content: tipText,
        success: function(res) {
          if (res.confirm) {
            common_vendor.index.showLoading({
              mask: true
            });
            if (that.useMockData) {
              setTimeout(() => {
                common_vendor.index.hideLoading();
                if (type == 0) {
                  that.list.splice(idx, 1);
                  if (that.list.length <= 0) {
                    that.isEmpty = true;
                  }
                  that.opTipsPopup("删除成功");
                } else if (type == 1) {
                  if (that.barIdx == 0) {
                    that.list[idx].status = 0;
                    that.list[idx].status_name = "已取消";
                  } else {
                    that.list.splice(idx, 1);
                    if (that.list.length <= 0) {
                      that.isEmpty = true;
                    }
                  }
                  that.opTipsPopup("取消成功");
                } else if (type == 2) {
                  if (that.barIdx == 0) {
                    that.list[idx].status = 4;
                    that.list[idx].status_name = "待评价";
                  } else {
                    that.list.splice(idx, 1);
                    if (that.list.length <= 0) {
                      that.isEmpty = true;
                    }
                  }
                  that.opTipsPopup("确认收货成功");
                }
                getApp().globalData.isCenterPage = true;
              }, 1e3);
              return;
            }
            request("order/edit", {
              id: that.list[idx].id,
              type,
              refund
            }, "POST").then(function(res2) {
              common_vendor.index.hideLoading();
              that.opTipsPopup(res2.msg);
              if (res2.code == 200) {
                if (that.barIdx == 0 && type == 1) {
                  that.list[idx].status = 0;
                  that.list[idx].status_name = "已取消";
                } else if (that.barIdx == 0 && type == 2) {
                  that.list[idx].status = 4;
                  that.list[idx].status_name = "待评价";
                } else {
                  that.list.splice(idx, 1);
                  if (that.list.length <= 0) {
                    that.isEmpty = true;
                  }
                  getApp().globalData.isCenterPage = true;
                }
              }
            }).catch(() => {
              common_vendor.index.hideLoading();
              showToast("网络异常，请稍后重试");
            });
          }
        }
      });
    },
    // 催发货
    urgeDeliveryClick(e) {
      let idx = e.currentTarget.dataset.idx;
      this.opTipsPopup("操作成功，我们会尽快为您发货 🎉");
      if (!this.useMockData) {
        request("order/edit", {
          id: this.list[idx].id,
          type: 3,
          refund: 0
        }, "POST");
      }
    },
    // 打开评价选择弹窗
    openOrderNote(e) {
      this.listIdx = e.currentTarget.dataset.idx;
      this.$refs.notePopup.open();
    },
    // 去评价
    toOrderNote(type) {
      this.$refs.notePopup.close();
      common_vendor.index.navigateTo({
        url: "/pages/note/add?type=" + type + "&oid=" + this.list[this.listIdx].id
      });
    },
    // 查看订单详情
    toOrderDetails(e) {
      common_vendor.index.navigateTo({
        url: "/pages/order/details?id=" + e.currentTarget.dataset.id
      });
    },
    // 切换标签
    barClick(e) {
      if (this.isThrottling) {
        this.barIdx = e.currentTarget.dataset.idx;
        this.isThrottling = false;
        this.page = 1;
        this.orderList();
      }
    },
    // 返回上一页
    navBack() {
      if (getCurrentPages().length > 1) {
        common_vendor.index.navigateBack();
      } else {
        common_vendor.index.switchTab({
          url: "/pages/tabbar/center"
        });
      }
    },
    // 显示提示
    opTipsPopup(title) {
      let that = this;
      that.tipsTitle = title;
      that.$refs.tipsPopup.open();
      setTimeout(function() {
        that.$refs.tipsPopup.close();
      }, 2e3);
    },
    // 去购物
    goToShop() {
      common_vendor.index.switchTab({
        url: "/pages/index/shop"
      });
    }
  }
};
if (!Array) {
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  const _component_emptyPage = common_vendor.resolveComponent("emptyPage");
  const _component_money = common_vendor.resolveComponent("money");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_easycom_uni_load_more2 + _component_emptyPage + _component_money + _easycom_uni_popup2)();
}
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_load_more + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_2$4,
    b: common_vendor.o((...args) => $options.navBack && $options.navBack(...args)),
    c: $data.titleBarHeight + "px",
    d: common_vendor.f($data.barList, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index == $data.barIdx ? "#000" : "#999",
        c: index == $data.barIdx ? "28rpx" : "26rpx",
        d: index == $data.barIdx ? 1 : 0,
        e: index,
        f: common_vendor.o((...args) => $options.barClick && $options.barClick(...args), index),
        g: index
      };
    }),
    e: $data.statusBarHeight + "px"
  }, {
    f: common_vendor.p({
      status: "loading"
    })
  }, {
    g: $data.isThrottling || $data.loadStatus == "loading" ? "0px" : "60rpx",
    h: $data.isEmpty
  }, $data.isEmpty ? {
    i: common_vendor.o($options.goToShop),
    j: common_vendor.p({
      title: "暂无相关订单",
      description: "去逛逛，挑点喜欢的产品下单吧",
      image: "/static/img/empty.png",
      buttonText: "去购物"
    })
  } : common_vendor.e({
    k: common_vendor.f($data.list, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.order_no),
        b: common_vendor.t(item.status_name)
      }, _ctx.imgIndex < 3 ? {
        c: common_vendor.f(item.goods, (img, imgIndex, i1) => {
          return {
            a: imgIndex,
            b: img.image
          };
        })
      } : {}, {
        d: item.goods.length > 3
      }, item.goods.length > 3 ? {
        e: common_vendor.t(item.goods.length - 3)
      } : {}, {
        f: "51d4b89f-2-" + i0,
        g: common_vendor.p({
          price: item.pay_price,
          size: "36"
        }),
        h: common_vendor.t(item.goods_count),
        i: item.express
      }, item.express ? {
        j: common_vendor.t(item.express),
        k: common_vendor.t(item.express_no)
      } : {}, {
        l: item.status == 0 || item.status == 4 || item.status == 5
      }, item.status == 0 || item.status == 4 || item.status == 5 ? {
        m: index,
        n: common_vendor.o((...args) => $options.editClick && $options.editClick(...args), item.id)
      } : {}, {
        o: (item.status == 1 || item.status == 2) && (item.pay_status == 1 || item.pay_status == 0)
      }, (item.status == 1 || item.status == 2) && (item.pay_status == 1 || item.pay_status == 0) ? {
        p: index,
        q: common_vendor.o((...args) => $options.editClick && $options.editClick(...args), item.id)
      } : {}, {
        r: item.status == 2
      }, item.status == 2 ? {
        s: index,
        t: common_vendor.o((...args) => $options.urgeDeliveryClick && $options.urgeDeliveryClick(...args), item.id)
      } : {}, {
        v: item.status == 3
      }, item.status == 3 ? {
        w: index,
        x: common_vendor.o((...args) => $options.editClick && $options.editClick(...args), item.id)
      } : {}, {
        y: item.status == 4
      }, item.status == 4 ? {
        z: index,
        A: common_vendor.o((...args) => $options.openOrderNote && $options.openOrderNote(...args), item.id)
      } : {}, {
        B: item.status == 1 && item.pay_status == 0
      }, item.status == 1 && item.pay_status == 0 ? {
        C: common_assets._imports_2$4,
        D: item.id,
        E: common_vendor.o((...args) => $options.paymentClick && $options.paymentClick(...args), item.id)
      } : {}, {
        F: item.id,
        G: item.id,
        H: common_vendor.o((...args) => $options.toOrderDetails && $options.toOrderDetails(...args), item.id)
      });
    }),
    l: _ctx.imgIndex < 3,
    m: $data.list.length > 0
  }, $data.list.length > 0 ? {
    n: common_vendor.p({
      status: $data.loadStatus
    })
  } : {}), {
    o: "calc(" + ($data.statusBarHeight + $data.titleBarHeight) + "px + 80rpx)",
    p: common_assets._imports_1$15,
    q: common_vendor.o(($event) => $options.toOrderNote(1)),
    r: common_assets._imports_1$15,
    s: common_vendor.o(($event) => $options.toOrderNote(2)),
    t: common_assets._imports_1$15,
    v: common_vendor.o(($event) => $options.toOrderNote(3)),
    w: common_vendor.sr("notePopup", "51d4b89f-4"),
    x: common_vendor.p({
      type: "center"
    }),
    y: common_vendor.t($data.tipsTitle),
    z: common_vendor.sr("tipsPopup", "51d4b89f-5"),
    A: common_vendor.p({
      type: "top",
      ["mask-background-color"]: "rgba(0, 0, 0, 0)"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/order/index.js.map
