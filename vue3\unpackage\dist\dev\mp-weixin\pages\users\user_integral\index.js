"use strict";
const api_user = require("../../../api/user.js");
const libs_login = require("../../../libs/login.js");
const common_vendor = require("../../../common/vendor.js");
const mixins_color = require("../../../mixins/color.js");
const common_assets = require("../../../common/assets.js");
const authorize = () => "../../../components/Authorize.js";
const _sfc_main = {
  components: {
    authorize
  },
  mixins: [mixins_color.colors],
  data() {
    return {
      navList: [
        {
          "name": this.$t(`分值明细`),
          "icon": "icon-mingxi"
        },
        {
          "name": this.$t(`分值提升`),
          "icon": "icon-tishengfenzhi"
        }
      ],
      current: 0,
      page: 1,
      limit: 10,
      integralList: [],
      userInfo: {},
      loadend: false,
      loading: false,
      loadTitle: this.$t(`加载更多`),
      isAuto: false,
      //没有授权的不会自动授权
      isShowAuth: false,
      //是否隐藏授权
      isTime: 0
    };
  },
  computed: common_vendor.mapGetters(["isLogin"]),
  watch: {
    isLogin: {
      handler: function(newV, oldV) {
        if (newV) {
          this.getUserInfo();
          this.getIntegralList();
        }
      },
      deep: true
    }
  },
  onLoad() {
    if (this.isLogin) {
      this.getUserInfo();
      this.getIntegralList();
    } else {
      libs_login.toLogin();
    }
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
    this.getIntegralList();
  },
  methods: {
    // Vue3: 过滤器已移除，改为方法
    dateFormat(value) {
      const date = new Date(value * 1e3);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    /**
     * 授权回调
     */
    onLoadFun: function() {
      this.getUserInfo();
      this.getIntegralList();
    },
    // 授权关闭
    authColse: function(e) {
      this.isShowAuth = e;
    },
    getUserInfo: function() {
      let that = this;
      api_user.postSignUser({
        sign: 1,
        integral: 1,
        all: 1
      }).then(function(res) {
        that.$set(that, "userInfo", res.data);
        let clearTime = res.data.clear_time;
        let showTime = clearTime - 86400 * 14;
        let timestamp = Date.parse(/* @__PURE__ */ new Date()) / 1e3;
        if (showTime < timestamp) {
          that.isTime = 1;
        } else {
          that.isTime = 0;
        }
      });
    },
    /**
     * 获取积分明细
     */
    getIntegralList: function() {
      let that = this;
      if (that.loading)
        return;
      if (that.loadend)
        return;
      that.loading = true;
      that.loadTitle = "";
      api_user.getIntegralList({
        page: that.page,
        limit: that.limit
      }).then(function(res) {
        let list = res.data, loadend = list.length < that.limit;
        that.integralList = that.$util.SplitArray(list, that.integralList);
        that.$set(that, "integralList", that.integralList);
        that.page = that.page + 1;
        that.loading = false;
        that.loadend = loadend;
        that.loadTitle = loadend ? that.$t(`我也是有底线的`) : that.$t(`加载更多`);
      }, function(res) {
        this.loading = false;
        that.loadTitle = that.$t(`加载更多`);
      });
    },
    nav: function(current) {
      this.current = current;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t(_ctx.$t(`当前积分`)),
    b: common_vendor.t($data.userInfo.integral),
    c: common_vendor.t($data.userInfo.sum_integral),
    d: common_vendor.t(_ctx.$t(`累计积分`)),
    e: common_vendor.t($data.userInfo.deduction_integral),
    f: common_vendor.t(_ctx.$t(`累计消费`)),
    g: common_vendor.t($data.userInfo.frozen_integral),
    h: common_vendor.t(_ctx.$t(`冻结积分`)),
    i: common_vendor.t(_ctx.$t(`积分规则`)),
    j: common_vendor.f($data.navList, (item, index, i0) => {
      return {
        a: common_vendor.n(item.icon),
        b: common_vendor.t(item.name),
        c: common_vendor.n($data.current == index ? "on" : ""),
        d: index,
        e: common_vendor.o(($event) => $options.nav(index), index)
      };
    }),
    k: !$data.isTime
  }, !$data.isTime ? {
    l: common_vendor.t(_ctx.$t(`提示：积分数值的高低会直接影响您的会员等级`))
  } : {
    m: common_vendor.t(_ctx.$t(`提示：你有`)),
    n: common_vendor.t($data.userInfo.clear_integral),
    o: common_vendor.t(_ctx.$t(`积分在`)),
    p: common_vendor.t($options.dateFormat($data.userInfo.clear_time)),
    q: common_vendor.t(_ctx.$t(`过期，请尽快使用`))
  }, {
    r: common_vendor.f($data.integralList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(_ctx.$t(item.title)),
        b: common_vendor.t(item.add_time),
        c: item.pm
      }, item.pm ? {
        d: common_vendor.t(item.number)
      } : {
        e: common_vendor.t(item.number)
      }, {
        f: index
      });
    }),
    s: $data.integralList.length > 0
  }, $data.integralList.length > 0 ? {
    t: common_vendor.t($data.loadTitle)
  } : {}, {
    v: $data.integralList.length == 0
  }, $data.integralList.length == 0 ? {} : {}, {
    w: $data.integralList.length ? 1 : "",
    x: common_assets._imports_0$19,
    y: common_vendor.t(_ctx.$t(`购买商品可获得积分奖励`)),
    z: common_vendor.t(_ctx.$t(`赚积分`)),
    A: common_assets._imports_0$19,
    B: common_vendor.t(_ctx.$t(`每日签到可获得积分奖励`)),
    C: common_vendor.t(_ctx.$t(`赚积分`)),
    D: common_vendor.s(_ctx.colorStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-9da6c933"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/users/user_integral/index.js.map
