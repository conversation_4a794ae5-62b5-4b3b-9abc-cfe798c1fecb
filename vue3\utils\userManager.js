// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

/**
 * 统一用户信息管理工具
 * 解决Vuex、Pinia、本地缓存之间的数据不一致问题
 */

import { useUserStore } from '@/stores/user.js'
import Cache from '@/utils/cache.js'
import cacheConfig from '@/config/cache.js'

const { UID, LOGIN_STATUS, USER_INFO } = cacheConfig

class UserManager {
  constructor() {
    this.userStore = null
    this.vuexStore = null
  }

  // 初始化，传入store实例
  init(vuexStore) {
    this.vuexStore = vuexStore
    this.userStore = useUserStore()
  }

  /**
   * 统一获取用户信息
   * 优先级：缓存 > Pinia > Vuex
   */
  getUserInfo() {
    try {
      // 从各个数据源获取用户信息
      const vuexUserInfo = this.vuexStore?.state?.app?.userInfo || {}
      const vuexUid = this.vuexStore?.state?.app?.uid || 0
      const vuexToken = this.vuexStore?.state?.app?.token || ''
      
      const piniaUserInfo = this.userStore?.userInfo || {}
      const piniaUid = this.userStore?.uid || 0
      const piniaToken = this.userStore?.token || ''
      
      const cachedUserInfo = uni.getStorageSync('USER_INFO') || {}
      const cachedUid = uni.getStorageSync('UID') || 0
      const cachedToken = uni.getStorageSync('token') || uni.getStorageSync('LOGIN_STATUS_TOKEN') || ''

      // 确定最权威的数据源
      const finalToken = cachedToken || piniaToken || vuexToken
      const finalUid = cachedUid || piniaUid || vuexUid || cachedUserInfo.uid || piniaUserInfo.uid || vuexUserInfo.uid || 0
      
      // 合并用户信息，确保uid一致性
      const finalUserInfo = {
        ...vuexUserInfo,
        ...piniaUserInfo,
        ...cachedUserInfo,
        uid: finalUid
      }

      return {
        userInfo: finalUserInfo,
        uid: finalUid,
        token: finalToken,
        isLogin: !!(finalToken && finalUid && finalUserInfo.nickname)
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return {
        userInfo: {},
        uid: 0,
        token: '',
        isLogin: false
      }
    }
  }

  /**
   * 统一更新用户信息到所有数据源
   */
  updateUserInfo(newUserInfo, options = {}) {
    try {
      if (!newUserInfo || typeof newUserInfo !== 'object') {
        console.warn('updateUserInfo: 无效的用户信息', newUserInfo)
        return false
      }

      const { syncToVuex = true, syncToPinia = true, syncToCache = true } = options

      // 获取当前用户信息
      const current = this.getUserInfo()
      const currentUid = current.uid || newUserInfo.uid || 0
      
      // 合并用户信息
      const finalUserInfo = {
        ...current.userInfo,
        ...newUserInfo,
        uid: currentUid
      }

      // 更新Vuex
      if (syncToVuex && this.vuexStore) {
        this.vuexStore.commit('UPDATE_USERINFO', finalUserInfo)
        if (currentUid) {
          this.vuexStore.commit('SETUID', currentUid)
        }
      }

      // 更新Pinia
      if (syncToPinia && this.userStore) {
        this.userStore.updateUserInfo(finalUserInfo)
        if (currentUid) {
          this.userStore.setUid(currentUid)
        }
      }

      // 更新缓存
      if (syncToCache) {
        uni.setStorageSync('USER_INFO', finalUserInfo)
        if (currentUid) {
          uni.setStorageSync('UID', currentUid)
        }
      }

      console.log('用户信息已统一更新:', {
        uid: currentUid,
        nickname: finalUserInfo.nickname,
        updateFields: Object.keys(newUserInfo),
        syncOptions: { syncToVuex, syncToPinia, syncToCache }
      })

      return true
    } catch (error) {
      console.error('统一更新用户信息失败:', error)
      return false
    }
  }

  /**
   * 设置登录状态和token
   */
  setLoginStatus(token, userInfo = null) {
    try {
      if (!token) {
        console.warn('setLoginStatus: token为空')
        return false
      }

      // 更新Vuex
      if (this.vuexStore) {
        this.vuexStore.commit('UPDATE_LOGIN', token)
      }

      // 更新Pinia
      if (this.userStore) {
        this.userStore.setToken(token)
        this.userStore.setLoginStatus(true)
      }

      // 更新缓存
      uni.setStorageSync('token', token)
      Cache.set(LOGIN_STATUS, token)

      // 如果提供了用户信息，一并更新
      if (userInfo) {
        this.updateUserInfo(userInfo)
      }

      console.log('登录状态已设置:', { hasToken: !!token, hasUserInfo: !!userInfo })
      return true
    } catch (error) {
      console.error('设置登录状态失败:', error)
      return false
    }
  }

  /**
   * 统一登出，清理所有数据源
   */
  logout() {
    try {
      // 重置Vuex
      if (this.vuexStore) {
        this.vuexStore.commit('LOGOUT')
      }

      // 重置Pinia
      if (this.userStore) {
        this.userStore.logout()
      }

      // 清除所有相关缓存
      const keysToRemove = [
        'USER_INFO', 'UID', 'token', 'LOGIN_STATUS_TOKEN',
        'pinia_user', 'pinia_social', 'pinia_app'
      ]

      keysToRemove.forEach(key => {
        try {
          uni.removeStorageSync(key)
        } catch (e) {
          console.warn(`清除${key}失败:`, e)
        }
      })

      // 清除旧系统缓存
      Cache.clear(LOGIN_STATUS)
      Cache.clear(USER_INFO)
      Cache.clear(UID)

      console.log('用户已统一登出，所有数据源已清理')
      return true
    } catch (error) {
      console.error('统一登出失败:', error)
      return false
    }
  }

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const { isLogin, token, uid, userInfo } = this.getUserInfo()
    return {
      isLogin,
      hasToken: !!token,
      hasUid: !!uid,
      hasUserInfo: !!(userInfo && userInfo.nickname),
      valid: isLogin && !!token && !!uid && !!(userInfo && userInfo.nickname)
    }
  }

  /**
   * 同步所有数据源
   */
  syncAllSources() {
    const current = this.getUserInfo()
    if (current.isLogin && current.userInfo.nickname) {
      this.updateUserInfo(current.userInfo)
      if (current.token) {
        this.setLoginStatus(current.token)
      }
    }
  }
}

// 创建单例实例
const userManager = new UserManager()

export default userManager
