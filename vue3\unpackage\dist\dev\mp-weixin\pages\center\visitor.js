"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_social = require("../../api/social.js");
if (!Math) {
  (emptyPage + common_vendor.unref(uniLoadMore))();
}
const uniLoadMore = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const emptyPage = () => "../../components/emptyPage/emptyPage.js";
const _sfc_main = /* @__PURE__ */ Object.assign({
  name: "VisitorPage"
}, {
  __name: "visitor",
  setup(__props) {
    const store = common_vendor.useStore();
    const isLogin = common_vendor.computed(() => store.getters.isLogin);
    common_vendor.computed(() => store.getters.userInfo);
    const tabList = common_vendor.ref(["谁看过我", "我看过谁"]);
    const currentTab = common_vendor.ref(0);
    const visitorList = common_vendor.ref([]);
    const page = common_vendor.ref(1);
    const limit = common_vendor.ref(20);
    const totalVisitors = common_vendor.ref(0);
    const todayVisitors = common_vendor.ref(0);
    const loading = common_vendor.ref(false);
    const isEmpty = common_vendor.ref(false);
    const loadStatus = common_vendor.ref("more");
    const dataFromCenter = common_vendor.ref(false);
    const loadText = common_vendor.reactive({
      contentdown: "上拉显示更多",
      contentrefresh: "正在加载...",
      contentnomore: "没有更多数据了"
    });
    const initPage = () => {
      if (!isLogin.value) {
        common_vendor.index.showToast({
          title: "请先登录",
          icon: "none"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
        return;
      }
      setTimeout(() => {
        if (visitorList.value.length === 0) {
          loadVisitorData();
        }
      }, 100);
    };
    const switchTab = (index) => {
      if (currentTab.value === index || loading.value)
        return;
      currentTab.value = index;
      page.value = 1;
      visitorList.value = [];
      isEmpty.value = false;
      loadStatus.value = "more";
      dataFromCenter.value = false;
      loadVisitorData(true);
    };
    const loadVisitorData = async (isRefresh = false) => {
      if (loading.value)
        return;
      if (isRefresh) {
        page.value = 1;
        visitorList.value = [];
        isEmpty.value = false;
        loadStatus.value = "more";
      }
      loading.value = true;
      try {
        const res = await api_social.getVisitorDetails({
          page: page.value,
          limit: limit.value,
          type: currentTab.value
          // 0-谁看过我，1-我看过谁
        });
        if (res.status === 200 || res.code === 200) {
          const data = res.data || {};
          const list = data.list || [];
          if (isRefresh) {
            visitorList.value = list;
          } else {
            visitorList.value = [...visitorList.value, ...list];
          }
          totalVisitors.value = data.total || 0;
          calculateTodayVisitors(list);
          const hasMore = page.value * limit.value < totalVisitors.value;
          if (!hasMore) {
            loadStatus.value = "noMore";
          }
          isEmpty.value = visitorList.value.length === 0;
        } else {
          handleLoadError(res.msg || "获取访客数据失败");
        }
      } catch (error) {
        handleLoadError("网络错误，请稍后重试");
      } finally {
        loading.value = false;
        if (isRefresh) {
          common_vendor.index.stopPullDownRefresh();
        }
      }
    };
    const handleLoadError = (message) => {
      common_vendor.index.showToast({
        title: message,
        icon: "none"
      });
      if (page.value === 1) {
        isEmpty.value = true;
      }
    };
    const calculateTodayVisitors = (list) => {
      const today = /* @__PURE__ */ new Date();
      const todayStr = today.getFullYear() + "-" + String(today.getMonth() + 1).padStart(2, "0") + "-" + String(today.getDate()).padStart(2, "0");
      todayVisitors.value = list.filter((visitor) => {
        if (typeof visitor.last_visit_time_str === "string") {
          const visitDate = visitor.last_visit_time_str.split(" ")[0];
          return visitDate === todayStr;
        } else if (typeof visitor.last_visit_time === "string") {
          const visitDate = visitor.last_visit_time.split(" ")[0];
          return visitDate === todayStr;
        }
        return false;
      }).length;
    };
    const refreshData = () => {
      loadVisitorData(true);
    };
    const loadMore = () => {
      if (loadStatus.value === "noMore" || loading.value)
        return;
      loadStatus.value = "loading";
      page.value++;
      loadVisitorData();
    };
    const handleVisitorUpdate = (data) => {
      if (data && data.visitors && !dataFromCenter.value) {
        visitorList.value = data.visitors;
        totalVisitors.value = data.total || 0;
        calculateTodayVisitors(data.visitors);
        isEmpty.value = visitorList.value.length === 0;
        if (data.page) {
          page.value = data.page;
        }
        if (data.limit) {
          limit.value = data.limit;
        }
        loadStatus.value = data.has_more ? "more" : "noMore";
        dataFromCenter.value = true;
      }
    };
    const goToUserProfile = (uid) => {
      if (!uid)
        return;
      common_vendor.index.navigateTo({
        url: `/pages/user/details?uid=${uid}`
      });
    };
    const formatVisitTime = (timeStr) => {
      if (!timeStr)
        return "";
      let visitTime;
      if (typeof timeStr === "string") {
        visitTime = new Date(timeStr).getTime();
      } else if (typeof timeStr === "number") {
        visitTime = timeStr > 1e12 ? timeStr : timeStr * 1e3;
      } else {
        return "";
      }
      const now = Date.now();
      const diff = now - visitTime;
      if (diff < 60 * 1e3) {
        return "刚刚";
      }
      if (diff < 60 * 60 * 1e3) {
        const minutes = Math.floor(diff / (60 * 1e3));
        return `${minutes}分钟前`;
      }
      if (diff < 24 * 60 * 60 * 1e3) {
        const hours = Math.floor(diff / (60 * 60 * 1e3));
        return `${hours}小时前`;
      }
      if (diff < 7 * 24 * 60 * 60 * 1e3) {
        const days = Math.floor(diff / (24 * 60 * 60 * 1e3));
        return `${days}天前`;
      }
      const date = new Date(visitTime);
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${month}月${day}日`;
    };
    common_vendor.onLoad(() => {
      initPage();
    });
    common_vendor.onShow(() => {
      common_vendor.index.$on("updateVisitorList", handleVisitorUpdate);
      if (visitorList.value.length === 0 && !loading.value) {
        loadVisitorData(true);
      }
    });
    common_vendor.onHide(() => {
      common_vendor.index.$off("updateVisitorList", handleVisitorUpdate);
    });
    common_vendor.onPullDownRefresh(() => {
      refreshData();
    });
    common_vendor.onReachBottom(() => {
      loadMore();
    });
    common_vendor.onUnmounted(() => {
      common_vendor.index.$off("updateVisitorList", handleVisitorUpdate);
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(tabList.value, (tab, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(tab),
            b: currentTab.value === index
          }, currentTab.value === index ? {} : {}, {
            c: index,
            d: currentTab.value === index ? 1 : "",
            e: common_vendor.o(($event) => switchTab(index), index)
          });
        }),
        b: common_vendor.t(totalVisitors.value),
        c: common_vendor.t(currentTab.value === 0 ? "总访客数" : "总浏览数"),
        d: common_vendor.t(todayVisitors.value),
        e: common_vendor.t(currentTab.value === 0 ? "今日访客" : "今日浏览"),
        f: isEmpty.value && !loading.value
      }, isEmpty.value && !loading.value ? {
        g: common_vendor.p({
          title: "暂无访客记录",
          description: "快去分享你的主页吸引更多访客吧~",
          image: "/static/img/empty-visitor.png"
        })
      } : {
        h: common_vendor.f(visitorList.value, (visitor, index, i0) => {
          return common_vendor.e({
            a: visitor.avatar || "/static/img/avatar.png",
            b: visitor.vip
          }, visitor.vip ? {
            c: visitor.vip_icon
          } : {}, {
            d: common_vendor.t(visitor.nickname || "匿名用户"),
            e: visitor.sex === 1
          }, visitor.sex === 1 ? {
            f: common_assets._imports_0$20
          } : visitor.sex === 2 ? {
            h: common_assets._imports_1$21
          } : {}, {
            g: visitor.sex === 2,
            i: visitor.auth_status === 2
          }, visitor.auth_status === 2 ? {
            j: common_assets._imports_0$5
          } : {}, {
            k: common_vendor.t(formatVisitTime(visitor.last_visit_time_str || visitor.last_visit_time)),
            l: common_vendor.t(visitor.visit_count),
            m: visitor.visitor_uid || index,
            n: common_vendor.o(($event) => goToUserProfile(visitor.visitor_uid), visitor.visitor_uid || index)
          });
        })
      }, {
        i: !isEmpty.value
      }, !isEmpty.value ? {
        j: common_vendor.o(loadMore),
        k: common_vendor.p({
          status: loadStatus.value,
          ["content-text"]: loadText
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-971c42a7"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/center/visitor.js.map
