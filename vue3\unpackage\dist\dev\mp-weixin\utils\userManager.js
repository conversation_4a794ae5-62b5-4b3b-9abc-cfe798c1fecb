"use strict";
const common_vendor = require("../common/vendor.js");
const stores_user = require("../stores/user.js");
const utils_cache = require("./cache.js");
const config_cache = require("../config/cache.js");
const { UID, LOGIN_STATUS, USER_INFO } = config_cache.cacheConfig;
class UserManager {
  constructor() {
    this.userStore = null;
    this.vuexStore = null;
  }
  // 初始化，传入store实例
  init(vuexStore) {
    this.vuexStore = vuexStore;
    this.userStore = stores_user.useUserStore();
  }
  /**
   * 统一获取用户信息
   * 优先级：缓存 > Pinia > Vuex
   */
  getUserInfo() {
    var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l;
    try {
      const vuexUserInfo = ((_c = (_b = (_a = this.vuexStore) == null ? void 0 : _a.state) == null ? void 0 : _b.app) == null ? void 0 : _c.userInfo) || {};
      const vuexUid = ((_f = (_e = (_d = this.vuexStore) == null ? void 0 : _d.state) == null ? void 0 : _e.app) == null ? void 0 : _f.uid) || 0;
      const vuexToken = ((_i = (_h = (_g = this.vuexStore) == null ? void 0 : _g.state) == null ? void 0 : _h.app) == null ? void 0 : _i.token) || "";
      const piniaUserInfo = ((_j = this.userStore) == null ? void 0 : _j.userInfo) || {};
      const piniaUid = ((_k = this.userStore) == null ? void 0 : _k.uid) || 0;
      const piniaToken = ((_l = this.userStore) == null ? void 0 : _l.token) || "";
      const cachedUserInfo = common_vendor.index.getStorageSync("USER_INFO") || {};
      const cachedUid = common_vendor.index.getStorageSync("UID") || 0;
      const cachedToken = common_vendor.index.getStorageSync("token") || common_vendor.index.getStorageSync("LOGIN_STATUS_TOKEN") || "";
      const finalToken = cachedToken || piniaToken || vuexToken;
      const finalUid = cachedUid || piniaUid || vuexUid || cachedUserInfo.uid || piniaUserInfo.uid || vuexUserInfo.uid || 0;
      const finalUserInfo = {
        ...vuexUserInfo,
        ...piniaUserInfo,
        ...cachedUserInfo,
        uid: finalUid
      };
      return {
        userInfo: finalUserInfo,
        uid: finalUid,
        token: finalToken,
        isLogin: !!(finalToken && finalUid && finalUserInfo.nickname)
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/userManager.js:72", "获取用户信息失败:", error);
      return {
        userInfo: {},
        uid: 0,
        token: "",
        isLogin: false
      };
    }
  }
  /**
   * 统一更新用户信息到所有数据源
   */
  updateUserInfo(newUserInfo, options = {}) {
    try {
      if (!newUserInfo || typeof newUserInfo !== "object") {
        common_vendor.index.__f__("warn", "at utils/userManager.js:88", "updateUserInfo: 无效的用户信息", newUserInfo);
        return false;
      }
      const { syncToVuex = true, syncToPinia = true, syncToCache = true } = options;
      const current = this.getUserInfo();
      const currentUid = current.uid || newUserInfo.uid || 0;
      const finalUserInfo = {
        ...current.userInfo,
        ...newUserInfo,
        uid: currentUid
      };
      if (syncToVuex && this.vuexStore) {
        this.vuexStore.commit("UPDATE_USERINFO", finalUserInfo);
        if (currentUid) {
          this.vuexStore.commit("SETUID", currentUid);
        }
      }
      if (syncToPinia && this.userStore) {
        this.userStore.updateUserInfo(finalUserInfo);
        if (currentUid) {
          this.userStore.setUid(currentUid);
        }
      }
      if (syncToCache) {
        common_vendor.index.setStorageSync("USER_INFO", finalUserInfo);
        if (currentUid) {
          common_vendor.index.setStorageSync("UID", currentUid);
        }
      }
      common_vendor.index.__f__("log", "at utils/userManager.js:129", "用户信息已统一更新:", {
        uid: currentUid,
        nickname: finalUserInfo.nickname,
        updateFields: Object.keys(newUserInfo),
        syncOptions: { syncToVuex, syncToPinia, syncToCache }
      });
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/userManager.js:138", "统一更新用户信息失败:", error);
      return false;
    }
  }
  /**
   * 设置登录状态和token
   */
  setLoginStatus(token, userInfo = null) {
    try {
      if (!token) {
        common_vendor.index.__f__("warn", "at utils/userManager.js:149", "setLoginStatus: token为空");
        return false;
      }
      if (this.vuexStore) {
        this.vuexStore.commit("UPDATE_LOGIN", token);
      }
      if (this.userStore) {
        this.userStore.setToken(token);
        this.userStore.setLoginStatus(true);
      }
      common_vendor.index.setStorageSync("token", token);
      utils_cache.Cache.set(LOGIN_STATUS, token);
      if (userInfo) {
        this.updateUserInfo(userInfo);
      }
      common_vendor.index.__f__("log", "at utils/userManager.js:173", "登录状态已设置:", { hasToken: !!token, hasUserInfo: !!userInfo });
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/userManager.js:176", "设置登录状态失败:", error);
      return false;
    }
  }
  /**
   * 统一登出，清理所有数据源
   */
  logout() {
    try {
      if (this.vuexStore) {
        this.vuexStore.commit("LOGOUT");
      }
      if (this.userStore) {
        this.userStore.logout();
      }
      const keysToRemove = [
        "USER_INFO",
        "UID",
        "token",
        "LOGIN_STATUS_TOKEN",
        "pinia_user",
        "pinia_social",
        "pinia_app"
      ];
      keysToRemove.forEach((key) => {
        try {
          common_vendor.index.removeStorageSync(key);
        } catch (e) {
          common_vendor.index.__f__("warn", "at utils/userManager.js:206", `清除${key}失败:`, e);
        }
      });
      utils_cache.Cache.clear(LOGIN_STATUS);
      utils_cache.Cache.clear(USER_INFO);
      utils_cache.Cache.clear(UID);
      common_vendor.index.__f__("log", "at utils/userManager.js:215", "用户已统一登出，所有数据源已清理");
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/userManager.js:218", "统一登出失败:", error);
      return false;
    }
  }
  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const { isLogin, token, uid, userInfo } = this.getUserInfo();
    return {
      isLogin,
      hasToken: !!token,
      hasUid: !!uid,
      hasUserInfo: !!(userInfo && userInfo.nickname),
      valid: isLogin && !!token && !!uid && !!(userInfo && userInfo.nickname)
    };
  }
  /**
   * 同步所有数据源
   */
  syncAllSources() {
    const current = this.getUserInfo();
    if (current.isLogin && current.userInfo.nickname) {
      this.updateUserInfo(current.userInfo);
      if (current.token) {
        this.setLoginStatus(current.token);
      }
    }
  }
}
const userManager = new UserManager();
exports.userManager = userManager;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/userManager.js.map
