{"version": 3, "file": "index.js", "sources": ["pages/order/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvb3JkZXIvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 导航栏 -->\r\n    <view class=\"nav-bar bf8\" :style=\"{'padding-top': statusBarHeight + 'px'}\">\r\n      <view class=\"bar-box df\" :style=\"{'height': titleBarHeight + 'px', 'width': '100%'}\">\r\n        <view class=\"bar-back df\" @tap=\"navBack\">\r\n          <image src=\"/static/img/z.png\" style=\"width:34rpx;height:34rpx\"></image>\r\n        </view>\r\n        <view class=\"bar-title ohto\">订单</view>\r\n      </view>\r\n      \r\n      <!-- 订单状态选项卡 -->\r\n      <view class=\"nav-box df\">\r\n        <view \r\n          v-for=\"(item, index) in barList\" \r\n          :key=\"index\"\r\n          class=\"nav-item df\"\r\n          @tap=\"barClick\" \r\n          :data-idx=\"index\">\r\n          <text :style=\"{'color': index == barIdx ? '#000' : '#999', 'font-size': index == barIdx ? '28rpx' : '26rpx'}\">{{item}}</text>\r\n          <view :style=\"{'opacity': index == barIdx ? 1 : 0}\" class=\"nav-line\"></view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 内容区域 -->\r\n    <view class=\"content df\" :style=\"{'margin-top': 'calc(' + (statusBarHeight + titleBarHeight) + 'px + 80rpx)'}\">\r\n      <!-- 加载中状态 -->\r\n      <view class=\"heio df\" :style=\"{'height': isThrottling || loadStatus == 'loading' ? '0px' : '60rpx'}\">\r\n        <uni-load-more v-if=\"true\" status=\"loading\"></uni-load-more>\r\n      </view>\r\n      \r\n      <!-- 空状态 -->\r\n      <emptyPage\r\n        v-if=\"isEmpty\"\r\n        title=\"暂无相关订单\"\r\n        description=\"去逛逛，挑点喜欢的产品下单吧\"\r\n        image=\"/static/img/empty.png\"\r\n        buttonText=\"去购物\"\r\n        @buttonClick=\"goToShop\"\r\n      />\r\n      \r\n      <!-- 订单列表 -->\r\n      <block v-else>\r\n        <view \r\n          v-for=\"(item, index) in list\" \r\n          :key=\"item.id\" \r\n          class=\"list-box\" \r\n          :data-id=\"item.id\" \r\n          @tap=\"toOrderDetails\">\r\n          <!-- 订单头部 -->\r\n          <view class=\"list-top df\">\r\n            <text># {{item.order_no}}</text>\r\n            <text style=\"color:#000;font-size:24rpx\">{{item.status_name}}</text>\r\n          </view>\r\n          \r\n          <!-- 订单商品 -->\r\n          <view class=\"list-info df\">\r\n            <view class=\"list-info-img\">\r\n              <image \r\n                v-for=\"(img, imgIndex) in item.goods\" \r\n                :key=\"imgIndex\" \r\n                v-if=\"imgIndex < 3\" \r\n                :src=\"img.image\" \r\n                mode=\"aspectFill\"></image>\r\n              <view v-if=\"item.goods.length > 3\" class=\"mask-img\">+{{item.goods.length-3}}</view>\r\n            </view>\r\n            <view class=\"list-info-pn\">\r\n              <money :price=\"item.pay_price\" size=\"36\"></money>\r\n              <view class=\"num\">共{{item.goods_count}}件</view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 物流信息 -->\r\n          <view v-if=\"item.express\" class=\"list-wuliu df\">\r\n            订单由<text>{{item.express}}：{{item.express_no}}</text>为您配送到家\r\n          </view>\r\n          \r\n          <!-- 操作按钮 -->\r\n          <view class=\"list-btn df\">\r\n            <!-- 删除按钮 -->\r\n            <view \r\n              v-if=\"item.status == 0 || item.status == 4 || item.status == 5\" \r\n              class=\"df btn1\" \r\n              :data-idx=\"index\" \r\n              data-type=\"0\" \r\n              @tap.stop=\"editClick\">\r\n              <text>删除</text>\r\n            </view>\r\n            \r\n            <!-- 取消订单 -->\r\n            <view \r\n              v-if=\"(item.status == 1 || item.status == 2) && (item.pay_status == 1 || item.pay_status == 0)\" \r\n              class=\"df btn1\" \r\n              :data-idx=\"index\" \r\n              data-type=\"1\" \r\n              @tap.stop=\"editClick\">\r\n              <text>取消订单</text>\r\n            </view>\r\n            \r\n            <!-- 催发货 -->\r\n            <view \r\n              v-if=\"item.status == 2\" \r\n              class=\"df btn2\" \r\n              :data-idx=\"index\" \r\n              @tap.stop=\"urgeDeliveryClick\">\r\n              <text>催发货</text>\r\n            </view>\r\n            \r\n            <!-- 确认收货 -->\r\n            <view \r\n              v-if=\"item.status == 3\" \r\n              class=\"df btn2\" \r\n              :data-idx=\"index\" \r\n              data-type=\"2\" \r\n              @tap.stop=\"editClick\">\r\n              <text>确认收货</text>\r\n            </view>\r\n            \r\n            <!-- 评价 -->\r\n            <view \r\n              v-if=\"item.status == 4\" \r\n              class=\"df btn2\" \r\n              :data-idx=\"index\" \r\n              @tap.stop=\"openOrderNote\">\r\n              <text>评价</text>\r\n            </view>\r\n            \r\n            <!-- 支付 -->\r\n            <view \r\n              v-if=\"item.status == 1 && item.pay_status == 0\" \r\n              class=\"df btn2\" \r\n              :data-id=\"item.id\" \r\n              @tap.stop=\"paymentClick\">\r\n              <image src=\"/static/img/z.png\"></image>\r\n              <text>支付</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 加载更多 -->\r\n        <uni-load-more v-if=\"list.length > 0\" :status=\"loadStatus\"></uni-load-more>\r\n      </block>\r\n    </view>\r\n    \r\n    <!-- 评价弹窗 -->\r\n    <uni-popup ref=\"notePopup\" type=\"center\">\r\n      <view class=\"note-box\">\r\n        <view class=\"note-add df\" @tap=\"toOrderNote(1)\">\r\n          <image src=\"/static/img/null.png\"></image>\r\n          <text>图文评价</text>\r\n        </view>\r\n        <view class=\"note-add df\" @tap=\"toOrderNote(2)\">\r\n          <image src=\"/static/img/null.png\"></image>\r\n          <text>视频评价</text>\r\n        </view>\r\n        <view class=\"note-add df\" @tap=\"toOrderNote(3)\">\r\n          <image src=\"/static/img/null.png\"></image>\r\n          <text>音文评价</text>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n    \r\n    <!-- 提示弹窗 -->\r\n    <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\r\n      <view class=\"tips-box df\">\r\n        <view class=\"tips-item\">{{tipsTitle}}</view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport navbar from '@/components/navbar/navbar.vue'\r\nimport lazyImage from '@/components/lazyImage/lazyImage.vue'\r\nimport money from '@/components/money/money.vue'\r\nimport uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue'\r\nimport emptyPage from '@/components/emptyPage/emptyPage.vue'\r\n//import { request, showToast } from '@/config/util.js'\r\nimport * as mockData from '@/config/mock.js'\r\n\r\nexport default {\r\n  components: {\r\n    navbar,\r\n    lazyImage,\r\n    money,\r\n    uniLoadMore,\r\n    emptyPage\r\n  },\r\n  data() {\r\n    return {\r\n      statusBarHeight: 20,\r\n      titleBarHeight: 44,\r\n      barList: ['全部', '待付款', '待发货', '待收货', '评价', '售后'],\r\n      barIdx: 0,\r\n      isThrottling: true,\r\n      list: [],\r\n      listIdx: 0,\r\n      page: 1,\r\n      isEmpty: false,\r\n      loadStatus: 'more',\r\n      tipsTitle: '',\r\n      useMockData: process.env.NODE_ENV === 'development' // 开发环境下使用mock数据\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    // 根据传入参数设置当前标签\r\n    if (options.idx) {\r\n      this.barIdx = parseInt(options.idx)\r\n    }\r\n    \r\n    this.orderList()\r\n  },\r\n  onReachBottom() {\r\n    if (this.list.length) {\r\n      this.loadStatus = 'loading'\r\n      this.page = this.page + 1\r\n      this.orderList()\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取订单列表\r\n    orderList() {\r\n      let that = this\r\n      \r\n      // 使用mock数据\r\n      if (this.useMockData) {\r\n        setTimeout(() => {\r\n          that.isThrottling = true\r\n          that.loadStatus = 'more'\r\n          \r\n          let filteredOrders = [...mockData.orderList]\r\n          \r\n          // 根据选项卡筛选订单\r\n          if (that.barIdx > 0 && that.barIdx <= 5) {\r\n            filteredOrders = filteredOrders.filter(order => \r\n              order.status == that.barIdx\r\n            )\r\n          }\r\n          \r\n          if (filteredOrders.length > 0) {\r\n            // 模拟分页 - 每页最多3条数据\r\n            const pageSize = 3\r\n            const startIndex = (that.page - 1) * pageSize\r\n            const endIndex = startIndex + pageSize\r\n            const pageData = filteredOrders.slice(startIndex, endIndex)\r\n            \r\n            if (pageData.length > 0) {\r\n              if (that.page === 1) {\r\n                that.list = pageData\r\n              } else {\r\n                that.list = that.list.concat(pageData)\r\n              }\r\n              that.isEmpty = false\r\n              that.loadStatus = endIndex >= filteredOrders.length ? 'noMore' : 'more'\r\n            } else {\r\n              that.loadStatus = 'noMore'\r\n            }\r\n          } else {\r\n            if (that.page === 1) {\r\n              that.isEmpty = true\r\n              that.list = []\r\n            } else {\r\n              that.loadStatus = 'noMore'\r\n            }\r\n          }\r\n        }, 500)\r\n        return\r\n      }\r\n      \r\n      // 正常API请求\r\n      request('order/list', {\r\n        type: that.barIdx,\r\n        page: that.page\r\n      }).then(function(res) {\r\n        that.isThrottling = true\r\n        that.loadStatus = 'more'\r\n        \r\n        if (res.code == 200 && res.data && res.data.data) {\r\n          if (res.data.data.length > 0) {\r\n            if (that.page === 1) {\r\n              that.list = res.data.data\r\n            } else {\r\n              that.list = that.list.concat(res.data.data)\r\n            }\r\n            that.page = res.data.current_page || that.page\r\n            that.isEmpty = false\r\n            \r\n            // 判断是否还有更多数据\r\n            if (res.data.current_page >= res.data.last_page) {\r\n              that.loadStatus = 'noMore'\r\n            }\r\n          } else if (that.page === 1) {\r\n            that.isEmpty = true\r\n            that.list = []\r\n          } else {\r\n            that.loadStatus = 'noMore'\r\n          }\r\n        } else {\r\n          that.loadStatus = 'more'\r\n          showToast(res.msg || '获取订单列表失败')\r\n          \r\n          // API请求失败时使用mock数据\r\n          if (that.page === 1) {\r\n            that.list = mockData.orderList\r\n            that.isEmpty = that.list.length === 0\r\n          }\r\n        }\r\n      }).catch(() => {\r\n        that.isThrottling = true\r\n        \r\n        // API请求异常时使用mock数据\r\n        if (that.page === 1) {\r\n          that.list = [...mockData.orderList]\r\n          that.isEmpty = that.list.length === 0\r\n        }\r\n        that.loadStatus = 'noMore'\r\n      })\r\n    },\r\n    \r\n    // 支付\r\n    paymentClick(e) {\r\n      let that = this\r\n      uni.showLoading({\r\n        title: '唤起支付中..',\r\n        mask: true\r\n      })\r\n      \r\n      if (this.useMockData) {\r\n        setTimeout(() => {\r\n          uni.hideLoading()\r\n          that.opTipsPopup('支付成功，我们会尽快为您发货 🎉')\r\n          that.barIdx = 2\r\n          that.isThrottling = false\r\n          that.page = 1\r\n          that.orderList()\r\n        }, 1000)\r\n        return\r\n      }\r\n      \r\n      request('order/payment', {\r\n        id: e.currentTarget.dataset.id\r\n      }, 'POST').then(function(res) {\r\n        uni.hideLoading()\r\n        \r\n        if (res.code == 200) {\r\n          let payData = res.data\r\n          uni.requestPayment({\r\n            provider: 'weixin',\r\n            timeStamp: payData.timestamp,\r\n            nonceStr: payData.nonceStr,\r\n            package: payData.package,\r\n            signType: payData.signType,\r\n            paySign: payData.paySign,\r\n            success: function() {\r\n              that.opTipsPopup('支付成功，我们会尽快为您发货 🎉')\r\n              that.barIdx = 2\r\n              that.isThrottling = false\r\n              that.page = 1\r\n              that.orderList()\r\n            },\r\n            fail: function() {\r\n              showToast('支付失败或已取消')\r\n            }\r\n          })\r\n        } else {\r\n          showToast(res.msg || '支付异常，请稍后重试')\r\n        }\r\n      }).catch(() => {\r\n        uni.hideLoading()\r\n        showToast('网络异常，请稍后重试')\r\n      })\r\n    },\r\n    \r\n    // 编辑订单(取消订单/确认收货/删除订单)\r\n    editClick(e) {\r\n      let that = this\r\n      let idx = e.currentTarget.dataset.idx\r\n      let type = e.currentTarget.dataset.type\r\n      let refund = 0\r\n      \r\n      // 已付款的订单取消，需退款\r\n      if (type == 1 && that.list[idx].pay_status == 1 && that.list[idx].status == 2) {\r\n        refund = 1\r\n      }\r\n      \r\n      // 确认提示\r\n      let tipText = '确定要'\r\n      if (type == 0) tipText += '删除'\r\n      else if (type == 1) tipText += '取消'\r\n      else if (type == 2) tipText += '确认收货'\r\n      tipText += '吗？'\r\n      \r\n      if (type == 0) tipText += '删除后将无法恢复。'\r\n      \r\n      uni.showModal({\r\n        title: '提示',\r\n        content: tipText,\r\n        success: function(res) {\r\n          if (res.confirm) {\r\n            uni.showLoading({\r\n              mask: true\r\n            })\r\n            \r\n            if (that.useMockData) {\r\n              setTimeout(() => {\r\n                uni.hideLoading()\r\n                \r\n                if (type == 0) {\r\n                  // 删除订单\r\n                  that.list.splice(idx, 1)\r\n                  if (that.list.length <= 0) {\r\n                    that.isEmpty = true\r\n                  }\r\n                  that.opTipsPopup('删除成功')\r\n                } else if (type == 1) {\r\n                  // 取消订单\r\n                  if (that.barIdx == 0) {\r\n                    that.list[idx].status = 0\r\n                    that.list[idx].status_name = '已取消'\r\n                  } else {\r\n                    that.list.splice(idx, 1)\r\n                    if (that.list.length <= 0) {\r\n                      that.isEmpty = true\r\n                    }\r\n                  }\r\n                  that.opTipsPopup('取消成功')\r\n                } else if (type == 2) {\r\n                  // 确认收货\r\n                  if (that.barIdx == 0) {\r\n                    that.list[idx].status = 4\r\n                    that.list[idx].status_name = '待评价'\r\n                  } else {\r\n                    that.list.splice(idx, 1)\r\n                    if (that.list.length <= 0) {\r\n                      that.isEmpty = true\r\n                    }\r\n                  }\r\n                  that.opTipsPopup('确认收货成功')\r\n                }\r\n                \r\n                getApp().globalData.isCenterPage = true\r\n              }, 1000)\r\n              return\r\n            }\r\n            \r\n            request('order/edit', {\r\n              id: that.list[idx].id,\r\n              type: type,\r\n              refund: refund\r\n            }, 'POST').then(function(res) {\r\n              uni.hideLoading()\r\n              that.opTipsPopup(res.msg)\r\n              \r\n              if (res.code == 200) {\r\n                if (that.barIdx == 0 && type == 1) {\r\n                  // 全部订单列表中取消订单\r\n                  that.list[idx].status = 0\r\n                  that.list[idx].status_name = '已取消'\r\n                } else if (that.barIdx == 0 && type == 2) {\r\n                  // 全部订单列表中确认收货\r\n                  that.list[idx].status = 4\r\n                  that.list[idx].status_name = '待评价'\r\n                } else {\r\n                  // 其他标签页中的操作，移除该订单\r\n                  that.list.splice(idx, 1)\r\n                  if (that.list.length <= 0) {\r\n                    that.isEmpty = true\r\n                  }\r\n                  getApp().globalData.isCenterPage = true\r\n                }\r\n              }\r\n            }).catch(() => {\r\n              uni.hideLoading()\r\n              showToast('网络异常，请稍后重试')\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 催发货\r\n    urgeDeliveryClick(e) {\r\n      let idx = e.currentTarget.dataset.idx\r\n      this.opTipsPopup('操作成功，我们会尽快为您发货 🎉')\r\n      \r\n      if (!this.useMockData) {\r\n        request('order/edit', {\r\n          id: this.list[idx].id,\r\n          type: 3,\r\n          refund: 0\r\n        }, 'POST')\r\n      }\r\n    },\r\n    \r\n    // 打开评价选择弹窗\r\n    openOrderNote(e) {\r\n      this.listIdx = e.currentTarget.dataset.idx\r\n      this.$refs.notePopup.open()\r\n    },\r\n    \r\n    // 去评价\r\n    toOrderNote(type) {\r\n      this.$refs.notePopup.close()\r\n      uni.navigateTo({\r\n        url: '/pages/note/add?type=' + type + '&oid=' + this.list[this.listIdx].id\r\n      })\r\n    },\r\n    \r\n    // 查看订单详情\r\n    toOrderDetails(e) {\r\n      uni.navigateTo({\r\n        url: '/pages/order/details?id=' + e.currentTarget.dataset.id\r\n      })\r\n    },\r\n    \r\n    // 切换标签\r\n    barClick(e) {\r\n      if (this.isThrottling) {\r\n        this.barIdx = e.currentTarget.dataset.idx\r\n        this.isThrottling = false\r\n        this.page = 1\r\n        this.orderList()\r\n      }\r\n    },\r\n    \r\n    // 返回上一页\r\n    navBack() {\r\n      if (getCurrentPages().length > 1) {\r\n        uni.navigateBack()\r\n      } else {\r\n        uni.switchTab({\r\n          url: '/pages/tabbar/center'\r\n        })\r\n      }\r\n    },\r\n    \r\n    // 显示提示\r\n    opTipsPopup(title) {\r\n      let that = this\r\n      that.tipsTitle = title\r\n      that.$refs.tipsPopup.open()\r\n\r\n      setTimeout(function() {\r\n        that.$refs.tipsPopup.close()\r\n      }, 2000)\r\n    },\r\n\r\n    // 去购物\r\n    goToShop() {\r\n      uni.switchTab({\r\n        url: '/pages/index/shop'\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\npage {\r\n  background: #f8f8f8;\r\n}\r\n\r\n/* 导航栏 */\r\n.nav-bar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  width: 100%;\r\n  z-index: 99;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.bar-box .bar-back {\r\n  padding: 0 30rpx;\r\n  width: 34rpx;\r\n  height: 100%;\r\n}\r\n\r\n.bar-box .bar-title {\r\n  font-size: 32rpx;\r\n  font-weight: 700;\r\n}\r\n\r\n.nav-box {\r\n  position: sticky;\r\n  left: 0;\r\n  z-index: 99;\r\n  margin-top: -1px;\r\n  width: 100%;\r\n  height: 80rpx;\r\n  justify-content: space-between;\r\n}\r\n\r\n.nav-box .nav-item {\r\n  width: 16.66%;\r\n  height: 100%;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n\r\n.nav-box .nav-item text {\r\n  font-weight: 700;\r\n  transition: all .3s ease-in-out;\r\n}\r\n\r\n.nav-box .nav-line {\r\n  position: absolute;\r\n  bottom: 12rpx;\r\n  width: 18rpx;\r\n  height: 6rpx;\r\n  border-radius: 6rpx;\r\n  background: #000;\r\n  transition: opacity .3s ease-in-out;\r\n}\r\n\r\n/* 内容区 */\r\n.content {\r\n  width: 100%;\r\n  flex-direction: column;\r\n}\r\n\r\n.list-box {\r\n  margin-top: 30rpx;\r\n  width: calc(100% - 100rpx);\r\n  padding: 30rpx 20rpx;\r\n  border-radius: 30rpx;\r\n  background: #fff;\r\n}\r\n\r\n.list-box .list-top {\r\n  width: 100%;\r\n  justify-content: space-between;\r\n}\r\n\r\n.list-top text {\r\n  color: #999;\r\n  font-size: 26rpx;\r\n  font-weight: 700;\r\n}\r\n\r\n.list-info {\r\n  width: 100%;\r\n  padding: 30rpx 0;\r\n  justify-content: space-between;\r\n}\r\n\r\n.list-info .list-info-img {\r\n  display: flex;\r\n  align-items: center;\r\n  position: relative;\r\n}\r\n\r\n.list-info .list-info-img image,\r\n.list-info .mask-img {\r\n  width: 130rpx;\r\n  height: 130rpx;\r\n  border-radius: 8rpx;\r\n}\r\n\r\n.list-info .list-info-img image {\r\n  margin-right: 8rpx;\r\n  animation: fadeIn .45s ease;\r\n}\r\n\r\n.list-info .mask-img {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 8rpx;\r\n  line-height: 130rpx;\r\n  text-align: center;\r\n  font-size: 24rpx;\r\n  font-weight: 700;\r\n  color: #fff;\r\n  background: rgba(0, 0, 0, .3);\r\n}\r\n\r\n.list-info .list-info-pn {\r\n  display: flex;\r\n  align-items: flex-end;\r\n  flex-direction: column;\r\n}\r\n\r\n.list-info-pn .num {\r\n  margin-top: 10rpx;\r\n  color: #999;\r\n  font-size: 20rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.list-wuliu {\r\n  margin-bottom: 30rpx;\r\n  z-index: 1;\r\n  width: calc(100% - 40rpx);\r\n  padding: 20rpx;\r\n  color: #999;\r\n  font-size: 24rpx;\r\n  background: #f8f8f8;\r\n  border-radius: 8rpx;\r\n}\r\n\r\n.list-wuliu text {\r\n  margin: 0 10rpx;\r\n  color: #000;\r\n  font-weight: 700;\r\n}\r\n\r\n.list-box .list-btn {\r\n  justify-content: flex-end;\r\n}\r\n\r\n.list-btn view {\r\n  margin-left: 20rpx;\r\n  padding: 0 30rpx;\r\n  height: 60rpx;\r\n  border-radius: 30rpx;\r\n}\r\n\r\n.list-btn view image {\r\n  width: 26rpx;\r\n  height: 26rpx;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.list-btn view text {\r\n  font-size: 20rpx;\r\n  line-height: 20rpx;\r\n  font-weight: 700;\r\n}\r\n\r\n.list-btn .btn1 {\r\n  color: #999;\r\n  border: 2rpx solid #F5F5F5;\r\n}\r\n\r\n.list-btn .btn2 {\r\n  color: #fff;\r\n  background: #000;\r\n  border: 2rpx solid #000;\r\n}\r\n\r\n/* 评价弹窗 */\r\n.note-box {\r\n  padding: 15rpx;\r\n  background: #fff;\r\n  border-radius: 30rpx;\r\n}\r\n\r\n.note-box .note-add {\r\n  margin: 30rpx;\r\n  width: 400rpx;\r\n  height: 90rpx;\r\n  font-size: 24rpx;\r\n  font-weight: 700;\r\n  color: #fff;\r\n  background: #000;\r\n  border-radius: 45rpx;\r\n  justify-content: center;\r\n}\r\n\r\n.note-box .note-add image {\r\n  margin-right: 10rpx;\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-box {\r\n  margin-top: 120rpx;\r\n  width: 100%;\r\n  flex-direction: column;\r\n}\r\n\r\n.empty-box image {\r\n  width: 240rpx;\r\n  height: 240rpx;\r\n}\r\n\r\n.empty-box .e1 {\r\n  margin-top: 30rpx;\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n}\r\n\r\n.empty-box .e2 {\r\n  margin-top: 12rpx;\r\n  font-size: 26rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 提示框 */\r\n.tips-box {\r\n  width: 100%;\r\n  justify-content: center;\r\n}\r\n\r\n.tips-item {\r\n  background: #000;\r\n  color: #fff;\r\n  padding: 20rpx 40rpx;\r\n  border-radius: 12rpx;\r\n  font-size: 24rpx;\r\n  font-weight: 700;\r\n}\r\n\r\n/* 加载动画 */\r\n.heio {\r\n  justify-content: center;\r\n}\r\n\r\n/* 通用 */\r\n.df {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.bf8 {\r\n  background: #fff;\r\n}\r\n\r\n.ohto {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    opacity: 1;\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'Z:/WWW/shejiao/vue3/pages/order/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["mockData.orderList", "uni", "res"], "mappings": ";;;;AA6KA,MAAA,SAAmB,MAAA;AACnB,MAAA,YAAsB,MAAA;AACtB,MAAA,QAAkB,MAAA;AAClB,MAAA,cAAwB,MAAA;AACxB,MAAA,YAAsB,MAAA;AAItB,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,OAAO;AACE,WAAA;AAAA,MACL,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,SAAS,CAAC,MAAM,OAAO,OAAO,OAAO,MAAM,IAAI;AAAA,MAC/C,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,MAAM,CAAC;AAAA,MACP,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,aAAa;AAAA;AAAA,IAAA;AAAA,EAEjB;AAAA,EACA,OAAO,SAAS;AAEd,QAAI,QAAQ,KAAK;AACV,WAAA,SAAS,SAAS,QAAQ,GAAG;AAAA,IACpC;AAEA,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,gBAAgB;AACV,QAAA,KAAK,KAAK,QAAQ;AACpB,WAAK,aAAa;AACb,WAAA,OAAO,KAAK,OAAO;AACxB,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,SAAS;AAAA;AAAA,IAEP,YAAY;AACV,UAAI,OAAO;AAGX,UAAI,KAAK,aAAa;AACpB,mBAAW,MAAM;AACf,eAAK,eAAe;AACpB,eAAK,aAAa;AAElB,cAAI,iBAAiB,CAAC,GAAGA,YAAAA,SAAkB;AAG3C,cAAI,KAAK,SAAS,KAAK,KAAK,UAAU,GAAG;AACvC,6BAAiB,eAAe;AAAA,cAAO,CAAA,UACrC,MAAM,UAAU,KAAK;AAAA,YAAA;AAAA,UAEzB;AAEI,cAAA,eAAe,SAAS,GAAG;AAE7B,kBAAM,WAAW;AACX,kBAAA,cAAc,KAAK,OAAO,KAAK;AACrC,kBAAM,WAAW,aAAa;AAC9B,kBAAM,WAAW,eAAe,MAAM,YAAY,QAAQ;AAEtD,gBAAA,SAAS,SAAS,GAAG;AACnB,kBAAA,KAAK,SAAS,GAAG;AACnB,qBAAK,OAAO;AAAA,cAAA,OACP;AACL,qBAAK,OAAO,KAAK,KAAK,OAAO,QAAQ;AAAA,cACvC;AACA,mBAAK,UAAU;AACf,mBAAK,aAAa,YAAY,eAAe,SAAS,WAAW;AAAA,YAAA,OAC5D;AACL,mBAAK,aAAa;AAAA,YACpB;AAAA,UAAA,OACK;AACD,gBAAA,KAAK,SAAS,GAAG;AACnB,mBAAK,UAAU;AACf,mBAAK,OAAO;YAAC,OACR;AACL,mBAAK,aAAa;AAAA,YACpB;AAAA,UACF;AAAA,WACC,GAAG;AACN;AAAA,MACF;AAGA,cAAQ,cAAc;AAAA,QACpB,MAAM,KAAK;AAAA,QACX,MAAM,KAAK;AAAA,MAAA,CACZ,EAAE,KAAK,SAAS,KAAK;AACpB,aAAK,eAAe;AACpB,aAAK,aAAa;AAElB,YAAI,IAAI,QAAQ,OAAO,IAAI,QAAQ,IAAI,KAAK,MAAM;AAChD,cAAI,IAAI,KAAK,KAAK,SAAS,GAAG;AACxB,gBAAA,KAAK,SAAS,GAAG;AACd,mBAAA,OAAO,IAAI,KAAK;AAAA,YAAA,OAChB;AACL,mBAAK,OAAO,KAAK,KAAK,OAAO,IAAI,KAAK,IAAI;AAAA,YAC5C;AACA,iBAAK,OAAO,IAAI,KAAK,gBAAgB,KAAK;AAC1C,iBAAK,UAAU;AAGf,gBAAI,IAAI,KAAK,gBAAgB,IAAI,KAAK,WAAW;AAC/C,mBAAK,aAAa;AAAA,YACpB;AAAA,UAAA,WACS,KAAK,SAAS,GAAG;AAC1B,iBAAK,UAAU;AACf,iBAAK,OAAO;UAAC,OACR;AACL,iBAAK,aAAa;AAAA,UACpB;AAAA,QAAA,OACK;AACL,eAAK,aAAa;AACR,oBAAA,IAAI,OAAO,UAAU;AAG3B,cAAA,KAAK,SAAS,GAAG;AACnB,iBAAK,OAAOA;AACP,iBAAA,UAAU,KAAK,KAAK,WAAW;AAAA,UACtC;AAAA,QACF;AAAA,MAAA,CACD,EAAE,MAAM,MAAM;AACb,aAAK,eAAe;AAGhB,YAAA,KAAK,SAAS,GAAG;AACnB,eAAK,OAAO,CAAC,GAAGA,YAAAA,SAAkB;AAC7B,eAAA,UAAU,KAAK,KAAK,WAAW;AAAA,QACtC;AACA,aAAK,aAAa;AAAA,MAAA,CACnB;AAAA,IACH;AAAA;AAAA,IAGA,aAAa,GAAG;AACd,UAAI,OAAO;AACXC,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAED,UAAI,KAAK,aAAa;AACpB,mBAAW,MAAM;AACfA,wBAAA,MAAI,YAAY;AAChB,eAAK,YAAY,mBAAmB;AACpC,eAAK,SAAS;AACd,eAAK,eAAe;AACpB,eAAK,OAAO;AACZ,eAAK,UAAU;AAAA,WACd,GAAI;AACP;AAAA,MACF;AAEA,cAAQ,iBAAiB;AAAA,QACvB,IAAI,EAAE,cAAc,QAAQ;AAAA,MAC3B,GAAA,MAAM,EAAE,KAAK,SAAS,KAAK;AAC5BA,sBAAA,MAAI,YAAY;AAEZ,YAAA,IAAI,QAAQ,KAAK;AACnB,cAAI,UAAU,IAAI;AAClBA,wBAAAA,MAAI,eAAe;AAAA,YACjB,UAAU;AAAA,YACV,WAAW,QAAQ;AAAA,YACnB,UAAU,QAAQ;AAAA,YAClB,SAAS,QAAQ;AAAA,YACjB,UAAU,QAAQ;AAAA,YAClB,SAAS,QAAQ;AAAA,YACjB,SAAS,WAAW;AAClB,mBAAK,YAAY,mBAAmB;AACpC,mBAAK,SAAS;AACd,mBAAK,eAAe;AACpB,mBAAK,OAAO;AACZ,mBAAK,UAAU;AAAA,YACjB;AAAA,YACA,MAAM,WAAW;AACf,wBAAU,UAAU;AAAA,YACtB;AAAA,UAAA,CACD;AAAA,QAAA,OACI;AACK,oBAAA,IAAI,OAAO,YAAY;AAAA,QACnC;AAAA,MAAA,CACD,EAAE,MAAM,MAAM;AACbA,sBAAA,MAAI,YAAY;AAChB,kBAAU,YAAY;AAAA,MAAA,CACvB;AAAA,IACH;AAAA;AAAA,IAGA,UAAU,GAAG;AACX,UAAI,OAAO;AACP,UAAA,MAAM,EAAE,cAAc,QAAQ;AAC9B,UAAA,OAAO,EAAE,cAAc,QAAQ;AACnC,UAAI,SAAS;AAGb,UAAI,QAAQ,KAAK,KAAK,KAAK,GAAG,EAAE,cAAc,KAAK,KAAK,KAAK,GAAG,EAAE,UAAU,GAAG;AACpE,iBAAA;AAAA,MACX;AAGA,UAAI,UAAU;AACd,UAAI,QAAQ;AAAc,mBAAA;AAAA,eACjB,QAAQ;AAAc,mBAAA;AAAA,eACtB,QAAQ;AAAc,mBAAA;AACpB,iBAAA;AAEX,UAAI,QAAQ;AAAc,mBAAA;AAE1BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,SAAS,KAAK;AACrB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,YAAY;AAAA,cACd,MAAM;AAAA,YAAA,CACP;AAED,gBAAI,KAAK,aAAa;AACpB,yBAAW,MAAM;AACfA,8BAAA,MAAI,YAAY;AAEhB,oBAAI,QAAQ,GAAG;AAER,uBAAA,KAAK,OAAO,KAAK,CAAC;AACnB,sBAAA,KAAK,KAAK,UAAU,GAAG;AACzB,yBAAK,UAAU;AAAA,kBACjB;AACA,uBAAK,YAAY,MAAM;AAAA,gBAAA,WACd,QAAQ,GAAG;AAEhB,sBAAA,KAAK,UAAU,GAAG;AACf,yBAAA,KAAK,GAAG,EAAE,SAAS;AACnB,yBAAA,KAAK,GAAG,EAAE,cAAc;AAAA,kBAAA,OACxB;AACA,yBAAA,KAAK,OAAO,KAAK,CAAC;AACnB,wBAAA,KAAK,KAAK,UAAU,GAAG;AACzB,2BAAK,UAAU;AAAA,oBACjB;AAAA,kBACF;AACA,uBAAK,YAAY,MAAM;AAAA,gBAAA,WACd,QAAQ,GAAG;AAEhB,sBAAA,KAAK,UAAU,GAAG;AACf,yBAAA,KAAK,GAAG,EAAE,SAAS;AACnB,yBAAA,KAAK,GAAG,EAAE,cAAc;AAAA,kBAAA,OACxB;AACA,yBAAA,KAAK,OAAO,KAAK,CAAC;AACnB,wBAAA,KAAK,KAAK,UAAU,GAAG;AACzB,2BAAK,UAAU;AAAA,oBACjB;AAAA,kBACF;AACA,uBAAK,YAAY,QAAQ;AAAA,gBAC3B;AAEO,yBAAE,WAAW,eAAe;AAAA,iBAClC,GAAI;AACP;AAAA,YACF;AAEA,oBAAQ,cAAc;AAAA,cACpB,IAAI,KAAK,KAAK,GAAG,EAAE;AAAA,cACnB;AAAA,cACA;AAAA,YACC,GAAA,MAAM,EAAE,KAAK,SAASC,MAAK;AAC5BD,4BAAA,MAAI,YAAY;AACX,mBAAA,YAAYC,KAAI,GAAG;AAEpBA,kBAAAA,KAAI,QAAQ,KAAK;AACnB,oBAAI,KAAK,UAAU,KAAK,QAAQ,GAAG;AAE5B,uBAAA,KAAK,GAAG,EAAE,SAAS;AACnB,uBAAA,KAAK,GAAG,EAAE,cAAc;AAAA,gBACpB,WAAA,KAAK,UAAU,KAAK,QAAQ,GAAG;AAEnC,uBAAA,KAAK,GAAG,EAAE,SAAS;AACnB,uBAAA,KAAK,GAAG,EAAE,cAAc;AAAA,gBAAA,OACxB;AAEA,uBAAA,KAAK,OAAO,KAAK,CAAC;AACnB,sBAAA,KAAK,KAAK,UAAU,GAAG;AACzB,yBAAK,UAAU;AAAA,kBACjB;AACO,2BAAE,WAAW,eAAe;AAAA,gBACrC;AAAA,cACF;AAAA,YAAA,CACD,EAAE,MAAM,MAAM;AACbD,4BAAA,MAAI,YAAY;AAChB,wBAAU,YAAY;AAAA,YAAA,CACvB;AAAA,UACH;AAAA,QACF;AAAA,MAAA,CACD;AAAA,IACH;AAAA;AAAA,IAGA,kBAAkB,GAAG;AACf,UAAA,MAAM,EAAE,cAAc,QAAQ;AAClC,WAAK,YAAY,mBAAmB;AAEhC,UAAA,CAAC,KAAK,aAAa;AACrB,gBAAQ,cAAc;AAAA,UACpB,IAAI,KAAK,KAAK,GAAG,EAAE;AAAA,UACnB,MAAM;AAAA,UACN,QAAQ;AAAA,WACP,MAAM;AAAA,MACX;AAAA,IACF;AAAA;AAAA,IAGA,cAAc,GAAG;AACV,WAAA,UAAU,EAAE,cAAc,QAAQ;AAClC,WAAA,MAAM,UAAU;IACvB;AAAA;AAAA,IAGA,YAAY,MAAM;AACX,WAAA,MAAM,UAAU;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,0BAA0B,OAAO,UAAU,KAAK,KAAK,KAAK,OAAO,EAAE;AAAA,MAAA,CACzE;AAAA,IACH;AAAA;AAAA,IAGA,eAAe,GAAG;AAChBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,6BAA6B,EAAE,cAAc,QAAQ;AAAA,MAAA,CAC3D;AAAA,IACH;AAAA;AAAA,IAGA,SAAS,GAAG;AACV,UAAI,KAAK,cAAc;AAChB,aAAA,SAAS,EAAE,cAAc,QAAQ;AACtC,aAAK,eAAe;AACpB,aAAK,OAAO;AACZ,aAAK,UAAU;AAAA,MACjB;AAAA,IACF;AAAA;AAAA,IAGA,UAAU;AACJ,UAAA,gBAAA,EAAkB,SAAS,GAAG;AAChCA,sBAAA,MAAI,aAAa;AAAA,MAAA,OACZ;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,KAAK;AAAA,QAAA,CACN;AAAA,MACH;AAAA,IACF;AAAA;AAAA,IAGA,YAAY,OAAO;AACjB,UAAI,OAAO;AACX,WAAK,YAAY;AACZ,WAAA,MAAM,UAAU;AAErB,iBAAW,WAAW;AACf,aAAA,MAAM,UAAU;SACpB,GAAI;AAAA,IACT;AAAA;AAAA,IAGA,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MAAA,CACN;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1iBA,GAAG,WAAW,eAAe;"}