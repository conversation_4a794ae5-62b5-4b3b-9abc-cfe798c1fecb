
.lazy-image[data-v-337fa078]{position:relative;width:100%;height:100%;overflow:hidden;background:#f8f8f8;}
.lazy-image uni-image[data-v-337fa078]{width:100%;height:100%;}
.lazy-image .err[data-v-337fa078]{position:absolute;top:0;left:0;width:100%;height:100%;background:rgba(248,248,248,0.7) url('../../static/img/load.png') no-repeat center/50%;}


/* 基础样式 - 参考 details.vue 的 empty-box 样式 */
.empty-page[data-v-5cea664a] {
  width: 100%;
  padding: 3.125rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.empty-content[data-v-5cea664a] {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
}

/* 图片样式 - 参考 details.vue */
.empty-image[data-v-5cea664a] {
  width: 9.375rem;
  height: 9.375rem;
  margin-bottom: 0.9375rem;
  opacity: 0.8;
}

/* 标题样式 - 参考 details.vue 的 .e1 */
.empty-title[data-v-5cea664a] {
  font-size: 0.9375rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.3125rem;
  line-height: 1.4;
}

/* 描述样式 - 参考 details.vue 的 .e2 */
.empty-description[data-v-5cea664a] {
  margin-top: 0.3125rem;
  color: #999;
  font-size: 0.8125rem;
  line-height: 1.5;
  margin-bottom: 1.25rem;
  max-width: 15.625rem;
  text-align: center;
}

/* 按钮样式 - 参考 details.vue 的 retry-btn */
.empty-button[data-v-5cea664a] {
  margin-top: 1.25rem;
  width: 6.25rem;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 700;
  color: #fff;
  background: #007aff;
  border-radius: 1.25rem;
  transition: all 0.3s ease;
}
.empty-button[data-v-5cea664a]:active {
  background: #0056cc;
  transform: scale(0.95);
}

/* 工具类 */
.df[data-v-5cea664a] {
  display: flex;
  align-items: center;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
.empty-title[data-v-5cea664a] {
    color: #fff;
}
.empty-description[data-v-5cea664a] {
    color: #ccc;
}
.empty-page[data-v-5cea664a] {
    background-color: #1a1a1a;
}
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
.empty-image[data-v-5cea664a] {
    width: 7.8125rem;
    height: 7.8125rem;
}
.empty-title[data-v-5cea664a] {
    font-size: 0.875rem;
}
.empty-description[data-v-5cea664a] {
    font-size: 0.75rem;
    padding: 0 1.25rem;
}
}

/* 小程序兼容性 */






/* H5 兼容性 */








/* 优化性能：添加will-change和transform3d */
.nav-bar{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
  /* APP端兼容性：条件编译backdrop-filter */
}
.bar-box .bar-back{
  padding: 0 0.9375rem;
  width: 1.0625rem;
  height: 100%;
}
.bar-box .bar-title{
  max-width: 60%;
  font-size: 1rem;
  font-weight: 700;
}
.nav-box{
  width: 100%;
  height: 2.5rem;
}
.nav-box .nav-item{
  padding: 0 0.9375rem;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  position: relative;
}
.nav-box .nav-item uni-text{
  font-weight: 700;
  transition: all .3s ease-in-out;
}
.nav-box .nav-line{
  position: absolute;
  bottom: 0.375rem;
  width: 0.5625rem;
  height: 0.1875rem;
  border-radius: 0.1875rem;
  background: #000;
  transition: opacity .3s ease-in-out;
}
.content-box{
  width: calc(100% - 2.5rem);
  padding: 0.625rem 1.25rem;
}
/* 加载中状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 1.875rem;
  margin-bottom: 0.625rem;
}
.loading-indicator {
  width: 0.9375rem;
  height: 0.9375rem;
  border: 0.09375rem solid #f3f3f3;
  border-top: 0.09375rem solid #000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.list-box{
  width: 100%;
  padding: 0.625rem 0;
  justify-content: space-between;
}
.list-box .list-item{
  width: calc(100% - 3.75rem - 2px);
}
.list-item .list-avatar{
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  border: 1px solid #f8f8f8;
  overflow: hidden;
}
.list-item .item-content{
  width: calc(100% - 3.125rem);
  margin-left: 0.625rem;
}
.item-content .name{
  font-size: 0.875rem;
  font-weight: 700;
}
.item-content .intro{
  color: #999;
  font-size: 0.625rem;
}
.list-box .list-btn{
  width: 3.125rem;
  height: 1.5625rem;
  line-height: 1.5625rem;
  text-align: center;
  font-size: 0.625rem;
  font-weight: 700;
  border-radius: 0.25rem;
}
.bg1{
  color: #999;
  background: #f8f8f8;
}
.bg2{
  color: #fff;
  background: #000;
}
.heio{
  transition: height 0.3s;
}
.empty-box{
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.125rem 0;
}
.empty-box uni-image{
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.9375rem;
}
.empty-box .e1{
  font-size: 0.875rem;
  font-weight: bold;
  margin-bottom: 0.3125rem;
}
.empty-box .e2{
  font-size: 0.75rem;
  color: #999;
}
.df{
  display: flex;
  align-items: center;
}
.bfw{
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255,255,255,.8);
}
.ohto{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
