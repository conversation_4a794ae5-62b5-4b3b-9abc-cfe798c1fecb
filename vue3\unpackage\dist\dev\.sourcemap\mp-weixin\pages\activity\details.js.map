{"version": 3, "file": "details.js", "sources": ["pages/activity/details.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWN0aXZpdHkvZGV0YWlscy52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 自定义顶部导航 -->\r\n    <view class=\"nav-box df\" :style=\"{'padding-top': statusBarHeight + 'px', 'background': 'rgba(255, 255, 255,' + navbarTrans + ')'}\">\r\n      <view class=\"nav-back df\" :style=\"{'height': titleBarHeight + 'px'}\" @tap=\"navBack\">\r\n        <image :class=\"navbarTrans != 1 ? '' : 'xwb'\" :src=\"navbarTrans == 1 ? '/static/img/z.png' : '/static/img/z1.png'\"></image>\r\n      </view>\r\n      <view v-if=\"navbarTrans == 1\" class=\"nav-title ohto\">{{activityInfo.name}}</view>\r\n    </view>\r\n\r\n    <!-- 轮播图 -->\r\n    <swiper class=\"swiper-box\" circular @change=\"swiperChange\">\r\n      <swiper-item v-for=\"(item, index) in activityInfo.imgs\" :key=\"index\" class=\"swiper-item\" @tap=\"swiperClick\" :data-i=\"index\">\r\n        <lazy-image :src=\"item\"></lazy-image>\r\n      </swiper-item>\r\n    </swiper>\r\n    <view class=\"indicator df\">\r\n      <block v-if=\"activityInfo.imgs.length > 1\">\r\n        <view v-for=\"(item, index) in activityInfo.imgs.length\" :key=\"index\" :class=\"['indicator-item', swiperIdx == index ? 'active' : '']\"></view>\r\n      </block>\r\n    </view>\r\n\r\n    <!-- 内容区域 -->\r\n    <view class=\"content-box\">\r\n      <view class=\"content-bar df\" :style=\"{'top': statusBarHeight + titleBarHeight - 1 + 'px'}\">\r\n        <view class=\"df\" style=\"height:100%;margin-left:15rpx\">\r\n          <view v-for=\"(item, index) in barList\" :key=\"index\" class=\"bar-nav df\" @tap=\"barClick\" :data-idx=\"index\">\r\n            <text :style=\"{'color': index == barIdx ? '#000' : '#999', 'font-size': index == barIdx ? '28rpx' : '26rpx'}\">{{item}}</text>\r\n            <view :style=\"{'opacity': index == barIdx ? 1 : 0}\" class=\"line\"></view>\r\n          </view>\r\n        </view>\r\n        <view class=\"df\" style=\"margin-right:30rpx\">\r\n          <view :class=\"['bar-btn', 'df', activityInfo.status == 1 ? 's1' : '', activityInfo.status == 2 ? 's2' : '']\">\r\n            <text style=\"margin-left:0\">{{activityInfo.status_str}}</text>\r\n          </view>\r\n          <view class=\"bar-btn df\" @tap=\"openActivityNote\" style=\"margin:0 15rpx\">\r\n            <image class=\"avatar\" :src=\"userAvatar\" mode=\"aspectFill\"></image>\r\n            <text>＋笔记</text>\r\n          </view>\r\n          <view class=\"bar-btn df\" @tap=\"shareClick(true)\">\r\n            <image class=\"icon\" src=\"/static/img/fx1.png\"></image>\r\n            <text>分享</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 详情内容 -->\r\n      <view v-if=\"barIdx == 0\" class=\"content-item\">\r\n        <view v-if=\"activityInfo.is_join\" class=\"joins-box df\" @tap=\"navigateToFun\" data-url=\"activity/index?type=1\">\r\n          <view class=\"txt df\">\r\n            <image class=\"avatar\" :src=\"userAvatar\" mode=\"aspectFill\"></image>\r\n            <text>您已参加该活动，前往查看门票</text>\r\n          </view>\r\n          <view class=\"arr effect df\">\r\n            <image src=\"/static/img/arrow.png\"></image>\r\n          </view>\r\n        </view>\r\n        <view class=\"info-map\">\r\n          <image class=\"bg\" src=\"/static/img/inset/map.jpg\" mode=\"aspectFill\" style=\"z-index:-2\"></image>\r\n          <view class=\"mk\"></view>\r\n          <view class=\"info-item\" @tap=\"openLocationClick\">\r\n            <view class=\"info-item-tit\"> 地点：<text user-select=\"true\">{{activityInfo.adds_name}}</text></view>\r\n            <view class=\"info-item-tit\"> 时间：<text user-select=\"true\">{{activityInfo.activity_time}}</text></view>\r\n            <view class=\"df\" style=\"justify-content:space-between\">\r\n              <view v-if=\"activityInfo.user_count\" class=\"cu-img-group\">\r\n                <view v-for=\"(img, index) in activityInfo.avatar_list\" :key=\"index\" class=\"img-group\">\r\n                  <image :src=\"img\" mode=\"aspectFill\"></image>\r\n                </view>\r\n                <view class=\"img-tit\">{{activityInfo.user_count}}人已参加</view>\r\n              </view>\r\n              <view v-else class=\"info-item-tit\">\r\n                <text>{{activityInfo.browse}}人想参加</text>\r\n              </view>\r\n              <view class=\"adds-box df\">\r\n                <image src=\"/static/img/hd.png\"></image>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view class=\"info-title\">{{activityInfo.name}}</view>\r\n        <view class=\"info-intro\">\r\n          <rich-text :nodes=\"activityInfo.intro\" @tap=\"onRichTextTap\"></rich-text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 相关笔记 -->\r\n      <view v-else :class=\"isWaterfall ? 'dynamic-box' : ''\">\r\n        <view v-if=\"isEmpty\" class=\"empty-box df\">\r\n          <image src=\"/static/img/inset/null.png\"/>\r\n          <view class=\"e1\">暂无相关笔记</view>\r\n          <view class=\"e2\">空空如也，等待探索</view>\r\n        </view>\r\n        <waterfall v-if=\"isWaterfall\" :note=\"list\" :page=\"page\"></waterfall>\r\n        <block v-else>\r\n          <block v-for=\"(item, index) in list\" :key=\"index\">\r\n            <card-gg @likeback=\"likeClick\" :item=\"item\" :idx=\"index\"></card-gg>\r\n          </block>\r\n        </block>\r\n        <uni-load-more :status=\"loadStatus\"></uni-load-more>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 底部操作栏 -->\r\n    <view class=\"footer-box bfw bUp\">\r\n      <view class=\"btn-box df\">\r\n        <view v-if=\"!isUser\" class=\"btn-item df\" @tap=\"navigateToFun\" data-url=\"center/means\" style=\"width:calc(100% - 120rpx);justify-content:center\">\r\n          <text>完善账号资料后即可参加活动，去完善</text>\r\n          <view class=\"arr effect df\" style=\"margin-left:20rpx\">\r\n            <image src=\"/static/img/y.png\"></image>\r\n          </view>\r\n        </view>\r\n        <block v-else>\r\n          <view class=\"btn-price\">\r\n            <view class=\"df\" style=\"height:48rpx\">\r\n              <view class=\"nian\">优惠价</view>\r\n              <money :price=\"activityInfo.product[productIdx].price\"></money>\r\n            </view>\r\n            <view class=\"through\">原价：¥{{activityInfo.product[productIdx].line_price}}</view>\r\n          </view>\r\n          <view class=\"btn-item df\" @tap=\"registerClick(true)\">\r\n            <image class=\"icon\" src=\"/static/img/hd.png\"></image>\r\n            <text>立即参加</text>\r\n          </view>\r\n        </block>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 报名弹窗 -->\r\n    <uni-popup ref=\"registerPopup\" type=\"bottom\" :safe-area=\"false\">\r\n      <view class=\"popup-box\">\r\n        <view class=\"popup-top df\">\r\n          <view class=\"popup-title\">\r\n            <view class=\"t1\">参加活动</view>\r\n            <view class=\"t2\">购买<text>\"</text>{{activityInfo.name}}<text>\"</text>的门票</view>\r\n          </view>\r\n          <view class=\"popup-close df\" @tap=\"registerClick(false)\">\r\n            <image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx\"/>\r\n          </view>\r\n        </view>\r\n        <scroll-view scroll-x=\"true\" class=\"scroll-box\">\r\n          <view class=\"product-box\">\r\n            <view v-for=\"(item, index) in activityInfo.product\" :key=\"index\" :class=\"['product-item', productIdx == index ? 'active' : '']\" @tap=\"productIdx = index\">\r\n              <view class=\"tag\">剩余{{item.stock}}张</view>\r\n              <view class=\"name\">{{item.name}}</view>\r\n              <view class=\"time\">报名截止时间：{{activityInfo.start_time}}</view>\r\n              <view style=\"display:flex;align-items:flex-end\">\r\n                <money :price=\"item.price\"></money>\r\n                <view class=\"td-lt\">¥{{item.line_price}}</view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </scroll-view>\r\n        <view class=\"quantity-box df\">\r\n          <view class=\"quantity-tit\">门票数（人）</view>\r\n          <view class=\"quantity-item df\">\r\n            <view class=\"quantity-btn\" :style=\"{'color': quantity > 1 ? '#000' : '#ccc'}\" @tap=\"quantityBtn(0)\">－ </view>\r\n            <input @blur=\"quantityBtn(2)\" type=\"number\" maxlength=\"2\" :value=\"quantity\" @input=\"quantity = $event.detail.value\"/>\r\n            <view class=\"quantity-btn\" :style=\"{'color': quantity < activityInfo.product[productIdx].stock ? '#000' : '#ccc'}\" @tap=\"quantityBtn(1)\">＋ </view>\r\n          </view>\r\n        </view>\r\n        <view class=\"quantity-box df\">\r\n          <view class=\"quantity-tit\">支付金额</view>\r\n          <money :price=\"activityInfo.product[productIdx].price * quantity\"></money>\r\n        </view>\r\n        <button class=\"popup-btn df\" @tap=\"wxPayClick\">\r\n          <image src=\"/static/img/pay.png\"></image>\r\n          <text>确认支付</text>\r\n        </button>\r\n      </view>\r\n    </uni-popup>\r\n\r\n    <!-- 分享弹窗 -->\r\n    <uni-popup ref=\"sharePopup\" class=\"r\">\r\n      <view class=\"share-popup\">\r\n        <image class=\"share-img\" src=\"/static/img/inset/share.jpg\"></image>\r\n        <view class=\"share-tips\">\r\n          <text>点击页面右上角</text>\r\n          <image src=\"/static/img/xcx.png\"></image>\r\n          <text style=\"margin-left:78rpx\">即可转发给朋友、分享到朋友圈、复制链接分享等操作。</text>\r\n        </view>\r\n        <view class=\"share-btn\" @tap=\"shareClick(false)\">确认</view>\r\n      </view>\r\n    </uni-popup>\r\n\r\n    <!-- 笔记发布类型弹窗 -->\r\n    <uni-popup ref=\"notePopup\" type=\"center\">\r\n      <view class=\"note-box\">\r\n        <view class=\"note-add df\" @tap=\"toActivityNote(1)\">\r\n          <image src=\"/static/img/tw.png\"></image>\r\n          <text>图文笔记</text>\r\n        </view>\r\n        <view class=\"note-add df\" @tap=\"toActivityNote(2)\">\r\n          <image src=\"/static/img/sp.png\"></image>\r\n          <text>视频笔记</text>\r\n        </view>\r\n        <view class=\"note-add df\" @tap=\"toActivityNote(3)\">\r\n          <image src=\"/static/img/yw.png\"></image>\r\n          <text>音文笔记</text>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n\r\n    <!-- 提示弹窗 -->\r\n    <uni-popup ref=\"tipsPopup\" type=\"top\" mask-background-color=\"rgba(0, 0, 0, 0)\">\r\n      <view class=\"tips-box df\">\r\n        <view class=\"tips-item\">{{tipsTitle}}</view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue'\r\nimport lazyImage from '@/components/lazyImage/lazyImage.vue'\r\nimport money from '@/components/money/money.vue'\r\nimport waterfall from '@/components/waterfall/waterfall.vue'\r\nimport cardGg from '@/components/card-gg/card-gg.vue'\r\n\r\nconst app = getApp();\r\n\r\nexport default {\r\n  components: {\r\n    uniLoadMore,\r\n    lazyImage,\r\n    money,\r\n    waterfall,\r\n    cardGg\r\n  },\r\n  data() {\r\n    return {\r\n      statusBarHeight: this.$store.state.statusBarHeight || 20,\r\n      titleBarHeight: this.$store.state.titleBarHeight || 44,\r\n      isUser: false,\r\n      navbarTrans: 0,\r\n      userAvatar: \"\",\r\n      barList: [\"详情\", \"笔记\"],\r\n      barIdx: 0,\r\n      shareView: false,\r\n      activityInfo: {\r\n        id: 0,\r\n        imgs: [],\r\n        name: \"名称加载中\",\r\n        intro: \"介绍加载中\",\r\n        activity_time: \"时间加载中\",\r\n        adds_name: \"地址加载中\",\r\n        status_str: \"加载中\",\r\n        start_time: \"时间加载中\",\r\n        is_join: false,\r\n        user_count: 0,\r\n        browse: 1,\r\n        status: 1,\r\n        product: [{\r\n          name: \"名称加载中\",\r\n          price: \"0.00\",\r\n          line_price: \"0.00\",\r\n          stock: 1\r\n        }]\r\n      },\r\n      productIdx: 0,\r\n      isQuantity: false,\r\n      quantity: 1,\r\n      swiperIdx: 0,\r\n      list: [],\r\n      page: 1,\r\n      isEmpty: false,\r\n      loadStatus: \"more\",\r\n      tipsTitle: \"\",\r\n      isWaterfall: false\r\n    };\r\n  },\r\n  async onLoad(option) {\r\n    // 显示分享菜单 - 添加平台兼容性检查\r\n    try {\r\n      // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ\r\n      if (typeof uni.showShareMenu === 'function') {\r\n        uni.showShareMenu();\r\n      }\r\n      // #endif\r\n    } catch (e) {\r\n      console.warn('showShareMenu not supported on this platform:', e)\r\n    }\r\n\r\n    await this.$onLaunched;\r\n\r\n    if (option.id) {\r\n      this.activityInfo.id = option.id;\r\n      this.activityDetails();\r\n      if (option.share) {\r\n        this.shareView = true;\r\n      }\r\n      this.userAvatar = uni.getStorageSync(\"userInfo\").avatar || \"/static/img/avatar.png\";\r\n    } else {\r\n      this.opTipsPopup(\"活动异常或已被下架，请稍后重试！\", true);\r\n    }\r\n  },\r\n  onShow() {\r\n    const userInfo = uni.getStorageSync(\"userInfo\");\r\n    if (userInfo && userInfo.mobile) {\r\n      this.isUser = true;\r\n    }\r\n  },\r\n  methods: {\r\n    activityDetails() {\r\n      // 模拟API请求获取活动详情\r\n      setTimeout(() => {\r\n        // 模拟数据\r\n        const activityData = {\r\n          id: this.activityInfo.id,\r\n          imgs: [\"/static/img/avatar.png\", \"/static/img/avatar.png\"],\r\n          name: \"夏季摄影大赛\",\r\n          intro: \"这是一场摄影爱好者的盛会，将在市中心广场举办。参赛者需携带自己的设备，活动现场将有专业摄影师指导。\\n\\n获奖者将有机会获得丰厚奖品，作品也将在展览厅展出一个月。\\n\\n欢迎各位摄影爱好者参加！\",\r\n          activity_time: \"2023-07-20 14:00-17:00\",\r\n          adds_name: \"市中心广场\",\r\n          status_str: \"报名中\",\r\n          start_time: \"2023-07-19 23:59:59\",\r\n          is_join: false,\r\n          user_count: 15,\r\n          browse: 123,\r\n          status: 1,\r\n          lat: \"39.908823\",\r\n          lng: \"116.397470\",\r\n          avatar_list: [\"/static/img/avatar.png\", \"/static/img/avatar.png\", \"/static/img/avatar.png\"],\r\n          product: [\r\n            {\r\n              id: 101,\r\n              name: \"普通票\",\r\n              price: \"19.90\",\r\n              line_price: \"39.90\",\r\n              stock: 20\r\n            },\r\n            {\r\n              id: 102,\r\n              name: \"VIP票\",\r\n              price: \"49.90\",\r\n              line_price: \"99.90\",\r\n              stock: 10\r\n            }\r\n          ]\r\n        };\r\n        \r\n        this.activityInfo = activityData;\r\n        \r\n        if (this.shareView) {\r\n          this.shareClick(true);\r\n        }\r\n        \r\n        this.navigationBarColor(\"#ffffff\");\r\n      }, 500);\r\n    },\r\n    \r\n    dynamicRecommend() {\r\n      let that = this;\r\n      that.loadStatus = \"loading\";\r\n      that.isEmpty = false;\r\n      that.isWaterfall = app.globalData.isWaterfall || false;\r\n      \r\n      // 模拟获取相关笔记\r\n      setTimeout(() => {\r\n        if (that.page == 1) {\r\n          that.list = [\r\n            {\r\n              id: 201,\r\n              uid: 1001,\r\n              user: {\r\n                id: 1001,\r\n                name: \"摄影爱好者\",\r\n                avatar: \"/static/img/avatar.png\"\r\n              },\r\n              title: \"参加了摄影活动的感受\",\r\n              content: \"今天参加了一场很棒的摄影活动，学到了很多技巧...\",\r\n              imgs: [\"/static/img/avatar.png\"], \r\n              like_count: 25,\r\n              comment_count: 8,\r\n              is_like: false\r\n            },\r\n            {\r\n              id: 202,\r\n              uid: 1002,\r\n              user: {\r\n                id: 1002,\r\n                name: \"风景摄影师\",\r\n                avatar: \"/static/img/avatar.png\"\r\n              },\r\n              title: \"分享几张活动中拍摄的照片\",\r\n              content: \"这些是我在活动中拍摄的几张照片，欢迎大家点评...\",\r\n              imgs: [\"/static/img/avatar.png\", \"/static/img/avatar.png\"],\r\n              like_count: 36,\r\n              comment_count: 12,\r\n              is_like: false\r\n            }\r\n          ];\r\n          that.isEmpty = false;\r\n        } else if (that.page == 2) {\r\n          that.list.push({\r\n            id: 203,\r\n            uid: 1003,\r\n            user: {\r\n              id: 1003,\r\n              name: \"手机摄影达人\",\r\n              avatar: \"/static/img/avatar.png\"\r\n            },\r\n            title: \"用手机也能拍出好照片\",\r\n            content: \"今天的活动中学到了很多用手机拍出专业效果的技巧...\",\r\n            imgs: [\"/static/img/avatar.png\"],\r\n            like_count: 18,\r\n            comment_count: 5,\r\n            is_like: false\r\n          });\r\n        } else {\r\n          // 没有更多数据\r\n          that.loadStatus = \"noMore\";\r\n        }\r\n        \r\n        if (that.list.length === 0) {\r\n          that.isEmpty = true;\r\n        }\r\n        \r\n        that.loadStatus = \"more\";\r\n      }, 500);\r\n    },\r\n    \r\n    wxPayClick() {\r\n      let that = this;\r\n      \r\n      // 模拟支付请求\r\n      uni.showLoading({\r\n        title: '正在支付...'\r\n      });\r\n      \r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        \r\n        // 模拟微信支付流程\r\n        uni.showModal({\r\n          title: '支付确认',\r\n          content: '是否确认支付' + (that.activityInfo.product[that.productIdx].price * that.quantity) + '元？',\r\n          success: function(res) {\r\n            if (res.confirm) {\r\n              app.globalData.isCenterPage = true;\r\n              that.$refs.registerPopup.close();\r\n              that.opTipsPopup(\"购买成功！正在为您出票...\");\r\n              \r\n              setTimeout(function() {\r\n                uni.navigateTo({\r\n                  url: \"/pages/activity/index?type=1\"\r\n                });\r\n              }, 2000);\r\n            }\r\n          }\r\n        });\r\n      }, 500);\r\n    },\r\n    \r\n    barClick(e) {\r\n      this.barIdx = e.currentTarget.dataset.idx;\r\n      if (this.barIdx == 1) {\r\n        this.page = 1;\r\n        this.list = [];\r\n        this.dynamicRecommend();\r\n      }\r\n    },\r\n    \r\n    quantityBtn(t) {\r\n      if (t == 1 && this.quantity < this.activityInfo.product[this.productIdx].stock) {\r\n        this.quantity = parseInt(this.quantity) + 1;\r\n      } else if (t == 0 && this.quantity != 1) {\r\n        this.quantity = parseInt(this.quantity) - 1;\r\n      } else if (t == 2 && this.quantity <= 0) {\r\n        this.quantity = 1;\r\n      } else if (t == 2 && this.quantity > this.activityInfo.product[this.productIdx].stock) {\r\n        this.quantity = this.activityInfo.product[this.productIdx].stock;\r\n      }\r\n    },\r\n    \r\n    swiperClick(e) {\r\n      let i = e.currentTarget.dataset.i;\r\n      uni.previewImage({\r\n        current: i,\r\n        urls: this.activityInfo.imgs\r\n      });\r\n    },\r\n    \r\n    swiperChange(e) {\r\n      this.swiperIdx = e.detail.current;\r\n    },\r\n    \r\n    openLocationClick() {\r\n      // 调用地图查看位置\r\n      uni.openLocation({\r\n        latitude: parseFloat(this.activityInfo.lat),\r\n        longitude: parseFloat(this.activityInfo.lng),\r\n        name: this.activityInfo.adds_name\r\n      });\r\n    },\r\n    \r\n    openActivityNote() {\r\n      this.$refs.notePopup.open();\r\n    },\r\n    \r\n    toActivityNote(type) {\r\n      if (this.activityInfo.is_join) {\r\n        uni.navigateTo({\r\n          url: \"/pages/note/add?type=\" + type + \r\n               \"&aid=\" + this.activityInfo.id + \r\n               \"&aname=\" + this.activityInfo.name + \r\n               \"&aimg=\" + this.activityInfo.imgs[0]\r\n        });\r\n      } else {\r\n        this.opTipsPopup(\"未参加该活动无法发布相关笔记！\");\r\n      }\r\n    },\r\n    \r\n    registerClick(show) {\r\n      if (!show) {\r\n        this.$refs.registerPopup.close();\r\n        return;\r\n      }\r\n      \r\n      let msg = \"活动 \" + this.activityInfo.status_str + \" 无法参加！\";\r\n      if (this.activityInfo.status != 1) {\r\n        return this.opTipsPopup(msg);\r\n      }\r\n      \r\n      this.$refs.registerPopup.open();\r\n    },\r\n    \r\n    shareClick(show) {\r\n      if (!show) {\r\n        this.$refs.sharePopup.close();\r\n      } else {\r\n        this.$refs.sharePopup.open();\r\n      }\r\n    },\r\n    \r\n    likeClick(e) {\r\n      this.list[e.idx].is_like = e.is_like;\r\n      this.list[e.idx].like_count = e.like_count;\r\n    },\r\n    \r\n    onRichTextTap() {\r\n      const intro = this.activityInfo.intro;\r\n      const imgRegex = /<img[^>]+src=\"([^\">]+)\"/g;\r\n      const imageUrls = [];\r\n      \r\n      let match;\r\n      while ((match = imgRegex.exec(intro)) !== null) {\r\n        imageUrls.push(match[1]);\r\n      }\r\n      \r\n      if (imageUrls.length) {\r\n        uni.previewImage({\r\n          current: 0,\r\n          urls: imageUrls\r\n        });\r\n      }\r\n    },\r\n    \r\n    navigateToFun(e) {\r\n      let url = e.currentTarget.dataset.url;\r\n      uni.navigateTo({\r\n        url: \"/pages/\" + url\r\n      });\r\n    },\r\n    \r\n    navBack() {\r\n      if (getCurrentPages().length > 1) {\r\n        uni.navigateBack();\r\n      } else {\r\n        uni.switchTab({\r\n          url: \"/pages/index/index\"\r\n        });\r\n      }\r\n    },\r\n    \r\n    opTipsPopup(title, isNeedBack = false) {\r\n      this.tipsTitle = title;\r\n      this.$refs.tipsPopup.open();\r\n      \r\n      setTimeout(() => {\r\n        this.$refs.tipsPopup.close();\r\n        if (isNeedBack) {\r\n          this.navBack();\r\n        }\r\n      }, 2000);\r\n    },\r\n    \r\n    navigationBarColor(frontColor) {\r\n      uni.setNavigationBarColor({\r\n        frontColor: frontColor,\r\n        backgroundColor: \"#ffffff\",\r\n        animation: {\r\n          duration: 400,\r\n          timingFunc: \"easeIn\"\r\n        }\r\n      });\r\n    }\r\n  },\r\n  onReachBottom() {\r\n    if (!this.isEmpty && this.list.length && this.barIdx == 1) {\r\n      this.page = this.page + 1;\r\n      this.dynamicRecommend();\r\n    }\r\n  },\r\n  onPageScroll(e) {\r\n    let frontColor = \"#ffffff\";\r\n    const scrollTop = e.scrollTop > 150 ? 150 : e.scrollTop;\r\n    const opacity = scrollTop / 150;\r\n    \r\n    if (opacity >= 1) {\r\n      frontColor = \"#000000\";\r\n    }\r\n    \r\n    this.navbarTrans = opacity;\r\n    this.navigationBarColor(frontColor);\r\n  },\r\n  onShareAppMessage() {\r\n    return {\r\n      title: this.activityInfo.name,\r\n      imageUrl: this.activityInfo.imgs[0],\r\n      path: \"/pages/activity/details?id=\" + this.activityInfo.id + \"&share=1\"\r\n    };\r\n  },\r\n  onShareTimeline() {\r\n    return {\r\n      title: this.activityInfo.name,\r\n      imageUrl: this.activityInfo.imgs[0],\r\n      query: \"id=\" + this.activityInfo.id\r\n    };\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n.container {\r\n  width: 100%;\r\n  padding-bottom: calc(env(safe-area-inset-bottom) + 160rpx);\r\n}\r\n.nav-box {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  z-index: 99;\r\n  box-sizing: border-box;\r\n}\r\n.nav-box .nav-back {\r\n  padding: 0 30rpx;\r\n  width: 34rpx;\r\n  height: 100%;\r\n}\r\n.nav-box .nav-back image {\r\n  width: 34rpx;\r\n  height: 34rpx;\r\n}\r\n.nav-box .nav-title {\r\n  max-width: 60%;\r\n  font-size: 32rpx;\r\n  font-weight: 700;\r\n}\r\n.swiper-box {\r\n  z-index: -1;\r\n  width: 100%;\r\n  height: auto;\r\n  aspect-ratio: 5/4;\r\n  overflow: hidden;\r\n}\r\n.swiper-box .swiper-item {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n.indicator {\r\n  margin-top: -68rpx;\r\n  width: 100%;\r\n  justify-content: center;\r\n}\r\n.indicator .indicator-item {\r\n  z-index: 1;\r\n  margin: 0 6rpx;\r\n  width: 8rpx;\r\n  height: 8rpx;\r\n  border-radius: 8rpx;\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transition: all 0.3s;\r\n}\r\n.indicator .active {\r\n  width: 24rpx;\r\n  background: rgba(255, 255, 255, 0.9);\r\n}\r\n.arr {\r\n  width: 44rpx;\r\n  height: 44rpx;\r\n  background: #fff;\r\n  border-radius: 50%;\r\n  justify-content: center;\r\n}\r\n.arr image {\r\n  width: 20rpx;\r\n  height: 20rpx;\r\n}\r\n.content-box {\r\n  width: 100%;\r\n  padding-top: 30rpx;\r\n}\r\n.content-box .content-bar {\r\n  position: -webkit-sticky;\r\n  position: sticky;\r\n  left: 0;\r\n  z-index: 99;\r\n  width: 100%;\r\n  height: 100rpx;\r\n  background: #fff;\r\n  border-radius: 30rpx 30rpx 0 0;\r\n  justify-content: space-between;\r\n}\r\n.content-bar .bar-nav {\r\n  padding: 0 15rpx;\r\n  height: 80rpx;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n.content-bar .bar-nav text {\r\n  font-weight: 700;\r\n  transition: all 0.3s ease-in-out;\r\n}\r\n.content-bar .bar-nav .line {\r\n  position: absolute;\r\n  bottom: 12rpx;\r\n  width: 18rpx;\r\n  height: 6rpx;\r\n  border-radius: 6rpx;\r\n  background: #000;\r\n  transition: opacity 0.3s ease-in-out;\r\n}\r\n.content-bar .bar-btn {\r\n  padding: 0 16rpx;\r\n  height: 50rpx;\r\n  line-height: 50rpx;\r\n  border-radius: 8rpx;\r\n  background: #fff;\r\n  border: 1px solid #f5f5f5;\r\n}\r\n.bar-btn text {\r\n  margin-left: 8rpx;\r\n  font-size: 20rpx;\r\n  font-weight: 700;\r\n}\r\n.bar-btn .avatar {\r\n  width: 30rpx;\r\n  height: 30rpx;\r\n  border-radius: 50%;\r\n}\r\n.bar-btn .icon {\r\n  width: 20rpx;\r\n  height: 20rpx;\r\n}\r\n.content-bar .s1 {\r\n  color: #fa5150;\r\n  background: rgba(250, 81, 80, 0.082);\r\n  border: 1px solid #FA515015;\r\n}\r\n.content-bar .s2 {\r\n  color: #000;\r\n  background: rgba(0, 0, 0, 0.082);\r\n  border: 1px solid #000;\r\n}\r\n.content-box .content-item {\r\n  width: calc(100% - 60rpx);\r\n  padding: 0 30rpx;\r\n}\r\n.content-box .dynamic-box {\r\n  width: calc(100% - 16rpx);\r\n  padding: 0 8rpx;\r\n}\r\n.content-box .joins-box {\r\n  margin-bottom: 30rpx;\r\n  width: calc(100% - 60rpx);\r\n  padding: 30rpx;\r\n  background: #000;\r\n  border-radius: 8rpx;\r\n  justify-content: space-between;\r\n}\r\n.content-box .joins-box .txt {\r\n  color: #fff;\r\n  font-size: 22rpx;\r\n  font-weight: 700;\r\n}\r\n.content-box .joins-box .txt image {\r\n  margin-right: 8rpx;\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  border-radius: 50%;\r\n}\r\n.content-box .info-map {\r\n  width: calc(100% - 2px);\r\n  border-radius: 8rpx;\r\n  border: 1px solid #f5f5f5;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n.content-box .info-map .bg, .content-box .info-map .mk {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n.content-box .info-map .mk {\r\n  z-index: -1;\r\n  background-image: linear-gradient(45deg, #fff, rgba(255, 255, 255, 0.6));\r\n}\r\n.content-box .info-item {\r\n  width: calc(100% - 60rpx);\r\n  padding: 30rpx;\r\n  font-weight: 700;\r\n}\r\n.info-item .info-item-tit {\r\n  color: #999;\r\n  font-size: 24rpx;\r\n  margin-bottom: 8rpx;\r\n}\r\n.info-item .info-item-tit text {\r\n  color: #000;\r\n}\r\n.info-item .adds-box {\r\n  width: 44rpx;\r\n  height: 44rpx;\r\n  justify-content: center;\r\n  border-radius: 22rpx 22rpx 4rpx;\r\n  box-shadow: 5px 5px 5px -4px rgba(0, 0, 0, 0.1);\r\n  background: #000;\r\n  transform: rotate(45deg);\r\n}\r\n.info-item .adds-box image {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n  transform: rotate(-45deg);\r\n}\r\n.info-item .cu-img-group {\r\n  margin-left: 16rpx;\r\n  direction: ltr;\r\n  unicode-bidi: bidi-override;\r\n  display: inline-block;\r\n}\r\n.cu-img-group .img-group {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  display: inline-flex;\r\n  position: relative;\r\n  margin-left: -16rpx;\r\n  border: 2rpx solid #f8f8f8;\r\n  background: #eee;\r\n  vertical-align: middle;\r\n  border-radius: 8rpx;\r\n  border-radius: 50%;\r\n}\r\n.cu-img-group .img-group image {\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 8rpx;\r\n  border-radius: 50%;\r\n}\r\n.cu-img-group .img-tit {\r\n  display: inline-flex;\r\n  margin-left: 8rpx;\r\n  color: #999;\r\n  font-size: 20rpx;\r\n}\r\n.content-box .info-title {\r\n  margin: 30rpx 0;\r\n  width: 100%;\r\n  font-size: 32rpx;\r\n  font-weight: 700;\r\n}\r\n.content-box .info-intro {\r\n  width: 100%;\r\n  color: #333;\r\n  font-size: 26rpx;\r\n  font-weight: 400;\r\n  word-break: break-word;\r\n  white-space: pre-line;\r\n}\r\n.footer-box {\r\n  position: fixed;\r\n  z-index: 99;\r\n  left: 0;\r\n  bottom: 0;\r\n  width: calc(100% - 60rpx);\r\n  padding: 30rpx;\r\n  border-top: 1px solid #f8f8f8;\r\n  padding-bottom: max(env(safe-area-inset-bottom), 30rpx);\r\n}\r\n.btn-box {\r\n  width: 100%;\r\n  justify-content: space-between;\r\n}\r\n.btn-box .btn-price {\r\n  height: 100rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n}\r\n.btn-price .nian {\r\n  margin-right: 6rpx;\r\n  font-size: 20rpx;\r\n  font-weight: 700;\r\n}\r\n.btn-price .through {\r\n  color: #999;\r\n  font-size: 20rpx;\r\n  text-decoration: line-through;\r\n}\r\n.btn-box .btn-item {\r\n  padding: 0 60rpx;\r\n  height: 100rpx;\r\n  font-size: 24rpx;\r\n  color: #fff;\r\n  font-weight: 700;\r\n  background: #000;\r\n  border-radius: 100rpx;\r\n}\r\n.btn-box .btn-means {\r\n  padding: 0 30rpx;\r\n  height: 90rpx;\r\n  font-size: 20rpx;\r\n  font-weight: 700;\r\n  background: #f5f5f5;\r\n  border-radius: 100rpx;\r\n}\r\n.btn-box .btn-means image {\r\n  margin-right: 12rpx;\r\n  width: 20rpx;\r\n  height: 20rpx;\r\n}\r\n.btn-box .btn-item .icon {\r\n  margin-right: 12rpx;\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n}\r\n.popup-box {\r\n  width: calc(100% - 60rpx);\r\n  padding: 30rpx;\r\n  background: #fff;\r\n  border-radius: 30rpx 30rpx 0 0;\r\n  padding-bottom: max(env(safe-area-inset-bottom), 60rpx);\r\n  position: relative;\r\n}\r\n.popup-box .popup-top {\r\n  width: 100%;\r\n  justify-content: space-between;\r\n}\r\n.popup-top .popup-title .t1 {\r\n  font-size: 38rpx;\r\n  font-weight: 700;\r\n}\r\n.popup-top .popup-title .t2 {\r\n  color: #999;\r\n  font-size: 20rpx;\r\n  font-weight: 300;\r\n}\r\n.popup-top .popup-close {\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  border-radius: 50%;\r\n  background: #f8f8f8;\r\n  justify-content: center;\r\n  transform: rotate(45deg);\r\n}\r\n.popup-box .popup-btn {\r\n  margin: 60rpx 0 30rpx;\r\n  width: 100%;\r\n  height: 100rpx;\r\n  font-size: 24rpx;\r\n  color: #fff;\r\n  font-weight: 700;\r\n  background: #000;\r\n  border-radius: 100rpx;\r\n  justify-content: center;\r\n}\r\n.popup-box .popup-btn image {\r\n  margin-right: 12rpx;\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n}\r\n.scroll-box {\r\n  width: 100%;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n}\r\n.popup-box .product-box {\r\n  width: 100%;\r\n  padding: 46rpx 0 30rpx;\r\n  display: flex;\r\n}\r\n.product-box .product-item {\r\n  flex-shrink: 0;\r\n  margin-right: 20rpx;\r\n  padding: 36rpx 20rpx 20rpx;\r\n  border-radius: 8rpx;\r\n  background: #f8f8f8;\r\n  border: 1px solid #f8f8f8;\r\n  position: relative;\r\n}\r\n.product-box .active {\r\n  background: #fff;\r\n  border: 1px solid #000;\r\n}\r\n.product-item .tag {\r\n  position: absolute;\r\n  top: -16rpx;\r\n  left: -1px;\r\n  padding: 0 16rpx;\r\n  height: 32rpx;\r\n  line-height: 32rpx;\r\n  text-align: center;\r\n  font-size: 18rpx;\r\n  font-weight: 500;\r\n  color: #fff;\r\n  background: linear-gradient(to right, #000, #555);\r\n  border-radius: 24rpx 32rpx 32rpx 0rpx;\r\n}\r\n.product-item .name {\r\n  font-size: 24rpx;\r\n  line-height: 24rpx;\r\n  font-weight: 500;\r\n}\r\n.product-item .time {\r\n  margin: 15rpx 0;\r\n  font-weight: 300;\r\n  font-size: 20rpx;\r\n  line-height: 20rpx;\r\n}\r\n.product-item .td-lt {\r\n  margin-left: 15rpx;\r\n  color: #999;\r\n  font-size: 20rpx;\r\n  line-height: 20rpx;\r\n  text-decoration: line-through;\r\n}\r\n.popup-box .quantity-box {\r\n  padding: 30rpx 0;\r\n  width: 100%;\r\n  justify-content: space-between;\r\n  border-top: 1px solid #f8f8f8;\r\n}\r\n.quantity-box .quantity-tit {\r\n  font-size: 24rpx;\r\n}\r\n.quantity-box .quantity-item {\r\n  height: 64rpx;\r\n  line-height: 64rpx;\r\n  border-radius: 32rpx;\r\n  border: 1px solid #f5f5f5;\r\n  font-size: 24rpx;\r\n  font-weight: 700;\r\n  text-align: center;\r\n}\r\n.quantity-item input {\r\n  width: 48rpx;\r\n  height: 64rpx;\r\n  line-height: 64rpx;\r\n  color: #000;\r\n}\r\n.quantity-item .quantity-btn {\r\n  width: 64rpx;\r\n  height: 64rpx;\r\n  line-height: 64rpx;\r\n}\r\n.share-popup {\r\n  background: #fff;\r\n  border-radius: 30rpx;\r\n  padding: 30rpx;\r\n  overflow: hidden;\r\n}\r\n.share-popup .share-img {\r\n  width: 473rpx;\r\n  height: 237.5rpx;\r\n  background: #f8f8f8;\r\n  border-radius: 8rpx;\r\n  display: block;\r\n}\r\n.share-popup .share-tips {\r\n  margin: 30rpx 0;\r\n  width: 473rpx;\r\n  font-size: 26rpx;\r\n  line-height: 48rpx;\r\n  position: relative;\r\n}\r\n.share-popup .share-tips image {\r\n  position: absolute;\r\n  top: 0;\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  margin: 0 15rpx;\r\n}\r\n.share-popup .share-btn {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  text-align: center;\r\n  font-size: 24rpx;\r\n  font-weight: 700;\r\n  color: #fff;\r\n  background: #000;\r\n  border-radius: 16rpx;\r\n}\r\n.note-box {\r\n  padding: 15rpx;\r\n  background: #fff;\r\n  border-radius: 30rpx;\r\n}\r\n.note-box .note-add {\r\n  margin: 30rpx;\r\n  width: 400rpx;\r\n  height: 90rpx;\r\n  font-size: 24rpx;\r\n  font-weight: 700;\r\n  color: #fff;\r\n  background: #000;\r\n  border-radius: 45rpx;\r\n  justify-content: center;\r\n}\r\n.note-box .note-add image {\r\n  margin-right: 10rpx;\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n.empty-box {\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n}\r\n.empty-box image {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n.empty-box .e1 {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 10rpx;\r\n}\r\n.empty-box .e2 {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n.tips-box {\r\n  padding: 20rpx 30rpx;\r\n  border-radius: 12rpx;\r\n  justify-content: center;\r\n}\r\n.tips-box .tips-item {\r\n  color: #fff;\r\n  font-size: 28rpx;\r\n  font-weight: 700;\r\n}\r\n.xwb {\r\n  filter: brightness(0);\r\n}\r\n.df {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.bfw {\r\n  background: #fff;\r\n}\r\n.bUp {\r\n  box-shadow: 0 -2px 5px 0 rgba(0, 0, 0, 0.05);\r\n}\r\n.ohto {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n.effect {\r\n  transition: all 0.3s ease-in-out;\r\n}\r\n</style> ", "import MiniProgramPage from 'Z:/WWW/shejiao/vue3/pages/activity/details.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAoNA,MAAA,cAAA,MAAA;AACA,MAAA,YAAA,MAAA;AACA,MAAA,QAAA,MAAA;AACA,MAAA,YAAA,MAAA;AACA,MAAA,SAAA,MAAA;AAEA,MAAA,MAAA,OAAA;AAEA,MAAA,YAAA;AAAA;;IAGI;AAAA;IAEA;AAAA;;EAGF,OAAA;AACE,WAAA;AAAA;;;;;MAME,SAAA,CAAA,MAAA,IAAA;AAAA,MACA,QAAA;AAAA,MACA,WAAA;AAAA;;QAGE,MAAA,CAAA;AAAA;;QAGA,eAAA;AAAA,QACA,WAAA;AAAA,QACA,YAAA;AAAA,QACA,YAAA;AAAA;;QAGA,QAAA;AAAA,QACA,QAAA;AAAA,QACA,SAAA,CAAA;AAAA;;UAGE,YAAA;AAAA,UACA,OAAA;AAAA;;;MAIJ,YAAA;AAAA;;MAGA,MAAA,CAAA;AAAA,MACA,MAAA;AAAA;MAEA,YAAA;AAAA;MAEA,aAAA;AAAA;;EAGJ,MAAA,OAAA,QAAA;AAEE,QAAA;AAEE,UAAA,OAAAA,cAAAA,MAAA,kBAAA,YAAA;AACEA,sBAAA,MAAA,cAAA;AAAA,MACF;AAAA,IAEF,SAAA,GAAA;;IAEA;AAEA,UAAA,KAAA;;AAGE,WAAA,aAAA,KAAA,OAAA;;AAEA,UAAA,OAAA,OAAA;AACE,aAAA,YAAA;AAAA,MACF;AACA,WAAA,aAAAA,oBAAA,eAAA,UAAA,EAAA,UAAA;AAAA;AAEA,WAAA,YAAA,oBAAA,IAAA;AAAA,IACF;AAAA;EAEF,SAAA;AACE,UAAA,WAAAA,cAAAA,MAAA,eAAA,UAAA;;AAEE,WAAA,SAAA;AAAA,IACF;AAAA;EAEF,SAAA;AAAA,IACE,kBAAA;AAEE,iBAAA,MAAA;AAEE,cAAA,eAAA;AAAA;;;;UAKE,eAAA;AAAA,UACA,WAAA;AAAA,UACA,YAAA;AAAA,UACA,YAAA;AAAA;;;UAIA,QAAA;AAAA,UACA,KAAA;AAAA,UACA,KAAA;AAAA,UACA,aAAA,CAAA,0BAAA,0BAAA,wBAAA;AAAA,UACA,SAAA;AAAA,YACE;AAAA,cACE,IAAA;AAAA;;cAGA,YAAA;AAAA,cACA,OAAA;AAAA;YAEF;AAAA,cACE,IAAA;AAAA;;cAGA,YAAA;AAAA,cACA,OAAA;AAAA,YACF;AAAA,UACF;AAAA;AAGF,aAAA,eAAA;AAEA,YAAA,KAAA,WAAA;AACE,eAAA,WAAA,IAAA;AAAA,QACF;AAEA,aAAA,mBAAA,SAAA;AAAA,MACF,GAAA,GAAA;AAAA;IAGF,mBAAA;;;AAGE,WAAA,UAAA;;AAIA,iBAAA,MAAA;AACE,YAAA,KAAA,QAAA,GAAA;;YAEI;AAAA,cACE,IAAA;AAAA,cACA,KAAA;AAAA;gBAEE,IAAA;AAAA;gBAEA,QAAA;AAAA;cAEF,OAAA;AAAA,cACA,SAAA;AAAA,cACA,MAAA,CAAA,wBAAA;AAAA;cAEA,eAAA;AAAA;;YAGF;AAAA,cACE,IAAA;AAAA,cACA,KAAA;AAAA;gBAEE,IAAA;AAAA;gBAEA,QAAA;AAAA;cAEF,OAAA;AAAA,cACA,SAAA;AAAA;;cAGA,eAAA;AAAA;YAEF;AAAA;AAEF,eAAA,UAAA;AAAA,QACF,WAAA,KAAA,QAAA,GAAA;AACE,eAAA,KAAA,KAAA;AAAA,YACE,IAAA;AAAA,YACA,KAAA;AAAA;cAEE,IAAA;AAAA;cAEA,QAAA;AAAA;YAEF,OAAA;AAAA,YACA,SAAA;AAAA,YACA,MAAA,CAAA,wBAAA;AAAA;YAEA,eAAA;AAAA;UAEF,CAAA;AAAA;;QAIF;;AAGE,eAAA,UAAA;AAAA,QACF;;MAGF,GAAA,GAAA;AAAA;;;AAOAA,oBAAAA,MAAA,YAAA;AAAA,QACE,OAAA;AAAA,MACF,CAAA;AAEA,iBAAA,MAAA;AACEA,sBAAA,MAAA,YAAA;AAGAA,sBAAAA,MAAA,UAAA;AAAA;UAEE,SAAA,WAAA,KAAA,aAAA,QAAA,KAAA,UAAA,EAAA,QAAA,KAAA,WAAA;AAAA,UACA,SAAA,SAAA,KAAA;AACE,gBAAA,IAAA,SAAA;AACE,kBAAA,WAAA,eAAA;AACA,mBAAA,MAAA,cAAA;AACA,mBAAA,YAAA,gBAAA;AAEA,yBAAA,WAAA;AACEA,8BAAAA,MAAA,WAAA;AAAA,kBACE,KAAA;AAAA,gBACF,CAAA;AAAA,cACF,GAAA,GAAA;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAA;AAAA,MACF,GAAA,GAAA;AAAA;;AAIA,WAAA,SAAA,EAAA,cAAA,QAAA;AACA,UAAA,KAAA,UAAA,GAAA;;;;MAIA;AAAA;IAGF,YAAA,GAAA;AACE,UAAA,KAAA,KAAA,KAAA,WAAA,KAAA,aAAA,QAAA,KAAA,UAAA,EAAA,OAAA;AACE,aAAA,WAAA,SAAA,KAAA,QAAA,IAAA;AAAA,MACF,WAAA,KAAA,KAAA,KAAA,YAAA,GAAA;AACE,aAAA,WAAA,SAAA,KAAA,QAAA,IAAA;AAAA,MACF,WAAA,KAAA,KAAA,KAAA,YAAA,GAAA;AACE,aAAA,WAAA;AAAA,MACF,WAAA,KAAA,KAAA,KAAA,WAAA,KAAA,aAAA,QAAA,KAAA,UAAA,EAAA,OAAA;AACE,aAAA,WAAA,KAAA,aAAA,QAAA,KAAA,UAAA,EAAA;AAAA,MACF;AAAA;IAGF,YAAA,GAAA;AACE,UAAA,IAAA,EAAA,cAAA,QAAA;AACAA,oBAAAA,MAAA,aAAA;AAAA,QACE,SAAA;AAAA;MAEF,CAAA;AAAA;IAGF,aAAA,GAAA;AACE,WAAA,YAAA,EAAA,OAAA;AAAA;IAGF,oBAAA;AAEEA,oBAAAA,MAAA,aAAA;AAAA,QACE,UAAA,WAAA,KAAA,aAAA,GAAA;AAAA,QACA,WAAA,WAAA,KAAA,aAAA,GAAA;AAAA,QACA,MAAA,KAAA,aAAA;AAAA,MACF,CAAA;AAAA;IAGF,mBAAA;;;IAIA,eAAA,MAAA;;AAEIA,sBAAAA,MAAA,WAAA;AAAA,UACE,KAAA,0BAAA,wCAEK,YAAA,KAAA,aAAA,OACA,WAAA,KAAA,aAAA,KAAA,CAAA;AAAA,QACP,CAAA;AAAA;AAEA,aAAA,YAAA,iBAAA;AAAA,MACF;AAAA;IAGF,cAAA,MAAA;AACE,UAAA,CAAA,MAAA;AACE,aAAA,MAAA,cAAA;AACA;AAAA,MACF;;AAGA,UAAA,KAAA,aAAA,UAAA,GAAA;;MAEA;AAEA,WAAA,MAAA,cAAA;;IAGF,WAAA,MAAA;AACE,UAAA,CAAA,MAAA;;;;MAIA;AAAA;;AAIA,WAAA,KAAA,EAAA,GAAA,EAAA,UAAA,EAAA;AACA,WAAA,KAAA,EAAA,GAAA,EAAA,aAAA,EAAA;AAAA;IAGF,gBAAA;AACE,YAAA,QAAA,KAAA,aAAA;AACA,YAAA,WAAA;AACA,YAAA,YAAA,CAAA;AAEA,UAAA;AACA,cAAA,QAAA,SAAA,KAAA,KAAA,OAAA,MAAA;;MAEA;AAEA,UAAA,UAAA,QAAA;AACEA,sBAAAA,MAAA,aAAA;AAAA,UACE,SAAA;AAAA;QAEF,CAAA;AAAA,MACF;AAAA;IAGF,cAAA,GAAA;AACE,UAAA,MAAA,EAAA,cAAA,QAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA,YAAA;AAAA,MACF,CAAA;AAAA;IAGF,UAAA;AACE,UAAA,gBAAA,EAAA,SAAA,GAAA;AACEA,sBAAA,MAAA,aAAA;AAAA;AAEAA,sBAAAA,MAAA,UAAA;AAAA;QAEA,CAAA;AAAA,MACF;AAAA;IAGF,YAAA,OAAA,aAAA,OAAA;AACE,WAAA,YAAA;;AAGA,iBAAA,MAAA;;AAEE,YAAA,YAAA;AACE,eAAA,QAAA;AAAA,QACF;AAAA,MACF,GAAA,GAAA;AAAA;IAGF,mBAAA,YAAA;;QAEI;AAAA;;;UAIE,YAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA,IACF;AAAA;EAEF,gBAAA;;AAEI,WAAA,OAAA,KAAA,OAAA;;IAEF;AAAA;EAEF,aAAA,GAAA;;;;AAKE,QAAA,WAAA,GAAA;AACE,mBAAA;AAAA,IACF;;AAGA,SAAA,mBAAA,UAAA;AAAA;EAEF,oBAAA;AACE,WAAA;AAAA;MAEE,UAAA,KAAA,aAAA,KAAA,CAAA;AAAA,MACA,MAAA,gCAAA,KAAA,aAAA,KAAA;AAAA;;EAGJ,kBAAA;AACE,WAAA;AAAA;MAEE,UAAA,KAAA,aAAA,KAAA,CAAA;AAAA,MACA,OAAA,QAAA,KAAA,aAAA;AAAA;EAEJ;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpnBA,GAAG,WAAW,eAAe;"}