{"version": 3, "file": "index.js", "sources": ["pages/setting/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc2V0dGluZy9pbmRleC52dWU"], "sourcesContent": ["<template>\n  <view class=\"container\">\n<!--    <navbar :bg=\"1\"></navbar>\n    <view class=\"title-box\" :style=\"{'margin-top': statusBarHeight + titleBarHeight + 'px'}\">\n      <view>设置 ⚙️</view>\n    </view> -->\n    \n    <!-- 用户信息区域 -->\n    <view class=\"table\">个人信息</view>\n    <view class=\"list-box\">\n      <button class=\"list df\" data-url=\"center/means\" @tap=\"navigateToFun\">\n        <image class=\"icon\" src=\"/static/img/setting/1.png\"></image>\n        <view class=\"list-item df bb1\">\n          <view class=\"title\">个人资料</view>\n          <image src=\"/static/img/x.png\"></image>\n        </view>\n      </button>\n      \n      <button class=\"list df\">\n        <image class=\"icon\" src=\"/static/img/setting/2.png\"></image>\n        <view class=\"list-item df bb1\">\n          <view class=\"title\">手机号码</view>\n          <!-- #ifdef MP -->\n          <button class=\"input-btn\" open-type=\"getPhoneNumber\" @getphonenumber=\"getphonenumber\" v-if=\"!userInfo.phone\">\n            点击绑定手机号 <text class=\"iconfont icon-xiangyou\"></text>\n          </button>\n          <!-- #endif -->\n          <!-- #ifndef MP -->\n          <navigator url=\"/pages/users/user_phone/index\" hover-class=\"none\" class=\"input-btn\" v-if=\"!userInfo.phone\">\n            点击绑定手机号<text class=\"iconfont icon-xiangyou\"></text>\n          </navigator>\n          <!-- #endif -->\n          <view class=\"input acea-row row-between-wrapper\" v-else>\n            <view class=\"\"></view>\n            <view class=\"acea-row row-middle\">\n              <text>{{userInfo.phone}}</text>\n              <text class=\"iconfont icon-suozi\"></text>\n            </view>\n          </view>\n        </view>\n      </button>\n        \n      <!-- #ifdef H5 -->\n      <button class=\"list df\" data-url=\"users/user_pwd_edit/index\" @tap=\"navigateToFun\" v-if=\"userInfo.phone && !isWeixinEnv()\">\n        <image class=\"icon\" src=\"/static/img/setting/4.png\"></image>\n        <view class=\"list-item df bb1\">\n          <view class=\"title\">密码</view>\n          <image src=\"/static/img/x.png\"></image>\n        </view>\n      </button>\n      <!-- #endif -->\n      \n      <!-- #ifdef APP-PLUS -->\n      <button class=\"list df\" data-url=\"users/user_pwd_edit/index\" @tap=\"navigateToFun\" v-if=\"userInfo.phone\">\n        <image class=\"icon\" src=\"/static/img/setting/4.png\"></image>\n        <view class=\"list-item df bb1\">\n          <view class=\"title\">密码</view>\n          <image src=\"/static/img/x.png\"></image>\n        </view>\n      </button>\n      <!-- #endif -->\n      \n      <button class=\"list df\" data-url=\"users/user_phone/index?type=1\" @tap=\"navigateToFun\" v-if=\"userInfo.phone\">\n        <image class=\"icon\" src=\"/static/img/setting/5.png\"></image>\n        <view class=\"list-item df\">\n          <view class=\"title\">更换手机号码</view>\n          <image src=\"/static/img/x.png\"></image>\n        </view>\n      </button>\n    </view>\n    \n    <!-- 账号切换区域 -->\n    <view class=\"wrapper\" v-if=\"switchUserInfo.length > 1\">\n      <view class='title'>账号切换</view>\n      <view class='wrapList'>\n        <view class='item' :class='index==userIndex?\"on\":\"\"' v-for=\"(item,index) in switchUserInfo\" :key='index' @tap=\"switchAccounts(index)\">\n          <view class='picTxt acea-row row-between-wrapper'>\n            <view class='pictrue acea-row row-center-wrapper'>\n              <image :src='item.avatar' mode=\"\"></image>\n            </view>\n            <view class='text'>\n              <view class='name line1'>{{item.nickname}}</view>\n              <view class='phone'>{{item.phone}}</view>\n            </view>\n          </view>\n          <view class='bnt acea-row row-center-wrapper' v-if='index!=userIndex'>切换</view>\n          <view class='currentBnt acea-row row-center-wrapper' v-else>当前</view>\n        </view>\n      </view>\n    </view>\n    \n    <view class=\"table\">账号管理</view>\n    <view class=\"list-box\">\n      <button class=\"list df\" data-url=\"center/address\" @tap=\"navigateToFun\">\n        <image class=\"icon\" src=\"/static/img/setting/102.png\"></image>\n        <view class=\"list-item df bb1\">\n          <view class=\"title\">收货地址</view>\n          <image src=\"/static/img/x.png\"></image>\n        </view>\n      </button>\n      \n      <button class=\"list df\" data-url=\"setting/realname\" @tap=\"navigateToFun\">\n        <image class=\"icon\" src=\"/static/img/setting/8.png\"></image>\n        <view class=\"list-item df bb1\">\n          <view class=\"title\">实名认证</view>\n          <image src=\"/static/img/x.png\"></image>\n        </view>\n      </button>\n      \n      <button class=\"list df\" data-url=\"center/invoice\" @tap=\"navigateToFun\" v-if=\"userInfo.invioce_func\">\n        <image class=\"icon\" src=\"/static/img/setting/103.png\"></image>\n        <view class=\"list-item df bb1\">\n          <view class=\"title\">发票管理</view>\n          <image src=\"/static/img/x.png\"></image>\n        </view>\n      </button>\n      \n      <button class=\"list df\" data-url=\"setting/logout\" @tap=\"navigateToFun\">\n        <image class=\"icon\" src=\"/static/img/setting/104.png\"></image>\n        <view class=\"list-item df\">\n          <view class=\"title\">注销账号</view>\n          <image src=\"/static/img/x.png\"></image>\n        </view>\n      </button>\n    </view>\n    \n    <view class=\"table\">通用</view>\n    <view class=\"list-box\">\n      <button class=\"list df\" open-type=\"contact\">\n        <image class=\"icon\" src=\"/static/img/setting/1.png\"></image>\n        <view class=\"list-item df bb1\">\n          <view class=\"title\">在线客服</view>\n          <image src=\"/static/img/x.png\"></image>\n        </view>\n      </button>\n      \n      <button class=\"list df\" data-url=\"setting/privacy\" @tap=\"navigateToFun\">\n        <image class=\"icon\" src=\"/static/img/setting/101.png\"></image>\n        <view class=\"list-item df bb1\">\n          <view class=\"title\">隐私与显示设置</view>\n          <image src=\"/static/img/x.png\"></image>\n        </view>\n      </button>\n      \n      <!-- #ifdef APP-PLUS -->\n      <view class=\"list df\" @click=\"initData\">\n        <image class=\"icon\" src=\"/static/img/setting/2.png\"></image>\n        <view class=\"list-item df bb1\">\n          <view class=\"title\">缓存大小</view>\n          <view class=\"value\">{{fileSizeString}}</view>\n          <image src=\"/static/img/x.png\"></image>\n        </view>\n      </view>\n      \n      <view class=\"list df\" @click=\"updateApp\">\n        <image class=\"icon\" src=\"/static/img/setting/3.png\"></image>\n        <view class=\"list-item df bb1\">\n          <view class=\"title\">当前版本</view>\n          <view class=\"value\">{{version}}</view>\n          <image src=\"/static/img/x.png\"></image>\n        </view>\n      </view>\n      <!-- #endif -->\n      \n    </view>\n    \n    <view class=\"table\">关于</view>\n    <view class=\"list-box\">\n      <button class=\"list df\" data-url=\"setting/xinxuan?type=3\" @tap=\"navigateToFun\">\n        <image class=\"icon\" src=\"/static/img/setting/6.png\"></image>\n        <view class=\"list-item df bb1\">\n          <view class=\"title\">隐私政策</view>\n          <image src=\"/static/img/x.png\"></image>\n        </view>\n      </button>\n      \n      <button class=\"list df\" data-url=\"setting/xinxuan?type=4\" @tap=\"navigateToFun\">\n        <image class=\"icon\" src=\"/static/img/setting/7.png\"></image>\n        <view class=\"list-item df bb1\">\n          <view class=\"title\">用户协议</view>\n          <image src=\"/static/img/x.png\"></image>\n        </view>\n      </button>\n      \n    </view>\n    \n    <!-- 退出登录按钮 -->\n    <view class=\"logout-section\">\n      <button class=\"logout-btn\" @click=\"outLogin\">\n        <image class=\"logout-icon\" src=\"/static/img/setting/5.png\"></image>\n        <text class=\"logout-text\">退出登录</text>\n      </button>\n    </view>\n    \n    <!-- #ifdef APP-PLUS -->\n    <app-update ref=\"appUpdate\" :force=\"true\" :tabbar=\"false\" :getVer='true' @isNew=\"isNew\"></app-update>\n    <!-- #endif -->\n  </view>\n</template>\n\n<script>\nimport navbar from '@/components/navbar/navbar.vue'\nimport {\n  getUserInfo,\n  getLogout,\n  mpBindingPhone\n} from '@/api/user.js'\nimport {\n  switchH5Login,\n  siteConfig\n} from '@/api/api.js';\nimport {\n  toLogin\n} from '@/libs/login.js';\nimport {\n  mapGetters\n} from \"vuex\";\n// #ifdef MP\nimport Routine from '@/libs/routine';\n// #endif\n// #ifdef H5\nimport wechat from '@/libs/wechat.js';\n// #endif\nimport Cache from '@/utils/cache';\nimport colors from '@/mixins/color.js';\n// #ifdef APP-PLUS\nimport appUpdate from \"@/components/update/app-update.vue\";\n// #endif\n\nconst app = getApp()\n\nexport default {\n  components: {\n    navbar,\n    // #ifdef APP-PLUS\n    appUpdate\n    // #endif\n  },\n  mixins: [colors],\n  // #ifdef H5\n  beforeCreate() {\n    this.$wechat = wechat;\n  },\n  // #endif\n  data() {\n    return {\n      statusBarHeight: 0,\n      titleBarHeight: 0,\n      appBq: [],\n      userInfo: {}, // 用户信息\n      fileSizeString: '', // 缓存大小\n      version: '', // 版本号\n      loginType: 'h5', // 登录类型\n      userIndex: 0, // 当前账号索引\n      switchUserInfo: [], // 账号切换列表\n      \n    }\n  },\n  computed: mapGetters(['isLogin']),\n  watch: {\n    isLogin: {\n      handler: function(newV, oldV) {\n        if (newV) {\n          this.getUserInfo();\n        }\n      },\n      deep: true\n    }\n  },\n  onLoad() {\n    let that = this\n    \n    // 初始化状态栏高度\n    // #ifdef MP-WEIXIN || MP-QQ || MP-TOUTIAO\n    that.statusBarHeight = app.globalData?.statusBarHeight || uni.getSystemInfoSync().statusBarHeight || 20\n    that.titleBarHeight = app.globalData?.titleBarHeight || 44\n    // #endif\n    \n    // #ifdef H5 || APP-PLUS\n    that.statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 20\n    that.titleBarHeight = 44\n    // #endif\n    \n    // 检查登录状态\n    if (this.isLogin) {\n      // 尝试从API获取数据\n      that.getConfigData()\n      that.getUserInfo()\n      \n      // #ifdef APP-PLUS\n      that.formatSize()\n      // 获取版本号\n      plus.runtime.getProperty(plus.runtime.appid, (inf) => {\n        that.version = inf.version;\n      });\n      // #endif \n    } else {\n      toLogin();\n    }\n  },\n  onUnload() {\n    // 页面卸载\n  },\n  onShow() {\n    // 页面显示时可以重新获取用户信息\n  },\n  methods: {\n    // 获取用户信息 (参考用户页面)\n    getUserInfo: function() {\n      let that = this;\n      \n      // 检查登录状态\n      if (!this.isLogin) {\n        console.log('用户未登录');\n        that.showToast('请先登录');\n        return;\n      }\n      \n      // 检查token\n      const token = this.$store.state.app.token;\n      console.log('当前token:', token);\n      \n      if (!token) {\n        console.log('没有token');\n        that.showToast('登录已过期，请重新登录');\n        return;\n      }\n      \n      getUserInfo().then(res => {\n        console.log('getUserInfo success:', res);\n        that.$set(that, 'userInfo', res.data);\n        \n        // 处理账号切换相关数据\n        let switchUserInfo = res.data.switchUserInfo || [];\n        for (let i = 0; i < switchUserInfo.length; i++) {\n          if (switchUserInfo[i].uid == that.userInfo.uid) that.userIndex = i;\n          // 切割h5用户；user_type状态：h5、routine（小程序）、wechat（公众号）；注：只有h5未注册手机号时，h5才可和小程序或是公众号数据想通；\n          //#ifdef H5\n          if (\n            !that.$wechat.isWeixin() &&\n            switchUserInfo[i].user_type != \"h5\" &&\n            switchUserInfo[i].phone === \"\"\n          )\n            switchUserInfo.splice(i, 1);\n          //#endif\n        }\n        that.$set(that, \"switchUserInfo\", switchUserInfo);\n      }).catch(err => {\n        console.error('获取用户信息失败:', err);\n        // 显示具体的错误信息\n        that.showToast('获取用户信息失败: ' + (err.msg || err.message || '网络错误'));\n      });\n    },\n    \n    // 获取配置数据\n    getConfigData() {\n      let that = this\n      \n      // 使用导入的siteConfig API获取配置\n      siteConfig().then(res => {\n        if (res.code == 200) {\n          if (res.data.app_xcx) {\n            app.globalData.appXx = res.data.app_xcx\n          }\n          if (res.data.app_bq) {\n            app.globalData.appBq = res.data.app_bq\n            that.appBq = res.data.app_bq\n            that.appBq[4] = that.appBq[4] ? that.appBq[4] : \"https://example.com\"\n          }\n          if (res.data.upload_type) {\n            app.globalData.uploadType = res.data.upload_type\n          }\n        } else {\n          console.warn('获取配置数据失败，使用默认配置')\n        }\n      }).catch((err) => {\n        console.warn('获取配置数据失败:', err)\n      })\n    },\n    \n    \n    // 账号切换功能\n    switchAccounts: function(index) {\n      let userInfo = this.switchUserInfo[index],\n          that = this;\n      that.userIndex = index;\n      if (that.switchUserInfo.length <= 1) return true;\n      if (userInfo === undefined) return that.showToast('切换的账号不存在');\n      \n      if (userInfo.user_type === 'h5') {\n        uni.showLoading({\n          title: '正在切换中'\n        });\n        // 使用switchH5Login API进行账号切换\n        switchH5Login().then(res => {\n          uni.hideLoading();\n          that.$store.commit(\"LOGIN\", {\n            'token': res.data.token,\n            'time': Cache.strTotime(res.data.expires_time) - Cache.time()\n          });\n          that.getUserInfo();\n        }).catch(err => {\n          uni.hideLoading();\n          return that.showToast(err);\n        })\n      } else {\n        that.$store.commit(\"LOGOUT\");\n        uni.showLoading({\n          title: '正在切换中'\n        });\n        toLogin();\n      }\n    },\n    \n    // 导航到指定页面\n    navigateToFun(e) {\n      let url = \"/pages/\" + e.currentTarget.dataset.url\n      uni.navigateTo({\n        url: url\n      })\n    },\n    \n    // 退出登录\n    outLogin: function() {\n      let that = this;\n      uni.showModal({\n        title: '提示',\n        content: '确认退出登录？',\n        success: function(res) {\n          if (res.confirm) {\n            getLogout()\n              .then(res => {\n                that.$store.commit(\"LOGOUT\");\n                uni.reLaunch({\n                  url: '/pages/index/index'\n                })\n              })\n              .catch(err => {\n                // 即使接口失败也要清除本地状态\n                that.$store.commit(\"LOGOUT\");\n                uni.reLaunch({\n                  url: '/pages/index/index'\n                })\n              });\n          }\n        }\n      });\n    },\n    \n    // #ifdef APP-PLUS\n    // 获取缓存大小\n    formatSize() {\n      let that = this;\n      plus.cache.calculate(function(size) {\n        let sizeCache = parseInt(size);\n        if (sizeCache == 0) {\n          that.fileSizeString = \"0B\";\n        } else if (sizeCache < 1024) {\n          that.fileSizeString = sizeCache + \"B\";\n        } else if (sizeCache < 1048576) {\n          that.fileSizeString = (sizeCache / 1024).toFixed(2) + \"KB\";\n        } else if (sizeCache < 1073741824) {\n          that.fileSizeString = (sizeCache / 1048576).toFixed(2) + \"MB\";\n        } else {\n          that.fileSizeString = (sizeCache / 1073741824).toFixed(2) + \"GB\";\n        }\n      });\n    },\n    \n    // 清理缓存\n    initData() {\n      uni.showModal({\n        title: '清除缓存',\n        content: '确定清楚本地缓存数据吗？',\n        success: (res) => {\n          if (res.confirm) {\n            this.clearCache()\n            this.formatSize()\n          }\n        }\n      });\n    },\n    \n    // 执行清理缓存\n    clearCache() {\n      let that = this;\n      let os = plus.os.name;\n      if (os == 'Android') {\n        let main = plus.android.runtimeMainActivity();\n        let sdRoot = main.getCacheDir();\n        let files = plus.android.invoke(sdRoot, \"listFiles\");\n        let len = files.length;\n        for (let i = 0; i < len; i++) {\n          let filePath = '' + files[i];\n          plus.io.resolveLocalFileSystemURL(filePath, function(entry) {\n            if (entry.isDirectory) {\n              entry.removeRecursively(function(entry) {\n                uni.showToast({\n                  title: '缓存清理完成',\n                  duration: 2000\n                });\n                that.formatSize();\n              }, function(e) {\n                console.log(e.message)\n              });\n            } else {\n              entry.remove();\n            }\n          }, function(e) {});\n        }\n      } else {\n        plus.cache.clear(function() {\n          uni.showToast({\n            title: '缓存清理完成',\n            duration: 2000\n          });\n          that.formatSize();\n        });\n      }\n    },\n    \n    // 检查更新\n    updateApp() {\n      // #ifdef APP-PLUS\n      console.log('updateApp 方法被调用 - APP环境');\n      console.log('appUpdate 组件引用:', this.$refs.appUpdate);\n\n      if (!this.$refs.appUpdate) {\n        console.error('appUpdate 组件引用不存在');\n        uni.showToast({\n          title: '更新组件未找到',\n          icon: 'none'\n        });\n        return;\n      }\n\n      if (typeof this.$refs.appUpdate.update !== 'function') {\n        console.error('appUpdate.update 方法不存在');\n        console.log('appUpdate 组件方法列表:', Object.keys(this.$refs.appUpdate));\n        uni.showToast({\n          title: '更新方法不存在',\n          icon: 'none'\n        });\n        return;\n      }\n\n      try {\n        this.$refs.appUpdate.update();\n        console.log('调用 appUpdate.update() 成功');\n      } catch (error) {\n        console.error('调用 appUpdate.update() 失败:', error);\n        uni.showToast({\n          title: '更新检查失败: ' + error.message,\n          icon: 'none'\n        });\n      }\n      // #endif\n\n      // #ifndef APP-PLUS\n      console.log('updateApp 方法被调用 - 非APP环境');\n      uni.showToast({\n        title: '仅在APP中支持版本更新',\n        icon: 'none'\n      });\n      // #endif\n    },\n    \n    // 检查是否为最新版本的回调\n    isNew() {\n      this.showToast({\n        title: '当前为最新版本'\n      });\n    },\n    // #endif\n    \n    // 微信小程序获取手机号\n    getphonenumber(e) {\n      if (e.detail.errMsg == 'getPhoneNumber:ok') {\n        Routine.getCode()\n          .then(code => {\n            let data = {\n              code,\n              iv: e.detail.iv,\n              encryptedData: e.detail.encryptedData,\n            }\n            mpBindingPhone(data).then(res => {\n              this.getUserInfo()\n              this.showToast({\n                title: res.msg,\n                icon: 'success'\n              });\n            }).catch(err => {\n              return this.showToast(err);\n            })\n          })\n          .catch(error => {\n            uni.hideLoading();\n          });\n      }\n    },\n    \n    // #ifdef H5\n    isWeixinEnv() {\n      return this.$wechat && this.$wechat.isWeixin();\n    },\n    // #endif\n    \n    // #ifndef H5\n    isWeixinEnv() {\n      return false;\n    },\n    // #endif\n    \n    // 通用提示函数\n    showToast(options) {\n      if (typeof options === 'string') {\n        uni.showToast({\n          title: options,\n          icon: 'none',\n          duration: 2000\n        });\n      } else {\n        const { title, icon = 'none', success } = options;\n        uni.showToast({\n          title: title,\n          icon: icon,\n          duration: 2000,\n          success: success\n        });\n      }\n    }\n  }\n}\n</script>\n\n<style>\npage {\n  background: #f8f8f8;\n  padding: 0 30rpx;\n  box-sizing: border-box;\n  overflow-x: hidden; \n}\n\n.title-box {\n\tpadding: 20rpx 0;\n\tfont-size: 40rpx;\n\tfont-weight: bold;\n}\n\n.table {\n\tpadding: 30rpx 0;\n\tcolor: #999;\n\tfont-size: 24rpx;\n\tfont-weight: 500;\n}\n\n.list-box {\n  width: 100%;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-sizing: border-box; \n}\n\n.list-box .list {\n\tmargin: 0;\n\tpadding: 0;\n\twidth: 100%;\n\tbackground: #fff!important;\n\tborder-radius: 0;\n}\n\n.list-box .list:last-child {\n\tborder-bottom: none;\n}\n\n.list-box .list .icon {\n\tmargin: 0 30rpx;\n\twidth: 38rpx;\n\theight: 38rpx;\n}\n\n.list-box .list-item {\n\twidth: calc(100% - 98rpx);\n\tpadding: 30rpx 30rpx 30rpx 0;\n\tjustify-content: space-between;\n}\n\n.list-box .list-item .title {\n\tfont-size: 24rpx;\n\tfont-weight: 500;\n\tmin-width: 120rpx;\n}\n\n.list-box .list-item image {\n\twidth: 24rpx;\n\theight: 24rpx;\n\ttransform: rotate(-90deg);\n}\n\n/* 按钮样式 */\n.input-btn {\n\tbackground: transparent !important;\n\tborder: none !important;\n\tcolor: #868686 !important;\n\tfont-size: 24rpx !important;\n\ttext-align: right;\n\tpadding: 0 !important;\n\tmargin: 0 !important;\n\tline-height: normal !important;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: flex-end;\n}\n\n.input-btn::after {\n\tborder: none !important;\n}\n\n/* 数值显示样式 */\n.list-box .list-item .value {\n\tcolor: #868686;\n\tfont-size: 24rpx;\n\tmargin-right: 10rpx;\n}\n\n/* 退出登录按钮样式 */\n.logout-section {\n  margin-top: 30rpx;\n}\n\n.logout-btn {\n  background: linear-gradient(135deg, #ff6b6b, #ff5252);\n  border: none;\n  padding: 0;\n  margin: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 690rpx;\n  height: 90rpx;\n  border-radius: 45rpx;\n  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);\n  transition: all 0.3s ease;\n}\n\n.logout-btn:active {\n  transform: translateY(2rpx);\n  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.4);\n}\n\n.logout-btn::after {\n  border: none;\n}\n\n.logout-icon {\n  width: 32rpx;\n  height: 32rpx;\n  margin-right: 10rpx;\n  filter: brightness(0) invert(1);\n}\n\n.logout-text {\n  font-size: 32rpx;\n  color: #fff;\n  font-weight: 500;\n}\n\n.version {\n\tpadding: 60rpx 0;\n\tflex-direction: column;\n\tjustify-content: center;\n}\n\n.version image {\n\tmargin-right: 10rpx;\n\twidth: 20rpx;\n\theight: 20rpx;\n}\n\n.version text {\n\tcolor: #999;\n\tfont-size: 18rpx;\n}\n\n.df {\n  display: flex;\n  align-items: center;\n}\n\n.acea-row {\n  display: flex;\n  flex-direction: row;\n}\n\n.row-center-wrapper {\n  justify-content: center;\n}\n\n.row-between-wrapper {\n  justify-content: space-between;\n}\n\n.row-middle {\n  align-items: center;\n}\n\n.bb1 {\n  border-bottom: 1px solid #f8f8f8;\n}\n\n/* 图标样式 */\n.iconfont {\n\tfont-size: 24rpx;\n\tcolor: #ccc;\n}\n\n.icon-xiangyou::before {\n\tcontent: '>';\n}\n\n.icon-suozi::before {\n\tcontent: '🔒';\n}\n\n/* 列表项间距调整 */\n.list-box .list:not(:last-child) .list-item {\n\tborder-bottom: 1px solid #f8f8f8;\n}\n\n.list-box .list:last-child .list-item {\n\tborder-bottom: none;\n}\n\n/* 账号切换样式 */\n.wrapper {\n  margin: 10rpx 0;\n  background-color: #fff;\n  padding: 36rpx 30rpx 13rpx 30rpx;\n  border-radius: 16rpx;\n}\n\n.wrapper .title {\n  margin-bottom: 30rpx;\n  font-size: 32rpx;\n  color: #282828;\n  font-weight: bold;\n}\n\n.wrapper .wrapList .item {\n  width: 690rpx;\n  height: 160rpx;\n  background-color: #f8f8f8;\n  border-radius: 20rpx;\n  margin-bottom: 22rpx;\n  padding: 0 30rpx;\n  position: relative;\n  border: 2rpx solid #f8f8f8;\n  box-sizing: border-box;\n}\n\n.wrapper .wrapList .item.on {\n  border-color: var(--view-theme);\n  border-radius: 20rpx;\n  background-color: #fff9f9;\n}\n\n.wrapper .wrapList .item .picTxt {\n  width: 445rpx;\n}\n\n.wrapper .wrapList .item .picTxt .pictrue {\n  width: 96rpx;\n  height: 96rpx;\n  position: relative;\n}\n\n.wrapper .wrapList .item .picTxt .pictrue image {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n}\n\n.wrapper .wrapList .item .picTxt .text {\n  width: 325rpx;\n}\n\n.wrapper .wrapList .item .picTxt .text .name {\n  width: 100%;\n  font-size: 30rpx;\n  color: #282828;\n}\n\n.wrapper .wrapList .item .picTxt .text .phone {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 10rpx;\n}\n\n.wrapper .wrapList .item .bnt {\n  font-size: 24rpx;\n  border-radius: 27rpx;\n  width: 140rpx;\n  height: 54rpx;\n  border: 2rpx solid var(--view-theme);\n  color: var(--view-theme);\n  background-color: #fff;\n}\n\n.wrapper .wrapList .item .currentBnt {\n  position: absolute;\n  right: 0;\n  top: 0;\n  font-size: 26rpx;\n  background-color: rgba(233, 51, 35, 0.1);\n  width: 140rpx;\n  height: 48rpx;\n  border-radius: 0 20rpx 0 20rpx;\n  color: var(--view-theme);\n}\n\n/* 输入框焦点样式 */\n.list-box .list-item .input input:focus {\n\tcolor: #333;\n}\n\n/* 禁用状态样式 */\n.list-box .list-item .input input[disabled] {\n\tcolor: #ccc;\n}\n\n/* 表单区域样式 */\nform {\n\tmargin-bottom: 20rpx;\n}\n\n/* 针对不同平台的样式适配 */\n/* #ifdef MP-WEIXIN */\nbutton {\n  background-color: transparent !important;\n}\n/* #endif */\n\n/* #ifdef H5 */\nbutton {\n  background-color: transparent;\n  border: none;\n}\n/* #endif */\n\n/* #ifdef APP-PLUS */\nbutton::after {\n  border: none;\n}\n/* #endif */\n</style> ", "import MiniProgramPage from 'Z:/WWW/shejiao/vue3/pages/setting/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["colors", "mapGetters", "uni", "<PERSON><PERSON><PERSON><PERSON>", "getUserInfo", "siteConfig", "switchH5Login", "<PERSON><PERSON>", "getLogout", "res", "Routine", "mpBindingPhone"], "mappings": ";;;;;;;;;AAyMA,eAAe,MAAW;AA4B1B,MAAM,MAAM,OAAO;AAEnB,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EAID;AAAA,EACD,QAAQ,CAACA,aAAAA,MAAM;AAAA,EAMf,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO,CAAE;AAAA,MACT,UAAU,CAAE;AAAA;AAAA,MACZ,gBAAgB;AAAA;AAAA,MAChB,SAAS;AAAA;AAAA,MACT,WAAW;AAAA;AAAA,MACX,WAAW;AAAA;AAAA,MACX,gBAAgB,CAAE;AAAA;AAAA,IAEpB;AAAA,EACD;AAAA,EACD,UAAUC,cAAAA,WAAW,CAAC,SAAS,CAAC;AAAA,EAChC,OAAO;AAAA,IACL,SAAS;AAAA,MACP,SAAS,SAAS,MAAM,MAAM;AAC5B,YAAI,MAAM;AACR,eAAK,YAAW;AAAA,QAClB;AAAA,MACD;AAAA,MACD,MAAM;AAAA,IACR;AAAA,EACD;AAAA,EACD,SAAS;;AACP,QAAI,OAAO;AAIX,SAAK,oBAAkB,SAAI,eAAJ,mBAAgB,oBAAmBC,oBAAI,oBAAoB,mBAAmB;AACrG,SAAK,mBAAiB,SAAI,eAAJ,mBAAgB,mBAAkB;AASxD,QAAI,KAAK,SAAS;AAEhB,WAAK,cAAc;AACnB,WAAK,YAAY;AAAA,WASZ;AACLC,iBAAAA;IACF;AAAA,EACD;AAAA,EACD,WAAW;AAAA,EAEV;AAAA,EACD,SAAS;AAAA,EAER;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,aAAa,WAAW;AACtB,UAAI,OAAO;AAGX,UAAI,CAAC,KAAK,SAAS;AACjBD,sBAAAA,MAAA,MAAA,OAAA,kCAAY,OAAO;AACnB,aAAK,UAAU,MAAM;AACrB;AAAA,MACF;AAGA,YAAM,QAAQ,KAAK,OAAO,MAAM,IAAI;AACpCA,yEAAY,YAAY,KAAK;AAE7B,UAAI,CAAC,OAAO;AACVA,sBAAAA,MAAY,MAAA,OAAA,kCAAA,SAAS;AACrB,aAAK,UAAU,aAAa;AAC5B;AAAA,MACF;AAEAE,2BAAa,EAAC,KAAK,SAAO;AACxBF,sBAAA,MAAA,MAAA,OAAA,kCAAY,wBAAwB,GAAG;AACvC,aAAK,KAAK,MAAM,YAAY,IAAI,IAAI;AAGpC,YAAI,iBAAiB,IAAI,KAAK,kBAAkB,CAAA;AAChD,iBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,cAAI,eAAe,CAAC,EAAE,OAAO,KAAK,SAAS;AAAK,iBAAK,YAAY;AAAA,QAUnE;AACA,aAAK,KAAK,MAAM,kBAAkB,cAAc;AAAA,MAClD,CAAC,EAAE,MAAM,SAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,kCAAc,aAAa,GAAG;AAE9B,aAAK,UAAU,gBAAgB,IAAI,OAAO,IAAI,WAAW,OAAO;AAAA,MAClE,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI,OAAO;AAGXG,yBAAY,EAAC,KAAK,SAAO;AACvB,YAAI,IAAI,QAAQ,KAAK;AACnB,cAAI,IAAI,KAAK,SAAS;AACpB,gBAAI,WAAW,QAAQ,IAAI,KAAK;AAAA,UAClC;AACA,cAAI,IAAI,KAAK,QAAQ;AACnB,gBAAI,WAAW,QAAQ,IAAI,KAAK;AAChC,iBAAK,QAAQ,IAAI,KAAK;AACtB,iBAAK,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;AAAA,UAClD;AACA,cAAI,IAAI,KAAK,aAAa;AACxB,gBAAI,WAAW,aAAa,IAAI,KAAK;AAAA,UACvC;AAAA,eACK;AACLH,wBAAAA,MAAa,MAAA,QAAA,kCAAA,iBAAiB;AAAA,QAChC;AAAA,MACF,CAAC,EAAE,MAAM,CAAC,QAAQ;AAChBA,sBAAAA,MAAA,MAAA,QAAA,kCAAa,aAAa,GAAG;AAAA,OAC9B;AAAA,IACF;AAAA;AAAA,IAID,gBAAgB,SAAS,OAAO;AAC9B,UAAI,WAAW,KAAK,eAAe,KAAK,GACpC,OAAO;AACX,WAAK,YAAY;AACjB,UAAI,KAAK,eAAe,UAAU;AAAG,eAAO;AAC5C,UAAI,aAAa;AAAW,eAAO,KAAK,UAAU,UAAU;AAE5D,UAAI,SAAS,cAAc,MAAM;AAC/BA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,QACT,CAAC;AAEDI,8BAAe,EAAC,KAAK,SAAO;AAC1BJ,wBAAG,MAAC,YAAW;AACf,eAAK,OAAO,OAAO,SAAS;AAAA,YAC1B,SAAS,IAAI,KAAK;AAAA,YAClB,QAAQK,YAAAA,MAAM,UAAU,IAAI,KAAK,YAAY,IAAIA,YAAK,MAAC,KAAK;AAAA,UAC9D,CAAC;AACD,eAAK,YAAW;AAAA,QAClB,CAAC,EAAE,MAAM,SAAO;AACdL,wBAAG,MAAC,YAAW;AACf,iBAAO,KAAK,UAAU,GAAG;AAAA,SAC1B;AAAA,aACI;AACL,aAAK,OAAO,OAAO,QAAQ;AAC3BA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,QACT,CAAC;AACDC,mBAAAA;MACF;AAAA,IACD;AAAA;AAAA,IAGD,cAAc,GAAG;AACf,UAAI,MAAM,YAAY,EAAE,cAAc,QAAQ;AAC9CD,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,OACD;AAAA,IACF;AAAA;AAAA,IAGD,UAAU,WAAW;AACnB,UAAI,OAAO;AACXA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,SAAS,KAAK;AACrB,cAAI,IAAI,SAAS;AACfM,+BAAU,EACP,KAAK,CAAAC,SAAO;AACX,mBAAK,OAAO,OAAO,QAAQ;AAC3BP,4BAAAA,MAAI,SAAS;AAAA,gBACX,KAAK;AAAA,eACN;AAAA,aACF,EACA,MAAM,SAAO;AAEZ,mBAAK,OAAO,OAAO,QAAQ;AAC3BA,4BAAAA,MAAI,SAAS;AAAA,gBACX,KAAK;AAAA,eACN;AAAA,YACH,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAiID,eAAe,GAAG;AAChB,UAAI,EAAE,OAAO,UAAU,qBAAqB;AAC1CQ,qBAAAA,QAAQ,QAAQ,EACb,KAAK,UAAQ;AACZ,cAAI,OAAO;AAAA,YACT;AAAA,YACA,IAAI,EAAE,OAAO;AAAA,YACb,eAAe,EAAE,OAAO;AAAA,UAC1B;AACAC,mBAAAA,eAAe,IAAI,EAAE,KAAK,SAAO;AAC/B,iBAAK,YAAY;AACjB,iBAAK,UAAU;AAAA,cACb,OAAO,IAAI;AAAA,cACX,MAAM;AAAA,YACR,CAAC;AAAA,UACH,CAAC,EAAE,MAAM,SAAO;AACd,mBAAO,KAAK,UAAU,GAAG;AAAA,WAC1B;AAAA,SACF,EACA,MAAM,WAAS;AACdT,wBAAG,MAAC,YAAW;AAAA,QACjB,CAAC;AAAA,MACL;AAAA,IACD;AAAA,IASD,cAAc;AACZ,aAAO;AAAA,IACR;AAAA;AAAA,IAID,UAAU,SAAS;AACjB,UAAI,OAAO,YAAY,UAAU;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AAAA,aACI;AACL,cAAM,EAAE,OAAO,OAAO,QAAQ,QAAQ,IAAI;AAC1CA,sBAAAA,MAAI,UAAU;AAAA,UACZ;AAAA,UACA;AAAA,UACA,UAAU;AAAA,UACV;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvnBA,GAAG,WAAW,eAAe;"}