{"version": 3, "file": "app.js", "sources": ["stores/app.js"], "sourcesContent": ["// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport { defineStore } from 'pinia'\n\nexport const useAppStore = defineStore('app', {\n  state: () => ({\n    // 系统信息\n    systemInfo: {\n      statusBarHeight: uni.getSystemInfoSync().statusBarHeight || 20,\n      titleBarHeight: 44,\n      screenWidth: uni.getSystemInfoSync().screenWidth,\n      screenHeight: uni.getSystemInfoSync().screenHeight,\n      platform: uni.getSystemInfoSync().platform\n    },\n    \n    // 应用配置\n    config: {\n      version: '1.0.0',\n      apiBaseUrl: '',\n      cdnBaseUrl: '',\n      debug: false\n    },\n    \n    // 主题设置\n    theme: {\n      mode: 'light', // light | dark\n      primaryColor: '#007AFF',\n      backgroundColor: '#F8F8F8'\n    },\n    \n    // 网络状态\n    networkStatus: {\n      isConnected: true,\n      networkType: 'wifi'\n    },\n    \n    // 加载状态\n    loading: {\n      global: false,\n      page: false,\n      component: {}\n    },\n    \n    // 缓存管理\n    cache: {\n      timeout: 300000, // 5分钟\n      maxSize: 50, // 最大缓存条数\n      data: new Map()\n    },\n    \n    // 热搜词\n    hotWords: [],\n    \n    // 首页数据缓存\n    indexData: {\n      banner: [],\n      categories: [],\n      products: [],\n      timestamp: 0\n    },\n    \n    // 侧边栏菜单\n    sidebarMenu: [],\n    \n    // 键盘高度（用于适配）\n    keyboardHeight: 0\n  }),\n\n  getters: {\n    // 是否为暗黑模式\n    isDarkMode: (state) => state.theme.mode === 'dark',\n    \n    // 是否为移动端\n    isMobile: (state) => ['ios', 'android'].includes(state.systemInfo.platform),\n    \n    // 安全区域高度\n    safeAreaHeight: (state) => {\n      return state.systemInfo.statusBarHeight + state.systemInfo.titleBarHeight\n    },\n    \n    // 是否有网络连接\n    isOnline: (state) => state.networkStatus.isConnected,\n    \n    // 全局是否加载中\n    isGlobalLoading: (state) => state.loading.global,\n    \n    // 缓存数据大小\n    cacheSize: (state) => state.cache.data.size,\n    \n    // 是否有热搜词\n    hasHotWords: (state) => state.hotWords.length > 0,\n    \n    // 首页数据是否有效\n    isIndexDataValid: (state) => {\n      const now = Date.now()\n      return state.indexData.timestamp && \n             (now - state.indexData.timestamp < state.cache.timeout)\n    }\n  },\n\n  actions: {\n    // 更新系统信息\n    updateSystemInfo() {\n      const systemInfo = uni.getSystemInfoSync()\n      this.systemInfo = {\n        statusBarHeight: systemInfo.statusBarHeight || 20,\n        titleBarHeight: 44,\n        screenWidth: systemInfo.screenWidth,\n        screenHeight: systemInfo.screenHeight,\n        platform: systemInfo.platform\n      }\n    },\n\n    // 设置主题\n    setTheme(theme) {\n      this.theme = { ...this.theme, ...theme }\n      \n      // 同步到本地存储\n      uni.setStorageSync('app_theme', this.theme)\n    },\n\n    // 切换主题模式\n    toggleThemeMode() {\n      this.theme.mode = this.theme.mode === 'light' ? 'dark' : 'light'\n      this.setTheme(this.theme)\n    },\n\n    // 更新网络状态\n    updateNetworkStatus(status) {\n      this.networkStatus = { ...this.networkStatus, ...status }\n    },\n\n    // 设置全局加载状态\n    setGlobalLoading(loading) {\n      this.loading.global = loading\n    },\n\n    // 设置页面加载状态\n    setPageLoading(loading) {\n      this.loading.page = loading\n    },\n\n    // 设置组件加载状态\n    setComponentLoading(componentId, loading) {\n      this.loading.component[componentId] = loading\n    },\n\n    // 获取组件加载状态\n    getComponentLoading(componentId) {\n      return this.loading.component[componentId] || false\n    },\n\n    // 缓存数据\n    setCache(key, data, timeout = null) {\n      const expireTime = timeout || this.cache.timeout\n      const cacheItem = {\n        data,\n        timestamp: Date.now(),\n        expireTime\n      }\n      \n      // 检查缓存大小，超出限制则清理最旧的数据\n      if (this.cache.data.size >= this.cache.maxSize) {\n        const firstKey = this.cache.data.keys().next().value\n        this.cache.data.delete(firstKey)\n      }\n      \n      this.cache.data.set(key, cacheItem)\n    },\n\n    // 获取缓存数据\n    getCache(key) {\n      const cacheItem = this.cache.data.get(key)\n      if (!cacheItem) return null\n      \n      const now = Date.now()\n      if (now - cacheItem.timestamp > cacheItem.expireTime) {\n        this.cache.data.delete(key)\n        return null\n      }\n      \n      return cacheItem.data\n    },\n\n    // 清除缓存\n    clearCache(key = null) {\n      if (key) {\n        this.cache.data.delete(key)\n      } else {\n        this.cache.data.clear()\n      }\n    },\n\n    // 清理过期缓存\n    cleanExpiredCache() {\n      const now = Date.now()\n      for (const [key, item] of this.cache.data.entries()) {\n        if (now - item.timestamp > item.expireTime) {\n          this.cache.data.delete(key)\n        }\n      }\n    },\n\n    // 设置热搜词\n    setHotWords(words) {\n      this.hotWords = words\n    },\n\n    // 设置首页数据\n    setIndexData(data) {\n      this.indexData = {\n        ...data,\n        timestamp: Date.now()\n      }\n    },\n\n    // 设置侧边栏菜单\n    setSidebarMenu(menu) {\n      this.sidebarMenu = menu\n    },\n\n    // 设置键盘高度\n    setKeyboardHeight(height) {\n      this.keyboardHeight = height\n    },\n\n    // 从本地存储初始化\n    initFromStorage() {\n      try {\n        // 恢复主题设置\n        const theme = uni.getStorageSync('app_theme')\n        if (theme) {\n          this.theme = { ...this.theme, ...theme }\n        }\n        \n        // 恢复其他设置...\n      } catch (error) {\n        console.warn('从本地存储初始化应用状态失败:', error)\n      }\n    },\n\n    // 重置应用状态\n    reset() {\n      this.loading = {\n        global: false,\n        page: false,\n        component: {}\n      }\n      this.clearCache()\n      this.hotWords = []\n      this.indexData = {\n        banner: [],\n        categories: [],\n        products: [],\n        timestamp: 0\n      }\n      this.sidebarMenu = []\n      this.keyboardHeight = 0\n    }\n  }\n})\n"], "names": ["defineStore", "uni"], "mappings": ";;AAYY,MAAC,cAAcA,cAAW,YAAC,OAAO;AAAA,EAC5C,OAAO,OAAO;AAAA;AAAA,IAEZ,YAAY;AAAA,MACV,iBAAiBC,cAAG,MAAC,kBAAmB,EAAC,mBAAmB;AAAA,MAC5D,gBAAgB;AAAA,MAChB,aAAaA,cAAAA,MAAI,kBAAiB,EAAG;AAAA,MACrC,cAAcA,cAAAA,MAAI,kBAAiB,EAAG;AAAA,MACtC,UAAUA,cAAAA,MAAI,kBAAiB,EAAG;AAAA,IACnC;AAAA;AAAA,IAGD,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,OAAO;AAAA,IACR;AAAA;AAAA,IAGD,OAAO;AAAA,MACL,MAAM;AAAA;AAAA,MACN,cAAc;AAAA,MACd,iBAAiB;AAAA,IAClB;AAAA;AAAA,IAGD,eAAe;AAAA,MACb,aAAa;AAAA,MACb,aAAa;AAAA,IACd;AAAA;AAAA,IAGD,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,WAAW,CAAE;AAAA,IACd;AAAA;AAAA,IAGD,OAAO;AAAA,MACL,SAAS;AAAA;AAAA,MACT,SAAS;AAAA;AAAA,MACT,MAAM,oBAAI,IAAK;AAAA,IAChB;AAAA;AAAA,IAGD,UAAU,CAAE;AAAA;AAAA,IAGZ,WAAW;AAAA,MACT,QAAQ,CAAE;AAAA,MACV,YAAY,CAAE;AAAA,MACd,UAAU,CAAE;AAAA,MACZ,WAAW;AAAA,IACZ;AAAA;AAAA,IAGD,aAAa,CAAE;AAAA;AAAA,IAGf,gBAAgB;AAAA,EACpB;AAAA,EAEE,SAAS;AAAA;AAAA,IAEP,YAAY,CAAC,UAAU,MAAM,MAAM,SAAS;AAAA;AAAA,IAG5C,UAAU,CAAC,UAAU,CAAC,OAAO,SAAS,EAAE,SAAS,MAAM,WAAW,QAAQ;AAAA;AAAA,IAG1E,gBAAgB,CAAC,UAAU;AACzB,aAAO,MAAM,WAAW,kBAAkB,MAAM,WAAW;AAAA,IAC5D;AAAA;AAAA,IAGD,UAAU,CAAC,UAAU,MAAM,cAAc;AAAA;AAAA,IAGzC,iBAAiB,CAAC,UAAU,MAAM,QAAQ;AAAA;AAAA,IAG1C,WAAW,CAAC,UAAU,MAAM,MAAM,KAAK;AAAA;AAAA,IAGvC,aAAa,CAAC,UAAU,MAAM,SAAS,SAAS;AAAA;AAAA,IAGhD,kBAAkB,CAAC,UAAU;AAC3B,YAAM,MAAM,KAAK,IAAK;AACtB,aAAO,MAAM,UAAU,aACf,MAAM,MAAM,UAAU,YAAY,MAAM,MAAM;AAAA,IACvD;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,mBAAmB;AACjB,YAAM,aAAaA,cAAG,MAAC,kBAAmB;AAC1C,WAAK,aAAa;AAAA,QAChB,iBAAiB,WAAW,mBAAmB;AAAA,QAC/C,gBAAgB;AAAA,QAChB,aAAa,WAAW;AAAA,QACxB,cAAc,WAAW;AAAA,QACzB,UAAU,WAAW;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,SAAS,OAAO;AACd,WAAK,QAAQ,EAAE,GAAG,KAAK,OAAO,GAAG,MAAO;AAGxCA,oBAAAA,MAAI,eAAe,aAAa,KAAK,KAAK;AAAA,IAC3C;AAAA;AAAA,IAGD,kBAAkB;AAChB,WAAK,MAAM,OAAO,KAAK,MAAM,SAAS,UAAU,SAAS;AACzD,WAAK,SAAS,KAAK,KAAK;AAAA,IACzB;AAAA;AAAA,IAGD,oBAAoB,QAAQ;AAC1B,WAAK,gBAAgB,EAAE,GAAG,KAAK,eAAe,GAAG,OAAQ;AAAA,IAC1D;AAAA;AAAA,IAGD,iBAAiB,SAAS;AACxB,WAAK,QAAQ,SAAS;AAAA,IACvB;AAAA;AAAA,IAGD,eAAe,SAAS;AACtB,WAAK,QAAQ,OAAO;AAAA,IACrB;AAAA;AAAA,IAGD,oBAAoB,aAAa,SAAS;AACxC,WAAK,QAAQ,UAAU,WAAW,IAAI;AAAA,IACvC;AAAA;AAAA,IAGD,oBAAoB,aAAa;AAC/B,aAAO,KAAK,QAAQ,UAAU,WAAW,KAAK;AAAA,IAC/C;AAAA;AAAA,IAGD,SAAS,KAAK,MAAM,UAAU,MAAM;AAClC,YAAM,aAAa,WAAW,KAAK,MAAM;AACzC,YAAM,YAAY;AAAA,QAChB;AAAA,QACA,WAAW,KAAK,IAAK;AAAA,QACrB;AAAA,MACD;AAGD,UAAI,KAAK,MAAM,KAAK,QAAQ,KAAK,MAAM,SAAS;AAC9C,cAAM,WAAW,KAAK,MAAM,KAAK,KAAM,EAAC,KAAI,EAAG;AAC/C,aAAK,MAAM,KAAK,OAAO,QAAQ;AAAA,MAChC;AAED,WAAK,MAAM,KAAK,IAAI,KAAK,SAAS;AAAA,IACnC;AAAA;AAAA,IAGD,SAAS,KAAK;AACZ,YAAM,YAAY,KAAK,MAAM,KAAK,IAAI,GAAG;AACzC,UAAI,CAAC;AAAW,eAAO;AAEvB,YAAM,MAAM,KAAK,IAAK;AACtB,UAAI,MAAM,UAAU,YAAY,UAAU,YAAY;AACpD,aAAK,MAAM,KAAK,OAAO,GAAG;AAC1B,eAAO;AAAA,MACR;AAED,aAAO,UAAU;AAAA,IAClB;AAAA;AAAA,IAGD,WAAW,MAAM,MAAM;AACrB,UAAI,KAAK;AACP,aAAK,MAAM,KAAK,OAAO,GAAG;AAAA,MAClC,OAAa;AACL,aAAK,MAAM,KAAK,MAAO;AAAA,MACxB;AAAA,IACF;AAAA;AAAA,IAGD,oBAAoB;AAClB,YAAM,MAAM,KAAK,IAAK;AACtB,iBAAW,CAAC,KAAK,IAAI,KAAK,KAAK,MAAM,KAAK,WAAW;AACnD,YAAI,MAAM,KAAK,YAAY,KAAK,YAAY;AAC1C,eAAK,MAAM,KAAK,OAAO,GAAG;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAGD,YAAY,OAAO;AACjB,WAAK,WAAW;AAAA,IACjB;AAAA;AAAA,IAGD,aAAa,MAAM;AACjB,WAAK,YAAY;AAAA,QACf,GAAG;AAAA,QACH,WAAW,KAAK,IAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,IAGD,eAAe,MAAM;AACnB,WAAK,cAAc;AAAA,IACpB;AAAA;AAAA,IAGD,kBAAkB,QAAQ;AACxB,WAAK,iBAAiB;AAAA,IACvB;AAAA;AAAA,IAGD,kBAAkB;AAChB,UAAI;AAEF,cAAM,QAAQA,cAAAA,MAAI,eAAe,WAAW;AAC5C,YAAI,OAAO;AACT,eAAK,QAAQ,EAAE,GAAG,KAAK,OAAO,GAAG,MAAO;AAAA,QACzC;AAAA,MAGF,SAAQ,OAAO;AACdA,sBAAAA,MAAa,MAAA,QAAA,wBAAA,mBAAmB,KAAK;AAAA,MACtC;AAAA,IACF;AAAA;AAAA,IAGD,QAAQ;AACN,WAAK,UAAU;AAAA,QACb,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,WAAW,CAAE;AAAA,MACd;AACD,WAAK,WAAY;AACjB,WAAK,WAAW,CAAE;AAClB,WAAK,YAAY;AAAA,QACf,QAAQ,CAAE;AAAA,QACV,YAAY,CAAE;AAAA,QACd,UAAU,CAAE;AAAA,QACZ,WAAW;AAAA,MACZ;AACD,WAAK,cAAc,CAAE;AACrB,WAAK,iBAAiB;AAAA,IACvB;AAAA,EACF;AACH,CAAC;;"}