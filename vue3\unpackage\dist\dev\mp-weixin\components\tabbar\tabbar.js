"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  __name: "tabbar",
  props: {
    currentPage: {
      type: Number,
      default: 0
    },
    currentMsg: {
      type: Number,
      default: 0
    }
  },
  emits: ["refresh"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const appXx = common_vendor.ref({ 3: "下面选择您要发布的类型" });
    const isAdd = common_vendor.ref(false);
    const isAddBtn = common_vendor.ref(false);
    const isAddBg = common_vendor.ref(false);
    const isLoggedIn = common_vendor.ref(false);
    const loginCheckTimer = common_vendor.ref(null);
    const cartBadgeCount = common_vendor.ref(0);
    const showCartBadge = common_vendor.ref(false);
    const lastClickTime = common_vendor.ref(0);
    const activeStates = common_vendor.reactive({
      tabbarItem: false,
      card: false,
      twoColumnCard: false
    });
    const tabBar = common_vendor.reactive([
      {
        selectedIconPath: "/static/img/tabbar/1.png",
        iconPath: "/static/img/tabbar/11.png",
        pagePath: "/pages/index/index"
      },
      {
        selectedIconPath: "/static/img/tabbar/2.png",
        iconPath: "/static/img/tabbar/22.png",
        pagePath: "/pages/index/dynamic"
      },
      {
        selectedIconPath: "/static/img/tabbar/3.png",
        iconPath: "/static/img/tabbar/33.png"
      },
      {
        selectedIconPath: "/static/img/tabbar/4.png",
        iconPath: "/static/img/tabbar/44.png",
        pagePath: "/pages/index/message"
      },
      {
        selectedIconPath: "/static/img/tabbar/5.png",
        iconPath: "/static/img/tabbar/55.png",
        pagePath: "/pages/index/center"
      }
    ]);
    const currentAppXx = common_vendor.computed(() => {
      return appXx.value[3] || "下面选择您要发布的类型";
    });
    const getGlobalData = (key, defaultValue) => {
      try {
        if (typeof getApp !== "function")
          return defaultValue;
        const app = getApp();
        if (!app || !app.globalData)
          return defaultValue;
        return app.globalData[key] !== void 0 ? app.globalData[key] : defaultValue;
      } catch (e) {
        common_vendor.index.__f__("warn", "at components/tabbar/tabbar.vue:218", "获取全局数据失败:", e);
        return defaultValue;
      }
    };
    const handleAdd = () => {
      setBarColor("#000000");
      isAddBg.value = true;
      isAddBtn.value = false;
      setTimeout(() => {
        isAdd.value = false;
        isAddBg.value = false;
      }, 300);
    };
    const handleTouchStart = (type) => {
      activeStates[type] = true;
    };
    const handleTouchEnd = (type) => {
      setTimeout(() => {
        activeStates[type] = false;
      }, 150);
    };
    const navigateToPage = (targetUrl) => {
      return new Promise((resolve, reject) => {
        common_vendor.index.reLaunch({
          url: targetUrl,
          success: resolve,
          fail: (err) => {
            common_vendor.index.redirectTo({
              url: targetUrl,
              success: resolve,
              fail: (redirectErr) => {
                common_vendor.index.navigateTo({
                  url: targetUrl,
                  success: resolve,
                  fail: reject
                });
              }
            });
          }
        });
      });
    };
    const toTabbar = async (index) => {
      const now = Date.now();
      if (now - lastClickTime.value < 100)
        return;
      lastClickTime.value = now;
      appXx.value = getGlobalData("appXx", { 3: "下面选择您要发布的类型" });
      if (index != props.currentPage && index != 2) {
        const targetUrl = tabBar[index].pagePath;
        try {
          await navigateToPage(targetUrl);
          common_vendor.index.__f__("log", "at components/tabbar/tabbar.vue:285", "页面切换成功:", targetUrl);
        } catch (err) {
          common_vendor.index.__f__("error", "at components/tabbar/tabbar.vue:287", "页面跳转失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败，请重试",
            icon: "none",
            duration: 2e3
          });
        }
      } else if (index == 2 && isAdd.value) {
        handleAdd();
      } else if (index == 2 && !isAdd.value) {
        isAdd.value = true;
        isAddBtn.value = true;
        setBarColor("#ffffff");
      } else if (index == props.currentPage && (index == 0 || index == 1)) {
        common_vendor.index.__f__("log", "at components/tabbar/tabbar.vue:304", "点击当前页面，触发刷新");
        emit("refresh");
      }
    };
    const toAdd = (type) => {
      common_vendor.index.__f__("log", "at components/tabbar/tabbar.vue:310", "toAdd 被调用，类型:", type);
      setBarColor("#000000");
      if (type === 5) {
        common_vendor.index.__f__("log", "at components/tabbar/tabbar.vue:315", "准备跳转到圈子创建页面");
        common_vendor.index.navigateTo({
          url: "/pages/center/circle-create",
          success: () => {
            common_vendor.index.__f__("log", "at components/tabbar/tabbar.vue:319", "圈子创建页面跳转成功");
            isAdd.value = false;
            isAddBtn.value = false;
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at components/tabbar/tabbar.vue:324", "navigateTo失败:", err);
            common_vendor.index.showToast({
              title: "功能开发中",
              icon: "none"
            });
            isAdd.value = false;
            isAddBtn.value = false;
          }
        });
      } else {
        common_vendor.index.__f__("log", "at components/tabbar/tabbar.vue:336", "准备跳转到笔记发布页面，类型:", type);
        common_vendor.index.navigateTo({
          url: "/pages/note/add?type=" + type,
          success: () => {
            common_vendor.index.__f__("log", "at components/tabbar/tabbar.vue:340", "笔记发布页面跳转成功");
            isAdd.value = false;
            isAddBtn.value = false;
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at components/tabbar/tabbar.vue:345", "跳转到笔记发布页面失败:", err);
            common_vendor.index.showToast({
              title: "页面跳转失败",
              icon: "none"
            });
            isAdd.value = false;
            isAddBtn.value = false;
          }
        });
      }
    };
    const setBarColor = (color) => {
      common_vendor.index.setNavigationBarColor({
        frontColor: color,
        backgroundColor: "#ffffff",
        animation: {
          duration: 300,
          timingFunc: "easeIn"
        }
      });
    };
    const checkGlobalLoginStatus = () => {
      try {
        const token = common_vendor.index.getStorageSync("token");
        const userInfo = common_vendor.index.getStorageSync("userInfo");
        const currentLoginStatus = !!(token && userInfo && userInfo.uid);
        if (isLoggedIn.value !== currentLoginStatus) {
          isLoggedIn.value = currentLoginStatus;
          if (common_vendor.index.$emit) {
            common_vendor.index.$emit("loginStateChanged", currentLoginStatus);
          }
        }
      } catch (error) {
        common_vendor.index.__f__("warn", "at components/tabbar/tabbar.vue:389", "检查登录状态失败:", error);
      }
    };
    const handleLoginStateChanged = (loginStatus) => {
      isLoggedIn.value = !!loginStatus;
    };
    const handleCartBadgeUpdate = (data) => {
      if (data && typeof data === "object") {
        cartBadgeCount.value = data.count || 0;
        showCartBadge.value = data.show || false;
      }
    };
    common_vendor.onMounted(() => {
      checkGlobalLoginStatus();
      if (common_vendor.index.$on) {
        common_vendor.index.$on("loginStateChanged", handleLoginStateChanged);
        common_vendor.index.$on("updateCartBadge", handleCartBadgeUpdate);
      }
      loginCheckTimer.value = setInterval(() => {
        checkGlobalLoginStatus();
      }, 6e4);
    });
    common_vendor.onBeforeUnmount(() => {
      if (loginCheckTimer.value) {
        clearInterval(loginCheckTimer.value);
        loginCheckTimer.value = null;
      }
      if (common_vendor.index.$off) {
        common_vendor.index.$off("loginStateChanged", handleLoginStateChanged);
        common_vendor.index.$off("updateCartBadge", handleCartBadgeUpdate);
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: isAdd.value
      }, isAdd.value ? {
        b: common_vendor.t(currentAppXx.value),
        c: common_assets._imports_0$21,
        d: common_assets._imports_1$7,
        e: common_assets._imports_2$14,
        f: common_vendor.n({
          "active-state": activeStates.card
        }),
        g: common_vendor.o(($event) => toAdd(1)),
        h: common_vendor.o(($event) => handleTouchStart("card")),
        i: common_vendor.o(($event) => handleTouchEnd("card")),
        j: common_assets._imports_3$7,
        k: common_vendor.n({
          "active-state": activeStates.twoColumnCard
        }),
        l: common_vendor.o(($event) => toAdd(2)),
        m: common_vendor.o(($event) => handleTouchStart("twoColumnCard")),
        n: common_vendor.o(($event) => handleTouchEnd("twoColumnCard")),
        o: common_assets._imports_2$5,
        p: common_vendor.n({
          "active-state": activeStates.twoColumnCard
        }),
        q: common_vendor.o(($event) => toAdd(3)),
        r: common_vendor.o(($event) => handleTouchStart("twoColumnCard")),
        s: common_vendor.o(($event) => handleTouchEnd("twoColumnCard")),
        t: common_assets._imports_5$8,
        v: common_assets._imports_2$14,
        w: common_vendor.o(($event) => toAdd(5)),
        x: common_vendor.o(() => {
        }),
        y: common_vendor.o(() => {
        }),
        z: !isAddBg.value ? 1 : "",
        A: isAddBg.value ? 1 : "",
        B: common_vendor.o(() => {
        }),
        C: common_vendor.o(handleAdd)
      } : {}, {
        D: !isAddBtn.value
      }, !isAddBtn.value ? {
        E: 0 === __props.currentPage ? tabBar[0].selectedIconPath : tabBar[0].iconPath,
        F: common_vendor.n({
          "active-state": activeStates.tabbarItem
        }),
        G: common_vendor.o(($event) => toTabbar(0)),
        H: common_vendor.o(($event) => handleTouchStart("tabbarItem")),
        I: common_vendor.o(($event) => handleTouchEnd("tabbarItem"))
      } : {}, {
        J: !isAddBtn.value
      }, !isAddBtn.value ? common_vendor.e({
        K: 1 === __props.currentPage ? tabBar[1].selectedIconPath : tabBar[1].iconPath,
        L: showCartBadge.value && cartBadgeCount.value > 0
      }, showCartBadge.value && cartBadgeCount.value > 0 ? {
        M: common_vendor.t(cartBadgeCount.value > 99 ? "99+" : cartBadgeCount.value),
        N: cartBadgeCount.value > 9 ? "0 10rpx" : "0"
      } : {}, {
        O: common_vendor.n({
          "active-state": activeStates.tabbarItem
        }),
        P: common_vendor.o(($event) => toTabbar(1)),
        Q: common_vendor.o(($event) => handleTouchStart("tabbarItem")),
        R: common_vendor.o(($event) => handleTouchEnd("tabbarItem"))
      }) : {}, {
        S: isAddBtn.value ? tabBar[2].selectedIconPath : tabBar[2].iconPath,
        T: isAddBtn.value ? "#fff" : "#000",
        U: isAddBtn.value ? "rotate(225deg)" : "rotate(0deg)",
        V: common_vendor.n({
          "active-state": activeStates.tabbarItem
        }),
        W: common_vendor.o(($event) => toTabbar(2)),
        X: common_vendor.o(($event) => handleTouchStart("tabbarItem")),
        Y: common_vendor.o(($event) => handleTouchEnd("tabbarItem")),
        Z: !isAddBtn.value
      }, !isAddBtn.value ? common_vendor.e({
        aa: 3 === __props.currentPage ? tabBar[3].selectedIconPath : tabBar[3].iconPath,
        ab: __props.currentMsg > 0
      }, __props.currentMsg > 0 ? {
        ac: common_vendor.t(__props.currentMsg > 99 ? "99+" : __props.currentMsg)
      } : {}, {
        ad: common_vendor.o(($event) => toTabbar(3))
      }) : {}, {
        ae: !isAddBtn.value
      }, !isAddBtn.value ? {
        af: 4 === __props.currentPage ? tabBar[4].selectedIconPath : tabBar[4].iconPath,
        ag: common_vendor.o(($event) => toTabbar(4))
      } : {}, {
        ah: !isAddBtn.value ? 1 : ""
      });
    };
  }
};
wx.createComponent(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/tabbar/tabbar.js.map
