"use strict";
const common_vendor = require("../../../common/vendor.js");
const api_public = require("../../../api/public.js");
const api_user = require("../../../api/user.js");
const api_social = require("../../../api/social.js");
const libs_routine = require("../../../libs/routine.js");
const mixins_color = require("../../../mixins/color.js");
const config_app = require("../../../config/app.js");
require("../../../utils/index.js");
const utils_cache = require("../../../utils/cache.js");
const app = getApp();
let statusBarHeight = common_vendor.index.getSystemInfoSync().statusBarHeight + "px";
const mobileLogin = () => "../components/login_mobile/index.js";
const routinePhone = () => "../components/login_mobile/routine_phone.js";
const editUserModal = () => "../../../components/eidtUserModal/index.js";
const privacyAgreementPopup = () => "../../../components/privacyAgreementPopup/index.js";
const { HTTP_REQUEST_URL } = config_app.config;
const _sfc_main = {
  mixins: [mixins_color.colors],
  data() {
    return {
      imgHost: HTTP_REQUEST_URL,
      isUp: false,
      canClose: true,
      phone: "",
      statusBarHeight,
      isHome: false,
      isPhoneBox: false,
      protocol: false,
      isShow: false,
      isLogin: false,
      logoUrl: "",
      code: "",
      authKey: "",
      options: "",
      userInfo: {},
      codeNum: 0,
      canUseGetUserProfile: false,
      canGetPrivacySetting: false,
      inAnimation: false,
      colorStatus: common_vendor.index.getStorageSync("color_status"),
      mp_is_new: this.$Cache.get("MP_VERSION_ISNEW") || false,
      configData: utils_cache.Cache.get("BASIC_CONFIG"),
      bindPhone: false
    };
  },
  components: {
    mobileLogin,
    routinePhone,
    editUserModal,
    privacyAgreementPopup
  },
  onLoad(options) {
    if (common_vendor.index.getUserProfile) {
      this.canUseGetUserProfile = true;
    }
    if (common_vendor.wx$1.getPrivacySetting) {
      this.canGetPrivacySetting = true;
    }
    this.userLogin();
    let pages = getCurrentPages();
    let prePage = pages[pages.length - 2];
    if (prePage && prePage.route == "pages/order_addcart/order_addcart") {
      this.isHome = true;
    } else {
      this.isHome = false;
    }
  },
  methods: {
    wechatAuthLogin(d, back_url) {
      common_vendor.index.showLoading({
        title: this.$t(`正在登录中`)
      });
      api_public.wechatAuthLogin(d).then((res) => {
        common_vendor.index.hideLoading();
        if (res.data.bindPhone) {
          this.authKey = res.data.key;
          common_vendor.index.navigateTo({
            url: `/pages/users/binding_phone/index?authKey=${this.authKey}&backUrl=${back_url}`
          });
        } else {
          let time = res.data.expires_time - this.$Cache.time();
          this.$store.commit("LOGIN", {
            token: res.data.token,
            time
          });
          this.getUserInfo(0, back_url);
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: err,
          icon: "none",
          duration: 2e3
        });
      });
    },
    onAgree() {
      this.protocol = true;
    },
    // 小程序 22.11.8日删除getUserProfile 接口获取用户昵称头像
    userLogin() {
      libs_routine.Routine.getCode().then((code) => {
        api_public.authType({
          code,
          spread_spid: app.globalData.spid,
          spread_code: app.globalData.code
        }).then((res) => {
          common_vendor.index.hideLoading();
          this.authKey = res.data.key;
          this.bindPhone = res.data.bindPhone;
        }).catch((err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: err,
            icon: "none",
            duration: 2e3
          });
        });
      }).catch((err) => {
        common_vendor.index.__f__("log", "at pages/users/wechat_login/index.vue:238", err);
      });
    },
    getAuthLogin() {
      if (!this.authKey)
        return;
      if (!this.protocol) {
        common_vendor.index.showToast({
          title: this.$t("请先阅读并同意协议"),
          icon: "none",
          duration: 2e3
        });
        return;
      }
      common_vendor.index.showLoading({
        title: this.$t(`正在登录中`)
      });
      api_public.authLogin({
        key: this.authKey
      }).then((res) => {
        let time = res.data.expires_time - this.$Cache.time();
        this.$store.commit("LOGIN", {
          token: res.data.token,
          time
        });
        this.getUserInfo(res.data.bindName);
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: err,
          icon: "none",
          duration: 2e3
        });
      });
    },
    ChangeIsDefault(e) {
      this.$set(this, "protocol", !this.protocol);
    },
    editSuccess() {
      this.isShow = false;
    },
    phoneLogin() {
      common_vendor.index.navigateTo({
        url: `/pages/users/binding_phone/index?authKey=${this.authKey}&pageType=0`
      });
    },
    closeEdit() {
      this.isShow = false;
      this.$util.Tips({
        title: this.$t(`登录成功`),
        icon: "success"
      }, {
        tab: 3
      });
    },
    onReject() {
      common_vendor.index.navigateBack();
    },
    back() {
      if (this.isLogin) {
        this.$store.commit("LOGIN", {
          token: "",
          time: 0
        });
      }
      common_vendor.index.navigateBack();
    },
    home() {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    },
    // 弹窗关闭
    maskClose(new_user) {
      this.isUp = false;
      if (new_user) {
        this.isShow = true;
      }
    },
    bindPhoneClose(data) {
      this.isPhoneBox = false;
      if (data.isStatus) {
        this.getUserInfo(data.new_user);
      }
    },
    // 小程序获取手机号码
    getphonenumber(e) {
      if (!this.protocol) {
        common_vendor.index.showToast({
          title: this.$t("请先阅读并同意协议"),
          icon: "none",
          duration: 2e3
        });
        return;
      }
      common_vendor.index.showLoading({
        title: this.$t(`正在登录中`)
      });
      libs_routine.Routine.getCode().then((code) => {
        this.getUserPhoneNumber(e.detail.encryptedData, e.detail.iv, code);
      }).catch((error) => {
        common_vendor.index.$emit("closePage", false);
        common_vendor.index.hideLoading();
      });
    },
    // 小程序获取手机号码回调
    getUserPhoneNumber(encryptedData, iv, code) {
      api_public.routineBindingPhone({
        encryptedData,
        iv,
        code,
        spread_spid: app.globalData.spid,
        spread_code: app.globalData.code,
        key: this.authKey
      }).then((res) => {
        let time = res.data.expires_time - this.$Cache.time();
        this.$store.commit("LOGIN", {
          token: res.data.token,
          time
        });
        this.$Cache.clear("snsapiKey");
        this.getUserInfo(res.data.bindName);
      }).catch((res) => {
        common_vendor.index.hideLoading();
      });
    },
    /**
     * 获取个人用户信息
     */
    getUserInfo(new_user, back_url) {
      let that = this;
      api_user.getUserInfo().then((res) => {
        that.userInfo = res.data;
        that.$store.commit("SETUID", res.data.uid);
        api_social.getUserSocialInfo().then((socialRes) => {
          common_vendor.index.hideLoading();
          if (socialRes.data) {
            that.$store.commit("UPDATE_USERINFO", socialRes.data);
          }
          if (new_user) {
            this.isShow = true;
          } else {
            that.$util.Tips({
              title: that.$t(`登录成功`),
              icon: "success"
            }, {
              tab: 3
            });
          }
        }).catch(() => {
          common_vendor.index.hideLoading();
          if (new_user) {
            this.isShow = true;
          } else {
            that.$util.Tips({
              title: that.$t(`登录成功`),
              icon: "success"
            }, {
              tab: 3
            });
          }
        });
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: err.msg,
          icon: "none",
          duration: 2e3
        });
      });
    },
    privacy(type) {
      common_vendor.index.navigateTo({
        url: "/pages/users/privacy/index?type=" + type
      });
    }
  }
};
if (!Array) {
  const _component_navbar = common_vendor.resolveComponent("navbar");
  const _component_mobileLogin = common_vendor.resolveComponent("mobileLogin");
  const _component_routinePhone = common_vendor.resolveComponent("routinePhone");
  const _component_editUserModal = common_vendor.resolveComponent("editUserModal");
  const _component_template = common_vendor.resolveComponent("template");
  const _component_privacyAgreementPopup = common_vendor.resolveComponent("privacyAgreementPopup");
  (_component_navbar + _component_mobileLogin + _component_routinePhone + _component_editUserModal + _component_template + _component_privacyAgreementPopup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.configData.wap_login_logo,
    b: common_vendor.t(_ctx.$t(`商城登录`)),
    c: common_vendor.t($data.configData.site_name),
    d: $data.configData.wechat_auth_switch
  }, $data.configData.wechat_auth_switch ? common_vendor.e({
    e: $data.bindPhone
  }, $data.bindPhone ? {
    f: common_vendor.t(_ctx.$t(`授权登录`)),
    g: common_vendor.o((...args) => $options.getphonenumber && $options.getphonenumber(...args))
  } : !$data.bindPhone ? {
    i: common_vendor.t(_ctx.$t(`授权登录`)),
    j: common_vendor.o((...args) => $options.getAuthLogin && $options.getAuthLogin(...args))
  } : {}, {
    h: !$data.bindPhone
  }) : {}, {
    k: $data.configData.phone_auth_switch
  }, $data.configData.phone_auth_switch ? {
    l: common_vendor.t(_ctx.$t(`手机号登录`)),
    m: common_vendor.o((...args) => $options.phoneLogin && $options.phoneLogin(...args))
  } : {}, {
    n: !$data.canGetPrivacySetting
  }, !$data.canGetPrivacySetting ? {
    o: common_vendor.n($data.inAnimation ? "trembling" : ""),
    p: common_vendor.o(($event) => $data.inAnimation = false),
    q: $data.protocol ? true : false,
    r: common_vendor.t(_ctx.$t(`已阅读并同意`)),
    s: common_vendor.t(_ctx.$t(`《用户协议》`)),
    t: common_vendor.o(($event) => $options.privacy(4)),
    v: common_vendor.t(_ctx.$t(`与`)),
    w: common_vendor.t(_ctx.$t(`《隐私协议》`)),
    x: common_vendor.o(($event) => $options.privacy(3)),
    y: common_vendor.o((...args) => $options.ChangeIsDefault && $options.ChangeIsDefault(...args))
  } : {}, {
    z: $data.statusBarHeight + "px",
    A: $data.isUp
  }, $data.isUp ? {
    B: common_vendor.o($options.maskClose),
    C: common_vendor.o(_ctx.wechatPhone),
    D: common_vendor.p({
      isUp: $data.isUp,
      canClose: $data.canClose,
      authKey: $data.authKey
    })
  } : {}, {
    E: $data.isPhoneBox
  }, $data.isPhoneBox ? {
    F: common_vendor.o($options.bindPhoneClose),
    G: common_vendor.p({
      logoUrl: $data.logoUrl,
      isPhoneBox: $data.isPhoneBox,
      authKey: $data.authKey
    })
  } : {}, {
    H: common_vendor.o($options.closeEdit),
    I: common_vendor.o($options.editSuccess),
    J: common_vendor.p({
      isShow: $data.isShow
    }),
    K: $data.canGetPrivacySetting
  }, $data.canGetPrivacySetting ? {
    L: common_vendor.o($options.onReject),
    M: common_vendor.o($options.onAgree)
  } : {}, {
    N: common_vendor.s(_ctx.colorStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/users/wechat_login/index.js.map
