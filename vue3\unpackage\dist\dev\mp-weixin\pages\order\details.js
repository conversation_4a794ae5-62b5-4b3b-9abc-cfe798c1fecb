"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const config_api = require("../../config/api.js");
const common_assets = require("../../common/assets.js");
const money = () => "../../components/money/money.js";
const app = getApp();
const _sfc_main = {
  components: {
    money
  },
  data() {
    return {
      statusBarHeight: this.$store.state.statusBarHeight || 20,
      titleBarHeight: this.$store.state.titleBarHeight || 44,
      orderInfo: {
        id: 0,
        status_str: "状态加载中",
        address: "",
        adds_mobile: "",
        adds_name: "",
        express: "",
        express_no: "",
        order_no: "",
        goods_count: 0,
        goods_amount: "0.00",
        order_amount: "0.00",
        discount_amount: "0.00",
        express_amount: "0.00",
        pay_status: 0,
        create_time: "",
        ship_time_str: "",
        confirm_time_str: "",
        remark: "",
        status: 0,
        refund: ["", "", ""],
        user_name: "",
        user_mobile: "",
        user_adds: "",
        goods: []
      },
      refundType: 0,
      refundExpress: "",
      refundExpressNo: "",
      refundReason: "",
      tipsTitle: "",
      // Mock数据
      mockOrderDetails: {
        id: 123456,
        status_str: "待收货",
        address: "广东省广州市天河区天河路123号",
        adds_mobile: "13800138000",
        adds_name: "张三",
        express: "顺丰速运",
        express_no: "SF1234567890123",
        order_no: "202305201234567890",
        goods_count: 3,
        goods_amount: "599.00",
        order_amount: "599.00",
        discount_amount: "100.00",
        express_amount: "0.00",
        pay_status: 1,
        create_time: "2023-05-20 12:34:56",
        ship_time_str: "2023-05-21 10:24:36",
        confirm_time_str: "",
        remark: "请尽快发货，谢谢！",
        status: 3,
        refund: ["张小姐", "13912345678", "广东省广州市天河区天河路789号"],
        user_name: "张三",
        user_mobile: "13800138000",
        user_adds: "广东省广州市天河区天河路123号",
        goods: [
          {
            goods_id: 1001,
            goods_name: "2023夏季新款连衣裙",
            product_name: "白色 L码",
            product_img: "/static/img/avatar.png",
            product_price: "299.00",
            quantity: 1,
            status: 0
          },
          {
            goods_id: 1002,
            goods_name: "轻薄透气运动鞋",
            product_name: "黑色 40码",
            product_img: "/static/img/avatar.png",
            product_price: "199.00",
            quantity: 1,
            status: 0
          },
          {
            goods_id: 1003,
            goods_name: "时尚斜挎小包",
            product_name: "米色 标准",
            product_img: "/static/img/avatar.png",
            product_price: "101.00",
            quantity: 1,
            status: 0
          }
        ]
      }
    };
  },
  onLoad(options) {
    if (options.id) {
      this.orderInfo.id = options.id;
      this.orderDetails();
    } else {
      this.opTipsPopup("未找到订单，请稍后重试！", true);
    }
  },
  methods: {
    // 获取订单详情
    orderDetails() {
      let that = this;
      if (config_api.api && config_api.api.orderDetailsUrl) {
        utils_request.request(config_api.api.orderDetailsUrl, {
          id: that.orderInfo.id
        }).then(function(res) {
          if (res.code == 200) {
            that.orderInfo = res.data;
          } else {
            that.opTipsPopup(res.msg, true);
          }
        });
      } else {
        setTimeout(() => {
          that.orderInfo = that.mockOrderDetails;
        }, 500);
      }
    },
    // 支付订单
    paymentClick() {
      let that = this;
      common_vendor.index.showLoading({
        title: "唤起支付中..",
        mask: true
      });
      if (config_api.api && config_api.api.orderPaymentUrl) {
        utils_request.request(config_api.api.orderPaymentUrl, {
          id: that.orderInfo.id
        }, "POST").then(function(res) {
          common_vendor.index.hideLoading();
          let payData = res.data;
          common_vendor.index.requestPayment({
            provider: "weixin",
            timeStamp: payData.timestamp,
            nonceStr: payData.nonceStr,
            package: payData.package,
            signType: payData.signType,
            paySign: payData.paySign,
            success: function() {
              that.opTipsPopup("支付成功，我们会尽快为您发货 🎉");
              that.orderInfo.pay_status = 1;
              that.orderInfo.status = 2;
              that.orderInfo.status_str = "待发货";
            }
          });
        });
      } else {
        setTimeout(() => {
          common_vendor.index.hideLoading();
          common_vendor.index.showModal({
            title: "模拟支付",
            content: "这是一个模拟的支付流程，点击确定将模拟支付成功",
            success: function(res) {
              if (res.confirm) {
                that.opTipsPopup("支付成功，我们会尽快为您发货 🎉");
                that.orderInfo.pay_status = 1;
                that.orderInfo.status = 2;
                that.orderInfo.status_str = "待发货";
              }
            }
          });
        }, 1e3);
      }
    },
    // 编辑订单（删除、取消、确认收货）
    editClick(e) {
      let that = this;
      let type = e.currentTarget.dataset.type;
      let refund = 0;
      if (type == 1 && that.orderInfo.pay_status == 1 && that.orderInfo.status == 2) {
        refund = 1;
      }
      common_vendor.index.showLoading({
        mask: true
      });
      if (config_api.api && config_api.api.editOrderUrl) {
        utils_request.request(config_api.api.editOrderUrl, {
          id: that.orderInfo.id,
          type,
          refund
        }, "POST").then(function(res) {
          common_vendor.index.hideLoading();
          that.opTipsPopup(res.msg);
          if (res.code == 200) {
            if (type == 0) {
              app.globalData.isCenterPage = true;
              that.opTipsPopup(res.msg, true);
              return;
            }
            if (type == 1) {
              that.orderInfo.status = 0;
              that.orderInfo.status_str = "已取消";
            } else if (type == 3) {
              that.orderInfo.status = 4;
              that.orderInfo.status_str = "待评价";
            }
          }
        });
      } else {
        setTimeout(() => {
          common_vendor.index.hideLoading();
          if (type == 0) {
            app.globalData.isCenterPage = true;
            that.opTipsPopup("订单已删除", true);
            return;
          }
          if (type == 1) {
            that.orderInfo.status = 0;
            that.orderInfo.status_str = "已取消";
            that.opTipsPopup("订单已取消");
          } else if (type == 2) {
            that.orderInfo.status = 4;
            that.orderInfo.status_str = "待评价";
            that.opTipsPopup("已确认收货");
          }
        }, 800);
      }
    },
    // 提交售后申请
    refundClick() {
      let that = this;
      if (that.refundType == 1 && !that.refundExpress) {
        that.opTipsPopup("请填写寄回物流名称");
        return;
      }
      if (that.refundType == 1 && !that.refundExpressNo) {
        that.opTipsPopup("请填写寄回物流单号");
        return;
      }
      if (!that.refundReason) {
        that.opTipsPopup("请填写售后原因");
        return;
      }
      common_vendor.index.showLoading({
        mask: true
      });
      if (config_api.api && config_api.api.refundOrderUrl) {
        utils_request.request(config_api.api.refundOrderUrl, {
          id: that.orderInfo.id,
          type: that.refundType,
          express: that.refundExpress,
          express_no: that.refundExpressNo,
          reason: that.refundReason
        }, "POST").then(function(res) {
          common_vendor.index.hideLoading();
          that.opTipsPopup(res.msg);
          if (res.code == 200) {
            that.orderInfo.pay_status = 2;
            that.orderInfo.status_str = that.orderInfo.status_str + "（售后中）";
            that.$refs.refundPopup.close();
          }
        });
      } else {
        setTimeout(() => {
          common_vendor.index.hideLoading();
          that.opTipsPopup("售后申请已提交，请等待商家处理");
          that.orderInfo.pay_status = 2;
          that.orderInfo.status_str = that.orderInfo.status_str + "（售后中）";
          that.$refs.refundPopup.close();
        }, 800);
      }
    },
    // 打开/关闭售后弹窗
    orderRefundClick(isOpen) {
      if (isOpen) {
        this.$refs.refundPopup.open();
      } else {
        this.$refs.refundPopup.close();
      }
    },
    // 打开评价弹窗
    openOrderNote() {
      this.$refs.notePopup.open();
    },
    // 跳转到评价页面
    toOrderNote(type) {
      common_vendor.index.navigateTo({
        url: "/pages/note/add?type=" + type + "&oid=" + this.orderInfo.id
      });
    },
    // 催发货
    urgeDeliveryClick() {
      this.opTipsPopup("操作成功，我们会尽快为您发货 🎉");
      if (config_api.api && config_api.api.editOrderUrl) {
        utils_request.request(config_api.api.editOrderUrl, {
          id: this.orderInfo.id,
          type: 3,
          refund: 0
        }, "POST");
      }
    },
    // 页面跳转
    navigateToFun(e) {
      common_vendor.index.navigateTo({
        url: "/pages/" + e.currentTarget.dataset.url
      });
    },
    // 返回上一页
    navBack() {
      if (getCurrentPages().length > 1) {
        common_vendor.index.navigateBack();
      } else {
        common_vendor.index.switchTab({
          url: "/pages/tabbar/shop"
        });
      }
    },
    // 提示弹窗
    opTipsPopup(msg, isBack = false) {
      let that = this;
      that.tipsTitle = msg;
      that.$refs.tipsPopup.open();
      setTimeout(function() {
        that.$refs.tipsPopup.close();
        if (isBack) {
          that.navBack();
        }
      }, 2e3);
    }
  }
};
if (!Array) {
  const _component_money = common_vendor.resolveComponent("money");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_component_money + _easycom_uni_popup2)();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: common_vendor.o((...args) => $options.navBack && $options.navBack(...args)),
    c: common_vendor.t($data.orderInfo.status_str),
    d: $data.titleBarHeight + "px",
    e: $data.statusBarHeight + "px",
    f: common_assets._imports_1$9,
    g: common_vendor.t($data.orderInfo.user_name),
    h: common_vendor.t($data.orderInfo.user_mobile),
    i: common_vendor.t($data.orderInfo.user_adds),
    j: $data.orderInfo.express
  }, $data.orderInfo.express ? {
    k: common_vendor.t($data.orderInfo.express),
    l: common_vendor.t($data.orderInfo.express_no)
  } : {}, {
    m: common_vendor.f($data.orderInfo.goods, (item, index, i0) => {
      return common_vendor.e({
        a: item.product_img,
        b: common_vendor.t(item.goods_name),
        c: common_vendor.t(item.product_name),
        d: common_vendor.t(item.quantity),
        e: "b578de62-0-" + i0,
        f: common_vendor.p({
          price: item.product_price
        }),
        g: item.status == 2
      }, item.status == 2 ? {} : {}, {
        h: index,
        i: "goods/details?id=" + item.goods_id,
        j: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), index)
      });
    }),
    n: common_vendor.t($data.orderInfo.order_no),
    o: $data.orderInfo.order_no,
    p: common_vendor.t($data.orderInfo.goods_count),
    q: common_vendor.p({
      price: $data.orderInfo.goods_amount,
      cor: "#999",
      qs: "28rpx",
      ts: "18rpx"
    }),
    r: $data.orderInfo.discount_amount
  }, $data.orderInfo.discount_amount ? {
    s: common_vendor.p({
      price: $data.orderInfo.discount_amount,
      cor: "#999",
      qs: "28rpx",
      ts: "18rpx"
    })
  } : {}, {
    t: $data.orderInfo.card_price
  }, $data.orderInfo.card_price ? {
    v: common_vendor.p({
      price: $data.orderInfo.card_price,
      cor: "#999",
      qs: "28rpx",
      ts: "18rpx"
    })
  } : {}, {
    w: common_vendor.p({
      price: $data.orderInfo.express_amount,
      cor: "#999",
      qs: "28rpx",
      ts: "18rpx"
    }),
    x: common_vendor.p({
      price: $data.orderInfo.order_amount,
      qs: "28rpx",
      ts: "18rpx"
    }),
    y: common_vendor.t($data.orderInfo.create_time),
    z: $data.orderInfo.ship_time_str
  }, $data.orderInfo.ship_time_str ? {
    A: common_vendor.t($data.orderInfo.ship_time_str)
  } : {}, {
    B: $data.orderInfo.confirm_time_str
  }, $data.orderInfo.confirm_time_str ? {
    C: common_vendor.t($data.orderInfo.confirm_time_str)
  } : {}, {
    D: common_vendor.t($data.orderInfo.remark ? $data.orderInfo.remark : "无"),
    E: common_assets._imports_2$3,
    F: $data.orderInfo.order_no,
    G: "/pages/order/details?id=" + $data.orderInfo.id,
    H: $data.orderInfo.status == 0 || $data.orderInfo.status == 4 || $data.orderInfo.status == 5
  }, $data.orderInfo.status == 0 || $data.orderInfo.status == 4 || $data.orderInfo.status == 5 ? {
    I: common_vendor.o((...args) => $options.editClick && $options.editClick(...args))
  } : {}, {
    J: !($data.orderInfo.status != 1 && $data.orderInfo.status != 2 || $data.orderInfo.pay_status != 1 && $data.orderInfo.pay_status != 0)
  }, !($data.orderInfo.status != 1 && $data.orderInfo.status != 2 || $data.orderInfo.pay_status != 1 && $data.orderInfo.pay_status != 0) ? {
    K: common_vendor.o((...args) => $options.editClick && $options.editClick(...args))
  } : {}, {
    L: ($data.orderInfo.pay_status == 1 || $data.orderInfo.pay_status == 3) && $data.orderInfo.status == 3
  }, ($data.orderInfo.pay_status == 1 || $data.orderInfo.pay_status == 3) && $data.orderInfo.status == 3 ? {
    M: common_vendor.o(($event) => $options.orderRefundClick(true))
  } : {}, {
    N: $data.orderInfo.status == 2
  }, $data.orderInfo.status == 2 ? {
    O: common_vendor.o((...args) => $options.urgeDeliveryClick && $options.urgeDeliveryClick(...args))
  } : {}, {
    P: $data.orderInfo.status == 3
  }, $data.orderInfo.status == 3 ? {
    Q: common_vendor.o((...args) => $options.editClick && $options.editClick(...args))
  } : {}, {
    R: $data.orderInfo.status == 4
  }, $data.orderInfo.status == 4 ? {
    S: common_vendor.o((...args) => $options.openOrderNote && $options.openOrderNote(...args))
  } : {}, {
    T: $data.orderInfo.status == 1 && $data.orderInfo.pay_status == 0
  }, $data.orderInfo.status == 1 && $data.orderInfo.pay_status == 0 ? {
    U: common_assets._imports_3$6,
    V: common_vendor.o((...args) => $options.paymentClick && $options.paymentClick(...args))
  } : {}, {
    W: $data.statusBarHeight + $data.titleBarHeight + "px",
    X: common_assets._imports_0$4,
    Y: common_vendor.o(($event) => $options.orderRefundClick(false)),
    Z: common_vendor.n($data.refundType == 0 ? "active" : ""),
    aa: common_vendor.o(($event) => $data.refundType = 0),
    ab: common_vendor.n($data.refundType == 1 ? "active" : ""),
    ac: common_vendor.o(($event) => $data.refundType = 1),
    ad: $data.refundType == 1
  }, $data.refundType == 1 ? {
    ae: common_vendor.t($data.orderInfo.refund[0]),
    af: common_vendor.t($data.orderInfo.refund[1]),
    ag: common_vendor.t($data.orderInfo.refund[2])
  } : {}, {
    ah: $data.refundType == 1
  }, $data.refundType == 1 ? {
    ai: $data.refundExpress,
    aj: common_vendor.o(($event) => $data.refundExpress = $event.detail.value),
    ak: $data.refundExpressNo,
    al: common_vendor.o(($event) => $data.refundExpressNo = $event.detail.value)
  } : {}, {
    am: $data.refundReason,
    an: common_vendor.o(($event) => $data.refundReason = $event.detail.value),
    ao: common_vendor.o((...args) => $options.refundClick && $options.refundClick(...args)),
    ap: common_vendor.sr("refundPopup", "b578de62-6"),
    aq: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    }),
    ar: common_assets._imports_1$7,
    as: common_vendor.o(($event) => $options.toOrderNote(1)),
    at: common_assets._imports_3$7,
    av: common_vendor.o(($event) => $options.toOrderNote(2)),
    aw: common_assets._imports_2$5,
    ax: common_vendor.o(($event) => $options.toOrderNote(3)),
    ay: common_vendor.sr("notePopup", "b578de62-7"),
    az: common_vendor.p({
      type: "center"
    }),
    aA: common_vendor.t($data.tipsTitle),
    aB: common_vendor.sr("tipsPopup", "b578de62-8"),
    aC: common_vendor.p({
      type: "top",
      ["mask-background-color"]: "rgba(0, 0, 0, 0)"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/order/details.js.map
