"use strict";
const common_vendor = require("../../common/vendor.js");
const api_social = require("../../api/social.js");
const common_assets = require("../../common/assets.js");
const lazyImage = () => "../lazyImage/lazyImage.js";
const _sfc_main = {
  name: "waterfall",
  components: {
    lazyImage
  },
  props: {
    page: {
      type: Number,
      default: 1
    },
    activity: {
      type: Array,
      default: () => [],
      required: false
    },
    note: {
      type: Array,
      default: () => [],
      required: false
    }
  },
  data() {
    return {
      left: 0,
      right: 0,
      leftList: [],
      rightList: [],
      isNoteVideo: true,
      _lastProcessedLength: 0,
      // 性能优化相关
      renderLeftItems: 10,
      // 左侧渲染数量
      renderRightItems: 10,
      // 右侧渲染数量
      renderStep: 5,
      // 每次增加渲染数量
      maxRenderItems: 50,
      // 最大渲染数量
      // 缓存优化
      processedItems: /* @__PURE__ */ new Map(),
      // 已处理项目缓存
      layoutCache: /* @__PURE__ */ new Map(),
      // 布局计算缓存
      // 防抖定时器
      layoutTimer: null,
      // 性能监控
      performanceMetrics: {
        layoutTime: 0,
        renderTime: 0,
        lastUpdate: 0
      }
    };
  },
  created() {
    try {
      if (typeof getApp === "function") {
        const app = getApp();
        if (app && app.globalData) {
          this.isNoteVideo = app.globalData.isNoteVideo !== void 0 ? app.globalData.isNoteVideo : true;
        }
      }
    } catch (e) {
      common_vendor.index.__f__("log", "at components/waterfall/waterfall.vue:235", "获取配置失败", e);
    }
  },
  watch: {
    note: {
      handler(newVal, oldVal) {
        if (!newVal || newVal.length === 0) {
          this.clearLayout();
          return;
        }
        if (this.layoutTimer) {
          clearTimeout(this.layoutTimer);
        }
        this.layoutTimer = setTimeout(() => {
          this.handleNoteChange(newVal, oldVal);
        }, 100);
      },
      immediate: false,
      deep: false
      // 不需要深度监听，只监听数组引用变化
    }
  },
  computed: {
    // 虚拟滚动 - 左侧可见列表
    visibleLeftList() {
      return this.leftList.slice(0, this.renderLeftItems);
    },
    // 虚拟滚动 - 右侧可见列表
    visibleRightList() {
      return this.rightList.slice(0, this.renderRightItems);
    },
    // 性能指标
    performanceInfo() {
      return {
        totalItems: this.leftList.length + this.rightList.length,
        renderedItems: this.renderLeftItems + this.renderRightItems,
        cacheSize: this.layoutCache.size,
        lastLayoutTime: this.performanceMetrics.layoutTime
      };
    }
  },
  mounted() {
    if (this.note && this.note.length > 0) {
      this.xinxuan(this.note);
    }
  },
  methods: {
    // 处理note数据变化
    handleNoteChange(newVal, oldVal) {
      const startTime = Date.now();
      try {
        const needProcess = this.page === 1 || !this._lastProcessedLength || newVal.length > this._lastProcessedLength;
        if (!needProcess) {
          return;
        }
        this.xinxuan(newVal);
        this.performanceMetrics.layoutTime = Date.now() - startTime;
        this.performanceMetrics.lastUpdate = Date.now();
      } catch (error) {
        common_vendor.index.__f__("error", "at components/waterfall/waterfall.vue:312", "瀑布流布局处理失败:", error);
        this.handleLayoutError(error);
      }
    },
    // 清空布局
    clearLayout() {
      this.left = 0;
      this.right = 0;
      this.leftList = [];
      this.rightList = [];
      this._lastProcessedLength = 0;
      this.processedItems.clear();
      this.layoutCache.clear();
    },
    // 处理布局错误
    handleLayoutError(error) {
      common_vendor.index.__f__("error", "at components/waterfall/waterfall.vue:330", "瀑布流布局错误:", error);
      this.clearLayout();
    },
    // 获取缓存的布局信息
    getCachedLayout(item) {
      var _a;
      const cacheKey = `${item.id}_${item.type}_${((_a = item.content) == null ? void 0 : _a.length) || 0}`;
      return this.layoutCache.get(cacheKey);
    },
    // 缓存布局信息
    setCachedLayout(item, layout) {
      var _a;
      const cacheKey = `${item.id}_${item.type}_${((_a = item.content) == null ? void 0 : _a.length) || 0}`;
      this.layoutCache.set(cacheKey, layout);
      if (this.layoutCache.size > 200) {
        const firstKey = this.layoutCache.keys().next().value;
        this.layoutCache.delete(firstKey);
      }
    },
    // 获取媒体URL - 根据内容类型返回合适的URL
    getMediaUrl(item) {
      var _a, _b, _c, _d, _e;
      if (!item)
        return "";
      if (item.type === 2 || item.type === 1) {
        if ((_a = item.imgs) == null ? void 0 : _a.length) {
          return typeof item.imgs[0] === "string" ? item.imgs[0] : item.imgs[0].url || "";
        }
        if ((_b = item.img) == null ? void 0 : _b.url) {
          return item.img.url;
        }
        if (item.images) {
          if (typeof item.images === "string") {
            try {
              const images = JSON.parse(item.images);
              return Array.isArray(images) && images.length > 0 ? images[0] : "";
            } catch (e) {
              return item.images;
            }
          } else if (Array.isArray(item.images) && item.images.length > 0) {
            return typeof item.images[0] === "string" ? item.images[0] : item.images[0].url || "";
          }
        }
      } else if (item.type === 3 || item.type === 2) {
        if (item.video_cover) {
          return item.video_cover;
        }
        if ((_c = item.video) == null ? void 0 : _c.cover) {
          return item.video.cover;
        }
        if ((_d = item.images) == null ? void 0 : _d.length) {
          return typeof item.images[0] === "string" ? item.images[0] : item.images[0].url || "";
        }
      } else if (item.type === 4 || item.type === 3) {
        if (item.audio_cover) {
          return item.audio_cover;
        }
        if ((_e = item.audio) == null ? void 0 : _e.cover) {
          return item.audio.cover;
        }
      }
      return "";
    },
    // 获取图片数量
    getImageCount(item) {
      var _a;
      if (!item)
        return 0;
      if ((_a = item.imgs) == null ? void 0 : _a.length) {
        return item.imgs.length;
      }
      if (item.img_count !== void 0) {
        return parseInt(item.img_count) || 0;
      }
      if (item.images) {
        if (Array.isArray(item.images)) {
          return item.images.length;
        }
        if (typeof item.images === "string") {
          try {
            const images = JSON.parse(item.images);
            return Array.isArray(images) ? images.length : 0;
          } catch (e) {
            return item.images.trim() ? 1 : 0;
          }
        }
      }
      return 0;
    },
    // 获取媒体尺寸信息，返回{width, height, ratio}
    getMediaDimensions(item) {
      var _a, _b, _c, _d, _e, _f, _g, _h;
      if (!item)
        return { width: 0, height: 0, ratio: 1 };
      let width = 0, height = 0, ratio = 1;
      if (((_a = item.img) == null ? void 0 : _a.wide) && ((_b = item.img) == null ? void 0 : _b.high) && item.img.high > 0) {
        width = item.img.wide;
        height = item.img.high;
        ratio = width / height;
      } else if (((_c = item.video) == null ? void 0 : _c.wide) && ((_d = item.video) == null ? void 0 : _d.high) && item.video.high > 0) {
        width = item.video.wide;
        height = item.video.high;
        ratio = width / height;
      } else if (((_f = (_e = item.imgs) == null ? void 0 : _e[0]) == null ? void 0 : _f.wide) && ((_h = (_g = item.imgs) == null ? void 0 : _g[0]) == null ? void 0 : _h.high) && item.imgs[0].high > 0) {
        width = item.imgs[0].wide;
        height = item.imgs[0].high;
        ratio = width / height;
      } else if (item.image_width && item.image_height && item.image_height > 0) {
        width = item.image_width;
        height = item.image_height;
        ratio = width / height;
      } else if (item.video_width && item.video_height && item.video_height > 0) {
        width = item.video_width;
        height = item.video_height;
        ratio = width / height;
      }
      if (ratio <= 0) {
        ratio = 1;
      }
      return { width, height, ratio };
    },
    // 优化后的瀑布流布局计算
    xinxuan(noteList) {
      const startTime = Date.now();
      if (this.page == 1) {
        this.clearLayout();
        if (this.activity.length) {
          this.left = 556;
        }
      } else {
        let existCount = this.leftList.length + this.rightList.length;
        let newCount = noteList.length - existCount;
        if (newCount <= 0) {
          return;
        }
        noteList = noteList.slice(-newCount);
      }
      this.batchProcessItems(noteList);
      this.updateRenderCounts();
      const processTime = Date.now() - startTime;
      this.performanceMetrics.layoutTime = processTime;
      if (processTime > 100) {
        common_vendor.index.__f__("warn", "at components/waterfall/waterfall.vue:521", "瀑布流布局计算较慢:", processTime + "ms");
      }
    },
    // 批量处理项目
    batchProcessItems(noteList) {
      const batchSize = 10;
      let currentIndex = 0;
      const processBatch = () => {
        const endIndex = Math.min(currentIndex + batchSize, noteList.length);
        for (let i = currentIndex; i < endIndex; i++) {
          this.processItem(noteList[i], i);
        }
        currentIndex = endIndex;
        if (currentIndex < noteList.length) {
          this.$nextTick(processBatch);
        } else {
          this._lastProcessedLength = this.leftList.length + this.rightList.length;
        }
      };
      processBatch();
    },
    // 处理单个项目
    processItem(originalItem, index) {
      const cached = this.getCachedLayout(originalItem);
      if (cached) {
        this.addItemToColumn(cached);
        return;
      }
      let item = { ...originalItem };
      const layout = this.calculateItemLayout(item);
      this.setCachedLayout(originalItem, layout);
      this.addItemToColumn(layout);
    },
    // 添加项目到列
    addItemToColumn(item) {
      if (this.left <= this.right) {
        this.left += item.bgHigh + 8;
        this.leftList.push(item);
      } else {
        this.right += item.bgHigh + 8;
        this.rightList.push(item);
      }
    },
    // 更新渲染数量
    updateRenderCounts() {
      this.renderLeftItems = Math.min(this.leftList.length, this.maxRenderItems);
      this.renderRightItems = Math.min(this.rightList.length, this.maxRenderItems);
    },
    // 计算项目布局
    calculateItemLayout(item) {
      const COLUMN_WIDTH = 359;
      if (!item.content) {
        item.content = "";
      }
      if (item.type === 0) {
        item.type = 1;
        item.isTextOnly = true;
      } else if (item.type === 1 && item.img) {
        item.type = 2;
      } else if (item.type === 2 && item.video) {
        item.type = 3;
      } else if (item.type === 3 && item.audio) {
        item.type = 4;
      }
      if (!item.type) {
        if (item.images && Array.isArray(item.images) && item.images.length > 0) {
          item.type = 2;
        } else if (item.video || item.video_cover) {
          item.type = 3;
        } else if (item.audio || item.audio_cover) {
          item.type = 4;
        } else {
          item.type = 1;
        }
      }
      let totalHeight = 0;
      let contentHeight = 0;
      let bottomHeight = 60;
      if (item.type === 1) {
        item.isTextOnly = true;
        const contentLength = item.content ? Array.from(item.content).length : 0;
        if (contentLength <= 30) {
          contentHeight = 120;
        } else if (contentLength <= 100) {
          contentHeight = 180;
        } else {
          contentHeight = 280;
        }
        item.high = contentHeight;
      } else if (item.type === 4) {
        contentHeight = 374;
        item.high = contentHeight;
        item.contentOne = true;
      } else if (item.type === 2 || item.type === 3) {
        const dimensions = this.getMediaDimensions(item);
        const ratio = dimensions.ratio || 1;
        if (ratio >= 1.5) {
          contentHeight = Math.round(COLUMN_WIDTH / ratio);
          contentHeight = Math.max(contentHeight, 180);
        } else if (ratio <= 0.7) {
          contentHeight = Math.round(COLUMN_WIDTH / ratio);
          contentHeight = Math.min(contentHeight, 400);
        } else {
          contentHeight = COLUMN_WIDTH;
        }
        item.high = contentHeight;
      }
      let extraContentHeight = 0;
      if ((item.type === 2 || item.type === 3) && item.content) {
        const contentLength = Array.from(item.content).length;
        if (contentLength <= 15) {
          extraContentHeight = 40;
          item.contentOne = true;
        } else if (contentLength <= 50) {
          extraContentHeight = 80;
          item.contentOne = false;
        } else {
          extraContentHeight = 100;
          item.contentOne = false;
        }
      }
      let relatedInfoHeight = 0;
      let hasRelatedInfo = false;
      if (item.location_name || item.adds_name) {
        hasRelatedInfo = true;
      }
      if (item.topic_info && item.topic_info.length || item.topics && item.topics.length) {
        hasRelatedInfo = true;
      }
      if (item.product_info || item.goods_info || item.goods || item.order_id) {
        hasRelatedInfo = true;
      }
      if (item.activity_id || item.activity && item.activity.id) {
        hasRelatedInfo = true;
      }
      if (item.circle_id) {
        hasRelatedInfo = true;
      }
      if (hasRelatedInfo) {
        relatedInfoHeight = 44;
      }
      totalHeight = contentHeight + extraContentHeight + relatedInfoHeight + bottomHeight;
      item.bgHigh = totalHeight;
      return item;
    },
    // 获取媒体尺寸信息
    getMediaDimensions(item) {
      let width = 0;
      let height = 0;
      let ratio = 1;
      if (item.image_width && item.image_height) {
        width = item.image_width;
        height = item.image_height;
      } else if (item.video_width && item.video_height) {
        width = item.video_width;
        height = item.video_height;
      } else if (item.video && item.video.wide && item.video.high) {
        width = item.video.wide;
        height = item.video.high;
      } else if (item.imgs && item.imgs[0] && item.imgs[0].wide && item.imgs[0].high) {
        width = item.imgs[0].wide;
        height = item.imgs[0].high;
      } else {
        width = 359;
        height = 359;
      }
      if (width > 0 && height > 0) {
        ratio = width / height;
      }
      return { width, height, ratio };
    },
    /**
     * 处理点赞事件
     * @param {Object} item 动态项目
     * @param {Number} index 索引
     */
    handleLike(item, index) {
      if (!this.$store.getters.isLogin) {
        common_vendor.index.navigateTo({
          url: "/pages/login/index"
        });
        return;
      }
      if (item.isProcessing)
        return;
      this.$set(item, "isProcessing", true);
      const newLikeStatus = item.is_like !== 1;
      const oldLikeCount = parseInt(item.likes || 0);
      this.$set(item, "is_like", newLikeStatus ? 1 : 0);
      this.$set(item, "likes", newLikeStatus ? oldLikeCount + 1 : Math.max(0, oldLikeCount - 1));
      api_social.likeDynamic({
        id: item.id,
        is_like: newLikeStatus ? 1 : 0
      }).then((res) => {
        this.$emit("likeback", {
          index,
          id: item.id,
          isLike: newLikeStatus
        });
      }).catch((err) => {
        this.$set(item, "is_like", newLikeStatus ? 0 : 1);
        this.$set(item, "likes", oldLikeCount);
        common_vendor.index.showToast({
          title: "操作失败，请重试",
          icon: "none"
        });
      }).finally(() => {
        this.$set(item, "isProcessing", false);
      });
    },
    // 页面跳转
    toPages(e) {
      const url = e.currentTarget.dataset.url;
      common_vendor.index.navigateTo({
        url: "/pages/" + url
      });
    },
    // 获取用户头像
    getUserAvatar(item) {
      if (!item)
        return "/static/img/avatar.png";
      if (item.user && item.user.avatar) {
        return item.user.avatar;
      }
      if (item.avatar) {
        return item.avatar;
      } else if (item.user_info && item.user_info.avatar) {
        return item.user_info.avatar;
      }
      return "/static/img/avatar.png";
    },
    // 获取用户名称
    getUserName(item) {
      if (!item)
        return "用户";
      if (item.user && item.user.name) {
        return item.user.name;
      }
      if (item.nickname) {
        return item.nickname;
      } else if (item.user && item.user.nickname) {
        return item.user.nickname;
      } else if (item.user_info && item.user_info.nickname) {
        return item.user_info.nickname;
      } else if (item.user_info && item.user_info.name) {
        return item.user_info.name;
      }
      return "用户";
    }
  }
};
if (!Array) {
  const _component_lazy_image = common_vendor.resolveComponent("lazy-image");
  _component_lazy_image();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.activity.length
  }, $props.activity.length ? {
    b: common_vendor.f($props.activity, (item, index, i0) => {
      return common_vendor.e({
        a: "8e26fd7f-0-" + i0,
        b: common_vendor.p({
          src: item.img
        }),
        c: common_vendor.t(item.status_str || "加载中"),
        d: common_vendor.t(item.name || "活动名称加载中"),
        e: common_vendor.t(item.adds_name || "活动地址加载中"),
        f: common_vendor.t(item.activity_time || "活动时间加载中"),
        g: item.user_count
      }, item.user_count ? {
        h: common_vendor.f(item.avatar_list, (img, imgIndex, i1) => {
          return {
            a: img,
            b: imgIndex
          };
        }),
        i: common_vendor.t(item.user_count)
      } : {
        j: common_vendor.t(item.browse)
      }, {
        k: common_vendor.t(item.is_join ? "查看详情" : "立即参加"),
        l: index,
        m: "activity/details?id=" + item.id,
        n: common_vendor.o((...args) => $options.toPages && $options.toPages(...args), index)
      });
    }),
    c: common_assets._imports_5$2,
    d: common_assets._imports_1$6,
    e: common_assets._imports_2$4,
    f: common_vendor.o((...args) => $options.toPages && $options.toPages(...args))
  } : {}, {
    g: common_vendor.f($data.leftList, (v, i, i0) => {
      return common_vendor.e({
        a: v.type == 1 && (!v.images || v.images.length === 0)
      }, v.type == 1 && (!v.images || v.images.length === 0) ? {
        b: common_vendor.t(v.content),
        c: v.high - 40 + "rpx",
        d: v.high + "rpx",
        e: v.high + "rpx"
      } : v.type != 1 ? common_vendor.e({
        g: v.type == 2 || v.type == 3
      }, v.type == 2 || v.type == 3 ? {
        h: "8e26fd7f-1-" + i0,
        i: common_vendor.p({
          src: $options.getMediaUrl(v)
        })
      } : {}, {
        j: v.type == 2 && $options.getImageCount(v) > 1
      }, v.type == 2 && $options.getImageCount(v) > 1 ? {
        k: common_assets._imports_0$22
      } : {}, {
        l: v.type == 3
      }, v.type == 3 ? {
        m: common_assets._imports_1$22
      } : {}, {
        n: v.type == 4
      }, v.type == 4 ? {
        o: v.audio_cover || (v.audio && v.audio.cover ? v.audio.cover : "/static/img/audio_cover.png"),
        p: v.audio_cover || (v.audio && v.audio.cover ? v.audio.cover : "/static/img/audio_cover.png"),
        q: common_assets._imports_2$5,
        r: common_vendor.t(v.audio_title || (v.audio && v.audio.name ? v.audio.name : "音频")),
        s: common_vendor.t(v.audio_intro || (v.audio && v.audio.intro ? v.audio.intro : "点击播放音频")),
        t: common_assets._imports_6
      } : {}, {
        v: v.top || v.is_top
      }, v.top || v.is_top ? {} : {}, {
        w: v.high + "rpx"
      }) : {}, {
        f: v.type != 1,
        x: v.type != 1 && v.type != 4 || v.images && v.images.length > 0
      }, v.type != 1 && v.type != 4 || v.images && v.images.length > 0 ? {
        y: common_vendor.t(v.content),
        z: v.contentOne ? 1 : ""
      } : {}, {
        A: $options.getUserAvatar(v),
        B: common_vendor.t($options.getUserName(v)),
        C: v.is_like
      }, v.is_like ? {
        D: common_assets._imports_8$2
      } : {
        E: common_assets._imports_9$2
      }, {
        F: common_vendor.t(v.likes || v.like_count || v.like_count_str || 0),
        G: i,
        H: common_vendor.o(($event) => $options.handleLike(v, i), i),
        I: i,
        J: v.bgHigh + "rpx",
        K: v.type == 2 || v.type == 3 || v.type == 4 ? "note/video?id=" + v.id : "note/details?id=" + v.id,
        L: common_vendor.o((...args) => $options.toPages && $options.toPages(...args), i)
      });
    }),
    h: common_vendor.f($data.rightList, (v, i, i0) => {
      return common_vendor.e({
        a: v.type == 1 && (!v.images || v.images.length === 0)
      }, v.type == 1 && (!v.images || v.images.length === 0) ? {
        b: common_vendor.t(v.content),
        c: v.high - 40 + "rpx",
        d: v.high + "rpx",
        e: v.high + "rpx"
      } : v.type != 1 ? common_vendor.e({
        g: v.type == 2 || v.type == 3
      }, v.type == 2 || v.type == 3 ? {
        h: "8e26fd7f-2-" + i0,
        i: common_vendor.p({
          src: $options.getMediaUrl(v)
        })
      } : {}, {
        j: v.type == 2 && $options.getImageCount(v) > 1
      }, v.type == 2 && $options.getImageCount(v) > 1 ? {
        k: common_assets._imports_0$22
      } : {}, {
        l: v.type == 3
      }, v.type == 3 ? {
        m: common_assets._imports_1$22
      } : {}, {
        n: v.type == 4
      }, v.type == 4 ? {
        o: v.audio_cover || (v.audio && v.audio.cover ? v.audio.cover : "/static/img/audio_cover.png"),
        p: v.audio_cover || (v.audio && v.audio.cover ? v.audio.cover : "/static/img/audio_cover.png"),
        q: common_assets._imports_2$5,
        r: common_vendor.t(v.audio_title || (v.audio && v.audio.name ? v.audio.name : "音频")),
        s: common_vendor.t(v.audio_intro || (v.audio && v.audio.intro ? v.audio.intro : "点击播放音频")),
        t: common_assets._imports_6
      } : {}, {
        v: v.top || v.is_top
      }, v.top || v.is_top ? {} : {}, {
        w: v.high + "rpx"
      }) : {}, {
        f: v.type != 1,
        x: v.type != 1 && v.type != 4 || v.images && v.images.length > 0
      }, v.type != 1 && v.type != 4 || v.images && v.images.length > 0 ? {
        y: common_vendor.t(v.content),
        z: v.contentOne ? 1 : ""
      } : {}, {
        A: $options.getUserAvatar(v),
        B: common_vendor.t($options.getUserName(v)),
        C: v.is_like
      }, v.is_like ? {
        D: common_assets._imports_8$2
      } : {
        E: common_assets._imports_9$2
      }, {
        F: common_vendor.t(v.likes || v.like_count || v.like_count_str || 0),
        G: i,
        H: common_vendor.o(($event) => $options.handleLike(v, i), i),
        I: i,
        J: v.bgHigh + "rpx",
        K: v.type == 2 || v.type == 3 || v.type == 4 ? "note/video?id=" + v.id : "note/details?id=" + v.id,
        L: common_vendor.o((...args) => $options.toPages && $options.toPages(...args), i)
      });
    })
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-8e26fd7f"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/waterfall/waterfall.js.map
