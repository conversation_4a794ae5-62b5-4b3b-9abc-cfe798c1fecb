{"version": 3, "file": "social.js", "sources": ["stores/social.js"], "sourcesContent": ["// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport { defineStore } from 'pinia'\n\nexport const useSocialStore = defineStore('social', {\n  state: () => ({\n    // 简化的动态缓存（只缓存关键数据）\n    currentDynamicList: [],\n    currentTab: 0,\n\n    // 动态数据缓存\n    dynamicCache: {\n      personal: {\n        notes: { data: [], timestamp: 0, totalCount: 0 },\n        likes: { data: [], timestamp: 0, totalCount: 0 }\n      },\n      home: { data: [], timestamp: 0, totalCount: 0 },\n      following: { data: [], timestamp: 0, totalCount: 0 }\n    },\n\n    // 发布状态\n    publishStatus: {\n      isPublishing: false,\n      progress: 0,\n      error: null\n    },\n\n    // 点赞状态缓存（使用Map但不持久化）\n    likeStatusCache: new Map(),\n\n    // 缓存配置\n    cacheConfig: {\n      timeout: 300000, // 5分钟\n      maxItems: 20 // 减少缓存数量\n    }\n  }),\n\n  getters: {\n    // 获取个人笔记缓存\n    getPersonalNotes: (state) => state.dynamicCache.personal.notes.data,\n    \n    // 获取个人点赞缓存\n    getPersonalLikes: (state) => state.dynamicCache.personal.likes.data,\n    \n    // 获取首页动态缓存\n    getHomeDynamics: (state) => state.dynamicCache.home.data,\n    \n    // 检查缓存是否有效\n    isCacheValid: (state) => (cacheType, subType = null) => {\n      const now = Date.now()\n      let cache\n      \n      if (subType) {\n        cache = state.dynamicCache[cacheType]?.[subType]\n      } else {\n        cache = state.dynamicCache[cacheType]\n      }\n      \n      return cache && cache.timestamp && \n             (now - cache.timestamp < state.cacheConfig.timeout) &&\n             cache.data.length > 0\n    },\n    \n    // 获取热门话题\n    getHotTopics: (state) => state.topics.hot,\n    \n    // 获取已加入圈子\n    getJoinedCircles: (state) => state.circles.joined,\n    \n    // 获取关注用户\n    getFollowUsers: (state) => state.followUsers,\n    \n    // 检查动态是否已点赞\n    isLiked: (state) => (dynamicId) => {\n      return state.likeStatusCache.get(dynamicId) || false\n    },\n    \n    // 是否正在发布\n    isPublishing: (state) => state.publishStatus.isPublishing\n  },\n\n  actions: {\n    // 设置动态缓存\n    setDynamicCache(type, subType, data, totalCount = 0) {\n      const now = Date.now()\n      \n      if (subType) {\n        if (!this.dynamicCache[type]) {\n          this.dynamicCache[type] = {}\n        }\n        this.dynamicCache[type][subType] = {\n          data: data.slice(0, this.cacheConfig.maxItems),\n          timestamp: now,\n          totalCount\n        }\n      } else {\n        this.dynamicCache[type] = {\n          data: data.slice(0, this.cacheConfig.maxItems),\n          timestamp: now,\n          totalCount\n        }\n      }\n    },\n\n    // 获取动态缓存\n    getDynamicCache(type, subType = null) {\n      if (subType) {\n        return this.dynamicCache[type]?.[subType] || { data: [], timestamp: 0, totalCount: 0 }\n      } else {\n        return this.dynamicCache[type] || { data: [], timestamp: 0, totalCount: 0 }\n      }\n    },\n\n    // 清除动态缓存\n    clearDynamicCache(type = null, subType = null) {\n      if (type && subType) {\n        if (this.dynamicCache[type]) {\n          this.dynamicCache[type][subType] = { data: [], timestamp: 0, totalCount: 0 }\n        }\n      } else if (type) {\n        this.dynamicCache[type] = { data: [], timestamp: 0, totalCount: 0 }\n      } else {\n        // 清除所有缓存\n        this.dynamicCache = {\n          personal: {\n            notes: { data: [], timestamp: 0, totalCount: 0 },\n            likes: { data: [], timestamp: 0, totalCount: 0 }\n          },\n          home: { data: [], timestamp: 0, totalCount: 0 },\n          following: { data: [], timestamp: 0, totalCount: 0 }\n        }\n      }\n    },\n\n    // 更新动态数据（添加新动态到缓存）\n    addDynamicToCache(type, subType, dynamic) {\n      const cache = this.getDynamicCache(type, subType)\n      const newData = [dynamic, ...cache.data]\n      this.setDynamicCache(type, subType, newData, cache.totalCount + 1)\n    },\n\n    // 从缓存中删除动态\n    removeDynamicFromCache(type, subType, dynamicId) {\n      const cache = this.getDynamicCache(type, subType)\n      const newData = cache.data.filter(item => item.id !== dynamicId)\n      this.setDynamicCache(type, subType, newData, Math.max(0, cache.totalCount - 1))\n    },\n\n    // 设置话题数据\n    setTopics(type, topics) {\n      this.topics[type] = topics\n    },\n\n    // 设置圈子数据\n    setCircles(type, circles) {\n      this.circles[type] = circles\n    },\n\n    // 设置关注用户列表\n    setFollowUsers(users) {\n      this.followUsers = users\n    },\n\n    // 设置@用户列表\n    setMentionUsers(users) {\n      this.mentionUsers = users\n    },\n\n    // 设置评论缓存\n    setCommentCache(dynamicId, comments) {\n      this.commentCache.set(dynamicId, {\n        data: comments,\n        timestamp: Date.now()\n      })\n    },\n\n    // 获取评论缓存\n    getCommentCache(dynamicId) {\n      const cache = this.commentCache.get(dynamicId)\n      if (!cache) return null\n      \n      const now = Date.now()\n      if (now - cache.timestamp > this.cacheConfig.timeout) {\n        this.commentCache.delete(dynamicId)\n        return null\n      }\n      \n      return cache.data\n    },\n\n    // 设置点赞状态\n    setLikeStatus(dynamicId, isLiked) {\n      this.likeStatusCache.set(dynamicId, isLiked)\n    },\n\n    // 批量设置点赞状态\n    setBatchLikeStatus(likeStatusMap) {\n      for (const [dynamicId, isLiked] of Object.entries(likeStatusMap)) {\n        this.likeStatusCache.set(dynamicId, isLiked)\n      }\n    },\n\n    // 设置发布状态\n    setPublishStatus(status) {\n      this.publishStatus = { ...this.publishStatus, ...status }\n    },\n\n    // 开始发布\n    startPublish() {\n      this.publishStatus = {\n        isPublishing: true,\n        progress: 0,\n        error: null\n      }\n    },\n\n    // 更新发布进度\n    updatePublishProgress(progress) {\n      this.publishStatus.progress = progress\n    },\n\n    // 发布成功\n    publishSuccess() {\n      this.publishStatus = {\n        isPublishing: false,\n        progress: 100,\n        error: null\n      }\n    },\n\n    // 发布失败\n    publishError(error) {\n      this.publishStatus = {\n        isPublishing: false,\n        progress: 0,\n        error\n      }\n    },\n\n    // 清理过期缓存\n    cleanExpiredCache() {\n      const now = Date.now()\n      \n      // 清理评论缓存\n      for (const [key, cache] of this.commentCache.entries()) {\n        if (now - cache.timestamp > this.cacheConfig.timeout) {\n          this.commentCache.delete(key)\n        }\n      }\n      \n      // 清理动态缓存\n      Object.keys(this.dynamicCache).forEach(type => {\n        const cache = this.dynamicCache[type]\n        if (cache.timestamp && now - cache.timestamp > this.cacheConfig.timeout) {\n          if (typeof cache === 'object' && cache.data) {\n            // 单层缓存\n            this.dynamicCache[type] = { data: [], timestamp: 0, totalCount: 0 }\n          } else {\n            // 多层缓存\n            Object.keys(cache).forEach(subType => {\n              if (cache[subType].timestamp && now - cache[subType].timestamp > this.cacheConfig.timeout) {\n                cache[subType] = { data: [], timestamp: 0, totalCount: 0 }\n              }\n            })\n          }\n        }\n      })\n    },\n\n    // 重置社交状态\n    reset() {\n      this.clearDynamicCache()\n      this.topics = {\n        hot: [],\n        recent: [],\n        followed: []\n      }\n      this.circles = {\n        joined: [],\n        recommended: []\n      }\n      this.followUsers = []\n      this.mentionUsers = []\n      this.commentCache.clear()\n      this.likeStatusCache.clear()\n      this.publishStatus = {\n        isPublishing: false,\n        progress: 0,\n        error: null\n      }\n    }\n  }\n})\n"], "names": ["defineStore"], "mappings": ";;AAYY,MAAC,iBAAiBA,cAAW,YAAC,UAAU;AAAA,EAClD,OAAO,OAAO;AAAA;AAAA,IAEZ,oBAAoB,CAAE;AAAA,IACtB,YAAY;AAAA;AAAA,IAGZ,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,OAAO,EAAE,MAAM,CAAE,GAAE,WAAW,GAAG,YAAY,EAAG;AAAA,QAChD,OAAO,EAAE,MAAM,CAAE,GAAE,WAAW,GAAG,YAAY,EAAG;AAAA,MACjD;AAAA,MACD,MAAM,EAAE,MAAM,CAAE,GAAE,WAAW,GAAG,YAAY,EAAG;AAAA,MAC/C,WAAW,EAAE,MAAM,CAAE,GAAE,WAAW,GAAG,YAAY,EAAG;AAAA,IACrD;AAAA;AAAA,IAGD,eAAe;AAAA,MACb,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACR;AAAA;AAAA,IAGD,iBAAiB,oBAAI,IAAK;AAAA;AAAA,IAG1B,aAAa;AAAA,MACX,SAAS;AAAA;AAAA,MACT,UAAU;AAAA;AAAA,IACX;AAAA,EACL;AAAA,EAEE,SAAS;AAAA;AAAA,IAEP,kBAAkB,CAAC,UAAU,MAAM,aAAa,SAAS,MAAM;AAAA;AAAA,IAG/D,kBAAkB,CAAC,UAAU,MAAM,aAAa,SAAS,MAAM;AAAA;AAAA,IAG/D,iBAAiB,CAAC,UAAU,MAAM,aAAa,KAAK;AAAA;AAAA,IAGpD,cAAc,CAAC,UAAU,CAAC,WAAW,UAAU,SAAS;;AACtD,YAAM,MAAM,KAAK,IAAK;AACtB,UAAI;AAEJ,UAAI,SAAS;AACX,iBAAQ,WAAM,aAAa,SAAS,MAA5B,mBAAgC;AAAA,MAChD,OAAa;AACL,gBAAQ,MAAM,aAAa,SAAS;AAAA,MACrC;AAED,aAAO,SAAS,MAAM,aACd,MAAM,MAAM,YAAY,MAAM,YAAY,WAC3C,MAAM,KAAK,SAAS;AAAA,IAC5B;AAAA;AAAA,IAGD,cAAc,CAAC,UAAU,MAAM,OAAO;AAAA;AAAA,IAGtC,kBAAkB,CAAC,UAAU,MAAM,QAAQ;AAAA;AAAA,IAG3C,gBAAgB,CAAC,UAAU,MAAM;AAAA;AAAA,IAGjC,SAAS,CAAC,UAAU,CAAC,cAAc;AACjC,aAAO,MAAM,gBAAgB,IAAI,SAAS,KAAK;AAAA,IAChD;AAAA;AAAA,IAGD,cAAc,CAAC,UAAU,MAAM,cAAc;AAAA,EAC9C;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,gBAAgB,MAAM,SAAS,MAAM,aAAa,GAAG;AACnD,YAAM,MAAM,KAAK,IAAK;AAEtB,UAAI,SAAS;AACX,YAAI,CAAC,KAAK,aAAa,IAAI,GAAG;AAC5B,eAAK,aAAa,IAAI,IAAI,CAAE;AAAA,QAC7B;AACD,aAAK,aAAa,IAAI,EAAE,OAAO,IAAI;AAAA,UACjC,MAAM,KAAK,MAAM,GAAG,KAAK,YAAY,QAAQ;AAAA,UAC7C,WAAW;AAAA,UACX;AAAA,QACD;AAAA,MACT,OAAa;AACL,aAAK,aAAa,IAAI,IAAI;AAAA,UACxB,MAAM,KAAK,MAAM,GAAG,KAAK,YAAY,QAAQ;AAAA,UAC7C,WAAW;AAAA,UACX;AAAA,QACD;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,MAAM,UAAU,MAAM;;AACpC,UAAI,SAAS;AACX,iBAAO,UAAK,aAAa,IAAI,MAAtB,mBAA0B,aAAY,EAAE,MAAM,CAAE,GAAE,WAAW,GAAG,YAAY,EAAG;AAAA,MAC9F,OAAa;AACL,eAAO,KAAK,aAAa,IAAI,KAAK,EAAE,MAAM,CAAE,GAAE,WAAW,GAAG,YAAY,EAAG;AAAA,MAC5E;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB,OAAO,MAAM,UAAU,MAAM;AAC7C,UAAI,QAAQ,SAAS;AACnB,YAAI,KAAK,aAAa,IAAI,GAAG;AAC3B,eAAK,aAAa,IAAI,EAAE,OAAO,IAAI,EAAE,MAAM,CAAA,GAAI,WAAW,GAAG,YAAY,EAAG;AAAA,QAC7E;AAAA,MACF,WAAU,MAAM;AACf,aAAK,aAAa,IAAI,IAAI,EAAE,MAAM,CAAE,GAAE,WAAW,GAAG,YAAY,EAAG;AAAA,MAC3E,OAAa;AAEL,aAAK,eAAe;AAAA,UAClB,UAAU;AAAA,YACR,OAAO,EAAE,MAAM,CAAE,GAAE,WAAW,GAAG,YAAY,EAAG;AAAA,YAChD,OAAO,EAAE,MAAM,CAAE,GAAE,WAAW,GAAG,YAAY,EAAG;AAAA,UACjD;AAAA,UACD,MAAM,EAAE,MAAM,CAAE,GAAE,WAAW,GAAG,YAAY,EAAG;AAAA,UAC/C,WAAW,EAAE,MAAM,CAAE,GAAE,WAAW,GAAG,YAAY,EAAG;AAAA,QACrD;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB,MAAM,SAAS,SAAS;AACxC,YAAM,QAAQ,KAAK,gBAAgB,MAAM,OAAO;AAChD,YAAM,UAAU,CAAC,SAAS,GAAG,MAAM,IAAI;AACvC,WAAK,gBAAgB,MAAM,SAAS,SAAS,MAAM,aAAa,CAAC;AAAA,IAClE;AAAA;AAAA,IAGD,uBAAuB,MAAM,SAAS,WAAW;AAC/C,YAAM,QAAQ,KAAK,gBAAgB,MAAM,OAAO;AAChD,YAAM,UAAU,MAAM,KAAK,OAAO,UAAQ,KAAK,OAAO,SAAS;AAC/D,WAAK,gBAAgB,MAAM,SAAS,SAAS,KAAK,IAAI,GAAG,MAAM,aAAa,CAAC,CAAC;AAAA,IAC/E;AAAA;AAAA,IAGD,UAAU,MAAM,QAAQ;AACtB,WAAK,OAAO,IAAI,IAAI;AAAA,IACrB;AAAA;AAAA,IAGD,WAAW,MAAM,SAAS;AACxB,WAAK,QAAQ,IAAI,IAAI;AAAA,IACtB;AAAA;AAAA,IAGD,eAAe,OAAO;AACpB,WAAK,cAAc;AAAA,IACpB;AAAA;AAAA,IAGD,gBAAgB,OAAO;AACrB,WAAK,eAAe;AAAA,IACrB;AAAA;AAAA,IAGD,gBAAgB,WAAW,UAAU;AACnC,WAAK,aAAa,IAAI,WAAW;AAAA,QAC/B,MAAM;AAAA,QACN,WAAW,KAAK,IAAK;AAAA,MAC7B,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,WAAW;AACzB,YAAM,QAAQ,KAAK,aAAa,IAAI,SAAS;AAC7C,UAAI,CAAC;AAAO,eAAO;AAEnB,YAAM,MAAM,KAAK,IAAK;AACtB,UAAI,MAAM,MAAM,YAAY,KAAK,YAAY,SAAS;AACpD,aAAK,aAAa,OAAO,SAAS;AAClC,eAAO;AAAA,MACR;AAED,aAAO,MAAM;AAAA,IACd;AAAA;AAAA,IAGD,cAAc,WAAW,SAAS;AAChC,WAAK,gBAAgB,IAAI,WAAW,OAAO;AAAA,IAC5C;AAAA;AAAA,IAGD,mBAAmB,eAAe;AAChC,iBAAW,CAAC,WAAW,OAAO,KAAK,OAAO,QAAQ,aAAa,GAAG;AAChE,aAAK,gBAAgB,IAAI,WAAW,OAAO;AAAA,MAC5C;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB,QAAQ;AACvB,WAAK,gBAAgB,EAAE,GAAG,KAAK,eAAe,GAAG,OAAQ;AAAA,IAC1D;AAAA;AAAA,IAGD,eAAe;AACb,WAAK,gBAAgB;AAAA,QACnB,cAAc;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACR;AAAA,IACF;AAAA;AAAA,IAGD,sBAAsB,UAAU;AAC9B,WAAK,cAAc,WAAW;AAAA,IAC/B;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,gBAAgB;AAAA,QACnB,cAAc;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACR;AAAA,IACF;AAAA;AAAA,IAGD,aAAa,OAAO;AAClB,WAAK,gBAAgB;AAAA,QACnB,cAAc;AAAA,QACd,UAAU;AAAA,QACV;AAAA,MACD;AAAA,IACF;AAAA;AAAA,IAGD,oBAAoB;AAClB,YAAM,MAAM,KAAK,IAAK;AAGtB,iBAAW,CAAC,KAAK,KAAK,KAAK,KAAK,aAAa,WAAW;AACtD,YAAI,MAAM,MAAM,YAAY,KAAK,YAAY,SAAS;AACpD,eAAK,aAAa,OAAO,GAAG;AAAA,QAC7B;AAAA,MACF;AAGD,aAAO,KAAK,KAAK,YAAY,EAAE,QAAQ,UAAQ;AAC7C,cAAM,QAAQ,KAAK,aAAa,IAAI;AACpC,YAAI,MAAM,aAAa,MAAM,MAAM,YAAY,KAAK,YAAY,SAAS;AACvE,cAAI,OAAO,UAAU,YAAY,MAAM,MAAM;AAE3C,iBAAK,aAAa,IAAI,IAAI,EAAE,MAAM,CAAE,GAAE,WAAW,GAAG,YAAY,EAAG;AAAA,UAC/E,OAAiB;AAEL,mBAAO,KAAK,KAAK,EAAE,QAAQ,aAAW;AACpC,kBAAI,MAAM,OAAO,EAAE,aAAa,MAAM,MAAM,OAAO,EAAE,YAAY,KAAK,YAAY,SAAS;AACzF,sBAAM,OAAO,IAAI,EAAE,MAAM,CAAE,GAAE,WAAW,GAAG,YAAY,EAAG;AAAA,cAC3D;AAAA,YACf,CAAa;AAAA,UACF;AAAA,QACF;AAAA,MACT,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,QAAQ;AACN,WAAK,kBAAmB;AACxB,WAAK,SAAS;AAAA,QACZ,KAAK,CAAE;AAAA,QACP,QAAQ,CAAE;AAAA,QACV,UAAU,CAAE;AAAA,MACb;AACD,WAAK,UAAU;AAAA,QACb,QAAQ,CAAE;AAAA,QACV,aAAa,CAAE;AAAA,MAChB;AACD,WAAK,cAAc,CAAE;AACrB,WAAK,eAAe,CAAE;AACtB,WAAK,aAAa,MAAO;AACzB,WAAK,gBAAgB,MAAO;AAC5B,WAAK,gBAAgB;AAAA,QACnB,cAAc;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACH,CAAC;;"}