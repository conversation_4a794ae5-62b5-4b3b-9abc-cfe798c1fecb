"use strict";
const common_vendor = require("../../../common/vendor.js");
const mixins_SendVerifyCode = require("../../../mixins/SendVerifyCode.js");
const mixins_color = require("../../../mixins/color.js");
const api_api = require("../../../api/api.js");
const api_user = require("../../../api/user.js");
const api_social = require("../../../api/social.js");
const api_public = require("../../../api/public.js");
const libs_routine = require("../../../libs/routine.js");
const utils_cache = require("../../../utils/cache.js");
const common_assets = require("../../../common/assets.js");
const app = getApp();
let statusBarHeight = common_vendor.index.getSystemInfoSync().statusBarHeight + "px";
const editUserModal = () => "../../../components/eidtUserModal/index.js";
const privacyAgreementPopup = () => "../../../components/privacyAgreementPopup/index.js";
const Verify = () => "../components/verify/index.js";
const _sfc_main = {
  mixins: [mixins_SendVerifyCode.sendVerifyCode, mixins_color.colors],
  components: {
    Verify,
    editUserModal,
    privacyAgreementPopup
  },
  data() {
    return {
      statusBarHeight,
      pageType: 1,
      // 0 登录 1 绑定手机
      phone: "",
      captcha: "",
      text: "获取验证码",
      isShow: false,
      protocol: false,
      inAnimation: false,
      authKey: "",
      backUrl: "",
      pageTitle: "绑定手机号",
      configData: utils_cache.Cache.get("BASIC_CONFIG"),
      canGetPrivacySetting: false
    };
  },
  onLoad(options) {
    if (options.authKey) {
      this.authKey = options.authKey;
    }
    if (common_vendor.wx$1.getPrivacySetting) {
      this.canGetPrivacySetting = true;
    }
    this.backUrl = options.backUrl || "";
    if (options.pageType) {
      this.pageType = options.pageType || 1;
      this.pageTitle = options.pageType == 1 ? "绑定手机号" : "手机号登录";
    }
    let pages = getCurrentPages();
    let prePage = pages[pages.length - 2];
    if (prePage && prePage.route == "pages/order_addcart/order_addcart") {
      this.isHome = true;
    } else {
      this.isHome = false;
    }
  },
  methods: {
    onAgree() {
      this.protocol = true;
    },
    submitData() {
      if (this.pageType == 0) {
        this.isLogin();
        return;
      }
      if (!this.rules())
        return;
      if (!this.authKey) {
        let key = this.$Cache.get("snsapiKey");
        this.phoneAuth(key);
      } else {
        this.phoneAuth(this.authKey);
      }
    },
    rules() {
      let that = this;
      if (!this.protocol && this.pageType == 0) {
        common_vendor.index.showToast({
          title: this.$t("请先阅读并同意协议"),
          icon: "none",
          duration: 2e3
        });
        return false;
      }
      if (!that.phone) {
        that.$util.Tips({
          title: that.$t(`请填写手机号码`)
        });
        return false;
      }
      if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.phone)) {
        that.$util.Tips({
          title: that.$t(`请输入正确的手机号码`)
        });
        return false;
      }
      if (!that.captcha) {
        return that.$util.Tips({
          title: that.$t(`请填写验证码`)
        });
      }
      return true;
    },
    isLogin() {
      if (!this.rules())
        return;
      common_vendor.index.showLoading({
        title: this.$t(`正在登录中`)
      });
      libs_routine.Routine.getCode().then((code) => {
        api_public.phoneLogin({
          code,
          spread_spid: app.globalData.spid,
          spread_code: app.globalData.code,
          phone: this.phone,
          captcha: this.captcha
        }).then((res) => {
          common_vendor.index.hideLoading();
          let time = res.data.expires_time - this.$Cache.time();
          this.$store.commit("LOGIN", {
            token: res.data.token,
            time
          });
          this.getUserInfo(res.data.bindName);
        }).catch((err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: err,
            icon: "none",
            duration: 2e3
          });
        });
      }).catch((err) => {
        common_vendor.index.__f__("log", "at pages/users/binding_phone/index.vue:217", err);
      });
    },
    phoneAuth(key) {
      common_vendor.index.showLoading({
        title: this.$t(`正在登录中`)
      });
      let met;
      met = api_public.phoneLogin;
      met({
        phone: this.phone,
        captcha: this.captcha,
        key
      }).then((res) => {
        let time = res.data.expires_time - this.$Cache.time();
        this.$store.commit("LOGIN", {
          token: res.data.token,
          time
        });
        this.getUserInfo(res.data.bindName);
      }).catch((error) => {
        common_vendor.index.hideLoading();
        this.$util.Tips({
          title: error
        });
      });
    },
    /**
     * 获取个人用户信息
     */
    getUserInfo(new_user) {
      let that = this;
      api_user.getUserInfo().then((res) => {
        that.userInfo = res.data;
        that.$store.commit("SETUID", res.data.uid);
        api_social.getUserSocialInfo().then((socialRes) => {
          common_vendor.index.hideLoading();
          if (socialRes.data) {
            that.$store.commit("UPDATE_USERINFO", socialRes.data);
          }
          if (new_user) {
            this.isShow = true;
          } else {
            that.$util.Tips({
              title: that.$t(`登录成功`),
              icon: "success"
            }, {
              tab: 3,
              url: this.configData.wechat_auth_switch ? 2 : 1
            });
          }
        }).catch(() => {
          common_vendor.index.hideLoading();
          if (new_user) {
            this.isShow = true;
          } else {
            that.$util.Tips({
              title: that.$t(`登录成功`),
              icon: "success"
            }, {
              tab: 3,
              url: this.configData.wechat_auth_switch ? 2 : 1
            });
          }
        });
      });
    },
    success(data) {
      this.$refs.verify.hide();
      let that = this;
      api_api.verifyCode().then((res) => {
        api_api.registerVerify(that.phone, "reset", res.data.key, this.captchaType, data.captchaVerification).then((res2) => {
          that.$util.Tips({
            title: res2.msg
          });
          that.sendCode();
        }).catch((err) => {
          return that.$util.Tips({
            title: err
          });
        });
      });
    },
    /**
     * 发送验证码
     *
     */
    async code() {
      let that = this;
      if (!that.phone)
        return that.$util.Tips({
          title: that.$t(`请填写手机号码`)
        });
      if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.phone))
        return that.$util.Tips({
          title: that.$t(`请输入正确的手机号码`)
        });
      this.$refs.verify.show();
      return;
    },
    ChangeIsDefault() {
      this.$set(this, "protocol", !this.protocol);
    },
    closeEdit() {
      this.isShow = false;
      this.$util.Tips({
        title: this.$t(`登录成功`),
        icon: "success"
      }, {
        tab: 3,
        url: 2
      });
    },
    editSuccess() {
      this.isShow = false;
    },
    back() {
      common_vendor.index.navigateBack({
        delta: this.configData.wechat_auth_switch ? 2 : 1
      });
    },
    privacy(type) {
      common_vendor.index.navigateTo({
        url: "/pages/users/privacy/index?type=" + type
      });
    }
  }
};
if (!Array) {
  const _component_navbar = common_vendor.resolveComponent("navbar");
  const _component_Verify = common_vendor.resolveComponent("Verify");
  const _component_editUserModal = common_vendor.resolveComponent("editUserModal");
  const _component_privacyAgreementPopup = common_vendor.resolveComponent("privacyAgreementPopup");
  (_component_navbar + _component_Verify + _component_editUserModal + _component_privacyAgreementPopup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.pageType == 1 ? _ctx.$t("绑定手机号") : _ctx.$t("手机号登录")),
    b: common_vendor.t($data.pageType == 1 ? _ctx.$t("登录注册需绑定手机号") : _ctx.$t("首次登录会自动注册")),
    c: common_assets._imports_0$18,
    d: _ctx.$t(`填写手机号码`),
    e: $data.phone,
    f: common_vendor.o(($event) => $data.phone = $event.detail.value),
    g: common_assets._imports_1$18,
    h: _ctx.$t(`填写验证码`),
    i: $data.captcha,
    j: common_vendor.o(($event) => $data.captcha = $event.detail.value),
    k: common_vendor.t($data.text),
    l: common_vendor.n(_ctx.disabled ? "active" : ""),
    m: common_vendor.o((...args) => $options.code && $options.code(...args)),
    n: $data.pageType == 0
  }, $data.pageType == 0 ? {
    o: common_vendor.t(_ctx.$t(`首次登录将自动创建账号`))
  } : {}, {
    p: common_vendor.t(_ctx.$t(`${$data.pageType == 1 ? "绑定手机号" : "立即登录"}`)),
    q: common_vendor.o((...args) => $options.submitData && $options.submitData(...args)),
    r: $data.pageType == 0 && !$data.canGetPrivacySetting
  }, $data.pageType == 0 && !$data.canGetPrivacySetting ? {
    s: common_vendor.n($data.inAnimation ? "trembling" : ""),
    t: common_vendor.o(($event) => $data.inAnimation = false),
    v: $data.protocol ? true : false,
    w: common_vendor.t(_ctx.$t(`已阅读并同意`)),
    x: common_vendor.t(_ctx.$t(`《用户协议》`)),
    y: common_vendor.o(($event) => $options.privacy(4)),
    z: common_vendor.t(_ctx.$t(`与`)),
    A: common_vendor.t(_ctx.$t(`《隐私协议》`)),
    B: common_vendor.o(($event) => $options.privacy(3)),
    C: common_vendor.o((...args) => $options.ChangeIsDefault && $options.ChangeIsDefault(...args))
  } : {}, {
    D: $data.statusBarHeight + "px",
    E: common_vendor.sr("verify", "79441b7e-1"),
    F: common_vendor.o($options.success),
    G: common_vendor.p({
      captchaType: "clickWord",
      imgSize: {
        width: "330px",
        height: "155px"
      }
    }),
    H: common_vendor.o($options.closeEdit),
    I: common_vendor.o($options.editSuccess),
    J: common_vendor.p({
      isShow: $data.isShow
    }),
    K: $data.canGetPrivacySetting
  }, $data.canGetPrivacySetting ? {
    L: common_vendor.o(_ctx.onReject),
    M: common_vendor.o($options.onAgree)
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/users/binding_phone/index.js.map
