"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const lazyImage = () => "../../components/lazyImage/lazyImage.js";
const uniLoadMore = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const app = getApp();
const _sfc_main = {
  components: {
    lazyImage,
    uniLoadMore
  },
  data() {
    return {
      statusBarHeight: this.$store.state.statusBarHeight || 20,
      titleBarHeight: this.$store.state.titleBarHeight || 44,
      barList: ["全部", "我的"],
      barIdx: 0,
      list: [],
      idx: 0,
      page: 1,
      isThrottling: true,
      isEmpty: false,
      loadStatus: "loading",
      refundQuantity: "",
      refundReason: "",
      tipsTitle: ""
    };
  },
  onLoad(option) {
    if (option.type) {
      this.barIdx = option.type;
    }
    this.activityList();
  },
  methods: {
    activityList() {
      let that = this;
      setTimeout(() => {
        that.isThrottling = true;
        that.loadStatus = "more";
        let data = {
          data: []
        };
        if (that.barIdx == 0) {
          data.data = [
            {
              id: 1,
              name: "夏季摄影大赛",
              img: "/static/img/avatar.png",
              activity_time: "2023-07-20 14:00-17:00",
              adds_name: "市中心广场",
              status_str: "报名中",
              is_join: false,
              browse: 123,
              user_count: 10,
              avatar_list: ["/static/img/avatar.png", "/static/img/avatar.png"]
            },
            {
              id: 2,
              name: "手工DIY工作坊",
              img: "/static/img/avatar.png",
              activity_time: "2023-07-25 10:00-12:00",
              adds_name: "文创园区5号楼",
              status_str: "报名中",
              is_join: true,
              browse: 89,
              user_count: 15,
              avatar_list: ["/static/img/avatar.png", "/static/img/avatar.png"]
            }
          ];
        } else {
          data.data = [
            {
              id: 2,
              name: "手工DIY工作坊",
              img: "/static/img/avatar.png",
              activity_time: "2023-07-25 10:00-12:00",
              adds_name: "文创园区5号楼",
              status_str: "已报名",
              is_join: true,
              product_status: 1,
              order_id: "ORDER12345",
              activity_product_name: "DIY工作坊标准票",
              quantity: 2,
              code: ["TICKET123456", "TICKET123457"],
              avatar_list: ["/static/img/avatar.png", "/static/img/avatar.png"]
            }
          ];
        }
        if (data.data.length > 0) {
          if (that.page == 1) {
            that.list = data.data;
          } else {
            that.list = that.list.concat(data.data);
          }
          that.isEmpty = false;
        } else if (that.page == 1) {
          that.list = [];
          that.isEmpty = true;
        }
        if (that.page > 1) {
          that.loadStatus = "noMore";
        }
      }, 500);
    },
    delActivity(e) {
      let that = this;
      let idx = e.currentTarget.dataset.idx;
      common_vendor.index.showModal({
        content: "确定要永久删除该活动吗？",
        confirmColor: "#FA5150",
        success: function(res) {
          if (res.confirm) {
            app.globalData.isCenterPage = true;
            that.opTipsPopup("删除成功");
            that.list.splice(idx, 1);
            if (that.list.length <= 0) {
              that.isEmpty = true;
              that.loadStatus = "more";
            }
          }
        }
      });
    },
    activityRefund() {
      let that = this;
      if (!that.refundQuantity || that.refundQuantity < 1) {
        return that.opTipsPopup("请填写需要售后的票数");
      }
      if (!that.refundReason) {
        return that.opTipsPopup("请填写售后原因");
      }
      setTimeout(() => {
        that.opTipsPopup("售后申请已提交，等待审核");
        that.list[that.idx].product_status = 3;
        that.$refs.refundPopup.close();
      }, 500);
    },
    barClick(e) {
      if (this.isThrottling) {
        this.isThrottling = false;
        let idx = e.currentTarget.dataset.idx;
        this.barIdx = idx;
        this.page = 1;
        this.activityList();
      }
    },
    toActivityDetails(e) {
      let id = e.currentTarget.dataset.id;
      common_vendor.index.navigateTo({
        url: "/pages/activity/details?id=" + id
      });
    },
    refundClick(show, idx = 0) {
      if (!show) {
        this.$refs.refundPopup.close();
      } else {
        this.idx = idx;
        this.$refs.refundPopup.open();
      }
    },
    ticketClick(show, idx = 0) {
      if (!show) {
        this.$refs.ticketPopup.close();
      } else {
        this.idx = idx;
        this.$refs.ticketPopup.open();
      }
    },
    opTipsPopup(msg) {
      let that = this;
      that.tipsTitle = msg;
      that.$refs.tipsPopup.open();
      setTimeout(function() {
        that.$refs.tipsPopup.close();
      }, 2e3);
    },
    navBack() {
      if (getCurrentPages().length > 1) {
        common_vendor.index.navigateBack();
      } else {
        common_vendor.index.switchTab({
          url: "/pages/index/index"
        });
      }
    }
  },
  onReachBottom() {
    if (this.list.length && this.loadStatus !== "noMore") {
      this.page = this.page + 1;
      this.loadStatus = "loading";
      this.activityList();
    }
  }
};
if (!Array) {
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  const _component_lazy_image = common_vendor.resolveComponent("lazy-image");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_easycom_uni_load_more2 + _component_lazy_image + _easycom_uni_popup2)();
}
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_load_more + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: common_vendor.o((...args) => $options.navBack && $options.navBack(...args)),
    c: $data.titleBarHeight + "px",
    d: common_vendor.f($data.barList, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index == $data.barIdx ? "#000" : "#999",
        c: index == $data.barIdx ? "28rpx" : "26rpx",
        d: index == $data.barIdx ? 1 : 0,
        e: index,
        f: common_vendor.o((...args) => $options.barClick && $options.barClick(...args), index),
        g: index
      };
    }),
    e: $data.statusBarHeight + "px",
    f: common_vendor.p({
      status: "loading"
    }),
    g: $data.isThrottling || $data.loadStatus == "loading" ? "0px" : "60rpx",
    h: $data.isEmpty
  }, $data.isEmpty ? {
    i: common_assets._imports_1$5,
    j: common_vendor.t($data.barIdx == 0 ? "暂无推荐活动" : "暂无参加活动")
  } : {
    k: common_vendor.f($data.list, (item, index, i0) => {
      return common_vendor.e({
        a: "62f64818-1-" + i0,
        b: common_vendor.p({
          src: item.img
        }),
        c: common_vendor.t(item.status_str ? item.status_str : "加载中"),
        d: common_vendor.o((...args) => $options.toActivityDetails && $options.toActivityDetails(...args), index),
        e: item.id,
        f: common_vendor.t(item.name ? item.name : "活动名称加载中"),
        g: common_vendor.o((...args) => $options.toActivityDetails && $options.toActivityDetails(...args), index),
        h: item.id,
        i: common_vendor.t(item.activity_time ? item.activity_time : "活动时间加载中"),
        j: common_vendor.o((...args) => $options.toActivityDetails && $options.toActivityDetails(...args), index),
        k: item.id,
        l: common_vendor.t(item.adds_name ? item.adds_name : "活动地址加载中"),
        m: common_vendor.o((...args) => $options.toActivityDetails && $options.toActivityDetails(...args), index),
        n: item.id,
        o: item.user_count
      }, item.user_count ? {
        p: common_vendor.f(item.avatar_list, (img, imgIndex, i1) => {
          return {
            a: img,
            b: imgIndex
          };
        }),
        q: common_vendor.t(item.user_count),
        r: common_vendor.o((...args) => $options.toActivityDetails && $options.toActivityDetails(...args), index),
        s: item.id
      } : {
        t: common_vendor.t(item.browse),
        v: common_vendor.o((...args) => $options.toActivityDetails && $options.toActivityDetails(...args), index),
        w: item.id
      }, $data.barIdx == 0 ? {
        x: common_vendor.t(item.is_join ? "查看详情" : "立即参加"),
        y: common_assets._imports_2$4,
        z: common_vendor.o((...args) => $options.toActivityDetails && $options.toActivityDetails(...args), index),
        A: item.id
      } : common_vendor.e({
        B: common_assets._imports_0$8,
        C: item.name,
        D: "/pages/activity/details?id=" + item.id,
        E: item.img,
        F: item.product_status == 1 || item.product_status == 3
      }, item.product_status == 1 || item.product_status == 3 ? common_vendor.e({
        G: item.product_status == 1
      }, item.product_status == 1 ? {} : {}, {
        H: common_vendor.o(($event) => $options.refundClick(true, index), index),
        I: common_vendor.o(($event) => $options.ticketClick(true, index), index)
      }) : item.product_status == 2 ? {
        K: common_assets._imports_6$1,
        L: common_vendor.o((...args) => $options.delActivity && $options.delActivity(...args), index),
        M: index,
        N: common_vendor.o(($event) => $options.ticketClick(true, index), index)
      } : {
        O: common_assets._imports_6$1,
        P: common_vendor.o((...args) => $options.delActivity && $options.delActivity(...args), index),
        Q: index
      }, {
        J: item.product_status == 2
      }), {
        R: index
      });
    }),
    l: common_assets._imports_1$6,
    m: common_assets._imports_5$2,
    n: $data.barIdx == 0
  }, {
    o: common_vendor.p({
      status: $data.loadStatus
    }),
    p: "calc(" + ($data.statusBarHeight + $data.titleBarHeight) + "px + 90rpx)",
    q: common_vendor.t($data.list[$data.idx] && $data.list[$data.idx].activity_product_name),
    r: common_assets._imports_0$4,
    s: common_vendor.o(($event) => $options.refundClick(false)),
    t: $data.list[$data.idx] && $data.list[$data.idx].quantity,
    v: "售后票数（最多" + ($data.list[$data.idx] && $data.list[$data.idx].quantity) + "张）",
    w: $data.refundQuantity,
    x: common_vendor.o(($event) => $data.refundQuantity = $event.detail.value),
    y: $data.refundReason,
    z: common_vendor.o(($event) => $data.refundReason = $event.detail.value),
    A: $data.list[$data.idx] && $data.list[$data.idx].product_status == 3
  }, $data.list[$data.idx] && $data.list[$data.idx].product_status == 3 ? {} : {}, {
    B: common_vendor.o(($event) => $options.refundClick(false)),
    C: common_vendor.o((...args) => $options.activityRefund && $options.activityRefund(...args)),
    D: common_vendor.sr("refundPopup", "62f64818-3"),
    E: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    }),
    F: common_vendor.t($data.list[$data.idx] && $data.list[$data.idx].activity_product_name),
    G: common_vendor.t($data.list[$data.idx] && $data.list[$data.idx].quantity),
    H: common_assets._imports_0$4,
    I: common_vendor.o(($event) => $options.ticketClick(false)),
    J: common_vendor.f($data.list[$data.idx] && $data.list[$data.idx].code, (code, codeIndex, i0) => {
      return common_vendor.e({
        a: common_vendor.t(code)
      }, $data.list[$data.idx] && $data.list[$data.idx].product_status != 1 ? {
        b: common_vendor.t($data.list[$data.idx] && $data.list[$data.idx].product_status == 2 ? "（已使用）" : $data.list[$data.idx] && $data.list[$data.idx].product_status == 3 ? "（售后中）" : "（已取消）")
      } : {}, {
        c: codeIndex
      });
    }),
    K: $data.list[$data.idx] && $data.list[$data.idx].product_status != 1,
    L: common_vendor.n($data.list[$data.idx] && $data.list[$data.idx].product_status != 1 && "item-active"),
    M: common_vendor.o(($event) => $options.ticketClick(false)),
    N: common_vendor.sr("ticketPopup", "62f64818-4"),
    O: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    }),
    P: common_vendor.t($data.tipsTitle),
    Q: common_vendor.sr("tipsPopup", "62f64818-5"),
    R: common_vendor.p({
      type: "top",
      ["mask-background-color"]: "rgba(0, 0, 0, 0)"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/activity/index.js.map
