---
type: "always_apply"
---

根据用户的文件路径判断是修改什么地方。我询问新的问题后代表修复完成，回答新问题前先删除之前的调试信息。
/crmeb/文件夹是CRMEB开源商城框架，开发需要遵循开发规范。PHP 版本PHP 7.1 ~ 7.4，数据库MySQL 5.7，开发规范https://doc.crmeb.com/single_open/open_v54/19949
/vue3/文件夹是基于uniapp开发的前端代码VUE3，需要用Composition API，能够兼容H5、小程序、APP。记住每次修改都需要考虑编译成H5、小程序、APP后的兼容性。UNIAPP官方文档：https://uniapp.dcloud.net.cn/tutorial/

小程序公共组件用法：
1、数据为空的用法
<!-- 基础用法 -->
<emptyPage title="暂无笔记内容" description="还没有发布过笔记" />

<!-- 自定义图片和按钮 -->
<emptyPage 
  title="暂无收藏" 
  description="快去收藏喜欢的内容吧"
  image="/static/img/favorite-empty.png"
  buttonText="去发现"
  @buttonClick="goToDiscover"
/>
2、缓存问题
Vuex store 使用 state.userInfo 和 state.uid
Pinia store 使用 userInfo.uid
本地缓存使用 USER_INFO 和 UID 分别存储