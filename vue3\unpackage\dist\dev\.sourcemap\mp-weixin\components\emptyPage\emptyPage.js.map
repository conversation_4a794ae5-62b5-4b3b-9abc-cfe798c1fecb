{"version": 3, "file": "emptyPage.js", "sources": ["components/emptyPage/emptyPage.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/WjovV1dXL3NoZWppYW8vdnVlMy9jb21wb25lbnRzL2VtcHR5UGFnZS9lbXB0eVBhZ2UudnVl"], "sourcesContent": ["<template>\n  <view class=\"empty-page\">\n    <view class=\"empty-content\">\n      <image\n        :src=\"image\"\n        mode=\"aspectFit\"\n        class=\"empty-image\"\n        :style=\"{ width: imageSize, height: imageSize }\"\n      />\n      <view class=\"empty-title\">{{ title }}</view>\n      <view v-if=\"description\" class=\"empty-description\">{{ description }}</view>\n      <view v-if=\"buttonText\" class=\"empty-button\" @tap=\"handleButtonClick\">\n        {{ buttonText }}\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'EmptyPage',\n  props: {\n    // 标题\n    title: {\n      type: String,\n      default: '暂无数据'\n    },\n    // 描述文字\n    description: {\n      type: String,\n      default: ''\n    },\n    // 图片地址\n    image: {\n      type: String,\n      default: '/static/img/empty.png'\n    },\n    // 图片尺寸\n    imageSize: {\n      type: String,\n      default: '300rpx'\n    },\n    // 按钮文字\n    buttonText: {\n      type: String,\n      default: ''\n    }\n  },\n  methods: {\n    handleButtonClick() {\n      this.$emit('buttonClick');\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 基础样式 - 参考 details.vue 的 empty-box 样式 */\n.empty-page {\n  width: 100%;\n  padding: 100rpx 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-direction: column;\n}\n\n.empty-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  width: 100%;\n}\n\n/* 图片样式 - 参考 details.vue */\n.empty-image {\n  width: 300rpx;\n  height: 300rpx;\n  margin-bottom: 30rpx;\n  opacity: 0.8;\n}\n\n/* 标题样式 - 参考 details.vue 的 .e1 */\n.empty-title {\n  font-size: 30rpx;\n  font-weight: 700;\n  color: #333;\n  margin-bottom: 10rpx;\n  line-height: 1.4;\n}\n\n/* 描述样式 - 参考 details.vue 的 .e2 */\n.empty-description {\n  margin-top: 10rpx;\n  color: #999;\n  font-size: 26rpx;\n  line-height: 1.5;\n  margin-bottom: 40rpx;\n  max-width: 500rpx;\n  text-align: center;\n}\n\n/* 按钮样式 - 参考 details.vue 的 retry-btn */\n.empty-button {\n  margin-top: 40rpx;\n  width: 200rpx;\n  height: 80rpx;\n  line-height: 80rpx;\n  text-align: center;\n  font-size: 28rpx;\n  font-weight: 700;\n  color: #fff;\n  background: #007aff;\n  border-radius: 40rpx;\n  transition: all 0.3s ease;\n}\n\n.empty-button:active {\n  background: #0056cc;\n  transform: scale(0.95);\n}\n\n/* 工具类 */\n.df {\n  display: flex;\n  align-items: center;\n}\n\n/* 深色模式适配 */\n@media (prefers-color-scheme: dark) {\n  .empty-title {\n    color: #fff;\n  }\n\n  .empty-description {\n    color: #ccc;\n  }\n\n  .empty-page {\n    background-color: #1a1a1a;\n  }\n}\n\n/* 响应式适配 */\n@media screen and (max-width: 750rpx) {\n  .empty-image {\n    width: 250rpx;\n    height: 250rpx;\n  }\n\n  .empty-title {\n    font-size: 28rpx;\n  }\n\n  .empty-description {\n    font-size: 24rpx;\n    padding: 0 40rpx;\n  }\n}\n\n/* 小程序兼容性 */\n/* #ifdef MP */\n.empty-page {\n  box-sizing: border-box;\n}\n/* #endif */\n\n/* H5 兼容性 */\n/* #ifdef H5 */\n.empty-button {\n  cursor: pointer;\n  user-select: none;\n}\n/* #endif */\n</style>\n", "import Component from 'Z:/WWW/shejiao/vue3/components/emptyPage/emptyPage.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AAmBA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA;AAAA,IAEL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA;AAAA,IAED,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA;AAAA,IAED,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA;AAAA,IAED,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA;AAAA,IAED,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,oBAAoB;AAClB,WAAK,MAAM,aAAa;AAAA,IAC1B;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;ACpDA,GAAG,gBAAgB,SAAS;"}