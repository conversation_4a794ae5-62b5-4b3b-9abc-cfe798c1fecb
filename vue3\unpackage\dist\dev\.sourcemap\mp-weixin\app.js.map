{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script setup>\nimport { reactive, computed, watch, getCurrentInstance } from 'vue'\nimport { onLaunch, onShow, onHide } from '@dcloudio/uni-app'\nimport { useStore } from 'vuex'\nimport appConfig from './config/app.js'\nconst { HTTP_REQUEST_URL } = appConfig\nimport { basicConfig, remoteRegister } from '@/api/public'\nimport Routine from './libs/routine.js'\nimport { silenceBindingSpread, initWorkermanUrl } from '@/utils'\nimport { colorChange, getCrmebCopyRight } from '@/api/api.js'\nimport { getLangJson, getLangVersion } from '@/api/user.js'\nimport Cache from '@/utils/cache'\nimport themeList from '@/utils/theme'\n\n// 获取当前实例和store\nconst instance = getCurrentInstance()\nconst store = useStore()\n\n// 应用配置 - 根据uni-app最佳实践\nconst APP_CONFIG = {\n  name: 'CRMEB社交电商',\n  version: '3.0.0',\n  debug: process.env.NODE_ENV === 'development',\n  // 性能监控配置\n  performance: {\n    enableMonitoring: true,\n    reportInterval: 30000 // 30秒上报一次\n  },\n  // 错误处理配置\n  errorHandling: {\n    enableReporting: true,\n    maxRetries: 3\n  },\n  // getApp()支持配置\n  getAppSupport: {\n    enableGlobalMethods: true,\n    enablePerformanceMonitor: true,\n    enableDebugInfo: true\n  }\n}\n\n// 全局数据 - 使用更清晰的结构\nconst globalData = reactive({\n\t// 用户相关\n\tuser: {\n\t\tspid: 0,\n\t\tisLogin: false,\n\t\tuserInfo: {},\n\t\tlocale: ''\n\t},\n\t// 应用状态\n\tapp: {\n\t\tcode: 0,\n\t\tisIframe: false,\n\t\ttabbarShow: true,\n\t\twindowHeight: 0,\n\t\tnavHeight: 0,\n\t\tnavH: 0\n\t},\n\t// 菜单和配置\n\tconfig: {\n\t\tMyMenus: [],\n\t\tbasicConfig: null,\n\t\tcolorStatus: null,\n\t\tviewColor: null\n\t}\n})\n\n// 计算属性\nconst isLogin = computed(() => store.getters.isLogin)\nconst cartNum = computed(() => store.getters.cartNum)\n\n// 性能监控\nconst performanceMonitor = {\n\tstartTime: Date.now(),\n\tmetrics: reactive({\n\t\tappLaunchTime: 0,\n\t\tfirstPageLoadTime: 0,\n\t\tapiCallCount: 0,\n\t\terrorCount: 0\n\t}),\n\n\t// 记录应用启动时间\n\trecordLaunchTime() {\n\t\tthis.metrics.appLaunchTime = Date.now() - this.startTime\n\t},\n\n\t// 记录首页加载时间\n\trecordFirstPageLoad() {\n\t\tthis.metrics.firstPageLoadTime = Date.now() - this.startTime\n\t},\n\n\t// 增加API调用计数\n\tincrementApiCall() {\n\t\tthis.metrics.apiCallCount++\n\t},\n\n\t// 增加错误计数\n\tincrementError() {\n\t\tthis.metrics.errorCount++\n\t}\n}\n\n// 错误处理函数\nconst handleError = (error, context = '') => {\n\t// 增加错误计数\n\tperformanceMonitor.incrementError()\n\n\tif (APP_CONFIG.debug) {\n\t\tconsole.error(`[${context}] Error:`, error)\n\t}\n\n\t// 错误上报\n\tif (APP_CONFIG.errorHandling.enableReporting) {\n\t\treportError(error, context)\n\t}\n}\n\n// 错误上报函数\nconst reportError = (error, context) => {\n\ttry {\n\t\t// 这里可以集成第三方错误监控服务\n\t\tconst errorInfo = {\n\t\t\tmessage: error.message || String(error),\n\t\t\tstack: error.stack,\n\t\t\tcontext,\n\t\t\ttimestamp: Date.now(),\n\t\t\tuserAgent: navigator?.userAgent || 'unknown',\n\t\t\turl: window?.location?.href || 'app',\n\t\t\tuserId: globalData.user.userInfo?.uid || 'anonymous'\n\t\t}\n\n\t\t// 可以发送到错误监控服务\n\t\t// errorReportingService.report(errorInfo)\n\n\t\tif (APP_CONFIG.debug) {\n\t\t\tconsole.log('Error reported:', errorInfo)\n\t\t}\n\t} catch (reportError) {\n\t\tif (APP_CONFIG.debug) {\n\t\t\tconsole.error('Failed to report error:', reportError)\n\t\t}\n\t}\n}\n\n// 监听器 - 登录状态变化\nwatch(isLogin, (newValue, oldValue) => {\n\ttry {\n\t\tglobalData.user.isLogin = newValue\n\t\tif (newValue) {\n\t\t\t// 登录成功后的处理\n\t\t\t// this.getCartNum()\n\t\t} else {\n\t\t\t// 退出登录后清理数据\n\t\t\tstore.commit('indexData/setCartNum', '')\n\t\t\tglobalData.user.userInfo = {}\n\t\t}\n\t} catch (error) {\n\t\thandleError(error, 'Login Status Watch')\n\t}\n}, { immediate: true })\n\n// 监听器 - 购物车数量变化\nwatch(cartNum, (newCart) => {\n\ttry {\n\t\tconst cartCount = Number(newCart) || 0\n\t\tstore.commit('indexData/setCartNum', cartCount.toString())\n\n\t\t// 通知自定义tabbar更新购物车数量\n\t\tuni.$emit('updateCartBadge', {\n\t\t\tcount: cartCount,\n\t\t\tshow: cartCount > 0\n\t\t})\n\t} catch (error) {\n\t\thandleError(error, 'Cart Number Watch')\n\t}\n}, { immediate: true })\n\n// 处理推广参数\nconst handleSpreadParams = (queryData) => {\n\ttry {\n\t\t// 处理spread参数\n\t\tif (queryData.query.spread) {\n\t\t\tCache.set('spread', queryData.query.spread)\n\t\t\tglobalData.user.spid = queryData.query.spread\n\t\t\tglobalData.user.pid = queryData.query.spread\n\t\t\tsilenceBindingSpread(globalData)\n\t\t}\n\n\t\t// 处理spid参数\n\t\tif (queryData.query.spid) {\n\t\t\tCache.set('spread', queryData.query.spid)\n\t\t\tglobalData.user.spid = queryData.query.spid\n\t\t\tglobalData.user.pid = queryData.query.spid\n\t\t\tsilenceBindingSpread(globalData)\n\t\t}\n\t} catch (error) {\n\t\thandleError(error, 'Handle Spread Params')\n\t}\n}\n\n// 处理小程序场景值\nconst handleMiniProgramScene = (queryData) => {\n\t// #ifdef MP\n\ttry {\n\t\tif (queryData.query.scene) {\n\t\t\tconst util = instance.appContext.config.globalProperties.$util\n\t\t\tconst param = util.getUrlParams(decodeURIComponent(queryData.query.scene))\n\n\t\t\tif (param.pid) {\n\t\t\t\tCache.set('spread', param.pid)\n\t\t\t\tglobalData.user.spid = param.pid\n\t\t\t\tglobalData.user.pid = param.pid\n\t\t\t} else {\n\t\t\t\t// 处理不同的小程序场景值\n\t\t\t\tconst sceneHandlers = {\n\t\t\t\t\t1047: () => globalData.app.code = queryData.query.scene, // 扫描小程序码\n\t\t\t\t\t1048: () => globalData.app.code = queryData.query.scene, // 长按图片识别小程序码\n\t\t\t\t\t1049: () => globalData.app.code = queryData.query.scene, // 手机相册选取小程序码\n\t\t\t\t\t1001: () => globalData.user.spid = queryData.query.scene  // 直接进入小程序\n\t\t\t\t}\n\n\t\t\t\tconst handler = sceneHandlers[queryData.scene]\n\t\t\t\tif (handler) {\n\t\t\t\t\thandler()\n\t\t\t\t}\n\t\t\t}\n\t\t\tsilenceBindingSpread(globalData)\n\t\t}\n\t} catch (error) {\n\t\thandleError(error, 'Handle MiniProgram Scene')\n\t}\n\t// #endif\n}\n\n// onShow 生命周期\nconst handleShow = () => {\n\ttry {\n\t\tconst queryData = uni.getEnterOptionsSync() // uni-app版本 3.5.1+ 支持\n\n\t\t// 处理推广参数\n\t\thandleSpreadParams(queryData)\n\n\t\t// 处理小程序场景值\n\t\thandleMiniProgramScene(queryData)\n\n\t} catch (error) {\n\t\thandleError(error, 'App Show')\n\t}\n}\n\n// 初始化基础配置\nconst initBasicConfig = async () => {\n\ttry {\n\t\tconst res = await basicConfig()\n\t\tglobalData.config.basicConfig = res.data\n\t\tuni.setStorageSync('BASIC_CONFIG', res.data)\n\t\treturn res.data\n\t} catch (error) {\n\t\thandleError(error, 'Init Basic Config')\n\t\treturn null\n\t}\n}\n\n// 初始化主题配置\nconst initThemeConfig = async () => {\n\ttry {\n\t\tconst res = await colorChange('color_change')\n\t\tconst themeMap = {\n\t\t\t1: 'blue',\n\t\t\t2: 'green',\n\t\t\t3: 'red',\n\t\t\t4: 'pink',\n\t\t\t5: 'orange'\n\t\t}\n\n\t\tconst status = res.data.status\n\t\tconst themeKey = themeMap[status] || 'red'\n\t\tconst selectedTheme = themeList[themeKey]\n\n\t\t// 保存配置\n\t\tuni.setStorageSync('is_diy', res.data.is_diy)\n\t\tuni.setStorageSync('color_status', status)\n\t\tuni.setStorageSync('viewColor', selectedTheme)\n\n\t\t// 更新全局数据\n\t\tglobalData.config.colorStatus = status\n\t\tglobalData.config.viewColor = selectedTheme\n\n\t\t// 发送事件通知\n\t\tuni.$emit('is_diy', res.data.is_diy)\n\t\tuni.$emit('ok', selectedTheme, status)\n\n\t\treturn { status, selectedTheme }\n\t} catch (error) {\n\t\thandleError(error, 'Init Theme Config')\n\t\treturn null\n\t}\n}\n\n// 处理H5特殊逻辑\nconst handleH5Logic = (option) => {\n\t// #ifdef H5\n\ttry {\n\t\t// 检查是否为iframe模式\n\t\tglobalData.app.isIframe = option.query?.mdType === 'iframeWindow'\n\n\t\t// 处理远程登录\n\t\tif (!isLogin.value && option.query?.remote_token) {\n\t\t\thandleRemoteRegister(option.query.remote_token)\n\t\t}\n\t} catch (error) {\n\t\thandleError(error, 'Handle H5 Logic')\n\t}\n\t// #endif\n}\n\n// onLaunch 生命周期\nconst handleLaunch = async (option) => {\n\ttry {\n\t\t// 初始化Workerman URL，避免循环依赖问题\n\t\tinitWorkermanUrl()\n\n\t\t// 并行初始化基础配置\n\t\tconst configPromises = [\n\t\t\tinitBasicConfig(),\n\t\t\tinitThemeConfig()\n\t\t]\n\n\t\t// 处理H5特殊逻辑\n\t\thandleH5Logic(option)\n\n\t\t// 等待基础配置完成\n\t\tawait Promise.allSettled(configPromises)\n\n\t\t// 初始化语言配置\n\t\tawait initLanguageConfig()\n\n\t\t// 初始化系统信息\n\t\tinitSystemInfo()\n\n\t\t// 处理平台特定逻辑\n\t\tawait handlePlatformSpecificLogic(option)\n\n\t\t// 获取版权信息\n\t\tinitCopyRight()\n\n\t\t// 记录应用启动完成时间\n\t\tperformanceMonitor.recordLaunchTime()\n\n\t\t// 启动性能监控\n\t\tif (APP_CONFIG.performance.enableMonitoring) {\n\t\t\tstartPerformanceMonitoring()\n\t\t}\n\n\t\t// 设置全局错误处理\n\t\tsetupGlobalErrorHandling()\n\n\t} catch (error) {\n\t\thandleError(error, 'App Launch')\n\t}\n}\n\n// 启动性能监控\nconst startPerformanceMonitoring = () => {\n\tif (!APP_CONFIG.performance.enableMonitoring) return\n\n\t// 定期上报性能数据\n\tsetInterval(() => {\n\t\tconst metrics = {\n\t\t\t...performanceMonitor.metrics,\n\t\t\tmemoryUsage: getMemoryUsage(),\n\t\t\ttimestamp: Date.now()\n\t\t}\n\n\t\tif (APP_CONFIG.debug) {\n\t\t\tconsole.log('Performance metrics:', metrics)\n\t\t}\n\n\t\t// 这里可以上报到性能监控服务\n\t\t// performanceService.report(metrics)\n\t}, APP_CONFIG.performance.reportInterval)\n}\n\n// 获取内存使用情况\nconst getMemoryUsage = () => {\n\ttry {\n\t\t// #ifdef H5\n\t\tif (performance?.memory) {\n\t\t\treturn {\n\t\t\t\tused: performance.memory.usedJSHeapSize,\n\t\t\t\ttotal: performance.memory.totalJSHeapSize,\n\t\t\t\tlimit: performance.memory.jsHeapSizeLimit\n\t\t\t}\n\t\t}\n\t\t// #endif\n\n\t\t// #ifdef APP-PLUS\n\t\t// 可以通过plus API获取内存信息\n\t\t// #endif\n\n\t\treturn null\n\t} catch (error) {\n\t\treturn null\n\t}\n}\n\n// 设置全局错误处理\nconst setupGlobalErrorHandling = () => {\n\t// #ifdef H5\n\tif (typeof window !== 'undefined') {\n\t\t// 捕获未处理的Promise错误\n\t\twindow.addEventListener('unhandledrejection', (event) => {\n\t\t\thandleError(event.reason, 'Unhandled Promise Rejection')\n\t\t})\n\n\t\t// 捕获全局JavaScript错误\n\t\twindow.addEventListener('error', (event) => {\n\t\t\thandleError(event.error || event.message, 'Global Error')\n\t\t})\n\t}\n\t// #endif\n}\n\n// 初始化语言配置\nconst initLanguageConfig = async () => {\n\ttry {\n\t\tconst res = await getLangVersion()\n\t\tconst version = res.data.version\n\t\tconst currentVersion = uni.getStorageSync('LANG_VERSION')\n\n\t\tif (version !== currentVersion) {\n\t\t\tconst langRes = await getLangJson()\n\t\t\tconst langKey = Object.keys(langRes.data)[0]\n\n\t\t\tCache.set('locale', langKey)\n\t\t\tglobalData.user.locale = langKey\n\n\t\t\t// 设置国际化语言\n\t\t\tconst i18n = instance.appContext.config.globalProperties.$i18n\n\t\t\tif (i18n) {\n\t\t\t\ti18n.setLocaleMessage(langKey, langRes.data[langKey])\n\t\t\t}\n\n\t\t\tuni.setStorageSync('localeJson', langRes.data)\n\t\t}\n\n\t\tuni.setStorageSync('LANG_VERSION', version)\n\t} catch (error) {\n\t\thandleError(error, 'Init Language Config')\n\t}\n}\n\n// 初始化系统信息\nconst initSystemInfo = () => {\n\ttry {\n\t\t// #ifdef APP-PLUS || H5\n\t\tuni.getSystemInfo({\n\t\t\tsuccess: function (res) {\n\t\t\t\t// 设置窗口高度\n\t\t\t\tglobalData.app.windowHeight = res.windowHeight + 'px'\n\n\t\t\t\t// 计算导航高度\n\t\t\t\tglobalData.app.navHeight = res.statusBarHeight * (750 / res.windowWidth) + 91\n\t\t\t},\n\t\t\tfail: (error) => {\n\t\t\t\thandleError(error, 'Get System Info')\n\t\t\t}\n\t\t})\n\t\t// #endif\n\n\t\t// #ifdef MP\n\t\t// 获取菜单按钮信息\n\t\tconst menuButtonInfo = uni.getMenuButtonBoundingClientRect()\n\t\tglobalData.app.navH = menuButtonInfo.top * 2 + menuButtonInfo.height / 2\n\n\t\t// 检查小程序版本\n\t\tconst version = uni.getSystemInfoSync().SDKVersion\n\t\tconst isNewVersion = Routine.compareVersion(version, '2.21.3') >= 0\n\t\tCache.set('MP_VERSION_ISNEW', isNewVersion)\n\t\t// #endif\n\n\t} catch (error) {\n\t\thandleError(error, 'Init System Info')\n\t}\n}\n// 处理平台特定逻辑\nconst handlePlatformSpecificLogic = async (option) => {\n\ttry {\n\t\t// #ifdef MP\n\t\tawait handleMiniProgramLogic(option)\n\t\t// #endif\n\n\t\t// #ifdef H5\n\t\thandleH5SpecificLogic()\n\t\t// #endif\n\t} catch (error) {\n\t\thandleError(error, 'Handle Platform Specific Logic')\n\t}\n}\n\n// 处理小程序特定逻辑\nconst handleMiniProgramLogic = async (option) => {\n\t// #ifdef MP\n\ttry {\n\t\t// 检查配置\n\t\tif (!HTTP_REQUEST_URL) {\n\t\t\tconsole.error(\n\t\t\t\t\"请配置根目录下的config.js文件中的 'HTTP_REQUEST_URL'\\n\\n请修改开发者工具中【详情】->【AppID】改为自己的Appid\\n\\n请前往后台【小程序】->【小程序配置】填写自己的 appId and AppSecret\"\n\t\t\t)\n\t\t\treturn false\n\t\t}\n\n\t\t// 处理小程序更新\n\t\tconst startParamObj = wx.getEnterOptionsSync()\n\t\tif (wx.canIUse('getUpdateManager') && startParamObj.scene !== 1154) {\n\t\t\thandleMiniProgramUpdate()\n\t\t}\n\n\t\treturn true\n\t} catch (error) {\n\t\thandleError(error, 'Handle MiniProgram Logic')\n\t\treturn false\n\t}\n\t// #endif\n}\n\n// 处理小程序更新\nconst handleMiniProgramUpdate = () => {\n\t// #ifdef MP\n\ttry {\n\t\tconst updateManager = wx.getUpdateManager()\n\n\t\tupdateManager.onCheckForUpdate((res) => {\n\t\t\tif (res.hasUpdate) {\n\t\t\t\tupdateManager.onUpdateReady(() => {\n\t\t\t\t\twx.showModal({\n\t\t\t\t\t\ttitle: '更新提示',\n\t\t\t\t\t\tcontent: '新版本已经下载好，是否重启当前应用？',\n\t\t\t\t\t\tsuccess(modalRes) {\n\t\t\t\t\t\t\tif (modalRes.confirm) {\n\t\t\t\t\t\t\t\tupdateManager.applyUpdate()\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t})\n\n\t\t\t\tupdateManager.onUpdateFailed(() => {\n\t\t\t\t\twx.showModal({\n\t\t\t\t\t\ttitle: '发现新版本',\n\t\t\t\t\t\tcontent: '请删除当前小程序，重新搜索打开...'\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t}\n\t\t})\n\t} catch (error) {\n\t\thandleError(error, 'Handle MiniProgram Update')\n\t}\n\t// #endif\n}\n\n// 处理H5特定逻辑\nconst handleH5SpecificLogic = () => {\n\t// #ifdef H5\n\ttry {\n\t\t// 添加统计脚本\n\t\tif (HTTP_REQUEST_URL && typeof document !== 'undefined') {\n\t\t\tconst script = document.createElement('script')\n\t\t\tscript.src = `${HTTP_REQUEST_URL}/api/get_script`\n\t\t\tscript.onerror = () => {\n\t\t\t\tif (APP_CONFIG.debug) {\n\t\t\t\t\tconsole.warn('Failed to load analytics script')\n\t\t\t\t}\n\t\t\t}\n\t\t\tdocument.head.appendChild(script)\n\t\t}\n\t} catch (error) {\n\t\thandleError(error, 'Handle H5 Specific Logic')\n\t}\n\t// #endif\n}\n\n// 初始化版权信息\nconst initCopyRight = async () => {\n\ttry {\n\t\tconst res = await getCrmebCopyRight()\n\t\tuni.setStorageSync('copyRight', res.data)\n\t} catch (error) {\n\t\thandleError(error, 'Init CopyRight')\n\t}\n}\n\n// 远程注册处理\nconst handleRemoteRegister = async (remote_token) => {\n\ttry {\n\t\tconst res = await remoteRegister({ remote_token })\n\t\tconst data = res.data\n\n\t\tif (data.get_remote_login_url) {\n\t\t\t// #ifdef H5\n\t\t\tif (typeof location !== 'undefined') {\n\t\t\t\tlocation.href = data.get_remote_login_url\n\t\t\t}\n\t\t\t// #endif\n\t\t} else {\n\t\t\t// 登录成功\n\t\t\tstore.commit('LOGIN', {\n\t\t\t\ttoken: data.token,\n\t\t\t\ttime: data.expires_time - Cache.time()\n\t\t\t})\n\t\t\tstore.commit('SETUID', data.userInfo.uid)\n\n\t\t\t// 更新全局用户信息\n\t\t\tglobalData.user.isLogin = true\n\t\t\tglobalData.user.userInfo = data.userInfo\n\n\t\t\t// #ifdef H5\n\t\t\tif (typeof location !== 'undefined') {\n\t\t\t\tlocation.reload()\n\t\t\t}\n\t\t\t// #endif\n\t\t}\n\t} catch (error) {\n\t\thandleError(error, 'Remote Register')\n\t}\n}\n\n// onHide 生命周期\nconst handleHide = () => {\n\ttry {\n\t\t// #ifdef H5\n\t\tCache.clear('snsapiKey')\n\t\t// #endif\n\t} catch (error) {\n\t\thandleError(error, 'App Hide')\n\t}\n}\n\n// 应用状态持久化\nconst saveAppState = () => {\n\ttry {\n\t\tconst appState = {\n\t\t\tuser: globalData.user,\n\t\t\tconfig: globalData.config,\n\t\t\ttimestamp: Date.now()\n\t\t}\n\t\tuni.setStorageSync('APP_STATE', appState)\n\t} catch (error) {\n\t\thandleError(error, 'Save App State')\n\t}\n}\n\n// 恢复应用状态\nconst restoreAppState = () => {\n\ttry {\n\t\tconst savedState = uni.getStorageSync('APP_STATE')\n\t\tif (savedState && savedState.timestamp) {\n\t\t\t// 检查状态是否过期（24小时）\n\t\t\tconst isExpired = Date.now() - savedState.timestamp > 24 * 60 * 60 * 1000\n\n\t\t\tif (!isExpired) {\n\t\t\t\t// 恢复用户状态\n\t\t\t\tif (savedState.user) {\n\t\t\t\t\tObject.assign(globalData.user, savedState.user)\n\t\t\t\t}\n\n\t\t\t\t// 恢复配置状态\n\t\t\t\tif (savedState.config) {\n\t\t\t\t\tObject.assign(globalData.config, savedState.config)\n\t\t\t\t}\n\n\t\t\t\treturn true\n\t\t\t}\n\t\t}\n\t\treturn false\n\t} catch (error) {\n\t\thandleError(error, 'Restore App State')\n\t\treturn false\n\t}\n}\n\n// 增强的onShow处理\nconst enhancedHandleShow = () => {\n\ttry {\n\t\t// 恢复应用状态\n\t\trestoreAppState()\n\n\t\t// 执行原有的Show逻辑\n\t\thandleShow()\n\n\t\t// 记录首页加载时间\n\t\tperformanceMonitor.recordFirstPageLoad()\n\n\t} catch (error) {\n\t\thandleError(error, 'Enhanced App Show')\n\t}\n}\n\n// 增强的onHide处理\nconst enhancedHandleHide = () => {\n\ttry {\n\t\t// 保存应用状态\n\t\tsaveAppState()\n\n\t\t// 执行原有的Hide逻辑\n\t\thandleHide()\n\n\t} catch (error) {\n\t\thandleError(error, 'Enhanced App Hide')\n\t}\n}\n\n// 注册应用生命周期钩子 - 修复uni.onAppLaunch错误\n// 在uni-app的<script setup>中，应该使用以下方式：\n\n// 应用启动生命周期\nonLaunch((options) => {\n\thandleLaunch(options)\n})\n\n// 应用显示生命周期\nonShow((options) => {\n\tenhancedHandleShow(options)\n})\n\n// 应用隐藏生命周期\nonHide(() => {\n\tenhancedHandleHide()\n})\n\n// 根据uni-app文档优化getApp()支持\n// 暴露全局数据和方法到应用实例，确保getApp()可以正确访问\nconst setupGlobalApp = () => {\n\tconst app = getCurrentInstance()\n\tif (app?.appContext?.app) {\n\t\t// 设置全局数据 - 支持getApp().globalData访问\n\t\tapp.appContext.app.globalData = globalData\n\n\t\t// 设置全局方法\n\t\tconst globalProperties = app.appContext.app.config.globalProperties\n\t\tglobalProperties.handleRemoteRegister = handleRemoteRegister\n\t\tglobalProperties.APP_CONFIG = APP_CONFIG\n\t\tglobalProperties.handleError = handleError\n\n\t\t// 添加便捷的全局方法\n\t\tglobalProperties.$globalData = globalData\n\t\tglobalProperties.$performanceMonitor = performanceMonitor\n\n\t\t// 设置应用版本信息\n\t\tapp.appContext.app.version = APP_CONFIG.version\n\t\tapp.appContext.app.name = APP_CONFIG.name\n\n\t\t// 添加全局工具方法 - 可通过getApp()访问\n\t\tapp.appContext.app.utils = {\n\t\t\t// 获取用户信息\n\t\t\tgetUserInfo: () => globalData.user.userInfo,\n\t\t\t// 检查登录状态\n\t\t\tisUserLoggedIn: () => globalData.user.isLogin,\n\t\t\t// 获取应用配置\n\t\t\tgetAppConfig: () => APP_CONFIG,\n\t\t\t// 获取性能数据\n\t\t\tgetPerformanceMetrics: () => performanceMonitor.metrics,\n\t\t\t// 更新用户信息\n\t\t\tupdateUserInfo: (userInfo) => {\n\t\t\t\tglobalData.user.userInfo = { ...globalData.user.userInfo, ...userInfo }\n\t\t\t},\n\t\t\t// 清理用户数据\n\t\t\tclearUserData: () => {\n\t\t\t\tglobalData.user.isLogin = false\n\t\t\t\tglobalData.user.userInfo = {}\n\t\t\t\tglobalData.user.spid = 0\n\t\t\t},\n\t\t\t// 获取主题配置\n\t\t\tgetThemeConfig: () => globalData.config.viewColor,\n\t\t\t// 获取基础配置\n\t\t\tgetBasicConfig: () => globalData.config.basicConfig\n\t\t}\n\n\t\treturn app.appContext.app\n\t}\n\treturn null\n}\n\n// 执行全局应用设置\nconst appInstance = setupGlobalApp()\n\n// 确保在应用启动后可以通过getApp()访问\nif (appInstance) {\n\t// 添加调试信息（仅开发环境）\n\tif (APP_CONFIG.debug) {\n\t\tconsole.log('✅ App.vue: 全局数据已设置，可通过getApp().globalData访问')\n\t\tconsole.log('📊 性能监控已启用，可通过getApp().$performanceMonitor访问')\n\t}\n}\n</script>\n\n<style>\r\nfont-family: PingFang SC-Regular, PingFang SC;\n/*\n * CRMEB社交电商 - 全局样式\n * 基于uni-app框架的响应式设计\n */\n\n/* 滚动条样式 - 兼容性优化 */\n.uni-scroll-view::-webkit-scrollbar,\n::-webkit-scrollbar {\n\tdisplay: none;\n\twidth: 0;\n\theight: 0;\n\tcolor: transparent;\n}\n\n/* 背景样式 */\n.bfw {\n\tbackground: #fff;\n}\n\n.bf8 {\n\tbackground: rgba(248, 248, 248, 0.95);\n\t-webkit-backdrop-filter: blur(10px);\n\tbackdrop-filter: blur(10px);\n}\n\n.bfh {\n\tbackground: rgba(0, 0, 0, 0.95);\n\t-webkit-backdrop-filter: blur(30px);\n\tbackdrop-filter: blur(30px);\n}\n\n/* 阴影效果 */\n.xwb {\n\tfilter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.25));\n}\n\n/* 文本溢出处理 */\n.ohto {\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\twhite-space: nowrap;\n}\n\n.ohto2 {\n\tdisplay: block;\n\twhite-space: pre-line;\n\tdisplay: -webkit-box;\n\toverflow: hidden;\n\t-webkit-line-clamp: 2;\n\tline-clamp: 2; /* 标准属性 */\n\ttext-overflow: ellipsis;\n\t-webkit-box-orient: vertical;\n}\n\n/* 微标签样式 */\n.microlabel {\n\tposition: absolute;\n\twidth: 16rpx;\n\theight: 16rpx;\n\tborder-radius: 50%;\n\tbackground: #fa5150;\n\tborder-width: 4rpx;\n\tborder-style: solid;\n\tborder-color: #fff;\n}\n\n/* 空状态样式 */\n.empty-box {\n\twidth: 100%;\n\tpadding: 160rpx 0;\n\tflex-direction: column;\n\tjustify-content: center;\n\talign-items: center;\n}\n\n.empty-box image {\n\twidth: 160rpx;\n\theight: 160rpx;\n}\n\n.empty-box .e1 {\n\tpadding: 20rpx 0 10rpx;\n\tfont-size: 26rpx;\n\tfont-weight: 600;\n}\n\n.empty-box .e2 {\n\tcolor: #999;\n\tfont-size: 20rpx;\n\tfont-weight: 300;\n}\n\n/* 按钮重置 */\nbutton::after {\n\tborder-radius: 0;\n\tborder: none;\n}\n\n/* 图片样式 */\nimg,\n[alt],\nimg[alt] {\n\tmax-width: 100%;\n\theight: auto;\n\tdisplay: block;\n}\n\n/* 动画容器 */\n.heio {\n\tjustify-content: center;\n\toverflow: hidden;\n\ttransition: height 0.45s ease-in-out;\n}\n\n/* 动画定义 */\n@keyframes fadeIn {\n\t0% { opacity: 0; }\n\t100% { opacity: 1; }\n}\n\n@keyframes fadeOut {\n\t0% { opacity: 1; }\n\t100% { opacity: 0; }\n}\n\n@keyframes wobble {\n\t10% { transform: rotate(15deg); }\n\t20% { transform: rotate(-10deg); }\n\t30% { transform: rotate(5deg); }\n\t40% { transform: rotate(-5deg); }\n\t50%, 100% { transform: rotate(0); }\n}\n\n@keyframes likes {\n\t0% { transform: scale(1); }\n\t25% { transform: scale(1.2); }\n\t50% { transform: scale(0.95); }\n\t100% { transform: scale(1); }\n}\n\n@keyframes btnEffect {\n\t0% { transform: translate(0); }\n\t10% { transform: translate(4px); }\n\t20% { transform: translate(-2px); }\n\t30% { transform: translate(4px); }\n\t40% { transform: translate(-2px); }\n\t50% { transform: translate(0); }\n}\n\n@keyframes bottomUp {\n\t0% { bottom: -240rpx; }\n\t100% { bottom: 0; }\n}\n\n/* 动画类 */\n.fade-in { animation: fadeIn 0.3s ease; }\n.fade-out { animation: fadeOut 0.3s ease; }\n.animate { animation: wobble 1.5s 0.15s linear infinite; }\n.hi { animation: likes 0.45s ease-in-out; }\n.effect { animation: btnEffect 3s both infinite; }\n.bUp { animation: bottomUp 0.6s ease; }\n\n/* 提示框样式 */\n.tips-box {\n\tmargin-top: 12vh;\n\twidth: 100%;\n\tjustify-content: center;\n}\n\n.tips-box .tips-item {\n\tpadding: 0 50rpx;\n\theight: 100rpx;\n\tline-height: 100rpx;\n\tfont-size: 24rpx;\n\tfont-weight: 700;\n\tcolor: #fff;\n\tbackground: rgba(0, 0, 0, 0.85);\n\tborder-radius: 50rpx;\n}\n\n/* 响应式设计 - 适配不同屏幕尺寸 */\n@media screen and (max-width: 750rpx) {\n\t.tips-box {\n\t\tmargin-top: 8vh;\n\t}\n\n\t.empty-box {\n\t\tpadding: 120rpx 0;\n\t}\n}\n</style>\n", "// +----------------------------------------------------------------------\r\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\r\n// +----------------------------------------------------------------------\r\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\r\n// +----------------------------------------------------------------------\r\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\r\n// +----------------------------------------------------------------------\r\n// | Author: CRMEB Team <<EMAIL>>\r\n// +----------------------------------------------------------------------\r\n\r\nimport { createApp } from 'vue'\r\nimport App from './App.vue'\r\nimport store from './store/index.js'\r\nimport pinia from './stores/index.js'\r\nimport Cache from './utils/cache.js'\r\nimport util from './utils/util.js'\r\nimport configs from './config/app.js'\r\nimport socket from './libs/new_chat.js'\r\nimport i18n from './utils/lang.js';\r\n// 创建Vue3应用实例\r\nconst app = createApp(App)\r\n\r\n// 创建简单的事件总线替代Vue2的事件总线（无需mitt依赖）\r\nconst eventBus = {\r\n\tevents: {},\r\n\t$emit(event, ...args) {\r\n\t\tif (this.events[event]) {\r\n\t\t\tthis.events[event].forEach(callback => callback(...args))\r\n\t\t}\r\n\t},\r\n\t$on(event, callback) {\r\n\t\tif (!this.events[event]) {\r\n\t\t\tthis.events[event] = []\r\n\t\t}\r\n\t\tthis.events[event].push(callback)\r\n\t},\r\n\t$off(event, callback) {\r\n\t\tif (this.events[event]) {\r\n\t\t\tconst index = this.events[event].indexOf(callback)\r\n\t\t\tif (index > -1) {\r\n\t\t\t\tthis.events[event].splice(index, 1)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 配置全局属性\r\napp.config.globalProperties.$util = util;\r\napp.config.globalProperties.$config = configs;\r\napp.config.globalProperties.$Cache = Cache;\r\napp.config.globalProperties.$eventHub = eventBus;\r\napp.config.globalProperties.$socket = new socket();\r\napp.config.productionTip = false\r\n//import pageLoading from './components/pageLoading.vue'\r\n//import skeleton from './components/skeleton/index.vue'\r\n//import easyLoadimage from '@/components/easy-loadimage/easy-loadimage.vue'\r\n//Vue.component('skeleton', skeleton)\r\n//Vue.component('pageLoading', pageLoading)\r\n//Vue.component('easyLoadimage', easyLoadimage)\r\n\r\n\r\n\r\nimport ActivePermission from './libs/permission.js';\r\napp.config.globalProperties.$permission = ActivePermission;\r\nimport {\r\n\tDebounce\r\n} from '@/utils/validate.js'\r\napp.config.globalProperties.$Debounce = Debounce\r\n// #ifdef H5\r\nimport {\r\n\tparseQuery\r\n} from \"./utils/index.js\";\r\nimport Auth from './libs/wechat.js';\r\nimport cacheConfig from './config/cache.js';\r\nconst { SPREAD } = cacheConfig;\r\napp.config.globalProperties.$wechat = Auth;\r\n\r\nlet cookieName = \"VCONSOLE\",\r\n\tquery = parseQuery(),\r\n\turlSpread = query[\"spread\"],\r\n\tvconsole = query[cookieName.toLowerCase()],\r\n\tmd5Crmeb = \"b14d1e9baeced9bb7525ab19ee35f2d2\", //CRMEB MD5 加密开启vconsole模式\r\n\tmd5UnCrmeb = \"3dca2162c4e101b7656793a1af20295c\"; //UN_CREMB MD5 加密关闭vconsole模式\r\n\r\nif (urlSpread !== undefined) {\r\n\tvar spread = Cache.get(SPREAD);\r\n\turlSpread = parseInt(urlSpread);\r\n\tif (!Number.isNaN(urlSpread) && spread !== urlSpread) {\r\n\t\tCache.set(\"spread\", urlSpread || 0);\r\n\t} else if (spread === 0 || typeof spread !== \"number\") {\r\n\t\tCache.set(\"spread\", urlSpread || 0);\r\n\t}\r\n}\r\n\r\nif (vconsole !== undefined) {\r\n\tif (vconsole === md5UnCrmeb && Cache.has(cookieName))\r\n\t\tCache.clear(cookieName);\r\n} else vconsole = Cache.get(cookieName);\r\n\r\n// import VConsole from './pages/extension/components/vconsole.min.js'\r\n\r\n// if (vconsole !== undefined && vconsole === md5Crmeb) {\r\n// \tCache.set(cookieName, md5Crmeb, 3600);\r\n// \tlet vConsole = new VConsole();\r\n// }\r\n\r\n// let snsapiBase = 'snsapi_base';\r\n// Auth.isWeixin() && Auth.oAuth(snsapiBase);\r\n\r\n// 记录进入app时的url\r\nif (typeof window.entryUrl === 'undefined' || window.entryUrl === '') {\r\n\twindow.entryUrl = location.href\r\n}\r\n\r\n//全局路由前置守卫\r\n// #endif\r\n\r\nApp.mpType = 'app'\r\n\r\n// 使用插件\r\napp.use(store) // 保留Vuex以兼容现有代码\r\napp.use(pinia) // 新增Pinia状态管理\r\napp.use(i18n)\r\n\r\n// 挂载应用\r\napp.mount('#app')"], "names": ["appConfig", "getCurrentInstance", "useStore", "reactive", "computed", "uni", "reportError", "watch", "<PERSON><PERSON>", "silenceBindingSpread", "basicConfig", "colorChange", "themeList", "initWorkermanUrl", "getLangVersion", "getLangJson", "Routine", "wx", "getCrmebCopyRight", "remoteRegister", "onLaunch", "onShow", "onHide", "app", "createApp", "App", "util", "configs", "socket", "ActivePermission", "<PERSON><PERSON><PERSON><PERSON>", "store", "pinia", "i18n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKM,UAAA,EAAE,iBAAqB,IAAAA;AAU7B,UAAM,WAAWC,cAAAA;AACjB,UAAM,QAAQC,cAAAA;AAGd,UAAM,aAAa;AAAA,MACjB,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA;AAAA,MAEP,aAAa;AAAA,QACX,kBAAkB;AAAA,QAClB,gBAAgB;AAAA;AAAA,MAClB;AAAA;AAAA,MAEA,eAAe;AAAA,QACb,iBAAiB;AAAA,QACjB,YAAY;AAAA,MACd;AAAA;AAAA,MAEA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,0BAA0B;AAAA,QAC1B,iBAAiB;AAAA,MACnB;AAAA,IAAA;AAIF,UAAM,aAAaC,cAAAA,SAAS;AAAA;AAAA,MAE3B,MAAM;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU,CAAC;AAAA,QACX,QAAQ;AAAA,MACT;AAAA;AAAA,MAEA,KAAK;AAAA,QACJ,MAAM;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,WAAW;AAAA,QACX,MAAM;AAAA,MACP;AAAA;AAAA,MAEA,QAAQ;AAAA,QACP,SAAS,CAAC;AAAA,QACV,aAAa;AAAA,QACb,aAAa;AAAA,QACb,WAAW;AAAA,MACZ;AAAA,IAAA,CACA;AAGD,UAAM,UAAUC,cAAAA,SAAS,MAAM,MAAM,QAAQ,OAAO;AACpD,UAAM,UAAUA,cAAAA,SAAS,MAAM,MAAM,QAAQ,OAAO;AAGpD,UAAM,qBAAqB;AAAA,MAC1B,WAAW,KAAK,IAAI;AAAA,MACpB,SAASD,cAAAA,SAAS;AAAA,QACjB,eAAe;AAAA,QACf,mBAAmB;AAAA,QACnB,cAAc;AAAA,QACd,YAAY;AAAA,MAAA,CACZ;AAAA;AAAA,MAGD,mBAAmB;AAClB,aAAK,QAAQ,gBAAgB,KAAK,QAAQ,KAAK;AAAA,MAChD;AAAA;AAAA,MAGA,sBAAsB;AACrB,aAAK,QAAQ,oBAAoB,KAAK,QAAQ,KAAK;AAAA,MACpD;AAAA;AAAA,MAGA,mBAAmB;AAClB,aAAK,QAAQ;AAAA,MACd;AAAA;AAAA,MAGA,iBAAiB;AAChB,aAAK,QAAQ;AAAA,MACd;AAAA,IAAA;AAID,UAAM,cAAc,CAAC,OAAO,UAAU,OAAO;AAE5C,yBAAmB,eAAe;AAElC,UAAI,WAAW,OAAO;AACrBE,4BAAA,MAAA,SAAA,kBAAc,IAAI,OAAO,YAAY,KAAK;AAAA,MAC3C;AAGI,UAAA,WAAW,cAAc,iBAAiB;AAC7C,oBAAY,OAAO,OAAO;AAAA,MAC3B;AAAA,IAAA;AAIK,UAAA,cAAc,CAAC,OAAO,YAAY;;AACnC,UAAA;AAEH,cAAM,YAAY;AAAA,UACjB,SAAS,MAAM,WAAW,OAAO,KAAK;AAAA,UACtC,OAAO,MAAM;AAAA,UACb;AAAA,UACA,WAAW,KAAK,IAAI;AAAA,UACpB,YAAW,uCAAW,cAAa;AAAA,UACnC,OAAK,sCAAQ,aAAR,mBAAkB,SAAQ;AAAA,UAC/B,UAAQ,gBAAW,KAAK,aAAhB,mBAA0B,QAAO;AAAA,QAAA;AAM1C,YAAI,WAAW,OAAO;AACrBA,wBAAA,MAAY,MAAA,OAAA,kBAAA,mBAAmB,SAAS;AAAA,QACzC;AAAA,eACQC,cAAa;AACrB,YAAI,WAAW,OAAO;AACrBD,wBAAA,MAAA,MAAA,SAAA,kBAAc,2BAA2BC,YAAW;AAAA,QACrD;AAAA,MACD;AAAA,IAAA;AAIKC,kBAAAA,MAAA,SAAS,CAAC,UAAU,aAAa;AAClC,UAAA;AACH,mBAAW,KAAK,UAAU;AAC1B,YAAI,UAAU;AAAA,QAAA,OAGP;AAEA,gBAAA,OAAO,wBAAwB,EAAE;AAC5B,qBAAA,KAAK,WAAW;QAC5B;AAAA,eACQ,OAAO;AACf,oBAAY,OAAO,oBAAoB;AAAA,MACxC;AAAA,IAAA,GACE,EAAE,WAAW,KAAA,CAAM;AAGhBA,wBAAA,SAAS,CAAC,YAAY;AACvB,UAAA;AACG,cAAA,YAAY,OAAO,OAAO,KAAK;AACrC,cAAM,OAAO,wBAAwB,UAAU,SAAU,CAAA;AAGzDF,sBAAA,MAAI,MAAM,mBAAmB;AAAA,UAC5B,OAAO;AAAA,UACP,MAAM,YAAY;AAAA,QAAA,CAClB;AAAA,eACO,OAAO;AACf,oBAAY,OAAO,mBAAmB;AAAA,MACvC;AAAA,IAAA,GACE,EAAE,WAAW,KAAA,CAAM;AAGhB,UAAA,qBAAqB,CAAC,cAAc;AACrC,UAAA;AAEC,YAAA,UAAU,MAAM,QAAQ;AAC3BG,sBAAA,MAAM,IAAI,UAAU,UAAU,MAAM,MAAM;AAC/B,qBAAA,KAAK,OAAO,UAAU,MAAM;AAC5B,qBAAA,KAAK,MAAM,UAAU,MAAM;AACtCC,sBAAA,qBAAqB,UAAU;AAAA,QAChC;AAGI,YAAA,UAAU,MAAM,MAAM;AACzBD,sBAAA,MAAM,IAAI,UAAU,UAAU,MAAM,IAAI;AAC7B,qBAAA,KAAK,OAAO,UAAU,MAAM;AAC5B,qBAAA,KAAK,MAAM,UAAU,MAAM;AACtCC,sBAAA,qBAAqB,UAAU;AAAA,QAChC;AAAA,eACQ,OAAO;AACf,oBAAY,OAAO,sBAAsB;AAAA,MAC1C;AAAA,IAAA;AAIK,UAAA,yBAAyB,CAAC,cAAc;AAEzC,UAAA;AACC,YAAA,UAAU,MAAM,OAAO;AAC1B,gBAAM,OAAO,SAAS,WAAW,OAAO,iBAAiB;AACzD,gBAAM,QAAQ,KAAK,aAAa,mBAAmB,UAAU,MAAM,KAAK,CAAC;AAEzE,cAAI,MAAM,KAAK;AACRD,wBAAAA,MAAA,IAAI,UAAU,MAAM,GAAG;AAClB,uBAAA,KAAK,OAAO,MAAM;AAClB,uBAAA,KAAK,MAAM,MAAM;AAAA,UAAA,OACtB;AAEN,kBAAM,gBAAgB;AAAA,cACrB,MAAM,MAAM,WAAW,IAAI,OAAO,UAAU,MAAM;AAAA;AAAA,cAClD,MAAM,MAAM,WAAW,IAAI,OAAO,UAAU,MAAM;AAAA;AAAA,cAClD,MAAM,MAAM,WAAW,IAAI,OAAO,UAAU,MAAM;AAAA;AAAA,cAClD,MAAM,MAAM,WAAW,KAAK,OAAO,UAAU,MAAM;AAAA;AAAA,YAAA;AAG9C,kBAAA,UAAU,cAAc,UAAU,KAAK;AAC7C,gBAAI,SAAS;AACJ;YACT;AAAA,UACD;AACAC,sBAAA,qBAAqB,UAAU;AAAA,QAChC;AAAA,eACQ,OAAO;AACf,oBAAY,OAAO,0BAA0B;AAAA,MAC9C;AAAA,IAAA;AAKD,UAAM,aAAa,MAAM;AACpB,UAAA;AACG,cAAA,YAAYJ,oBAAI;AAGtB,2BAAmB,SAAS;AAG5B,+BAAuB,SAAS;AAAA,eAExB,OAAO;AACf,oBAAY,OAAO,UAAU;AAAA,MAC9B;AAAA,IAAA;AAID,UAAM,kBAAkB,YAAY;AAC/B,UAAA;AACG,cAAA,MAAM,MAAMK,WAAAA;AACP,mBAAA,OAAO,cAAc,IAAI;AAChCL,sBAAAA,MAAA,eAAe,gBAAgB,IAAI,IAAI;AAC3C,eAAO,IAAI;AAAA,eACH,OAAO;AACf,oBAAY,OAAO,mBAAmB;AAC/B,eAAA;AAAA,MACR;AAAA,IAAA;AAID,UAAM,kBAAkB,YAAY;AAC/B,UAAA;AACG,cAAA,MAAM,MAAMM,oBAAY,cAAc;AAC5C,cAAM,WAAW;AAAA,UAChB,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,QAAA;AAGE,cAAA,SAAS,IAAI,KAAK;AAClB,cAAA,WAAW,SAAS,MAAM,KAAK;AAC/B,cAAA,gBAAgBC,sBAAU,QAAQ;AAGxCP,sBAAA,MAAI,eAAe,UAAU,IAAI,KAAK,MAAM;AACxCA,sBAAAA,MAAA,eAAe,gBAAgB,MAAM;AACrCA,sBAAAA,MAAA,eAAe,aAAa,aAAa;AAG7C,mBAAW,OAAO,cAAc;AAChC,mBAAW,OAAO,YAAY;AAG9BA,sBAAA,MAAI,MAAM,UAAU,IAAI,KAAK,MAAM;AAC/BA,sBAAAA,MAAA,MAAM,MAAM,eAAe,MAAM;AAE9B,eAAA,EAAE,QAAQ;eACT,OAAO;AACf,oBAAY,OAAO,mBAAmB;AAC/B,eAAA;AAAA,MACR;AAAA,IAAA;AAIK,UAAA,gBAAgB,CAAC,WAAW;AAAA,IAAA;AAiB5B,UAAA,eAAe,OAAO,WAAW;AAClC,UAAA;AAEcQ,oBAAAA;AAGjB,cAAM,iBAAiB;AAAA,UACtB,gBAAgB;AAAA,UAChB,gBAAgB;AAAA,QAAA;AAIjB,sBAAc,MAAM;AAGd,cAAA,QAAQ,WAAW,cAAc;AAGvC,cAAM,mBAAmB;AAGV;AAGf,cAAM,4BAA4B,MAAM;AAG1B;AAGd,2BAAmB,iBAAiB;AAGhC,YAAA,WAAW,YAAY,kBAAkB;AACjB;QAC5B;AAGyB;eAEjB,OAAO;AACf,oBAAY,OAAO,YAAY;AAAA,MAChC;AAAA,IAAA;AAID,UAAM,6BAA6B,MAAM;AACpC,UAAA,CAAC,WAAW,YAAY;AAAkB;AAG9C,kBAAY,MAAM;AACjB,cAAM,UAAU;AAAA,UACf,GAAG,mBAAmB;AAAA,UACtB,aAAa,eAAe;AAAA,UAC5B,WAAW,KAAK,IAAI;AAAA,QAAA;AAGrB,YAAI,WAAW,OAAO;AACrBR,wBAAA,MAAY,MAAA,OAAA,kBAAA,wBAAwB,OAAO;AAAA,QAC5C;AAAA,MAAA,GAIE,WAAW,YAAY,cAAc;AAAA,IAAA;AAIzC,UAAM,iBAAiB,MAAM;AACxB,UAAA;AAeI,eAAA;AAAA,eACC,OAAO;AACR,eAAA;AAAA,MACR;AAAA,IAAA;AAID,UAAM,2BAA2B,MAAM;AAAA,IAAA;AAiBvC,UAAM,qBAAqB,YAAY;AAClC,UAAA;AACG,cAAA,MAAM,MAAMS,SAAAA;AACZ,cAAA,UAAU,IAAI,KAAK;AACnB,cAAA,iBAAiBT,cAAAA,MAAI,eAAe,cAAc;AAExD,YAAI,YAAY,gBAAgB;AACzB,gBAAA,UAAU,MAAMU,SAAAA;AACtB,gBAAM,UAAU,OAAO,KAAK,QAAQ,IAAI,EAAE,CAAC;AAErCP,sBAAAA,MAAA,IAAI,UAAU,OAAO;AAC3B,qBAAW,KAAK,SAAS;AAGzB,gBAAM,OAAO,SAAS,WAAW,OAAO,iBAAiB;AACzD,cAAI,MAAM;AACT,iBAAK,iBAAiB,SAAS,QAAQ,KAAK,OAAO,CAAC;AAAA,UACrD;AAEIH,wBAAAA,MAAA,eAAe,cAAc,QAAQ,IAAI;AAAA,QAC9C;AAEIA,sBAAAA,MAAA,eAAe,gBAAgB,OAAO;AAAA,eAClC,OAAO;AACf,oBAAY,OAAO,sBAAsB;AAAA,MAC1C;AAAA,IAAA;AAID,UAAM,iBAAiB,MAAM;AACxB,UAAA;AAkBG,cAAA,iBAAiBA,oBAAI;AAC3B,mBAAW,IAAI,OAAO,eAAe,MAAM,IAAI,eAAe,SAAS;AAGjE,cAAA,UAAUA,cAAAA,MAAI,kBAAA,EAAoB;AACxC,cAAM,eAAeW,aAAAA,QAAQ,eAAe,SAAS,QAAQ,KAAK;AAC5DR,oBAAAA,MAAA,IAAI,oBAAoB,YAAY;AAAA,eAGlC,OAAO;AACf,oBAAY,OAAO,kBAAkB;AAAA,MACtC;AAAA,IAAA;AAGK,UAAA,8BAA8B,OAAO,WAAW;AACjD,UAAA;AAEH,cAAM,uBAAuB,MAAM;AAAA,eAM3B,OAAO;AACf,oBAAY,OAAO,gCAAgC;AAAA,MACpD;AAAA,IAAA;AAIK,UAAA,yBAAyB,OAAO,WAAW;AAE5C,UAAA;AAEH,YAAI,CAAC,kBAAkB;AACtBH,wBAAAA,MAAA;AAAA,YAAA;AAAA,YAAA;AAAA,YACC;AAAA,UAAA;AAEM,iBAAA;AAAA,QACR;AAGM,cAAA,gBAAgBY,mBAAG;AACzB,YAAIA,cAAAA,KAAG,QAAQ,kBAAkB,KAAK,cAAc,UAAU,MAAM;AAC3C;QACzB;AAEO,eAAA;AAAA,eACC,OAAO;AACf,oBAAY,OAAO,0BAA0B;AACtC,eAAA;AAAA,MACR;AAAA,IAAA;AAKD,UAAM,0BAA0B,MAAM;AAEjC,UAAA;AACG,cAAA,gBAAgBA,mBAAG;AAEX,sBAAA,iBAAiB,CAAC,QAAQ;AACvC,cAAI,IAAI,WAAW;AAClB,0BAAc,cAAc,MAAM;AACjCA,4BAAAA,KAAG,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,SAAS;AAAA,gBACT,QAAQ,UAAU;AACjB,sBAAI,SAAS,SAAS;AACrB,kCAAc,YAAY;AAAA,kBAC3B;AAAA,gBACD;AAAA,cAAA,CACA;AAAA,YAAA,CACD;AAED,0BAAc,eAAe,MAAM;AAClCA,4BAAAA,KAAG,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,SAAS;AAAA,cAAA,CACT;AAAA,YAAA,CACD;AAAA,UACF;AAAA,QAAA,CACA;AAAA,eACO,OAAO;AACf,oBAAY,OAAO,2BAA2B;AAAA,MAC/C;AAAA,IAAA;AA0BD,UAAM,gBAAgB,YAAY;AAC7B,UAAA;AACG,cAAA,MAAM,MAAMC,QAAAA;AACdb,sBAAAA,MAAA,eAAe,aAAa,IAAI,IAAI;AAAA,eAChC,OAAO;AACf,oBAAY,OAAO,gBAAgB;AAAA,MACpC;AAAA,IAAA;AAIK,UAAA,uBAAuB,OAAO,iBAAiB;AAChD,UAAA;AACH,cAAM,MAAM,MAAMc,WAAAA,eAAe,EAAE,aAAc,CAAA;AACjD,cAAM,OAAO,IAAI;AAEjB,YAAI,KAAK,sBAAsB;AAAA,QAAA,OAMxB;AAEN,gBAAM,OAAO,SAAS;AAAA,YACrB,OAAO,KAAK;AAAA,YACZ,MAAM,KAAK,eAAeX,YAAAA,MAAM,KAAK;AAAA,UAAA,CACrC;AACD,gBAAM,OAAO,UAAU,KAAK,SAAS,GAAG;AAGxC,qBAAW,KAAK,UAAU;AACf,qBAAA,KAAK,WAAW,KAAK;AAAA,QAOjC;AAAA,eACQ,OAAO;AACf,oBAAY,OAAO,iBAAiB;AAAA,MACrC;AAAA,IAAA;AAID,UAAM,aAAa,MAAM;AAAA,IAOxB;AAID,UAAM,eAAe,MAAM;AACtB,UAAA;AACH,cAAM,WAAW;AAAA,UAChB,MAAM,WAAW;AAAA,UACjB,QAAQ,WAAW;AAAA,UACnB,WAAW,KAAK,IAAI;AAAA,QAAA;AAEjBH,sBAAAA,MAAA,eAAe,aAAa,QAAQ;AAAA,eAChC,OAAO;AACf,oBAAY,OAAO,gBAAgB;AAAA,MACpC;AAAA,IAAA;AAID,UAAM,kBAAkB,MAAM;AACzB,UAAA;AACG,cAAA,aAAaA,cAAAA,MAAI,eAAe,WAAW;AAC7C,YAAA,cAAc,WAAW,WAAW;AAEjC,gBAAA,YAAY,KAAK,QAAQ,WAAW,YAAY,KAAK,KAAK,KAAK;AAErE,cAAI,CAAC,WAAW;AAEf,gBAAI,WAAW,MAAM;AACpB,qBAAO,OAAO,WAAW,MAAM,WAAW,IAAI;AAAA,YAC/C;AAGA,gBAAI,WAAW,QAAQ;AACtB,qBAAO,OAAO,WAAW,QAAQ,WAAW,MAAM;AAAA,YACnD;AAEO,mBAAA;AAAA,UACR;AAAA,QACD;AACO,eAAA;AAAA,eACC,OAAO;AACf,oBAAY,OAAO,mBAAmB;AAC/B,eAAA;AAAA,MACR;AAAA,IAAA;AAID,UAAM,qBAAqB,MAAM;AAC5B,UAAA;AAEa;AAGL;AAGX,2BAAmB,oBAAoB;AAAA,eAE/B,OAAO;AACf,oBAAY,OAAO,mBAAmB;AAAA,MACvC;AAAA,IAAA;AAID,UAAM,qBAAqB,MAAM;AAC5B,UAAA;AAEU;AAGF;eAEH,OAAO;AACf,oBAAY,OAAO,mBAAmB;AAAA,MACvC;AAAA,IAAA;AAODe,kBAAA,SAAS,CAAC,YAAY;AACrB,mBAAa,OAAO;AAAA,IAAA,CACpB;AAGDC,kBAAA,OAAO,CAAC,YAAY;AACnB,yBAA0B;AAAA,IAAA,CAC1B;AAGDC,kBAAAA,OAAO,MAAM;AACO;IAAA,CACnB;AAID,UAAM,iBAAiB,MAAM;;AAC5B,YAAMC,OAAMtB,cAAAA;AACR,WAAA,KAAAsB,QAAA,gBAAAA,KAAK,eAAL,mBAAiB,KAAK;AAErB,QAAAA,KAAA,WAAW,IAAI,aAAa;AAGhC,cAAM,mBAAmBA,KAAI,WAAW,IAAI,OAAO;AACnD,yBAAiB,uBAAuB;AACxC,yBAAiB,aAAa;AAC9B,yBAAiB,cAAc;AAG/B,yBAAiB,cAAc;AAC/B,yBAAiB,sBAAsB;AAGnC,QAAAA,KAAA,WAAW,IAAI,UAAU,WAAW;AACpC,QAAAA,KAAA,WAAW,IAAI,OAAO,WAAW;AAGjC,QAAAA,KAAA,WAAW,IAAI,QAAQ;AAAA;AAAA,UAE1B,aAAa,MAAM,WAAW,KAAK;AAAA;AAAA,UAEnC,gBAAgB,MAAM,WAAW,KAAK;AAAA;AAAA,UAEtC,cAAc,MAAM;AAAA;AAAA,UAEpB,uBAAuB,MAAM,mBAAmB;AAAA;AAAA,UAEhD,gBAAgB,CAAC,aAAa;AAClB,uBAAA,KAAK,WAAW,EAAE,GAAG,WAAW,KAAK,UAAU,GAAG;UAC9D;AAAA;AAAA,UAEA,eAAe,MAAM;AACpB,uBAAW,KAAK,UAAU;AACf,uBAAA,KAAK,WAAW;AAC3B,uBAAW,KAAK,OAAO;AAAA,UACxB;AAAA;AAAA,UAEA,gBAAgB,MAAM,WAAW,OAAO;AAAA;AAAA,UAExC,gBAAgB,MAAM,WAAW,OAAO;AAAA,QAAA;AAGzC,eAAOA,KAAI,WAAW;AAAA,MACvB;AACO,aAAA;AAAA,IAAA;AAIR,UAAM,cAAc;AAGpB,QAAI,aAAa;AAEhB,UAAI,WAAW,OAAO;2DACT,6CAA6C;2DAC7C,8CAA8C;AAAA,MAC3D;AAAA,IACD;;;;;ACrwBA,MAAM,MAAMC,cAAS,UAACC,SAAG;AAGzB,MAAM,WAAW;AAAA,EAChB,QAAQ,CAAE;AAAA,EACV,MAAM,UAAU,MAAM;AACrB,QAAI,KAAK,OAAO,KAAK,GAAG;AACvB,WAAK,OAAO,KAAK,EAAE,QAAQ,cAAY,SAAS,GAAG,IAAI,CAAC;AAAA,IACxD;AAAA,EACD;AAAA,EACD,IAAI,OAAO,UAAU;AACpB,QAAI,CAAC,KAAK,OAAO,KAAK,GAAG;AACxB,WAAK,OAAO,KAAK,IAAI,CAAE;AAAA,IACvB;AACD,SAAK,OAAO,KAAK,EAAE,KAAK,QAAQ;AAAA,EAChC;AAAA,EACD,KAAK,OAAO,UAAU;AACrB,QAAI,KAAK,OAAO,KAAK,GAAG;AACvB,YAAM,QAAQ,KAAK,OAAO,KAAK,EAAE,QAAQ,QAAQ;AACjD,UAAI,QAAQ,IAAI;AACf,aAAK,OAAO,KAAK,EAAE,OAAO,OAAO,CAAC;AAAA,MAClC;AAAA,IACD;AAAA,EACD;AACF;AAGA,IAAI,OAAO,iBAAiB,QAAQC;AACpC,IAAI,OAAO,iBAAiB,UAAUC;AACtC,IAAI,OAAO,iBAAiB,SAASnB;AACrC,IAAI,OAAO,iBAAiB,YAAY;AACxC,IAAI,OAAO,iBAAiB,UAAU,IAAIoB,cAAM,OAAA;AAChD,IAAI,OAAO,gBAAgB;AAW3B,IAAI,OAAO,iBAAiB,cAAcC;AAI1C,IAAI,OAAO,iBAAiB,YAAYC,eAAQ;AAkDhDL,UAAI,SAAS;AAGb,IAAI,IAAIM,YAAAA,KAAK;AACb,IAAI,IAAIC,aAAAA,KAAK;AACb,IAAI,IAAIC,WAAAA,IAAI;AAGZ,IAAI,MAAM,MAAM;"}