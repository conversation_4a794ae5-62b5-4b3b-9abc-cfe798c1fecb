{"version": 3, "file": "index.js", "sources": ["pages/search/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc2VhcmNoL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 导航栏 -->\r\n    <view class=\"nav-bar\" :style=\"{'padding-top': statusBarHeight + 'px'}\">\r\n      <view class=\"back-box df\" :style=\"{'height': titleBarHeight + 'px', 'width': '100%'}\">\r\n        <view class=\"back-item df\" @tap=\"navBack\">\r\n          <image src=\"/static/img/back.png\" style=\"width:34rpx;height:34rpx\"></image>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 搜索框 -->\r\n      <view class=\"nav-search df\">\r\n        <input @confirm=\"searchClick\" \r\n               focus=\"true\" \r\n               confirm-type=\"search\" \r\n               placeholder=\"输入关键词搜索\" \r\n               placeholder-class=\"ph\" \r\n               v-model=\"kw\"/>\r\n        <view class=\"btn\" @tap=\"searchClick\">搜索</view>\r\n      </view>\r\n      \r\n      <!-- 标签栏 -->\r\n      <view class=\"bar-box df\">\r\n        <view v-for=\"(item, index) in barList\" \r\n              :key=\"index\" \r\n              class=\"bar-item df\" \r\n              @tap=\"barClick\" \r\n              :data-idx=\"index\">\r\n          <text :style=\"{'color': index == barIdx ? '#000' : '#999', 'font-size': index == barIdx ? '28rpx' : '26rpx'}\">\r\n            {{item}}\r\n          </text>\r\n          <view :style=\"{'opacity': index == barIdx ? 1 : 0}\" class=\"bar-line\"></view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 内容区域 -->\r\n    <view :class=\"['content-box', barIdx == 1 && 'goods']\" :style=\"{'margin-top': 'calc(' + (statusBarHeight + titleBarHeight) + 'px + 200rpx)'}\">\r\n      <!-- 空状态 -->\r\n      <emptyPage\r\n        v-if=\"isEmpty\"\r\n        title=\"没有找到相应内容\"\r\n        description=\"换个词或切换搜索类型试试\"\r\n        image=\"/static/img/empty.png\"\r\n      />\r\n      \r\n      <!-- 有内容状态 -->\r\n      <block v-else>\r\n        <!-- 笔记 -->\r\n        <view v-if=\"barIdx == 0\" :class=\"[isWaterfall && 'dynamic-box']\">\r\n          <waterfall v-if=\"isWaterfall\" :note=\"list\" :page=\"page\"></waterfall>\r\n          <block v-else>\r\n            <card-gg v-for=\"(item, index) in list\" \r\n                     :key=\"index\" \r\n                     :item=\"item\" \r\n                     :idx=\"index\" \r\n                     @likeback=\"likeClick\"\r\n                     @update=\"onCardUpdate\">\r\n            </card-gg>\r\n          </block>\r\n        </view>\r\n        \r\n        <!-- 其他类型搜索结果 -->\r\n        <block v-else>\r\n          <view v-for=\"(item, index) in list\" \r\n                :key=\"index\" \r\n                :class=\"[barIdx == 1 && 'goods-item']\" \r\n                :data-id=\"item.id\" \r\n                @tap=\"toNavigate\">\r\n            \r\n            <!-- 商品项 -->\r\n            <block v-if=\"barIdx == 1\">\r\n              <view class=\"goods-img\">\r\n                <view class=\"goods-img-item\">\r\n                  <lazy-image :src=\"item.imgs[0]\"></lazy-image>\r\n                </view>\r\n              </view>\r\n              <view class=\"goods-name ohto2\">{{item.name}}</view>\r\n              <view class=\"goods-price\">\r\n                <money :type=\"1\" :price=\"item.product.price\"></money>\r\n                <view class=\"price-h\" style=\"text-decoration:line-through\">¥{{item.product.line_price}}</view>\r\n                <view class=\"price-h\">{{item.buy ? item.buy + \"人已买\" : item.cart + item.browse + \"人想买\"}}</view>\r\n              </view>\r\n              <view class=\"goods-tag df\">\r\n                <view v-for=\"(tag, tagIndex) in item.tags\" :key=\"tagIndex\" class=\"tag-item\">{{tag}}</view>\r\n              </view>\r\n            </block>\r\n            \r\n            <!-- 圈子项 -->\r\n            <view v-if=\"barIdx == 2\" class=\"circle-box df\">\r\n              <view class=\"circle-avatar\">\r\n                <lazy-image :src=\"item.avatar\" :br=\"'168rpx'\"></lazy-image>\r\n                <view v-if=\"item.is_hot\" class=\"tag\" style=\"background:#FA5150\"></view>\r\n                <view v-else-if=\"item.is_new\" class=\"tag\" style=\"background:#4CD964\"></view>\r\n              </view>\r\n              <view class=\"circle-item\">\r\n                <view class=\"name ohto2\">{{item.name}}</view>\r\n                <view class=\"intro ohto2\">{{item.intro}}</view>\r\n                <view v-if=\"item.user_count > 0\" class=\"cu-img-group\">\r\n                  <view v-for=\"(img, imgIndex) in item.user\" :key=\"imgIndex\" class=\"cu-img\">\r\n                    <image :src=\"img\" mode=\"aspectFill\"></image>\r\n                  </view>\r\n                  <view class=\"cu-txt\">{{item.user_count}}人加入 · {{item.dynamic_count}}篇笔记 </view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n            \r\n            <!-- 用户项 -->\r\n            <view v-if=\"barIdx == 3\" class=\"user-box df\">\r\n              <view class=\"user-avatar\">\r\n                <lazy-image :src=\"item.avatar\"></lazy-image>\r\n              </view>\r\n              <view class=\"user-item\">\r\n                <view class=\"name ohto\">{{item.name}}</view>\r\n                <view class=\"unm\">笔记 · {{item.dynamic}}｜粉丝 · {{item.fans}}</view>\r\n                <view class=\"user-tag df\" style=\"width:100%\">\r\n                  <view v-if=\"item.gender != 2\" class=\"tag-item df\">\r\n                    <image :src=\"item.gender == 0 ? '/static/img/nv.png' : '/static/img/nan.png'\"></image>\r\n                    <text v-if=\"item.age && item.age != '暂不展示'\" style=\"margin-left:8rpx\">{{item.age}}</text>\r\n                  </view>\r\n                  <view class=\"tag-item df\">IP属地：{{item.province}}</view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n            \r\n            <!-- 活动项 -->\r\n            <view v-if=\"barIdx >= 4 || barList[barIdx] == '活动'\" class=\"activity-item df\">\r\n              <view class=\"activity-img\">\r\n                <lazy-image :src=\"item.img\"></lazy-image>\r\n                <view class=\"activity-state df\">{{item.status_str || \"加载中\"}}</view>\r\n              </view>\r\n              <view class=\"activity-data\">\r\n                <view class=\"title ohto\" :data-id=\"item.id\">{{item.name || \"活动名称加载中\"}}</view>\r\n                <view class=\"txt df\" :data-id=\"item.id\">\r\n                  <image src=\"/static/img/time.png\"></image>\r\n                  <view class=\"ohto\">{{item.activity_time || \"活动时间加载中\"}}</view>\r\n                </view>\r\n                <view class=\"txt df\" :data-id=\"item.id\">\r\n                  <image src=\"/static/img/location.png\"></image>\r\n                  <view class=\"ohto\">{{item.adds_name || \"活动地址加载中\"}}</view>\r\n                </view>\r\n                <view v-if=\"item.user_count\" class=\"cu-img-group\">\r\n                  <view v-for=\"(img, imgIndex) in item.avatar_list\" :key=\"imgIndex\" class=\"cu-img\">\r\n                    <image :src=\"img\" mode=\"aspectFill\"></image>\r\n                  </view>\r\n                  <view class=\"cu-tit\">{{item.user_count}}人已参加</view>\r\n                </view>\r\n                <view v-else class=\"cu-txt-group\">{{item.browse}}人想参加</view>\r\n                <view class=\"activity-btn df\">\r\n                  <button class=\"btn-item df w100\">\r\n                    <text>{{item.is_join ? \"查看详情\" : \"立即参加\"}}</text>\r\n                    <image class=\"effect icon\" src=\"/static/img/arrow-r.png\"></image>\r\n                  </button>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </block>\r\n      </block>\r\n      \r\n      <!-- 加载更多 -->\r\n      <view class=\"df\" style=\"width:100%;justify-content:center\">\r\n        <uni-load-more :status=\"loadStatus\"></uni-load-more>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 提示弹窗 -->\r\n    <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\r\n      <view class=\"tips-box df\">\r\n        <view class=\"tips-item\">{{tipsTitle}}</view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport request from '@/utils/request.js'\r\nimport * as api from '@/config/api.js'\r\nimport lazyImage from '@/components/lazyImage/lazyImage'\r\nimport cardGg from '@/components/card-gg/card-gg.vue'\r\nimport money from '@/components/money/money.vue'\r\nimport uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue'\r\nimport waterfall from '@/components/waterfall/waterfall.vue'\r\nimport emptyPage from '@/components/emptyPage/emptyPage.vue'\r\n\r\nconst app = getApp();\r\n\r\nexport default {\r\n  components: {\r\n    lazyImage,\r\n    cardGg,\r\n    money,\r\n    uniLoadMore,\r\n    waterfall,\r\n    emptyPage\r\n  },\r\n  data() {\r\n    return {\r\n      statusBarHeight: this.$store.state.statusBarHeight || 20,\r\n      titleBarHeight: this.$store.state.titleBarHeight || 44,\r\n      kw: \"\",\r\n      barList: [\"笔记\", \"商品\", \"圈子\", \"用户\"],\r\n      barIdx: 0,\r\n      isThrottling: true,\r\n      list: [],\r\n      page: 1,\r\n      isEmpty: false,\r\n      loadStatus: \"more\",\r\n      tipsTitle: \"\",\r\n      isWaterfall: false,\r\n      \r\n      // 模拟数据 - 笔记\r\n      mockNotes: [\r\n        {\r\n          id: 1001,\r\n          type: 1,\r\n          user_id: 2001,\r\n          avatar: \"/static/img/avatar.png\",\r\n          nickname: \"旅行达人\",\r\n          is_follow: false,\r\n          is_like: false,\r\n          content: \"今天去了一个超美的地方，分享给大家！这里的风景真的太美了，空气清新，环境宜人。\",\r\n          imgs: [\r\n            { url: \"/static/img/note1.jpg\", wide: 800, high: 1200 },\r\n            { url: \"/static/img/note2.jpg\", wide: 800, high: 1200 }\r\n          ],\r\n          adds_name: \"北京市朝阳区\",\r\n          comment_count: 28,\r\n          like_count: 156,\r\n          browse_count: 1024,\r\n          create_time_str: \"2小时前\"\r\n        },\r\n        {\r\n          id: 1002,\r\n          type: 2,\r\n          user_id: 2002,\r\n          avatar: \"/static/img/avatar.png\",\r\n          nickname: \"美食家\",\r\n          is_follow: true,\r\n          is_like: true,\r\n          content: \"发现了一家超赞的餐厅，味道真的一级棒！强烈推荐给大家！\",\r\n          video: {\r\n            url: \"https://example.com/video.mp4\",\r\n            cover: \"/static/img/video-cover.jpg\",\r\n            wide: 540,\r\n            high: 960\r\n          },\r\n          adds_name: \"上海市黄浦区\",\r\n          comment_count: 42,\r\n          like_count: 267,\r\n          browse_count: 2345,\r\n          create_time_str: \"昨天\"\r\n        },\r\n        {\r\n          id: 1003,\r\n          type: 1,\r\n          user_id: 2003,\r\n          avatar: \"/static/img/avatar.png\",\r\n          nickname: \"时尚博主\",\r\n          is_follow: false,\r\n          is_like: false,\r\n          content: \"分享一套今日穿搭，简约而不简单，希望大家喜欢~\",\r\n          imgs: [\r\n            { url: \"/static/img/note3.jpg\", wide: 800, high: 1200 },\r\n            { url: \"/static/img/note4.jpg\", wide: 800, high: 1200 },\r\n            { url: \"/static/img/note5.jpg\", wide: 800, high: 1200 }\r\n          ],\r\n          adds_name: \"广州市天河区\",\r\n          comment_count: 36,\r\n          like_count: 198,\r\n          browse_count: 1876,\r\n          create_time_str: \"3天前\"\r\n        }\r\n      ],\r\n      \r\n      // 模拟数据 - 商品\r\n      mockGoods: [\r\n        {\r\n          id: 2001,\r\n          name: \"2023夏季新款连衣裙\",\r\n          imgs: [\"/static/img/product1.png\"],\r\n          product: {\r\n            price: \"199.00\",\r\n            line_price: \"299.00\"\r\n          },\r\n          buy: 128,\r\n          cart: 45,\r\n          browse: 1560,\r\n          tags: [\"新品\", \"热卖\"]\r\n        },\r\n        {\r\n          id: 2002,\r\n          name: \"轻薄透气运动鞋\",\r\n          imgs: [\"/static/img/product2.png\"],\r\n          product: {\r\n            price: \"299.00\",\r\n            line_price: \"399.00\"\r\n          },\r\n          buy: 89,\r\n          cart: 32,\r\n          browse: 980,\r\n          tags: [\"限时特价\"]\r\n        },\r\n        {\r\n          id: 2003,\r\n          name: \"时尚斜挎小包\",\r\n          imgs: [\"/static/img/product3.png\"],\r\n          product: {\r\n            price: \"159.00\",\r\n            line_price: \"259.00\"\r\n          },\r\n          buy: 56,\r\n          cart: 28,\r\n          browse: 756,\r\n          tags: [\"爆款\"]\r\n        }\r\n      ],\r\n      \r\n      // 模拟数据 - 圈子\r\n      mockCircles: [\r\n        {\r\n          id: 3001,\r\n          avatar: \"/static/img/circle1.png\",\r\n          name: \"时尚穿搭圈\",\r\n          intro: \"分享最新穿搭灵感，让你时刻保持时尚\",\r\n          is_hot: true,\r\n          is_new: false,\r\n          user_count: 12560,\r\n          dynamic_count: 3567,\r\n          user: [\r\n            \"/static/img/avatar.png\",\r\n            \"/static/img/avatar.png\",\r\n            \"/static/img/avatar.png\"\r\n          ]\r\n        },\r\n        {\r\n          id: 3002,\r\n          avatar: \"/static/img/circle2.png\",\r\n          name: \"美食爱好者\",\r\n          intro: \"发现城市隐藏的美食宝藏，享受舌尖上的美味\",\r\n          is_hot: false,\r\n          is_new: true,\r\n          user_count: 8934,\r\n          dynamic_count: 2456,\r\n          user: [\r\n            \"/static/img/avatar.png\",\r\n            \"/static/img/avatar.png\",\r\n            \"/static/img/avatar.png\"\r\n          ]\r\n        },\r\n        {\r\n          id: 3003,\r\n          avatar: \"/static/img/circle3.png\",\r\n          name: \"旅行探索家\",\r\n          intro: \"一起去探索世界的每个角落，记录旅途中的精彩\",\r\n          is_hot: true,\r\n          is_new: false,\r\n          user_count: 10238,\r\n          dynamic_count: 2987,\r\n          user: [\r\n            \"/static/img/avatar.png\",\r\n            \"/static/img/avatar.png\",\r\n            \"/static/img/avatar.png\"\r\n          ]\r\n        }\r\n      ],\r\n      \r\n      // 模拟数据 - 用户\r\n      mockUsers: [\r\n        {\r\n          id: 4001,\r\n          avatar: \"/static/img/avatar.png\",\r\n          name: \"时尚达人\",\r\n          dynamic: 156,\r\n          fans: 3456,\r\n          gender: 0, // 0-女 1-男 2-未知\r\n          age: 28,\r\n          province: \"北京\"\r\n        },\r\n        {\r\n          id: 4002,\r\n          avatar: \"/static/img/avatar.png\",\r\n          name: \"美食博主\",\r\n          dynamic: 234,\r\n          fans: 5678,\r\n          gender: 1,\r\n          age: 32,\r\n          province: \"上海\"\r\n        },\r\n        {\r\n          id: 4003,\r\n          avatar: \"/static/img/avatar.png\",\r\n          name: \"旅行摄影师\",\r\n          dynamic: 198,\r\n          fans: 4567,\r\n          gender: 1,\r\n          age: 30,\r\n          province: \"广州\"\r\n        }\r\n      ],\r\n      \r\n      // 模拟数据 - 活动\r\n      mockActivities: [\r\n        {\r\n          id: 5001,\r\n          img: \"/static/img/activity1.png\",\r\n          status_str: \"进行中\",\r\n          name: \"夏日时尚穿搭分享会\",\r\n          activity_time: \"2023-07-15 14:00-17:00\",\r\n          adds_name: \"北京市朝阳区时尚广场B座\",\r\n          is_join: false,\r\n          user_count: 156,\r\n          avatar_list: [\r\n            \"/static/img/avatar.png\",\r\n            \"/static/img/avatar.png\",\r\n            \"/static/img/avatar.png\"\r\n          ],\r\n          browse: 2345\r\n        },\r\n        {\r\n          id: 5002,\r\n          img: \"/static/img/activity2.png\",\r\n          status_str: \"即将开始\",\r\n          name: \"美食品鉴会\",\r\n          activity_time: \"2023-07-20 18:00-21:00\",\r\n          adds_name: \"上海市黄浦区美食中心\",\r\n          is_join: true,\r\n          user_count: 128,\r\n          avatar_list: [\r\n            \"/static/img/avatar.png\",\r\n            \"/static/img/avatar.png\",\r\n            \"/static/img/avatar.png\"\r\n          ],\r\n          browse: 1987\r\n        },\r\n        {\r\n          id: 5003,\r\n          img: \"/static/img/activity3.png\",\r\n          status_str: \"已结束\",\r\n          name: \"城市摄影大赛\",\r\n          activity_time: \"2023-07-01 09:00-17:00\",\r\n          adds_name: \"广州市天河区摄影艺术中心\",\r\n          is_join: false,\r\n          user_count: 0,\r\n          browse: 3456\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    // 如果有tab参数则切换到对应标签\r\n    if (options.bar) {\r\n      this.barIdx = options.bar\r\n    }\r\n    \r\n    // 检查是否启用活动标签\r\n    if (app.globalData?.isActivity) {\r\n      this.barList.push(\"活动\")\r\n    }\r\n  },\r\n  methods: {\r\n    // 搜索点击\r\n    searchClick() {\r\n      if (!this.kw) {\r\n        return this.opTipsPopup(\"请输入搜索关键词\")\r\n      }\r\n      \r\n      this.isThrottling = false\r\n      this.list = []\r\n      this.page = 1\r\n      this.userSearch()\r\n    },\r\n    \r\n    // 执行搜索\r\n    userSearch() {\r\n      let that = this\r\n      that.loadStatus = \"loading\"\r\n      that.isEmpty = false\r\n      \r\n      // 处理活动类型\r\n      let searchType = that.barIdx\r\n      if (that.barIdx > 3 && that.barList[that.barIdx] == \"活动\") {\r\n        searchType = \"activity\"\r\n      }\r\n      \r\n      // 设置瀑布流显示\r\n      that.isWaterfall = app.globalData?.isWaterfall || false\r\n      \r\n      // 检查API是否可用\r\n      if (api.api) {\r\n        let searchUrl = that.isWaterfall ? api.api.waterfallSearchUrl : api.api.searchUrl\r\n        \r\n        if (searchUrl) {\r\n          request(searchUrl, {\r\n            kw: that.kw,\r\n            page: that.page,\r\n            type: searchType\r\n          }).then(function(res) {\r\n            that.isThrottling = true\r\n            that.loadStatus = \"more\"\r\n            \r\n            if (res.data.data.length > 0) {\r\n              if (that.page == 1) {\r\n                that.list = res.data.data\r\n              } else {\r\n                that.list = that.list.concat(res.data.data)\r\n              }\r\n              that.page = res.data.current_page\r\n            } else if (that.page == 1) {\r\n              that.isEmpty = true\r\n            }\r\n          })\r\n        } else {\r\n          that.useMockData()\r\n        }\r\n      } else {\r\n        that.useMockData()\r\n      }\r\n    },\r\n    \r\n    // 使用模拟数据\r\n    useMockData() {\r\n      let that = this\r\n      \r\n      setTimeout(() => {\r\n        that.isThrottling = true\r\n        that.loadStatus = \"more\"\r\n        \r\n        // 根据当前标签选择对应的模拟数据\r\n        let mockData = []\r\n        if (that.barIdx == 0) {\r\n          mockData = that.mockNotes\r\n        } else if (that.barIdx == 1) {\r\n          mockData = that.mockGoods\r\n        } else if (that.barIdx == 2) {\r\n          mockData = that.mockCircles\r\n        } else if (that.barIdx == 3) {\r\n          mockData = that.mockUsers\r\n        } else if (that.barIdx > 3 || that.barList[that.barIdx] == \"活动\") {\r\n          mockData = that.mockActivities\r\n        }\r\n        \r\n        // 如果有搜索关键词则过滤数据\r\n        if (that.kw) {\r\n          let keyword = that.kw.toLowerCase()\r\n          mockData = mockData.filter(item => {\r\n            // 根据不同类型使用不同的字段搜索\r\n            if (that.barIdx == 0) {\r\n              return item.content.toLowerCase().includes(keyword) || \r\n                     item.nickname.toLowerCase().includes(keyword)\r\n            } else if (that.barIdx == 1) {\r\n              return item.name.toLowerCase().includes(keyword) || \r\n                     (item.tags && item.tags.some(tag => tag.toLowerCase().includes(keyword)))\r\n            } else if (that.barIdx == 2) {\r\n              return item.name.toLowerCase().includes(keyword) || \r\n                     item.intro.toLowerCase().includes(keyword)\r\n            } else if (that.barIdx == 3) {\r\n              return item.name.toLowerCase().includes(keyword) || \r\n                     item.province.toLowerCase().includes(keyword)\r\n            } else {\r\n              return item.name.toLowerCase().includes(keyword) || \r\n                     item.adds_name.toLowerCase().includes(keyword)\r\n            }\r\n          })\r\n        }\r\n        \r\n        if (mockData.length > 0) {\r\n          if (that.page == 1) {\r\n            that.list = mockData\r\n          } else {\r\n            that.list = that.list.concat(mockData)\r\n          }\r\n        } else if (that.page == 1) {\r\n          that.isEmpty = true\r\n        }\r\n      }, 500)\r\n    },\r\n    \r\n    // 标签切换\r\n    barClick(e) {\r\n      if (!this.kw) {\r\n        return this.opTipsPopup(\"请输入搜索关键词\")\r\n      }\r\n      \r\n      if (this.isThrottling) {\r\n        this.isThrottling = false\r\n        this.barIdx = e.currentTarget.dataset.idx\r\n        this.list = []\r\n        this.page = 1\r\n        this.userSearch()\r\n      }\r\n    },\r\n    \r\n    // 跳转到详情\r\n    toNavigate(e) {\r\n      let id = e.currentTarget.dataset.id\r\n      let url = \"/pages/note/details?id=\" + id\r\n      \r\n      if (this.barIdx == 1) {\r\n        url = \"/pages/goods/details?id=\" + id\r\n      } else if (this.barIdx == 2) {\r\n        url = \"/pages/note/circle?id=\" + id\r\n      } else if (this.barIdx == 3) {\r\n        url = \"/pages/user/details?id=\" + id\r\n      } else if (this.barIdx > 3 && this.barList[this.barIdx] == \"活动\") {\r\n        url = \"/pages/activity/details?id=\" + id\r\n      }\r\n      \r\n      uni.navigateTo({\r\n        url: url\r\n      })\r\n    },\r\n    \r\n    // 点赞回调\r\n    likeClick(e) {\r\n      this.list[e.idx].is_like = e.is_like\r\n      this.list[e.idx].like_count = e.like_count\r\n    },\r\n    \r\n    // 提示弹窗\r\n    opTipsPopup(msg) {\r\n      let that = this\r\n      that.tipsTitle = msg\r\n      that.$refs.tipsPopup.open()\r\n      \r\n      setTimeout(function() {\r\n        that.$refs.tipsPopup.close()\r\n      }, 2000)\r\n    },\r\n    \r\n    // 返回上一页\r\n    navBack() {\r\n      if (getCurrentPages().length > 1) {\r\n        uni.navigateBack()\r\n      } else {\r\n        uni.switchTab({\r\n          url: \"/pages/index/index\"\r\n        })\r\n      }\r\n    },\r\n\r\n    // 刷新列表\r\n    fetchList() {\r\n      this.isThrottling = false\r\n      this.list = []\r\n      this.page = 1\r\n      this.userSearch()\r\n    },\r\n\r\n    onCardUpdate({ vote_info, idx }) {\r\n      if (this.list[idx]) {\r\n        this.$set(this.list[idx], 'vote_info', vote_info);\r\n      }\r\n    }\r\n  },\r\n  onReachBottom() {\r\n    // 如果列表有内容，加载更多\r\n    if (this.list.length) {\r\n      this.page = this.page + 1\r\n      this.userSearch(false)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.nav-bar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  width: 100%;\r\n  z-index: 99;\r\n  background: #fff;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.back-box .back-item {\r\n  padding: 0 30rpx;\r\n  width: 34rpx;\r\n  height: 100%;\r\n}\r\n\r\n.nav-bar .nav-search {\r\n  width: 100%;\r\n  padding: 20rpx 0;\r\n}\r\n\r\n.nav-bar .nav-search input {\r\n  margin-left: 30rpx;\r\n  width: calc(100% - 200rpx);\r\n  height: 80rpx;\r\n  padding: 0 30rpx;\r\n  font-size: 26rpx;\r\n  font-weight: 700;\r\n  border-radius: 8rpx;\r\n  background: #f8f8f8;\r\n  justify-content: space-between;\r\n}\r\n\r\n.nav-bar .nav-search .btn {\r\n  width: 120rpx;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  text-align: center;\r\n  font-size: 26rpx;\r\n  font-weight: 700;\r\n}\r\n\r\n.nav-bar .nav-search .ph {\r\n  color: #999;\r\n}\r\n\r\n.nav-bar .nav-search image {\r\n  width: 38rpx;\r\n  height: 38rpx;\r\n}\r\n\r\n.nav-bar .bar-box {\r\n  width: 100%;\r\n  height: 80rpx;\r\n}\r\n\r\n.bar-box .bar-item {\r\n  padding: 0 30rpx;\r\n  height: 100%;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n\r\n.bar-box .bar-item text {\r\n  font-weight: 700;\r\n  transition: all .3s ease-in-out;\r\n}\r\n\r\n.bar-item .bar-line {\r\n  position: absolute;\r\n  bottom: 12rpx;\r\n  width: 18rpx;\r\n  height: 6rpx;\r\n  border-radius: 6rpx;\r\n  background: #000;\r\n  transition: opacity .3s ease-in-out;\r\n}\r\n\r\n.content-box {\r\n  width: 100%;\r\n  padding-bottom: 30rpx;\r\n}\r\n\r\n.content-box .dynamic-box {\r\n  width: calc(100% - 16rpx);\r\n  padding: 22rpx 8rpx 0;\r\n}\r\n\r\n.goods {\r\n  width: 100%;\r\n  padding: 20rpx 0 0;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.goods .goods-item {\r\n  width: calc(50% - 19rpx);\r\n  margin: 10rpx 0 0 10rpx;\r\n  background: #fff;\r\n  border-radius: 8rpx;\r\n  overflow: hidden;\r\n  border: 2rpx solid #f5f5f5;\r\n}\r\n\r\n.goods-item .goods-img {\r\n  width: 100%;\r\n  padding-top: 100%;\r\n  position: relative;\r\n}\r\n\r\n.goods-img .goods-img-item {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.goods-item .goods-name {\r\n  width: calc(100% - 40rpx);\r\n  margin: 15rpx 20rpx;\r\n  font-size: 26rpx;\r\n  line-height: 36rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.goods-item .goods-price {\r\n  width: calc(100% - 30rpx);\r\n  margin: 0 20rpx 20rpx;\r\n  display: flex;\r\n  align-items: flex-end;\r\n}\r\n\r\n.goods-price .price-h {\r\n  margin-left: 15rpx;\r\n  color: #999;\r\n  font-size: 20rpx;\r\n  line-height: 20rpx;\r\n}\r\n\r\n.goods-item .goods-tag {\r\n  width: calc(100% - 30rpx);\r\n  margin: 0 15rpx 15rpx;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.goods-tag .tag-item {\r\n  margin: 0 5rpx 5rpx;\r\n  height: 40rpx;\r\n  padding: 0 12rpx;\r\n  line-height: 40rpx;\r\n  font-size: 18rpx;\r\n  font-weight: 500;\r\n  background: #f8f8f8;\r\n  border-radius: 8rpx;\r\n}\r\n\r\n.content-box .circle-box {\r\n  width: calc(100% - 60rpx);\r\n  padding: 30rpx 30rpx 10rpx;\r\n  justify-content: space-between;\r\n}\r\n\r\n.circle-box .circle-avatar {\r\n  width: 168rpx;\r\n  height: 168rpx;\r\n  border-radius: 50%;\r\n  background: #f8f8f8;\r\n  border: 1px solid #f8f8f8;\r\n  position: relative;\r\n}\r\n\r\n.circle-avatar .tag {\r\n  position: absolute;\r\n  right: 0;\r\n  bottom: 0;\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  border-radius: 50%;\r\n  border: 6rpx solid #fff;\r\n}\r\n\r\n.circle-box .circle-item {\r\n  width: calc(100% - 198rpx);\r\n  margin-left: 30rpx;\r\n}\r\n\r\n.circle-item .name {\r\n  font-size: 32rpx;\r\n  font-weight: 700;\r\n}\r\n\r\n.circle-item .intro {\r\n  margin: 10rpx 0 20rpx;\r\n  color: #999;\r\n  font-size: 24rpx;\r\n}\r\n\r\n.cu-img-group {\r\n  direction: ltr;\r\n  unicode-bidi: bidi-override;\r\n  display: inline-block;\r\n  margin-left: 16rpx;\r\n}\r\n\r\n.cu-img-group .cu-img {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  display: inline-flex;\r\n  position: relative;\r\n  margin-left: -16rpx;\r\n  border: 4rpx solid #fff;\r\n  background: #f8f8f8;\r\n  vertical-align: middle;\r\n  border-radius: 50%;\r\n}\r\n\r\n.cu-img-group .cu-img image {\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 50%;\r\n}\r\n\r\n.cu-img-group .cu-txt {\r\n  margin-left: 10rpx;\r\n  display: inline-flex;\r\n  color: #999;\r\n  font-size: 20rpx;\r\n  font-weight: 700;\r\n}\r\n\r\n.content-box .user-box {\r\n  width: calc(100% - 60rpx);\r\n  padding: 30rpx;\r\n  justify-content: space-between;\r\n}\r\n\r\n.user-box .user-avatar {\r\n  width: 100rpx;\r\n  height: 100rpx;\r\n  border-radius: 50%;\r\n  border: 1px solid #f8f8f8;\r\n  overflow: hidden;\r\n}\r\n\r\n.user-box .user-item {\r\n  width: calc(100% - 120rpx - 2px);\r\n  margin-left: 20rpx;\r\n}\r\n\r\n.user-item .name {\r\n  font-size: 28rpx;\r\n  font-weight: 700;\r\n}\r\n\r\n.user-item .unm {\r\n  margin: 5rpx 0 10rpx;\r\n  color: #999;\r\n  font-size: 20rpx;\r\n}\r\n\r\n.user-tag .tag-item {\r\n  margin-right: 16rpx;\r\n  height: 44rpx;\r\n  padding: 0 14rpx;\r\n  border-radius: 8rpx;\r\n  background: #f8f8f8;\r\n  font-weight: 500;\r\n  font-size: 20rpx;\r\n  justify-content: center;\r\n}\r\n\r\n.user-tag .tag-item image {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n}\r\n\r\n.activity-item {\r\n  width: calc(100% - 60rpx);\r\n  padding: 30rpx;\r\n}\r\n\r\n.activity-item .activity-img {\r\n  width: 275rpx;\r\n  height: 220rpx;\r\n  border-radius: 8rpx;\r\n  background: #f8f8f8;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.activity-img .activity-state {\r\n  position: absolute;\r\n  top: 15rpx;\r\n  left: 15rpx;\r\n  width: 68rpx;\r\n  height: 40rpx;\r\n  color: #fff;\r\n  font-size: 16rpx;\r\n  font-weight: 700;\r\n  border-radius: 8rpx;\r\n  background: rgba(0, 0, 0, .6);\r\n  justify-content: center;\r\n}\r\n\r\n.activity-item .activity-data {\r\n  padding-left: 20rpx;\r\n  width: calc(100% - 295rpx);\r\n  height: 220rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n  position: relative;\r\n}\r\n\r\n.activity-data .title {\r\n  font-size: 28rpx;\r\n  line-height: 28rpx;\r\n  font-weight: 700;\r\n  padding-bottom: 12rpx;\r\n}\r\n\r\n.activity-data .txt {\r\n  margin-bottom: 4rpx;\r\n}\r\n\r\n.activity-data .txt image {\r\n  margin-right: 8rpx;\r\n  width: 20rpx;\r\n  height: 20rpx;\r\n}\r\n\r\n.activity-data .txt view {\r\n  width: calc(100% - 26rpx);\r\n  color: #999;\r\n  font-size: 20rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.activity-data .cu-img-group {\r\n  margin: 8rpx 0 16rpx 16rpx;\r\n  direction: ltr;\r\n  unicode-bidi: bidi-override;\r\n  display: inline-block;\r\n}\r\n\r\n.cu-img-group .cu-tit {\r\n  display: inline-flex;\r\n  margin-left: 8rpx;\r\n  color: #999;\r\n  font-size: 20rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.activity-data .cu-txt-group {\r\n  margin: 8rpx 0 16rpx;\r\n  font-size: 20rpx;\r\n  font-weight: 500;\r\n  color: #999;\r\n}\r\n\r\n.activity-data .activity-btn {\r\n  width: 100%;\r\n  height: 60rpx;\r\n  justify-content: space-between;\r\n}\r\n\r\n.activity-btn .btn-item {\r\n  padding: 0;\r\n  margin: 0;\r\n  height: 60rpx;\r\n  font-size: 20rpx;\r\n  font-weight: 700;\r\n  color: #000;\r\n  background: #f8f8f8;\r\n  border-radius: 8rpx;\r\n  justify-content: center;\r\n}\r\n\r\n.activity-btn .btn-item .icon {\r\n  margin-left: 10rpx;\r\n  width: 20rpx;\r\n  height: 20rpx;\r\n}\r\n\r\n.activity-btn .btn-item .img {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n}\r\n\r\n.w100 {\r\n  width: 100%;\r\n}\r\n\r\n.empty-box {\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n}\r\n\r\n.empty-box image {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.empty-box .e1 {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.empty-box .e2 {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.tips-box {\r\n  padding: 20rpx 30rpx;\r\n  border-radius: 12rpx;\r\n  justify-content: center;\r\n}\r\n\r\n.tips-box .tips-item {\r\n  color: #fff;\r\n  font-size: 28rpx;\r\n  font-weight: 700;\r\n}\r\n\r\n.df {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ohto {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.ohto2 {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n}\r\n</style>", "import MiniProgramPage from 'Z:/WWW/shejiao/vue3/pages/search/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["api.api", "request", "uni"], "mappings": ";;;;;AAkLA,kBAAkB,MAAW;AAC7B,eAAe,MAAW;AAC1B,MAAO,QAAO,MAAW;AACzB,MAAO,cAAa,MAAW;AAC/B,MAAK,YAAa,MAAW;AAC7B,MAAK,YAAa,MAAW;AAE7B,MAAM,MAAM,OAAM;AAElB,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB,KAAK,OAAO,MAAM,mBAAmB;AAAA,MACtD,gBAAgB,KAAK,OAAO,MAAM,kBAAkB;AAAA,MACpD,IAAI;AAAA,MACJ,SAAS,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAChC,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,MAAM,CAAE;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,aAAa;AAAA;AAAA,MAGb,WAAW;AAAA,QACT;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,WAAW;AAAA,UACX,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,YACJ,EAAE,KAAK,yBAAyB,MAAM,KAAK,MAAM,KAAM;AAAA,YACvD,EAAE,KAAK,yBAAyB,MAAM,KAAK,MAAM,KAAK;AAAA,UACvD;AAAA,UACD,WAAW;AAAA,UACX,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,iBAAiB;AAAA,QAClB;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,WAAW;AAAA,UACX,SAAS;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,YACL,KAAK;AAAA,YACL,OAAO;AAAA,YACP,MAAM;AAAA,YACN,MAAM;AAAA,UACP;AAAA,UACD,WAAW;AAAA,UACX,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,iBAAiB;AAAA,QAClB;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,WAAW;AAAA,UACX,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,YACJ,EAAE,KAAK,yBAAyB,MAAM,KAAK,MAAM,KAAM;AAAA,YACvD,EAAE,KAAK,yBAAyB,MAAM,KAAK,MAAM,KAAM;AAAA,YACvD,EAAE,KAAK,yBAAyB,MAAM,KAAK,MAAM,KAAK;AAAA,UACvD;AAAA,UACD,WAAW;AAAA,UACX,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,iBAAiB;AAAA,QACnB;AAAA,MACD;AAAA;AAAA,MAGD,WAAW;AAAA,QACT;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM,CAAC,0BAA0B;AAAA,UACjC,SAAS;AAAA,YACP,OAAO;AAAA,YACP,YAAY;AAAA,UACb;AAAA,UACD,KAAK;AAAA,UACL,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,MAAM,CAAC,MAAM,IAAI;AAAA,QAClB;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM,CAAC,0BAA0B;AAAA,UACjC,SAAS;AAAA,YACP,OAAO;AAAA,YACP,YAAY;AAAA,UACb;AAAA,UACD,KAAK;AAAA,UACL,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,MAAM,CAAC,MAAM;AAAA,QACd;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM,CAAC,0BAA0B;AAAA,UACjC,SAAS;AAAA,YACP,OAAO;AAAA,YACP,YAAY;AAAA,UACb;AAAA,UACD,KAAK;AAAA,UACL,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,MAAM,CAAC,IAAI;AAAA,QACb;AAAA,MACD;AAAA;AAAA,MAGD,aAAa;AAAA,QACX;AAAA,UACE,IAAI;AAAA,UACJ,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,eAAe;AAAA,UACf,MAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,eAAe;AAAA,UACf,MAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,eAAe;AAAA,UACf,MAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACD;AAAA;AAAA,MAGD,WAAW;AAAA,QACT;AAAA,UACE,IAAI;AAAA,UACJ,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,UACN,QAAQ;AAAA;AAAA,UACR,KAAK;AAAA,UACL,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,KAAK;AAAA,UACL,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,KAAK;AAAA,UACL,UAAU;AAAA,QACZ;AAAA,MACD;AAAA;AAAA,MAGD,gBAAgB;AAAA,QACd;AAAA,UACE,IAAI;AAAA,UACJ,KAAK;AAAA,UACL,YAAY;AAAA,UACZ,MAAM;AAAA,UACN,eAAe;AAAA,UACf,WAAW;AAAA,UACX,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,aAAa;AAAA,YACX;AAAA,YACA;AAAA,YACA;AAAA,UACD;AAAA,UACD,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,KAAK;AAAA,UACL,YAAY;AAAA,UACZ,MAAM;AAAA,UACN,eAAe;AAAA,UACf,WAAW;AAAA,UACX,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,aAAa;AAAA,YACX;AAAA,YACA;AAAA,YACA;AAAA,UACD;AAAA,UACD,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,KAAK;AAAA,UACL,YAAY;AAAA,UACZ,MAAM;AAAA,UACN,eAAe;AAAA,UACf,WAAW;AAAA,UACX,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,OAAO,SAAS;;AAEd,QAAI,QAAQ,KAAK;AACf,WAAK,SAAS,QAAQ;AAAA,IACxB;AAGA,SAAI,SAAI,eAAJ,mBAAgB,YAAY;AAC9B,WAAK,QAAQ,KAAK,IAAI;AAAA,IACxB;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,cAAc;AACZ,UAAI,CAAC,KAAK,IAAI;AACZ,eAAO,KAAK,YAAY,UAAU;AAAA,MACpC;AAEA,WAAK,eAAe;AACpB,WAAK,OAAO,CAAC;AACb,WAAK,OAAO;AACZ,WAAK,WAAW;AAAA,IACjB;AAAA;AAAA,IAGD,aAAa;;AACX,UAAI,OAAO;AACX,WAAK,aAAa;AAClB,WAAK,UAAU;AAGf,UAAI,aAAa,KAAK;AACtB,UAAI,KAAK,SAAS,KAAK,KAAK,QAAQ,KAAK,MAAM,KAAK,MAAM;AACxD,qBAAa;AAAA,MACf;AAGA,WAAK,gBAAc,SAAI,eAAJ,mBAAgB,gBAAe;AAGlD,UAAIA,gBAAS;AACX,YAAI,YAAY,KAAK,cAAcA,WAAAA,IAAQ,qBAAqBA,WAAAA,IAAQ;AAExE,YAAI,WAAW;AACbC,wBAAAA,QAAQ,WAAW;AAAA,YACjB,IAAI,KAAK;AAAA,YACT,MAAM,KAAK;AAAA,YACX,MAAM;AAAA,UACR,CAAC,EAAE,KAAK,SAAS,KAAK;AACpB,iBAAK,eAAe;AACpB,iBAAK,aAAa;AAElB,gBAAI,IAAI,KAAK,KAAK,SAAS,GAAG;AAC5B,kBAAI,KAAK,QAAQ,GAAG;AAClB,qBAAK,OAAO,IAAI,KAAK;AAAA,qBAChB;AACL,qBAAK,OAAO,KAAK,KAAK,OAAO,IAAI,KAAK,IAAI;AAAA,cAC5C;AACA,mBAAK,OAAO,IAAI,KAAK;AAAA,YACvB,WAAW,KAAK,QAAQ,GAAG;AACzB,mBAAK,UAAU;AAAA,YACjB;AAAA,WACD;AAAA,eACI;AACL,eAAK,YAAY;AAAA,QACnB;AAAA,aACK;AACL,aAAK,YAAY;AAAA,MACnB;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AACZ,UAAI,OAAO;AAEX,iBAAW,MAAM;AACf,aAAK,eAAe;AACpB,aAAK,aAAa;AAGlB,YAAI,WAAW,CAAC;AAChB,YAAI,KAAK,UAAU,GAAG;AACpB,qBAAW,KAAK;AAAA,mBACP,KAAK,UAAU,GAAG;AAC3B,qBAAW,KAAK;AAAA,mBACP,KAAK,UAAU,GAAG;AAC3B,qBAAW,KAAK;AAAA,mBACP,KAAK,UAAU,GAAG;AAC3B,qBAAW,KAAK;AAAA,mBACP,KAAK,SAAS,KAAK,KAAK,QAAQ,KAAK,MAAM,KAAK,MAAM;AAC/D,qBAAW,KAAK;AAAA,QAClB;AAGA,YAAI,KAAK,IAAI;AACX,cAAI,UAAU,KAAK,GAAG,YAAY;AAClC,qBAAW,SAAS,OAAO,UAAQ;AAEjC,gBAAI,KAAK,UAAU,GAAG;AACpB,qBAAO,KAAK,QAAQ,YAAW,EAAG,SAAS,OAAO,KAC3C,KAAK,SAAS,cAAc,SAAS,OAAO;AAAA,uBAC1C,KAAK,UAAU,GAAG;AAC3B,qBAAO,KAAK,KAAK,YAAW,EAAG,SAAS,OAAO,KACvC,KAAK,QAAQ,KAAK,KAAK,KAAK,SAAO,IAAI,YAAa,EAAC,SAAS,OAAO,CAAC;AAAA,uBACrE,KAAK,UAAU,GAAG;AAC3B,qBAAO,KAAK,KAAK,YAAW,EAAG,SAAS,OAAO,KACxC,KAAK,MAAM,cAAc,SAAS,OAAO;AAAA,uBACvC,KAAK,UAAU,GAAG;AAC3B,qBAAO,KAAK,KAAK,YAAW,EAAG,SAAS,OAAO,KACxC,KAAK,SAAS,cAAc,SAAS,OAAO;AAAA,mBAC9C;AACL,qBAAO,KAAK,KAAK,YAAW,EAAG,SAAS,OAAO,KACxC,KAAK,UAAU,cAAc,SAAS,OAAO;AAAA,YACtD;AAAA,WACD;AAAA,QACH;AAEA,YAAI,SAAS,SAAS,GAAG;AACvB,cAAI,KAAK,QAAQ,GAAG;AAClB,iBAAK,OAAO;AAAA,iBACP;AACL,iBAAK,OAAO,KAAK,KAAK,OAAO,QAAQ;AAAA,UACvC;AAAA,QACF,WAAW,KAAK,QAAQ,GAAG;AACzB,eAAK,UAAU;AAAA,QACjB;AAAA,MACD,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAGD,SAAS,GAAG;AACV,UAAI,CAAC,KAAK,IAAI;AACZ,eAAO,KAAK,YAAY,UAAU;AAAA,MACpC;AAEA,UAAI,KAAK,cAAc;AACrB,aAAK,eAAe;AACpB,aAAK,SAAS,EAAE,cAAc,QAAQ;AACtC,aAAK,OAAO,CAAC;AACb,aAAK,OAAO;AACZ,aAAK,WAAW;AAAA,MAClB;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,GAAG;AACZ,UAAI,KAAK,EAAE,cAAc,QAAQ;AACjC,UAAI,MAAM,4BAA4B;AAEtC,UAAI,KAAK,UAAU,GAAG;AACpB,cAAM,6BAA6B;AAAA,iBAC1B,KAAK,UAAU,GAAG;AAC3B,cAAM,2BAA2B;AAAA,iBACxB,KAAK,UAAU,GAAG;AAC3B,cAAM,4BAA4B;AAAA,iBACzB,KAAK,SAAS,KAAK,KAAK,QAAQ,KAAK,MAAM,KAAK,MAAM;AAC/D,cAAM,gCAAgC;AAAA,MACxC;AAEAC,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,OACD;AAAA,IACF;AAAA;AAAA,IAGD,UAAU,GAAG;AACX,WAAK,KAAK,EAAE,GAAG,EAAE,UAAU,EAAE;AAC7B,WAAK,KAAK,EAAE,GAAG,EAAE,aAAa,EAAE;AAAA,IACjC;AAAA;AAAA,IAGD,YAAY,KAAK;AACf,UAAI,OAAO;AACX,WAAK,YAAY;AACjB,WAAK,MAAM,UAAU,KAAK;AAE1B,iBAAW,WAAW;AACpB,aAAK,MAAM,UAAU,MAAM;AAAA,MAC5B,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,UAAU;AACR,UAAI,gBAAe,EAAG,SAAS,GAAG;AAChCA,sBAAAA,MAAI,aAAa;AAAA,aACZ;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,KAAK;AAAA,SACN;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACV,WAAK,eAAe;AACpB,WAAK,OAAO,CAAC;AACb,WAAK,OAAO;AACZ,WAAK,WAAW;AAAA,IACjB;AAAA,IAED,aAAa,EAAE,WAAW,OAAO;AAC/B,UAAI,KAAK,KAAK,GAAG,GAAG;AAClB,aAAK,KAAK,KAAK,KAAK,GAAG,GAAG,aAAa,SAAS;AAAA,MAClD;AAAA,IACF;AAAA,EACD;AAAA,EACD,gBAAgB;AAEd,QAAI,KAAK,KAAK,QAAQ;AACpB,WAAK,OAAO,KAAK,OAAO;AACxB,WAAK,WAAW,KAAK;AAAA,IACvB;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrpBA,GAAG,WAAW,eAAe;"}