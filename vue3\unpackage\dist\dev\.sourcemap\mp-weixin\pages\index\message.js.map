{"version": 3, "file": "message.js", "sources": ["pages/index/message.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvbWVzc2FnZS52dWU"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 顶部导航栏 -->\n    <view class=\"nav-box bfw\" :style=\"{'padding-top': statusBarHeight + 'px'}\">\n      <view class=\"nav-title df\" :style=\"{'height': titleBarHeight + 'px'}\">\n        <view>消息</view>\n        <view class=\"nav-del df\" @tap=\"allLook\">\n          <image src=\"/static/img/qc.png\"></image>\n        </view>\n      </view>\n    </view>\n\n    <!-- 功能图标区域 -->\n    <view class=\"icon-section\" :style=\"{'margin-top': 'calc(' + (statusBarHeight + titleBarHeight) + 'px + 20rpx)'}\">\n      <view class=\"icon-row\">\n        <view class=\"icon-item\" @tap=\"goToLike\">\n          <view class=\"icon-wrapper like-icon\">\n            <image src=\"/static/img/qz2.png\" class=\"icon-image\"></image>\n          </view>\n          <text class=\"icon-label\">喜欢</text>\n        </view>\n\n        <view class=\"icon-item\" @tap=\"goToComment\">\n          <view class=\"icon-wrapper comment-icon\">\n            <image src=\"/static/img/qz2.png\" class=\"icon-image\"></image>\n          </view>\n          <text class=\"icon-label\">评论</text>\n        </view>\n\n        <view class=\"icon-item\" @tap=\"goToFavorite\">\n          <view class=\"icon-wrapper favorite-icon\">\n            <image src=\"/static/img/qz2.png\" class=\"icon-image\"></image>\n          </view>\n          <text class=\"icon-label\">收藏</text>\n        </view>\n\n        <view class=\"icon-item\" @tap=\"goToContacts\">\n          <view class=\"icon-wrapper contacts-icon\">\n            <image src=\"/static/img/qz2.png\" class=\"icon-image\"></image>\n            <view v-if=\"contactsCount > 0\" class=\"icon-badge\">{{ contactsCount }}</view>\n          </view>\n          <text class=\"icon-label\">人脉</text>\n        </view>\n      </view>\n    </view>\n    <!-- 消息列表 -->\n    <view class=\"message-list\">\n      <emptyPage\n        v-if=\"isEmpty\"\n        title=\"暂无相关通知\"\n        description=\"在信息爆炸的时代，这里格外宁静\"\n        image=\"/static/img/qz2.png\"\n      />\n\n      <block v-else>\n        <view v-for=\"(item, index) in messageList\" :key=\"index\" class=\"message-item\" @tap=\"() => handleMessageClick(item, index)\">\n          <view class=\"message-left\">\n            <view class=\"message-icon-wrapper\" :class=\"item.iconClass\">\n              <image src=\"/static/img/qz2.png\" class=\"message-icon\"></image>\n            </view>\n          </view>\n\n          <view class=\"message-content\">\n            <view class=\"message-header\">\n              <text class=\"message-title\">{{ item.title }}</text>\n              <view class=\"message-right\">\n                <text class=\"message-time\">{{ item.time }}</text>\n                <view v-if=\"item.unreadCount > 0\" class=\"message-badge\">{{ item.unreadCount }}</view>\n              </view>\n            </view>\n            <view class=\"message-desc\">{{ item.description }}</view>\n          </view>\n        </view>\n      </block>\n\n      <uni-load-more v-if=\"!isEmpty\" :status=\"loadStatus\"></uni-load-more>\n    </view>\n    <uni-popup ref=\"morePopup\" type=\"bottom\" :safe-area=\"false\">\n      <view class=\"more-popup\" catchtouchmove=\"true\">\n        <view class=\"more-item df\" @tap=\"readNotice\">\n          <text>已读</text>\n          <image src=\"/static/img/temp/mail.png\"></image>\n        </view>\n        <view class=\"more-item df\" @tap=\"delNotice\">\n          <text style=\"color:#FA5150\">删除</text>\n          <image src=\"/static/img/temp/del_r.png\"></image>\n        </view>\n        <view class=\"more-item\" @tap=\"closeMoreClick\">\n          <text style=\"color:#999\">取消</text>\n        </view>\n      </view>\n    </uni-popup>\n    <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\n      <view class=\"tips-box df\">\n        <view class=\"tips-item bfh\">{{tipsTitle}}</view>\n      </view>\n    </uni-popup>\n    <tabbar v-if=\"true\" :currentPage=\"3\" :currentMsg=\"currentMsg\"></tabbar>\n  </view>\n</template>\n\n<script>\nimport uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more'\nimport tabbar from '@/components/tabbar/tabbar'\nimport emptyPage from '@/components/emptyPage/emptyPage.vue'\n\nexport default {\n  components: {\n    uniLoadMore,\n    tabbar,\n    emptyPage\n  },\n  data() {\n    return {\n      statusBarHeight: this.$store.state.statusBarHeight || 20,\n      titleBarHeight: this.$store.state.titleBarHeight || 44,\n      currentMsg: 0,\n      contactsCount: 2, // 人脉红点数量\n      isEmpty: false,\n      loadStatus: 'more',\n      tipsTitle: '',\n      // 消息列表数据\n      messageList: [\n        {\n          id: 1,\n          title: '应用消息',\n          description: '生活是流星，开启一段新的旅行',\n          time: '18:36',\n          unreadCount: 1,\n          iconClass: 'app-icon',\n          type: 'app'\n        },\n        {\n          id: 2,\n          title: '系统公告',\n          description: '熟读《图鸟人脉防骗指南》',\n          time: '12:29',\n          unreadCount: 12,\n          iconClass: 'system-icon',\n          type: 'system'\n        },\n        {\n          id: 3,\n          title: '人脉通知',\n          description: '有两位新的朋友申请待通过',\n          time: '11:06',\n          unreadCount: 2,\n          iconClass: 'contact-icon',\n          type: 'contact'\n        },\n        {\n          id: 4,\n          title: '智能助手',\n          description: '人工智能，助力您的魅力值提升',\n          time: '10:68',\n          unreadCount: 0,\n          iconClass: 'ai-icon',\n          type: 'ai'\n        },\n        {\n          id: 5,\n          title: '付衣衣',\n          description: '我喜欢你，就像你喜欢我一样',\n          time: '08:26',\n          unreadCount: 0,\n          iconClass: 'user-icon',\n          type: 'user',\n          avatar: '/static/img/logo.png'\n        }\n      ]\n    }\n  },\n  async onLoad() {\n    this.$store.commit('SET_CURRENT_MSG', false)\n    await this.$onLaunched\n    this.$store.commit('SET_CURRENT_MSG', true)\n\n    // 初始化页面数据\n    this.initPageData()\n  },\n  onShow() {\n    // 每次显示页面时更新消息数量\n    this.initPageData()\n  },\n  onPullDownRefresh() {\n    this.initPageData()\n    uni.stopPullDownRefresh()\n  },\n\n  onReachBottom() {\n    // 这里可以实现加载更多逻辑\n    console.log('到达底部，可以加载更多消息')\n  },\n  onShareAppMessage() {\n    return {\n      title: this.$store.state.appInfo?.title || '小程序示例',\n      imageUrl: this.$store.state.appInfo?.shareImg || '/static/img/temp/1.jpg'\n    }\n  },\n  onShareTimeline() {\n    return {\n      title: this.$store.state.appInfo?.title || '小程序示例',\n      imageUrl: this.$store.state.appInfo?.shareImg || '/static/img/temp/1.jpg'\n    }\n  },\n  methods: {\n    // 顶部功能图标点击事件\n    goToLike() {\n      uni.navigateTo({\n        url: '/pages/like/index'\n      })\n    },\n\n    goToComment() {\n      uni.navigateTo({\n        url: '/pages/comment/index'\n      })\n    },\n\n    goToFavorite() {\n      uni.navigateTo({\n        url: '/pages/favorite/index'\n      })\n    },\n\n    goToContacts() {\n      uni.navigateTo({\n        url: '/pages/contacts/index'\n      })\n    },\n\n    // 消息项点击事件\n    handleMessageClick(item, index) {\n      // 根据消息类型跳转到不同页面\n      switch(item.type) {\n        case 'app':\n          uni.navigateTo({\n            url: '/pages/app-message/index'\n          })\n          break\n        case 'system':\n          uni.navigateTo({\n            url: '/pages/system-notice/index'\n          })\n          break\n        case 'contact':\n          uni.navigateTo({\n            url: '/pages/contact-notice/index'\n          })\n          break\n        case 'ai':\n          uni.navigateTo({\n            url: '/pages/ai-assistant/index'\n          })\n          break\n        case 'user':\n          uni.navigateTo({\n            url: '/pages/chat/index?userId=' + item.id\n          })\n          break\n        default:\n          console.log('未知消息类型')\n      }\n\n      // 如果有未读消息，标记为已读\n      if (item.unreadCount > 0) {\n        this.markAsRead(item, index)\n      }\n    },\n\n    // 标记消息为已读\n    markAsRead(item, index) {\n      this.messageList[index].unreadCount = 0\n      // 这里可以调用API标记为已读\n      console.log('标记消息已读:', item.title)\n    },\n\n    // 初始化页面数据\n    initPageData() {\n      // 计算总的未读消息数量\n      this.currentMsg = this.messageList.reduce((total, item) => total + item.unreadCount, 0)\n\n      // 更新全局消息状态\n      if (this.currentMsg > 0) {\n        this.$store.commit('SET_UNREAD_MESSAGE_COUNT', this.currentMsg)\n      }\n    },\n\n    // 全部标记为已读\n    allLook() {\n      if (this.currentMsg === 0) {\n        uni.showToast({\n          title: '暂无未读消息！',\n          icon: 'none'\n        })\n        return\n      }\n\n      // 标记所有消息为已读\n      this.messageList.forEach(item => {\n        item.unreadCount = 0\n      })\n\n      this.currentMsg = 0\n      this.contactsCount = 0\n\n      // 更新全局状态\n      this.$store.commit('SET_UNREAD_MESSAGE_COUNT', 0)\n\n      uni.showToast({\n        title: '已将所有消息标记为已读！',\n        icon: 'success'\n      })\n    },\n\n    // 提示弹窗\n    opTipsPopup(title) {\n      this.tipsTitle = title\n      this.$refs.tipsPopup.open()\n\n      setTimeout(() => {\n        this.$refs.tipsPopup.close()\n      }, 2000)\n    }\n  }\n}\n</script>\n\n<style>\n.container {\n  min-height: 100vh;\n  background: #f8f8f8;\n}\n\n/* 顶部导航栏 */\n.nav-box {\n  position: fixed;\n  top: 0;\n  width: 100%;\n  z-index: 99;\n  box-sizing: border-box;\n}\n\n.nav-box .nav-title {\n  padding: 0 30rpx;\n  font-size: 40rpx;\n  font-weight: 700;\n}\n\n.nav-title .nav-del {\n  margin-left: 15rpx;\n  width: 40rpx;\n  height: 40rpx;\n  border-radius: 50%;\n  background: #f8f8f8;\n  border: 1px solid #f5f5f5;\n  justify-content: center;\n}\n\n.nav-title .nav-del image {\n  width: 28rpx;\n  height: 28rpx;\n}\n\n.df {\n  display: flex;\n  align-items: center;\n}\n\n.bfw {\n  background: #fff;\n}\n\n/* 功能图标区域 */\n.icon-section {\n  padding: 20rpx 30rpx;\n  background: #fff;\n}\n\n.icon-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.icon-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  flex: 1;\n}\n\n.icon-wrapper {\n  position: relative;\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 16rpx;\n}\n\n.like-icon {\n  background: linear-gradient(135deg, #ff6b9d, #ff8a9b);\n}\n\n.comment-icon {\n  background: linear-gradient(135deg, #ffd93d, #ffb347);\n}\n\n.favorite-icon {\n  background: linear-gradient(135deg, #6bcf7f, #4ecdc4);\n}\n\n.contacts-icon {\n  background: linear-gradient(135deg, #74b9ff, #0984e3);\n}\n\n.icon-image {\n  width: 48rpx;\n  height: 48rpx;\n  filter: brightness(0) invert(1);\n}\n\n.icon-badge {\n  position: absolute;\n  top: -8rpx;\n  right: -8rpx;\n  min-width: 34rpx;\n  height: 34rpx;\n  line-height: 34rpx;\n  text-align: center;\n  font-size: 20rpx;\n  font-weight: 700;\n  color: #fff;\n  background: #ff4757;\n  border-radius: 34rpx;\n  border: 2rpx solid #fff;\n}\n\n.icon-label {\n  font-size: 24rpx;\n  color: #999;\n  font-weight: 400;\n}\n\n/* 消息列表 */\n.message-list {\n  background: #fff;\n  margin-top: 20rpx;\n  padding-bottom: 180rpx;\n}\n\n.message-item {\n  display: flex;\n  align-items: center;\n  padding: 24rpx 30rpx;\n  border-bottom: 1px solid #f5f5f5;\n}\n\n.message-item:last-child {\n  border-bottom: none;\n}\n\n.message-left {\n  margin-right: 24rpx;\n}\n\n.message-icon-wrapper {\n  position: relative;\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 16rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.app-icon {\n  background: linear-gradient(135deg, #74b9ff, #0984e3);\n}\n\n.system-icon {\n  background: linear-gradient(135deg, #ffd93d, #ffb347);\n}\n\n.contact-icon {\n  background: linear-gradient(135deg, #6bcf7f, #4ecdc4);\n}\n\n.ai-icon {\n  background: linear-gradient(135deg, #74b9ff, #0984e3);\n}\n\n.user-icon {\n  background: #f5f5f5;\n  border-radius: 50%;\n  overflow: hidden;\n}\n\n.message-icon {\n  width: 40rpx;\n  height: 40rpx;\n  filter: brightness(0) invert(1);\n}\n\n.user-icon .message-icon {\n  width: 80rpx;\n  height: 80rpx;\n  filter: none;\n}\n\n.message-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.message-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 8rpx;\n}\n\n.message-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #000;\n}\n\n.message-right {\n  display: flex;\n  align-items: center;\n}\n\n.message-time {\n  font-size: 24rpx;\n  color: #999;\n  margin-right: 12rpx;\n}\n\n.message-badge {\n  min-width: 34rpx;\n  height: 34rpx;\n  line-height: 34rpx;\n  text-align: center;\n  font-size: 20rpx;\n  font-weight: 700;\n  color: #fff;\n  background: #ff4757;\n  border-radius: 34rpx;\n}\n\n.message-desc {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.4;\n}\n\n/* 空状态 */\n.empty-box {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 120rpx 0;\n  background: #fff;\n}\n\n.empty-image {\n  width: 280rpx;\n  height: 280rpx;\n  opacity: 0.6;\n}\n\n.empty-title {\n  margin-top: 40rpx;\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.empty-desc {\n  margin-top: 20rpx;\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 弹窗样式 */\n.more-popup {\n  width: 100%;\n  background: #fff;\n  border-radius: 30rpx 30rpx 0 0;\n  overflow: hidden;\n}\n\n.more-popup .more-item {\n  width: calc(100% - 60rpx);\n  padding: 30rpx;\n  font-size: 26rpx;\n  font-weight: 700;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.more-popup .more-item:first-child {\n  padding-top: 40rpx;\n}\n\n.more-popup .more-item:last-child {\n  padding-bottom: 80rpx;\n}\n\n.more-popup .more-item image {\n  width: 36rpx;\n  height: 36rpx;\n}\n\n.tips-box {\n  width: 100%;\n  display: flex;\n  justify-content: center;\n}\n\n.tips-item {\n  background: rgba(0, 0, 0, 0.8);\n  color: #fff;\n  padding: 20rpx 40rpx;\n  border-radius: 8rpx;\n  font-size: 28rpx;\n}\n</style>\n", "import MiniProgramPage from 'Z:/WWW/shejiao/vue3/pages/index/message.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAsGA,MAAA,cAAA,MAAA;AACA,MAAA,SAAA,MAAA;AACA,MAAA,YAAA,MAAA;AAEA,MAAA,YAAA;AAAA;;IAGI;AAAA,IACA;AAAA;EAEF,OAAA;AACE,WAAA;AAAA;;;;;;MAME,YAAA;AAAA;;;QAIE;AAAA;;;;;UAME,WAAA;AAAA,UACA,MAAA;AAAA;QAEF;AAAA;;;;UAKE,aAAA;AAAA;;;QAIF;AAAA;;;;;;;;QASA;AAAA;;;;;UAME,WAAA;AAAA,UACA,MAAA;AAAA;QAEF;AAAA;;;;;UAME,WAAA;AAAA;;QAGF;AAAA,MACF;AAAA,IACF;AAAA;;AAGA,SAAA,OAAA,OAAA,mBAAA,KAAA;AACA,UAAA,KAAA;AACA,SAAA,OAAA,OAAA,mBAAA,IAAA;AAGA,SAAA,aAAA;AAAA;EAEF,SAAA;AAEE,SAAA,aAAA;AAAA;EAEF,oBAAA;AACE,SAAA,aAAA;;;EAIF,gBAAA;AAEEA,kBAAAA,MAAA,MAAA,OAAA,kCAAA,eAAA;AAAA;EAEF,oBAAA;;AACE,WAAA;AAAA;MAEE,YAAA,UAAA,OAAA,MAAA,YAAA,mBAAA,aAAA;AAAA,IACF;AAAA;EAEF,kBAAA;;AACE,WAAA;AAAA;MAEE,YAAA,UAAA,OAAA,MAAA,YAAA,mBAAA,aAAA;AAAA,IACF;AAAA;EAEF,SAAA;AAAA;AAAA;AAGIA,oBAAAA,MAAA,WAAA;AAAA;;;;AAMAA,oBAAAA,MAAA,WAAA;AAAA;;;IAKF,eAAA;AACEA,oBAAAA,MAAA,WAAA;AAAA;;;IAKF,eAAA;AACEA,oBAAAA,MAAA,WAAA;AAAA;;;;IAMF,mBAAA,MAAA,OAAA;AAEE,cAAA,KAAA,MAAA;AAAA,QACE,KAAA;AACEA,wBAAAA,MAAA,WAAA;AAAA;;;;AAKAA,wBAAAA,MAAA,WAAA;AAAA,YACE,KAAA;AAAA;;;AAIFA,wBAAAA,MAAA,WAAA;AAAA,YACE,KAAA;AAAA;;QAGJ,KAAA;AACEA,wBAAAA,MAAA,WAAA;AAAA,YACE,KAAA;AAAA;;;AAIFA,wBAAAA,MAAA,WAAA;AAAA,YACE,KAAA,8BAAA,KAAA;AAAA;;QAGJ;;MAEF;;;MAKA;AAAA;;;AAKA,WAAA,YAAA,KAAA,EAAA,cAAA;AAEAA,oBAAA,MAAA,MAAA,OAAA,kCAAA,WAAA,KAAA,KAAA;AAAA;;IAIF,eAAA;AAEE,WAAA,aAAA,KAAA,YAAA,OAAA,CAAA,OAAA,SAAA,QAAA,KAAA,aAAA,CAAA;AAGA,UAAA,KAAA,aAAA,GAAA;;MAEA;AAAA;;IAIF,UAAA;;AAEIA,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA;AAAA;;;MAIJ;AAGA,WAAA,YAAA,QAAA,UAAA;AACE,aAAA,cAAA;AAAA;AAGF,WAAA,aAAA;AACA,WAAA,gBAAA;;AAKAA,oBAAAA,MAAA,UAAA;AAAA,QACE,OAAA;AAAA;;;;IAMJ,YAAA,OAAA;AACE,WAAA,YAAA;;AAGA,iBAAA,MAAA;;MAEA,GAAA,GAAA;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpUA,GAAG,WAAW,eAAe;"}