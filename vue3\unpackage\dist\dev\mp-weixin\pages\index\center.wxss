
.nav-box{
  position:fixed;
  z-index:99;
  top:0;
  left:0;
  width:100%;
  box-sizing:border-box;
  transition:all .3s ease-in-out
}
.nav-box .nav-item{
  position: relative;
  width: 100%;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.nav-box .nav-item .ohto{
  max-width: 420rpx;
  font-size: 26rpx;
  font-weight: 700;
  transition: all 0.3s ease-in-out;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.nav-user-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}
.user-box{
  width:calc(100% - 60rpx);
  padding:60rpx 30rpx;
  color:#fff;
  position:relative;
  overflow:hidden
}
.user-box .user-img,
.user-box .user-bg{
  position:absolute;
  top:0;
  left:0;
  width:100%;
  height:100%
}
.user-box .user-bg{
  z-index:-1;
  /* 优化性能：条件编译backdrop-filter */

  -webkit-backdrop-filter:saturate(150%) blur(25px);
  backdrop-filter:saturate(150%) blur(25px);

  background:rgba(0,0,0,.6)
}
.user-box .user-top{
  width:100%;
  justify-content:space-between
}
.user-top .avatar{
  width:140rpx;
  height:140rpx;
  border-radius:50%;
  background:#fff;
  border:2px solid #f5f5f5;
  overflow:hidden
}
.user-box .user-name{
  margin:20rpx 0 10rpx;
  width:100%;
  font-size:34rpx;
  font-weight:700
}
.user-box .user-intro{
  width:100%;
  word-break:break-word;
  white-space:pre-line
}
.user-box .user-intro text{
  color:#ccc;
  font-size:24rpx;
  font-weight:400
}
.user-box .user-tag{
  margin:20rpx 0;
  width:100%
}
.user-tag .tag-item{
  margin-right:16rpx;
  height:44rpx;
  padding:0 14rpx;
  border-radius:8rpx;
  background:rgba(255,255,255,.15);
  font-weight:500;
  font-size:20rpx;
  justify-content:center
}
.user-tag .tag-item image{
  width:24rpx;
  height:24rpx
}
.user-num-wrap {
  width: 100%;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}
.user-num {
  flex: 1;
}
.user-num .num-item {
  margin-right: 30rpx;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.user-num .num-item .t1 {
  color: #fff;
  font-size: 32rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
}
.user-num .num-item .t2 {
  font-size: 20rpx;
  font-weight: 300;
  color: #ccc;
}
.visitor-item {
  position: relative;
}
.visitor-item .badge {
  position: absolute;
  top: -12rpx;
  right: -28rpx;
  min-width: 32rpx;
  height: 32rpx;
  padding: 0 6rpx;
  background: #ff3a3a;
  color: #fff;
  border-radius: 16rpx;
  font-size: 20rpx;
  text-align: center;
  line-height: 32rpx;
  z-index: 2;
  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}
.user-actions {
  align-items: center;
}
.btn-item {
  padding: 0 30rpx;
  height: 64rpx;
  line-height: 64rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: 700;
}
.bg1 {
  color: #fff;
  background: rgba(255,255,255,.15);
}
.btn-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 8rpx;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}
.btn-icon image {
  width: 32rpx;
  height: 32rpx;
}
.user-block{
  margin-top:-30rpx;
  width:100%;
  white-space:nowrap;
  background:#fff;
  border-radius:30rpx 30rpx 0 0
}
.user-block .block-box{
  width:100%;
  padding:30rpx 15rpx;
  display:flex
}
.block-box .block-item{
  flex-shrink:0;
  margin:0 15rpx;
  padding:24rpx;
  background:#f8f8f8;
  border-radius:16rpx;
  justify-content:space-between;
  position:relative
}
.block-item .block-title .t1{
  font-size:26rpx;
  font-weight:700
}
.block-item .block-title .t2{
  margin-top:4rpx;
  color:#999;
  font-size:16rpx;
  font-weight:300
}
.block-item .cu-group{
  position:relative;
  right:38rpx
}
.cu-group .cu-item{
  z-index:3;
  width:68rpx;
  height:68rpx;
  border-radius:8rpx;
  overflow:hidden
}
.cu-group .cu-item .icon{
  margin:18rpx;
  width:32rpx;
  height:32rpx
}
.cu-group .cu-item .img{
  width:100%;
  height:100%
}
.cu-group .cu-lump2{
  position:absolute;
  z-index:2;
  left:18rpx;
  width:58rpx;
  height:58rpx;
  border-radius:8rpx;
  background:#dbdbdb
}
.cu-group .cu-lump1{
  position:absolute;
  z-index:1;
  left:38rpx;
  width:48rpx;
  height:48rpx;
  border-radius:8rpx;
  background:#eaeaea
}
.block-item .block-icon{
  position:absolute;
  right:12rpx;
  width:20rpx;
  height:20rpx;
  transform:rotate(-90deg)
}
.bar-box{
  position:-webkit-sticky;
  position:sticky;
  left:0;
  z-index:99;
  margin-top:-1px;
  width:100%;
  height:80rpx;
  background:#fff
}
.bar-box .bar-item{
  padding:0 30rpx;
  height:100%;
  flex-direction:column;
  justify-content:center;
  position:relative
}
.bar-box .bar-item text{
  font-weight:700;
  transition:all .3s ease-in-out
}
.bar-item .bar-line{
  position:absolute;
  bottom:12rpx;
  width:18rpx;
  height:6rpx;
  border-radius:6rpx;
  background:#000;
  transition:opacity .3s ease-in-out
}
/* 优化的CSS类名 */
.content-container{
  padding-bottom:180rpx
}
.like-popup{
  background:#fff;
  width:400rpx;
  padding:30rpx;
  border-radius:30rpx;
  overflow:hidden
}
.like-popup .like-img{
  margin:0 40rpx;
  width:320rpx;
  height:200rpx
}
.like-popup .like-content{
  margin:20rpx 0 40rpx;
  width:100%;
  color:#333;
  font-size:26rpx;
  text-align:center
}
.like-popup .like-btn{
  width:100%;
  height:80rpx;
  line-height:80rpx;
  text-align:center;
  font-size:24rpx;
  font-weight:700;
  color:#fff;
  background:#000;
  border-radius:16rpx
}
.df {
  display: flex;
  align-items: center;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.empty-state {
  flex-direction: column;
  padding: 120rpx 0;
}
.empty-state image {
  width: 280rpx;
  height: 280rpx;
}
.empty-state .empty-title {
  margin-top: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
}
.empty-state .empty-subtitle {
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #999;
}

/* 重试按钮样式已删除 */
.loading-state {
  flex-direction: column;
  padding: 120rpx 0;
}
.loading-text {
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #999;
}
.loading-indicator {
  justify-content: center;
}
.nav-menu-btn {
  position: absolute;
  left: 30rpx;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  -webkit-tap-highlight-color: transparent;
}
.nav-menu-btn image {
  width: 24rpx;
  height: 24rpx;
}
.nav-menu-btn:active {
  background: rgba(0,0,0,0.5);
}
.sidebar-menu {
  position: fixed;
  top: 0;
  left: -75%;
  width: 75%;
  height: 100%;
  max-height: 100vh;
  background: #fff;
  z-index: 999;
  box-shadow: 2rpx 0 10rpx rgba(0,0,0,0.1);
  transform: translateX(0);
  transition: transform 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.sidebar-menu.active {
  transform: translateX(100%);
}
.sidebar-header {
  flex-shrink: 0;
  padding: 30rpx;
  border-bottom: 1px solid #f5f5f5;
  position: relative; /* 为关闭按钮提供定位基准 */
}
.sidebar-user-info {
  display: flex;
  align-items: center;
}
.sidebar-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.sidebar-user-details {
  flex: 1;
  overflow: hidden;
}
.sidebar-user-name {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.user-status {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.status-item {
  display: flex;
  align-items: center;
  padding: 4rpx 10rpx;
  border-radius: 20rpx;
  margin-right: 12rpx;
  font-size: 20rpx;
  margin-top: 4rpx;
}
.status-icon {
  width: 80rpx;
  height: 40rpx;
  margin-right: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.status-icon image {
  width: 80rpx;
  height: 36rpx;
  display: block; /* 确保图片正确显示 */
}
.member-card {
  flex-shrink: 0;
  margin: 0 30rpx 20rpx;
  padding: 30rpx 20rpx;
  background: #2c2c2c;
  border-radius: 16rpx;
  color: #fff;
}
.member-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.member-label {
  font-size: 30rpx;
  font-weight: bold;
}
.member-price {
  padding: 6rpx 20rpx;
  background: #fff;
  color: #333;
  border-radius: 30rpx;
  font-size: 22rpx;
}
.member-benefits {
  margin-bottom: 10rpx;
}
.member-rights {
  font-size: 22rpx;
  color: rgba(255,255,255,0.8);
}
.member-desc {
  margin-top: 16rpx;
  font-size: 22rpx;
  color: rgba(255,255,255,0.7);
}
.sidebar-scroll {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}
.sidebar-content {
  padding: 16rpx 0 200rpx;
  background-color: #f7f7f7;
}

/* 菜单部分样式 */
.menu-section {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}
.section-title {
  padding: 20rpx 30rpx 10rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1px solid #f5f5f5;
}

/* 菜单宫格样式 */
.menu-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 10rpx;
  background-color: #fff;
}
.grid-item {
  width: 33.33%;
  text-align: center;
  padding: 20rpx 0;
  position: relative;
}
.grid-item:active {
  background-color: #f8f8f8;
}
.grid-icon-wrapper {
  position: relative;
  margin: 0 auto 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.grid-icon {
  width: 50rpx;
  height: 50rpx;
}
.grid-badge {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  min-width: 32rpx;
  height: 32rpx;
  padding: 0 6rpx;
  background: #ff3a3a;
  color: #fff;
  border-radius: 16rpx;
  font-size: 20rpx;
  text-align: center;
  line-height: 32rpx;
  z-index: 2;
  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}
.grid-text {
  font-size: 24rpx;
  color: #333;
  display: block;
  padding: 0 10rpx;
}
.sidebar-footer {
  flex-shrink: 0;
  background: #fff;
}
.bottom-nav {
  width: 100%;
  height: 120rpx;
  justify-content: space-around;
  padding: 0;
  background-color: #f7f7f7;
}
.bottom-nav-item {
  flex: 1;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.nav-icon-box {
  width: 50rpx;
  height: 50rpx;
  justify-content: center;
  align-items: center;
}
.nav-icon {
  width: 44rpx;
  height: 44rpx;
}
.nav-text {
  font-size: 22rpx;
  color: #666;
  margin-top: 8rpx;
}
.copyright-text {
  text-align: center;
  color: #999;
  font-size: 20rpx;
  padding: 10rpx 0 30rpx;
}
.sidebar-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0,0,0,0.5);
  z-index: 998;
  touch-action: none;
}
.no-scroll {
  overflow: hidden !important;
}
.container.no-scroll,
.container[style*="position: fixed"] {
  position: fixed !important;
  left: 0 !important;
  width: 100% !important;
  overflow: hidden !important;
  touch-action: none !important;
  height: 100vh !important;
}
.container[style*="position: fixed"] .user-box,
.container[style*="position: fixed"] .nav-box {
  transform: none !important;
}
@media screen and (max-height: 667px) {
.sidebar-scroll {
    height: calc(100vh - 350rpx - 170rpx);
}
}
.sidebar-item:active {
  background-color: #f8f8f8;
}
.menu-group {
  margin-bottom: 0;
  margin: 0 20rpx;
  border-radius: 16rpx;
  background-color: #fff;
  overflow: hidden;
}
.menu-divider {
  height: 16rpx;
  background-color: #f7f7f7;
  margin: 0;
  width: 100%;
}
.close-btn {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
button, 
.btn, 
.nav-menu-btn, 
view[role="button"] {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}
.user-top {
  width: 100%;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.avatar-wrapper {
  position: relative;
  margin-right: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  background: #fff;
  border: 2px solid #f5f5f5;
  overflow: hidden;
  position: relative;
}
.profile-percent {
  position: absolute;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
  background: #ff6600;
  border-radius: 12rpx;
  padding: 4rpx 10rpx;
  display: flex;
  align-items: center;
  font-size: 20rpx;
  color: #fff;
  z-index: 2;
  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}
.edit-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 4rpx;
}
.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-top: 20rpx;
}
.user-name-row {
  align-items: center;
  margin-bottom: 10rpx;
}
.user-name-text {
  font-size: 34rpx;
  font-weight: 700;
  color: #fff;
  margin-right: 12rpx;
}
.status-icon {
  width: 80rpx;
  height: 40rpx;
  margin-right: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.status-icon image {
  width: 80rpx;
  height: 36rpx;
  display: block; /* 确保图片正确显示 */
}
.user-id {
  font-size: 22rpx;
  color: rgba(255,255,255,0.6);
}
.user-id-row {
  align-items: center;
  margin-top: 8rpx;
}
.sex-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 6rpx;
  background: rgba(255,255,255,0.15);
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
}
.sex-icon image {
  width: 20rpx;
  height: 20rpx;
}
.vip-icon {
  border-radius: 6rpx;
  padding: 2rpx;
}
.verified-icon {
  border-radius: 6rpx;
  padding: 2rpx;
}
.tag-wrapper {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
.tag-scroll-view {
  width: calc(100% - 70rpx);
  white-space: nowrap;
}
.tag-add-btn {
  position: absolute;
  right: 0rpx;
  width: 46rpx;
  height: 46rpx;
  border-radius: 46rpx;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tag-add-btn text {
  font-size: 40rpx;
  color: #666;
  font-weight: 300;
}
.tag-add-empty {
  width: 100%;
  height: 60rpx;
  background: #f8f8f8;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tag-add-empty text {
  color: #999;
  font-size: 26rpx;
}
.tag-empty {
  flex: 1;
  height: 60rpx;
  background: rgba(255,255,255,.15);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
.tag-empty text {
  color: rgba(255,255,255,0.6);
  font-size: 24rpx;
}
.user-tag {
  flex-wrap: nowrap;
  padding: 0;
  margin-top: 0;
}
.tag-wrapper .user-tag {
  display: inline-flex;
  padding-right: 20rpx;
}
.tag-wrapper .tag-item {
  margin-right: 20rpx;
  flex-shrink: 0;
}
.menu-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx 10rpx;
  background-color: #fff;
}
.grid-item {
  width: 33.33%;
  text-align: center;
  padding: 20rpx 0;
  position: relative;
}
.grid-icon {
  width: 50rpx;
  height: 50rpx;
}
.grid-badge {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  min-width: 32rpx;
  height: 32rpx;
  padding: 0 6rpx;
  background: #ff3a3a;
  color: #fff;
  border-radius: 16rpx;
  font-size: 20rpx;
  text-align: center;
  line-height: 32rpx;
  z-index: 2;
  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}
.grid-text {
  font-size: 24rpx;
  color: #333;
  display: block;
  padding: 0 10rpx;
}
.profile-progress {
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  right: -8rpx;
  bottom: -8rpx;
  z-index: 10;
}
.progress-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.progress-inner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0,0,0,0.6);
  border-radius: 20rpx;
  padding: 4rpx 8rpx;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.progress-text {
  color: #fff;
  font-size: 20rpx;
  font-weight: 700;
  line-height: 1;
}
.avatar {
  position: relative;
}
.user-box .user-intro{
  width:100%;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.user-box .user-intro .intro-text{
  color:#ccc;
  font-size:24rpx;
  font-weight:400;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-all;
  flex: 1;
}
.user-box .user-intro .more-btn{
  position: absolute;
  right: 0;
  top: 0;
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.user-box .user-intro .more-btn image{
  width: 32rpx;
  height: 32rpx;
}
.user-box .user-top{
  width:100%;
  justify-content:space-between;
  position: relative;
  padding-right: 40rpx;
}
.user-box .user-top .right-arrow {
  position: absolute;
  right: 0;
  top: 50%;
  display: flex;
  transform: translateY(-50%) rotate(270deg)
}
.user-box .user-top .right-arrow image {
  width: 140rpx;
  height: 40rpx;
}
.stat-box {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: calc(100% - 60rpx);
  padding: 20rpx 0;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  margin: 20rpx auto;
  position: relative;
}
.stat-divider {
  width: 1px;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.2);
}
.stat-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  text-align: center;
  padding: 10rpx 0;
}
.stat-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.stat-icon .iconfont {
  font-size: 36rpx;
}
.stat-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 8rpx;
  line-height: 1;
}
.stat-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1;
}
.user-num-wrap {
  width: 100%;
}
.stat-badge {
  position: absolute;
  top: -8rpx;
  right: -10rpx;
  min-width: 30rpx;
  height: 30rpx;
  line-height: 30rpx;
  padding: 0 6rpx;
  background-color: #ff3a3a;
  color: #ffffff;
  font-size: 16rpx;
  border-radius: 15rpx;
  text-align: center;
}
.stat-icon .icon-like {
  color: #ff7ca8;
  font-size: 42rpx;
}
.stat-icon .icon-eye {
  color: #e3d6ff;
  font-size: 42rpx;
}
.stat-icon .icon-heart {
  color: #e3d6ff;
  font-size: 42rpx;
}
.stat-like-text {
  color: #ff7ca8;
  font-size: 42rpx;
}
.stat-eye-text {
  color: #e3d6ff;
  font-size: 42rpx;
}
.stat-heart-text {
  color: #e3d6ff;
  font-size: 42rpx;
}
