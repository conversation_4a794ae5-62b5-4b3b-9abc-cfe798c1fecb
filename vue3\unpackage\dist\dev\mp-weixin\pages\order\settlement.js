"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const config_api = require("../../config/api.js");
const common_assets = require("../../common/assets.js");
const navbar = () => "../../components/navbar/navbar.js";
const money = () => "../../components/money/money.js";
const uniPopup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
const app = getApp();
const _sfc_main = {
  components: {
    navbar,
    money,
    uniPopup
  },
  data() {
    var _a, _b;
    return {
      statusBarHeight: this.$store.state.statusBarHeight || 20,
      titleBarHeight: this.$store.state.titleBarHeight || 44,
      appCard: ((_a = app.globalData) == null ? void 0 : _a.isCard) || false,
      appXx: ((_b = app.globalData) == null ? void 0 : _b.appXx) || ["商城"],
      type: 0,
      productId: 0,
      quantity: 1,
      addsInfo: {
        id: 0
      },
      goodsList: [],
      orderCount: 0,
      goodsAmount: "0.00",
      discountAmount: "0.00",
      orderAmount: "0.00",
      reserveOrderAmount: "0.00",
      cardAmount: 0,
      cardId: 0,
      cardCount: 0,
      cardSelectId: 0,
      cardList: [],
      remark: "",
      tipsTitle: "",
      // Mock数据
      mockGoodsList: [
        {
          goods_id: 1001,
          goods_name: "2023夏季新款连衣裙",
          quantity: 1,
          product: {
            img: "/static/img/avatar.png",
            name: "白色 L码",
            price: "299.00",
            line_price: "399.00"
          }
        },
        {
          goods_id: 1002,
          goods_name: "轻薄透气运动鞋",
          quantity: 1,
          product: {
            img: "/static/img/avatar.png",
            name: "黑色 40码",
            price: "199.00",
            line_price: "299.00"
          }
        }
      ],
      mockAddress: {
        id: 1,
        name: "张三",
        mobile: "13800138000",
        province: "广东省",
        city: "广州市",
        county: "天河区",
        detailed: "天河路123号时尚大厦A座2301"
      },
      mockCardList: [
        {
          id: 101,
          available: true,
          card: {
            subscript: "满300可用",
            price: "50.00",
            intro: "商城通用满300减50优惠券",
            neck_create_time: "2023-05-01",
            expire_time: "2023-12-31"
          }
        },
        {
          id: 102,
          available: true,
          card: {
            subscript: "无门槛",
            price: "10.00",
            intro: "商城通用无门槛优惠券",
            neck_create_time: "2023-05-01",
            expire_time: "2023-12-31"
          }
        },
        {
          id: 103,
          available: false,
          card: {
            subscript: "满500可用",
            price: "100.00",
            intro: "商城通用满500减100优惠券",
            neck_create_time: "2023-05-01",
            expire_time: "2023-12-31"
          }
        }
      ]
    };
  },
  onLoad(options) {
    if (options.type && options.type == 1) {
      this.type = options.type;
      this.productId = options.pid;
      this.quantity = options.quantity;
    }
    this.addressInfo(0);
    this.goodsSettlement();
  },
  methods: {
    // 获取商品结算信息
    goodsSettlement() {
      let that = this;
      if (config_api.api && config_api.api.goodsSettlementUrl) {
        utils_request.request(config_api.api.goodsSettlementUrl, {
          type: that.type,
          pid: that.productId,
          quantity: that.quantity
        }).then(function(res) {
          if (res.code == 200) {
            that.goodsList = res.data;
            that.calculate();
          } else {
            that.opTipsPopup(res.msg, true);
          }
        });
      } else {
        setTimeout(() => {
          that.goodsList = that.mockGoodsList;
          that.calculate();
        }, 500);
      }
    },
    // 结算支付
    settlementPay() {
      let that = this;
      if (that.orderAmount <= 0) {
        that.opTipsPopup("支付金额不能小于0.01！");
        return;
      }
      if (that.addsInfo.id <= 0) {
        that.opTipsPopup("请选择一个收货地址！");
        return;
      }
      common_vendor.index.showLoading({
        title: "生成订单中..",
        mask: true
      });
      if (config_api.api && config_api.api.settlementPayUrl) {
        utils_request.request(config_api.api.settlementPayUrl, {
          type: that.type,
          pid: that.productId,
          quantity: that.quantity,
          aid: that.addsInfo.id,
          cid: that.cardId,
          remark: that.remark
        }, "POST").then(function(res) {
          common_vendor.index.hideLoading();
          app.globalData.isCenterPage = true;
          if (res.code == 200) {
            let payData = res.data;
            common_vendor.index.requestPayment({
              provider: "weixin",
              timeStamp: payData.timestamp,
              nonceStr: payData.nonceStr,
              package: payData.package,
              signType: payData.signType,
              paySign: payData.paySign,
              success: function() {
                that.opTipsPopup("下单成功，我们会尽快为您发货 🎉");
                setTimeout(function() {
                  common_vendor.index.redirectTo({
                    url: "/pages/order/index?idx=2"
                  });
                }, 1e3);
              },
              fail: function() {
                that.opTipsPopup("支付失败！");
                setTimeout(function() {
                  common_vendor.index.redirectTo({
                    url: "/pages/order/index?idx=1"
                  });
                }, 1e3);
              }
            });
          } else {
            that.opTipsPopup(res.msg);
          }
        });
      } else {
        setTimeout(() => {
          common_vendor.index.hideLoading();
          app.globalData.isCenterPage = true;
          common_vendor.index.showModal({
            title: "模拟支付",
            content: "这是一个模拟的支付流程，点击确定将模拟支付成功",
            success: function(res) {
              if (res.confirm) {
                that.opTipsPopup("下单成功，我们会尽快为您发货 🎉");
                setTimeout(function() {
                  common_vendor.index.redirectTo({
                    url: "/pages/order/index?idx=2"
                  });
                }, 1e3);
              } else {
                that.opTipsPopup("支付失败！");
                setTimeout(function() {
                  common_vendor.index.redirectTo({
                    url: "/pages/order/index?idx=1"
                  });
                }, 1e3);
              }
            }
          });
        }, 1e3);
      }
    },
    // 获取地址信息
    addressInfo(id) {
      let that = this;
      common_vendor.index.showLoading({
        mask: true
      });
      if (config_api.api && config_api.api.userAddressInfoUrl) {
        utils_request.request(config_api.api.userAddressInfoUrl, {
          id
        }).then(function(res) {
          common_vendor.index.hideLoading();
          if (res.code == 200 && res.data) {
            that.addsInfo = res.data;
          }
        });
      } else {
        setTimeout(() => {
          common_vendor.index.hideLoading();
          that.addsInfo = that.mockAddress;
        }, 500);
      }
    },
    // 计算价格
    calculate() {
      let that = this;
      let count = 0;
      let goodsAmount = 0;
      let discountAmount = 0;
      let orderAmount = 0;
      for (let item of that.goodsList) {
        goodsAmount += parseFloat(item.product.line_price * item.quantity);
        discountAmount += parseFloat((item.product.line_price - item.product.price) * item.quantity);
        orderAmount += parseFloat(item.product.price * item.quantity);
        count += parseInt(item.quantity);
      }
      that.goodsAmount = goodsAmount.toFixed(2);
      that.discountAmount = discountAmount.toFixed(2);
      that.orderAmount = orderAmount.toFixed(2);
      that.orderCount = count;
      if (that.appCard) {
        that.useCard();
      }
    },
    // 使用卡券
    useCard() {
      let that = this;
      if (config_api.api && config_api.api.useCardUrl) {
        utils_request.request(config_api.api.useCardUrl, {
          product_id: that.productId,
          amount: that.orderAmount
        }, "POST").then(function(res) {
          if (res.code == 200 && res.data[0]) {
            that.cardList = res.data[2];
            that.cardCount = res.data[1];
            that.cardSelectId = res.data[0].id;
            that.cardId = res.data[0].id;
            that.cardAmount = res.data[0].card.price;
            that.reserveOrderAmount = that.orderAmount;
            let newAmount = that.reserveOrderAmount - res.data[0].card.price;
            that.orderAmount = newAmount.toFixed(2);
          }
        });
      } else {
        setTimeout(() => {
          that.cardList = that.mockCardList;
          that.cardCount = that.mockCardList.length;
          let availableCard = that.mockCardList.find((card) => card.available === true);
          if (availableCard) {
            that.cardSelectId = availableCard.id;
            that.cardId = availableCard.id;
            that.cardAmount = availableCard.card.price;
            that.reserveOrderAmount = that.orderAmount;
            let newAmount = parseFloat(that.reserveOrderAmount) - parseFloat(availableCard.card.price);
            that.orderAmount = newAmount > 0 ? newAmount.toFixed(2) : "0.01";
          }
        }, 500);
      }
    },
    // 卡券弹窗点击
    cardPopupClick(isOpen) {
      if (isOpen) {
        this.$refs.cardPopup.open();
      } else {
        if (this.cardId != this.cardSelectId) {
          for (let item of this.cardList) {
            if (item.id == this.cardSelectId) {
              let newAmount = parseFloat(this.reserveOrderAmount) - parseFloat(item.card.price);
              this.orderAmount = newAmount > 0 ? newAmount.toFixed(2) : "0.01";
              this.cardId = item.id;
              this.cardAmount = item.card.price;
              break;
            }
          }
        }
        this.$refs.cardPopup.close();
      }
    },
    // 页面跳转
    navigateToFun(e) {
      common_vendor.index.navigateTo({
        url: "/pages/" + e.currentTarget.dataset.url
      });
    },
    // 返回上一页
    navBack() {
      if (getCurrentPages().length > 1) {
        common_vendor.index.navigateBack();
      } else {
        common_vendor.index.switchTab({
          url: "/pages/tabbar/shop"
        });
      }
    },
    // 提示弹窗
    opTipsPopup(msg, isBack = false) {
      let that = this;
      that.tipsTitle = msg;
      that.$refs.tipsPopup.open();
      setTimeout(function() {
        that.$refs.tipsPopup.close();
        if (isBack) {
          that.navBack();
        }
      }, 2e3);
    }
  }
};
if (!Array) {
  const _component_navbar = common_vendor.resolveComponent("navbar");
  const _component_money = common_vendor.resolveComponent("money");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_component_navbar + _component_money + _easycom_uni_popup2)();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      bg: 1
    }),
    b: common_assets._imports_1$9,
    c: $data.addsInfo.id > 0
  }, $data.addsInfo.id > 0 ? {
    d: common_vendor.t($data.addsInfo.province),
    e: common_vendor.t($data.addsInfo.city),
    f: common_vendor.t($data.addsInfo.county),
    g: common_vendor.t($data.addsInfo.detailed),
    h: common_vendor.t($data.addsInfo.name),
    i: common_vendor.t($data.addsInfo.mobile)
  } : {}, {
    j: common_assets._imports_1$4,
    k: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    l: $data.statusBarHeight + $data.titleBarHeight + "px",
    m: $data.goodsList.length
  }, $data.goodsList.length ? {
    n: common_vendor.f($data.goodsList, (item, index, i0) => {
      return {
        a: item.product.img,
        b: common_vendor.t(item.goods_name),
        c: common_vendor.t(item.product.name),
        d: common_vendor.t(item.quantity),
        e: "2becfba8-1-" + i0,
        f: common_vendor.p({
          price: item.product.price
        }),
        g: index
      };
    })
  } : {}, {
    o: common_vendor.t($data.orderCount),
    p: common_vendor.p({
      price: $data.goodsAmount,
      qs: "28rpx",
      ts: "18rpx"
    }),
    q: common_vendor.p({
      price: $data.discountAmount,
      cor: "#999",
      qs: "28rpx",
      ts: "18rpx"
    }),
    r: $data.appCard
  }, $data.appCard ? {
    s: $data.cardAmount ? "#FA5150" : "#999",
    t: common_vendor.p({
      price: $data.cardAmount,
      cor: $data.cardAmount ? "#FA5150" : "#999",
      qs: "28rpx",
      ts: "18rpx"
    }),
    v: common_vendor.t($data.cardCount ? "共 " + $data.cardCount + " 张可用" : "暂无可用"),
    w: common_assets._imports_1$4,
    x: common_vendor.o(($event) => $options.cardPopupClick(true))
  } : {}, {
    y: common_vendor.p({
      price: 0,
      cor: "#999",
      qs: "28rpx",
      ts: "18rpx"
    }),
    z: common_vendor.p({
      price: $data.orderAmount,
      qs: "28rpx",
      ts: "18rpx"
    }),
    A: $data.remark,
    B: common_vendor.o(($event) => $data.remark = $event.detail.value),
    C: common_vendor.t($data.appXx[0]),
    D: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    E: common_assets._imports_3$6,
    F: common_vendor.t($data.orderAmount),
    G: common_vendor.o((...args) => $options.settlementPay && $options.settlementPay(...args)),
    H: common_assets._imports_0$4,
    I: common_vendor.o(($event) => $options.cardPopupClick(false)),
    J: $data.cardList.length <= 0
  }, $data.cardList.length <= 0 ? {
    K: common_assets._imports_4$3
  } : {
    L: common_vendor.f($data.cardList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.card.subscript),
        b: common_vendor.t(item.card.price),
        c: common_vendor.t(item.card.intro),
        d: common_vendor.t(item.card.neck_create_time),
        e: common_vendor.t(item.card.expire_time),
        f: !item.available
      }, !item.available ? {} : {}, {
        g: index,
        h: item.id == $data.cardSelectId ? "#000" : "#f8f8f8",
        i: common_vendor.o(($event) => $data.cardSelectId = item.id, index)
      });
    }),
    M: common_assets._imports_5$4
  }, {
    N: common_vendor.o(($event) => $options.cardPopupClick(false)),
    O: common_vendor.sr("cardPopup", "2becfba8-7"),
    P: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    }),
    Q: common_vendor.t($data.tipsTitle),
    R: common_vendor.sr("tipsPopup", "2becfba8-8"),
    S: common_vendor.p({
      type: "top",
      ["mask-background-color"]: "rgba(0, 0, 0, 0)"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/order/settlement.js.map
