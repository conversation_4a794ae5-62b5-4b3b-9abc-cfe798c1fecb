"use strict";
const common_vendor = require("../../common/vendor.js");
const api_social = require("../../api/social.js");
const common_assets = require("../../common/assets.js");
const lazyImage = () => "../../components/lazyImage/lazyImage.js";
const uniLoadMore = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
getApp();
const _sfc_main = {
  components: {
    lazyImage,
    uniLoadMore
  },
  data() {
    return {
      statusBarHeight: this.$store.state.statusBarHeight || 20,
      titleBarHeight: this.$store.state.titleBarHeight || 44,
      barList: ["全部", "我的"],
      barIdx: 0,
      list: [],
      page: 1,
      limit: 10,
      totalCount: 0,
      isEmpty: false,
      isThrottling: true,
      loadStatus: "more",
      tipsTitle: "",
      showLoading: false,
      loadingTimer: null,
      debounceTimer: null
    };
  },
  onLoad(option) {
    if (option && option.type) {
      this.barIdx = parseInt(option.type) || 0;
    }
    this.getCircleData();
  },
  // 开启下拉刷新
  onPullDownRefresh() {
    if (this.loadStatus !== "loading") {
      this.page = 1;
      this.getCircleData();
    } else {
      common_vendor.index.stopPullDownRefresh();
    }
  },
  methods: {
    // 获取圈子数据
    getCircleData() {
      let that = this;
      that.isThrottling = false;
      that.loadStatus = "loading";
      if (that.loadingTimer) {
        clearTimeout(that.loadingTimer);
      }
      that.loadingTimer = setTimeout(() => {
        if (that.loadStatus === "loading") {
          that.showLoading = true;
        }
      }, 300);
      const params = {
        page: that.page,
        limit: that.limit
      };
      let apiMethod;
      if (that.barIdx === 0) {
        apiMethod = api_social.getCircleList;
      } else {
        apiMethod = api_social.getJoinedCircles;
      }
      apiMethod(params).then((res) => {
        that.isThrottling = true;
        that.loadStatus = "more";
        if (that.loadingTimer) {
          clearTimeout(that.loadingTimer);
          that.loadingTimer = null;
        }
        that.showLoading = false;
        common_vendor.index.stopPullDownRefresh();
        if (res.status === 200 && res.data) {
          const responseData = res.data;
          if (responseData.list && responseData.list.length > 0) {
            if (that.page == 1) {
              that.list = responseData.list;
            } else {
              that.list = that.list.concat(responseData.list);
            }
            if (responseData.count !== void 0) {
              that.totalCount = responseData.count;
            }
            that.isEmpty = false;
          } else if (that.page == 1) {
            that.isEmpty = true;
            that.list = [];
          }
          if (that.list.length >= that.totalCount && that.list.length > 0) {
            that.loadStatus = "noMore";
          }
        } else {
          if (that.page == 1) {
            that.isEmpty = true;
            that.list = [];
          }
          that.opTipsPopup(res.msg || "获取数据失败");
        }
      }).catch((err) => {
        that.isThrottling = true;
        that.loadStatus = "more";
        if (that.loadingTimer) {
          clearTimeout(that.loadingTimer);
          that.loadingTimer = null;
        }
        that.showLoading = false;
        common_vendor.index.stopPullDownRefresh();
        if (that.page == 1) {
          that.isEmpty = true;
          that.list = [];
        }
        that.opTipsPopup("网络错误，请稍后重试");
        common_vendor.index.__f__("error", "at pages/center/circle.vue:233", "获取圈子列表失败:", err);
      });
    },
    // 标签切换
    barClick(e) {
      if (!this.isThrottling || this.loadStatus === "loading") {
        return;
      }
      const clickIdx = parseInt(e.currentTarget.dataset.idx);
      if (clickIdx === this.barIdx) {
        return;
      }
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }
      this.debounceTimer = setTimeout(() => {
        this.isThrottling = false;
        this.barIdx = clickIdx;
        this.page = 1;
        this.loadStatus = "loading";
        this.list = [];
        this.getCircleData();
      }, 100);
    },
    // 跳转到圈子详情
    toCircle(e) {
      if (!e || !e.currentTarget || !e.currentTarget.dataset || !e.currentTarget.dataset.id) {
        this.opTipsPopup("圈子信息无效");
        return;
      }
      const id = parseInt(e.currentTarget.dataset.id);
      if (!id || id <= 0) {
        this.opTipsPopup("圈子ID无效");
        return;
      }
      common_vendor.index.navigateTo({
        url: "/pages/note/circle?id=" + id,
        fail: () => {
          this.opTipsPopup("页面跳转失败");
        }
      });
    },
    // 返回上一页
    navBack() {
      const pages = getCurrentPages();
      if (pages.length > 1) {
        common_vendor.index.navigateBack({
          fail: () => {
            common_vendor.index.switchTab({
              url: "/pages/user/index"
            });
          }
        });
      } else {
        common_vendor.index.switchTab({
          url: "/pages/user/index"
        });
      }
    },
    // 显示提示信息
    opTipsPopup(msg) {
      this.tipsTitle = msg;
      this.$refs.tipsPopup.open();
      setTimeout(() => {
        this.$refs.tipsPopup.close();
      }, 2e3);
    }
  },
  onReachBottom() {
    if (this.loadStatus === "loading") {
      return;
    }
    if (this.isThrottling && this.list.length && this.list.length < this.totalCount) {
      this.page = this.page + 1;
      this.loadStatus = "loading";
      this.getCircleData();
    } else if (this.list.length >= this.totalCount && this.list.length > 0) {
      this.loadStatus = "noMore";
    }
  },
  onUnload() {
    if (this.loadingTimer) {
      clearTimeout(this.loadingTimer);
      this.loadingTimer = null;
    }
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
  }
};
if (!Array) {
  const _component_lazy_image = common_vendor.resolveComponent("lazy-image");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_component_lazy_image + _easycom_uni_popup2)();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_2$4,
    b: common_vendor.o((...args) => $options.navBack && $options.navBack(...args)),
    c: $data.titleBarHeight + "px",
    d: common_vendor.f($data.barList, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index == $data.barIdx ? "#000" : "#999",
        c: index == $data.barIdx ? "28rpx" : "26rpx",
        d: index == $data.barIdx ? 1 : 0,
        e: index,
        f: common_vendor.o((...args) => $options.barClick && $options.barClick(...args), index),
        g: index
      };
    }),
    e: $data.statusBarHeight + "px",
    f: $data.showLoading
  }, $data.showLoading ? {} : {}, {
    g: $data.isEmpty
  }, $data.isEmpty ? {
    h: common_assets._imports_3$1,
    i: common_vendor.t($data.barIdx == 0 ? "暂无推荐圈子" : "暂无加入圈子")
  } : {
    j: common_vendor.f($data.list, (item, index, i0) => {
      return common_vendor.e({
        a: "c98d18d0-0-" + i0,
        b: common_vendor.p({
          src: item.circle_avatar || item.avatar,
          br: "168rpx"
        }),
        c: item.is_official == 1
      }, item.is_official == 1 ? {} : item.is_hot == 1 ? {} : {}, {
        d: item.is_hot == 1,
        e: common_vendor.t(item.circle_name || item.name),
        f: common_vendor.t(item.circle_description || item.intro),
        g: item.member_count > 0
      }, item.member_count > 0 ? common_vendor.e({
        h: _ctx.imgIndex < 3
      }, _ctx.imgIndex < 3 ? {
        i: common_vendor.f(item.recent_members, (member, imgIndex, i1) => {
          return {
            a: member.avatar,
            b: imgIndex
          };
        })
      } : {}, {
        j: common_vendor.t(item.member_count),
        k: common_vendor.t(item.dynamic_count || 0),
        l: item.view_count
      }, item.view_count ? {
        m: common_vendor.t(item.view_count)
      } : {}) : {}, {
        n: item.recent_topics && item.recent_topics.length
      }, item.recent_topics && item.recent_topics.length ? {
        o: common_vendor.f(item.recent_topics, (topic, topicIndex, i1) => {
          return {
            a: common_vendor.t(topic),
            b: topicIndex
          };
        })
      } : {}, {
        p: index,
        q: item.id,
        r: common_vendor.o((...args) => $options.toCircle && $options.toCircle(...args), index)
      });
    })
  }, {
    k: $data.list.length > 0 && $data.loadStatus === "noMore"
  }, $data.list.length > 0 && $data.loadStatus === "noMore" ? {} : {}, {
    l: "calc(" + ($data.statusBarHeight + $data.titleBarHeight) + "px + 90rpx)",
    m: common_vendor.t($data.tipsTitle),
    n: common_vendor.sr("tipsPopup", "c98d18d0-1"),
    o: common_vendor.p({
      type: "top",
      ["mask-background-color"]: "rgba(0, 0, 0, 0)"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/center/circle.js.map
