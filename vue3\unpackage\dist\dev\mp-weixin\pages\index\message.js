"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const uniLoadMore = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const tabbar = () => "../../components/tabbar/tabbar.js";
const emptyPage = () => "../../components/emptyPage/emptyPage.js";
const _sfc_main = {
  components: {
    uniLoadMore,
    tabbar,
    emptyPage
  },
  data() {
    return {
      statusBarHeight: this.$store.state.statusBarHeight || 20,
      titleBarHeight: this.$store.state.titleBarHeight || 44,
      currentMsg: 0,
      contactsCount: 2,
      // 人脉红点数量
      isEmpty: false,
      loadStatus: "more",
      tipsTitle: "",
      // 消息列表数据
      messageList: [
        {
          id: 1,
          title: "应用消息",
          description: "生活是流星，开启一段新的旅行",
          time: "18:36",
          unreadCount: 1,
          iconClass: "app-icon",
          type: "app"
        },
        {
          id: 2,
          title: "系统公告",
          description: "熟读《图鸟人脉防骗指南》",
          time: "12:29",
          unreadCount: 12,
          iconClass: "system-icon",
          type: "system"
        },
        {
          id: 3,
          title: "人脉通知",
          description: "有两位新的朋友申请待通过",
          time: "11:06",
          unreadCount: 2,
          iconClass: "contact-icon",
          type: "contact"
        },
        {
          id: 4,
          title: "智能助手",
          description: "人工智能，助力您的魅力值提升",
          time: "10:68",
          unreadCount: 0,
          iconClass: "ai-icon",
          type: "ai"
        },
        {
          id: 5,
          title: "付衣衣",
          description: "我喜欢你，就像你喜欢我一样",
          time: "08:26",
          unreadCount: 0,
          iconClass: "user-icon",
          type: "user",
          avatar: "/static/img/logo.png"
        }
      ]
    };
  },
  async onLoad() {
    this.$store.commit("SET_CURRENT_MSG", false);
    await this.$onLaunched;
    this.$store.commit("SET_CURRENT_MSG", true);
    this.initPageData();
  },
  onShow() {
    this.initPageData();
  },
  onPullDownRefresh() {
    this.initPageData();
    common_vendor.index.stopPullDownRefresh();
  },
  onReachBottom() {
    common_vendor.index.__f__("log", "at pages/index/message.vue:192", "到达底部，可以加载更多消息");
  },
  onShareAppMessage() {
    var _a, _b;
    return {
      title: ((_a = this.$store.state.appInfo) == null ? void 0 : _a.title) || "小程序示例",
      imageUrl: ((_b = this.$store.state.appInfo) == null ? void 0 : _b.shareImg) || "/static/img/temp/1.jpg"
    };
  },
  onShareTimeline() {
    var _a, _b;
    return {
      title: ((_a = this.$store.state.appInfo) == null ? void 0 : _a.title) || "小程序示例",
      imageUrl: ((_b = this.$store.state.appInfo) == null ? void 0 : _b.shareImg) || "/static/img/temp/1.jpg"
    };
  },
  methods: {
    // 顶部功能图标点击事件
    goToLike() {
      common_vendor.index.navigateTo({
        url: "/pages/like/index"
      });
    },
    goToComment() {
      common_vendor.index.navigateTo({
        url: "/pages/comment/index"
      });
    },
    goToFavorite() {
      common_vendor.index.navigateTo({
        url: "/pages/favorite/index"
      });
    },
    goToContacts() {
      common_vendor.index.navigateTo({
        url: "/pages/contacts/index"
      });
    },
    // 消息项点击事件
    handleMessageClick(item, index) {
      switch (item.type) {
        case "app":
          common_vendor.index.navigateTo({
            url: "/pages/app-message/index"
          });
          break;
        case "system":
          common_vendor.index.navigateTo({
            url: "/pages/system-notice/index"
          });
          break;
        case "contact":
          common_vendor.index.navigateTo({
            url: "/pages/contact-notice/index"
          });
          break;
        case "ai":
          common_vendor.index.navigateTo({
            url: "/pages/ai-assistant/index"
          });
          break;
        case "user":
          common_vendor.index.navigateTo({
            url: "/pages/chat/index?userId=" + item.id
          });
          break;
        default:
          common_vendor.index.__f__("log", "at pages/index/message.vue:262", "未知消息类型");
      }
      if (item.unreadCount > 0) {
        this.markAsRead(item, index);
      }
    },
    // 标记消息为已读
    markAsRead(item, index) {
      this.messageList[index].unreadCount = 0;
      common_vendor.index.__f__("log", "at pages/index/message.vue:275", "标记消息已读:", item.title);
    },
    // 初始化页面数据
    initPageData() {
      this.currentMsg = this.messageList.reduce((total, item) => total + item.unreadCount, 0);
      if (this.currentMsg > 0) {
        this.$store.commit("SET_UNREAD_MESSAGE_COUNT", this.currentMsg);
      }
    },
    // 全部标记为已读
    allLook() {
      if (this.currentMsg === 0) {
        common_vendor.index.showToast({
          title: "暂无未读消息！",
          icon: "none"
        });
        return;
      }
      this.messageList.forEach((item) => {
        item.unreadCount = 0;
      });
      this.currentMsg = 0;
      this.contactsCount = 0;
      this.$store.commit("SET_UNREAD_MESSAGE_COUNT", 0);
      common_vendor.index.showToast({
        title: "已将所有消息标记为已读！",
        icon: "success"
      });
    },
    // 提示弹窗
    opTipsPopup(title) {
      this.tipsTitle = title;
      this.$refs.tipsPopup.open();
      setTimeout(() => {
        this.$refs.tipsPopup.close();
      }, 2e3);
    }
  }
};
if (!Array) {
  const _component_emptyPage = common_vendor.resolveComponent("emptyPage");
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  const _component_tabbar = common_vendor.resolveComponent("tabbar");
  (_component_emptyPage + _easycom_uni_load_more2 + _easycom_uni_popup2 + _component_tabbar)();
}
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_load_more + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$3,
    b: common_vendor.o((...args) => $options.allLook && $options.allLook(...args)),
    c: $data.titleBarHeight + "px",
    d: $data.statusBarHeight + "px",
    e: common_assets._imports_1$2,
    f: common_vendor.o((...args) => $options.goToLike && $options.goToLike(...args)),
    g: common_assets._imports_1$2,
    h: common_vendor.o((...args) => $options.goToComment && $options.goToComment(...args)),
    i: common_assets._imports_1$2,
    j: common_vendor.o((...args) => $options.goToFavorite && $options.goToFavorite(...args)),
    k: common_assets._imports_1$2,
    l: $data.contactsCount > 0
  }, $data.contactsCount > 0 ? {
    m: common_vendor.t($data.contactsCount)
  } : {}, {
    n: common_vendor.o((...args) => $options.goToContacts && $options.goToContacts(...args)),
    o: "calc(" + ($data.statusBarHeight + $data.titleBarHeight) + "px + 20rpx)",
    p: $data.isEmpty
  }, $data.isEmpty ? {
    q: common_vendor.p({
      title: "暂无相关通知",
      description: "在信息爆炸的时代，这里格外宁静",
      image: "/static/img/qz2.png"
    })
  } : {
    r: common_vendor.f($data.messageList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.n(item.iconClass),
        b: common_vendor.t(item.title),
        c: common_vendor.t(item.time),
        d: item.unreadCount > 0
      }, item.unreadCount > 0 ? {
        e: common_vendor.t(item.unreadCount)
      } : {}, {
        f: common_vendor.t(item.description),
        g: index,
        h: common_vendor.o(() => $options.handleMessageClick(item, index), index)
      });
    }),
    s: common_assets._imports_1$2
  }, {
    t: !$data.isEmpty
  }, !$data.isEmpty ? {
    v: common_vendor.p({
      status: $data.loadStatus
    })
  } : {}, {
    w: common_assets._imports_2$2,
    x: common_vendor.o((...args) => _ctx.readNotice && _ctx.readNotice(...args)),
    y: common_assets._imports_3$3,
    z: common_vendor.o((...args) => _ctx.delNotice && _ctx.delNotice(...args)),
    A: common_vendor.o((...args) => _ctx.closeMoreClick && _ctx.closeMoreClick(...args)),
    B: common_vendor.sr("morePopup", "172eaf98-2"),
    C: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    }),
    D: common_vendor.t($data.tipsTitle),
    E: common_vendor.sr("tipsPopup", "172eaf98-3"),
    F: common_vendor.p({
      type: "top",
      ["mask-background-color"]: "rgba(0, 0, 0, 0)"
    })
  }, {
    G: common_vendor.p({
      currentPage: 3,
      currentMsg: $data.currentMsg
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/message.js.map
